<template>
  <div
    id="userLayout"
    :class="['user-layout-wrapper', device]"
  >
    <div class="container">
      <router-view />
    </div>
  </div>
</template>

<script>
import { mixinDevice } from '@/utils/mixin'

export default {
  name: 'UserLayout',
  mixins: [mixinDevice],
  data () {
    return {}
  },
  mounted () {
    document.body.classList.add('userLayout')
  },
  beforeDestroy () {
    document.body.classList.remove('userLayout')
  }
}
</script>

<style lang="less" scoped>
#userLayout.user-layout-wrapper {
  height: 100%;

  &.mobile {
    .container {
      .main {
        max-width: 368px;
        width: 98%;
      }
    }
  }

  .container {
    width: 100%;
    height: 100%;
    background-size: 100%;
    background-repeat: no-repeat;
    position: relative;
  }
}
</style>
