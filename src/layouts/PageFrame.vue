<template>
  <section>
    <a-spin
      size="large"
      tip="正在加载访问中..."
      wrapperClassName="wrapper"
      :spinning="spinning"
    >
      <iframe
        ref="iframe"
        width="100%"
        height="100%"
        scrolling="yes"
        frameborder="0"
        :src="external"
      />
    </a-spin>
  </section>
</template>

<script>
import { ExternalLink } from '@/utils/mixin'

export default {
  name: 'PageFrame',
  mixins: [ExternalLink],
  data () {
    return {
      spinning: true
    }
  },
  computed: {
    external () {
      // 处理外部链接
      return this.getExternal(this.$route)
    }
  },
  mounted () {
    // 设定加载超时时间
    setTimeout(() => {
      this.spinning = false
    }, 10000)
    // 外部链接加载状态关闭
    this.$refs.iframe.onload = () => {
      this.spinning = false
    }
  }
}
</script>

<style lang="less" scoped>
section {
  display: flex;
  flex-direction: column;
  flex: 1 0 auto;
  width: 100%;
  height: 100%;
  .wrapper {
    display: flex;
    flex-direction: column;
    flex: 1 0 auto;
    width: 100%;
    height: 100%;
    ::v-deep {
      .ant-spin-container {
        display: flex;
        flex-direction: column;
        flex: 1 0 auto;
        width: 100%;
        height: 100%;
      }
    }
  }
  iframe {
    display: flex;
    flex-direction: column;
    flex: 1 0 auto;
    width: 100%;
    height: 100%;
  }
}
</style>
