import { constantRouterMap } from '@/config/router.config'
import { UserLayout } from '@/layouts'

Object.assign(constantRouterMap, [
  {
    path: '/user',
    component: UserLayout,
    redirect: '/user/login',
    hidden: true,
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import(`@/views/material/user/Login`)
      },
      {
        path: 'loginSys',
        name: 'loginSys',
        component: () => import(`@/views/material/user/LoginSys`)
      }
    ]
  },
  {
    path: '/sso/SSOlogin',
    name: 'SSOlogin',
    component: () => import('@/views/system/sso/SSOlogin')
  },
  {
    path: '/sso/SSOMDlogin',
    name: 'SSOMDlogin',
    component: () => import('@/views/system/sso/SSOMDlogin')
  }
])

export {
  constantRouterMap
}
