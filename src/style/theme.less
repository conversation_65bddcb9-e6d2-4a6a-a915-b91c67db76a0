.theme(
  @backcolor:#f5f5f5,
  @fcolor:#1b1b1b,
  @boxcolor: #ffffff77,
  @btncolor: rgb(255, 255, 255),
  @flexboxcolor: #ededed,
  @modelcolor: #fffffff3,
  @tablethcolor: #ededed,
  @tabletdcolor: #ffffff,
  @collapsecolor: #ffffff,
  @collapsecolorbottom: #ecececd0,
  @selectbackcolor: #ffffff,
  @hoverbackcolor: #b7dcff,
  @selectrangecolor: #b6e8ff,
  @inputcolor: #f1f1f1
  ) {
  .m-art {
    margin: -18px -18px 0;
    position: relative;
    background: @backcolor;
    height: auto;
    .ant-pagination {
      color: @fcolor;
    }
    .ant-drawer-header {
      background-color: @modelcolor !important;
      color: @fcolor !important;
      .ant-drawer-close {
        color: @fcolor !important;
      }
      .ant-drawer-title {
        color: @fcolor !important;
      }
    }
    .drawer-footer {
      background-color: @modelcolor !important;
      color: @fcolor !important;
      .footer-fixed {
        background-color: @modelcolor !important;
        color: @fcolor !important;
      }
    }
    .ant-drawer-content {
      background-color: @modelcolor !important;
      color: @fcolor !important;
      .ant-form-item-label {
        color: @fcolor !important;
      }
      label {
        color: @fcolor !important;
      }
      span {
        color: @fcolor !important;
      }
      .workorder-empty {
        color: @fcolor !important;
      }
    }
    .image {
      width: 100%;
      height: auto;
      z-index: 1;
      display: block;
      background-repeat: no-repeat;
      background-size: contain;
      padding: 80px 65px;
    }
    .ant-progress-text {
      color: @fcolor;
    }
    .ant-calendar-picker {
      color: @fcolor;
      background: @backcolor;
    }

    .ant-input:-webkit-autofill {
      box-shadow: 0 0 0px 1000px transparent inset !important;
    }
    .ant-input {
      border: none;
      background: @inputcolor;
    }
    .ant-input:-internal-autofill-previewed {
      -webkit-text-fill-color: @fcolor !important;
      transition: background-color 5000s ease-in-out 0s !important;
    }
    .ant-input:-internal-autofill-selected {
      -webkit-text-fill-color: @fcolor !important;
      transition: background-color 5000s ease-in-out 0s !important;
    }
    .ant-btn {
      background-image: @btncolor;
      color: @fcolor;
      border: none;
    }
    .ant-calendar-range-picker-input {
      color: @fcolor;
    }
    .ant-calendar-picker-icon {
      color: @fcolor;
    }
    .ant-calendar-picker-clear {
      background: transparent;
    }
    .rangePickerIceGai {
      background: @selectbackcolor !important;
      .ant-dropdown-menu {
        background: @selectbackcolor !important;
        color: @fcolor;
      }
      .ant-dropdown-menu-item {
        color: @fcolor;
      }
      .ant-dropdown-menu-item:hover {
        background: @hoverbackcolor !important;
        color: @fcolor !important;
      }
      .ant-calendar {
        background: @selectbackcolor !important;
        color: @fcolor !important;
      }
      .ant-select-dropdown-menu-item-selected {
        background: @backcolor !important;
        color: @fcolor !important;
      }
      .ant-select-dropdown {
        background: @selectbackcolor !important;
        color: @fcolor !important;
      }
      .ant-select-dropdown-menu-item {
        background: @selectbackcolor !important;
        color: @fcolor !important;
      }
      .ant-select-dropdown-menu-item:hover:not(.ant-select-dropdown-menu-item-disabled) {
        background: @hoverbackcolor !important;
        color: @fcolor !important;
      }
      .ant-calendar-input {
        background: @selectbackcolor !important;
        color: @fcolor !important;
      }
      .ant-calendar-year-panel {
        background: @selectbackcolor !important;
        color: @fcolor !important;
      }
      .ant-calendar-year-panel-decade-select {
        color: @fcolor;
      }
      .ant-calendar-year-panel-next-decade-cell .ant-calendar-year-panel-year {
        color: @fcolor;
        opacity: 0.7;
      }
      .ant-calendar-year-panel-last-decade-cell .ant-calendar-year-panel-year {
        color: @fcolor;
        opacity: 0.7;
      }
      .ant-calendar-year-panel-year {
        color: @fcolor;
      }
      .ant-calendar-month-panel {
        background: @selectbackcolor !important;
        color: @fcolor !important;
      }
      .ant-calendar-month-panel-year-select {
        color: @fcolor;
      }
      .ant-calendar-month-panel-month {
        color: @fcolor;
      }
      .ant-calendar-month-panel-month:hover {
        color: #5fb1ff !important;
      }
      .ant-calendar-year-select {
        color: @fcolor;
      }
      .ant-calendar-month-select {
        color: @fcolor;
      }
      .ant-calendar-date {
        color: @fcolor;
      }
      .ant-calendar-year-panel-year:hover {
        color: #5fb1ff !important;
      }
      .ant-calendar-range .ant-calendar-in-range-cell::before {
        background: @selectrangecolor !important;
      }
      .ant-calendar-selected-start-date .ant-calendar-date {
        color: #ffffff;
      }
    }
    .ant-form-item-label > label {
      color: @fcolor !important;
    }
    .ant-select-selection {
      background: @backcolor;
      border: none;
    }
    .ant-input {
      color: @fcolor;
    }
    .ant-select-selection__placeholder {
      color: #666;
    }
    .ant-select-arrow-icon {
      color: #666;
    }
    .ant-calendar-picker-container {
      color: @fcolor;
      background: @backcolor;
    }
    .ant-select-selection__rendered {
      color: @fcolor;
    }
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      outline: 3px solid rgba(14, 102, 184, 0.274);
    }
    .m-btn {
      position: absolute;
      top: 20px;
      right: 20px;
    }
    .ant-badge-status-text {
      color: @fcolor;
      font-size: 12px;
    }
    .ant-radio-group {
      /* background-color: #ffffff00; */
      .ant-radio-button-wrapper {
        background-color: @btncolor;
        color: @fcolor;
        border: none;
      }
    }
    .ant-radio-button-wrapper-checked {
      color: #0060c1 !important;
      border: 1px solid #0060c1 !important;
    }
    .m-routerview {
      width: 100%;
      height: 100%;
      margin-top: 80px;
    }
    .ant-modal-content {
      background-color: @modelcolor !important;
      color: @fcolor !important;
      overflow: scroll;
    }
    .ant-modal-header {
      background-color: @modelcolor !important;
      color: @fcolor !important;
    }
    .ant-modal-title {
      color: @fcolor !important;
    }
    .ant-modal-close {
      color: @fcolor !important;
    }

    .ant-table-thead > tr > th {
      color: @fcolor !important;
      background: @tablethcolor !important;
    }
    table {
      background-color: transparent !important;
    }
    .ant-table-thead {
      background-color: transparent !important;
    }
    .ant-table-fixed .ant-table-row-hover,
    .ant-table-row-hover > td {
      background-color: transparent !important;
    }
    .ant-table-fixed .ant-table-fixed-columns-in-body {
      background-color: @tabletdcolor !important;
      color: @fcolor !important;
    }
    .ant-table-fixed .ant-table-row-cell-break-word {
      background-color: @tabletdcolor !important;
      color: @fcolor !important;
    }
    .ant-table-placeholder {
      background-color: @tabletdcolor !important;
    }
    .ant-empty-description {
      color: @fcolor !important;
    }
    .ant-table-row-cell-ellipsis {
      background-color: @tabletdcolor !important;
      color: @fcolor !important;
    }
    .ant-table-row-cell-break-word > tr > td {
      background-color: @tabletdcolor !important;
      color: @fcolor !important;
    }
    .ant-table-row-cell-break-word > tr:nth-child(n):hover > td {
      background-color: @tabletdcolor !important;
      color: @fcolor !important;
    }
    .ant-table-fixed-header > .ant-table-content > .ant-table-scroll > .ant-table-body {
      background-color: @tabletdcolor !important;
    }
    .ant-table-fixed-header .ant-table-scroll .ant-table-header {
      background-color: @tabletdcolor !important;
      overflow: hidden !important;
      margin-bottom: 0 !important;
    }
    .ant-collapse-borderless {
      background: @collapsecolor !important;
      color: @fcolor !important;
    }
    .ant-collapse > .ant-collapse-item > .ant-collapse-header {
      color: @fcolor !important;
    }
    .ant-collapse-content {
      color: @fcolor !important;
    }
    .ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-arrow {
      top: 23px;
    }
    .ant-tag-checkable {
      background: #ccd3da;
    }
    .ant-tag-checkable-checked {
      background: #1890ff !important;
    }
    .ant-progress-inner {
      background: #8a9fb448;
    }
  }
  .cardSlick {
    background: @boxcolor;
  }
  .crewCard {
    width: 100%;
    height: 190px;
    margin: 0.5%;
    background: @boxcolor;

    .crewCard-head {
      width: 100%;
      height: 45px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 3px 15px;
      border: 1px solid black;
      border-image: linear-gradient(to top left, rgba(255, 255, 255, 0) 0%, #0060c1 20%, rgba(255, 255, 255, 0) 99%) 21;
      border-width: 1px 0;
      .crewCard-title {
        display: flex;
        align-items: center;
        width: 100%;
        height: 45px;
        font-size: 18px;
        font-weight: 700;
        color: @fcolor;
      }
      .crewCard-icon {
        font-size: 18px;
        font-weight: 400;
        color: @fcolor;
      }
    }
    .crewCard-text {
      color: @fcolor;
      padding: 5px 0;
    }
    .commonText {
      color: @fcolor;
    }
  }
  .CrewCardPlus {
    width: 100%;
    height: 100%;
    background: @boxcolor;
    .crewCard-head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 45px;
      padding: 3px 15px;
      border: 1px solid black;
      border-image: linear-gradient(to top left, rgba(255, 255, 255, 0) 0%, #0060c1 20%, rgba(255, 255, 255, 0) 99%) 21;
      border-width: 1px 0;
      .crewCard-title {
        display: flex;
        align-items: center;
        height: 45px;
        font-size: 18px;
        font-weight: 700;
        color: @fcolor;
      }
      .crewCard-icon {
        font-size: 18px;
        font-weight: 400;
        color: @fcolor;
      }
    }
    .crewCard-text {
      height: 100%;
      color: @fcolor;
    }
    .content-bottom {
      border: 1px solid black;
      border-image: linear-gradient(to top left, rgba(255, 255, 255, 0) 0%, #0060c1 20%, rgba(255, 255, 255, 0) 99%) 21;
      border-width: 1px 0;
      .info-bottom {
        background-color: @collapsecolorbottom;
      }
    }
  }
  .CrewCardPlusOrder {
    background: @boxcolor;
    width: 49%;
    height: 100%;
    .crewCard-head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 45px;
      padding: 3px 15px;
      border: 1px solid black;
      border-image: linear-gradient(to top left, rgba(255, 255, 255, 0) 0%, #0060c1 20%, rgba(255, 255, 255, 0) 99%) 21;
      border-width: 1px 0;
      .crewCard-title {
        display: flex;
        align-items: center;
        width: 100%;
        height: 45px;
        font-size: 18px;
        font-weight: 700;
        color: @fcolor;
      }
      .crewCard-icon {
        font-size: 18px;
        font-weight: 400;
        color: @fcolor;
      }
    }
    .crewCard-text {
      height: 100%;
      color: @fcolor;
    }
  }
  .CrewCardDeb {
    background: @boxcolor;
    width: 49%;
    height: 100%;
    .crewCard-head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 45px;
      padding: 3px 15px;
      border: 1px solid black;
      border-image: linear-gradient(to top left, rgba(255, 255, 255, 0) 0%, #0060c1 20%, rgba(255, 255, 255, 0) 99%) 21;
      border-width: 1px 0;
      .crewCard-title {
        display: flex;
        align-items: center;
        align-items: center;
        width: 100%;
        height: 45px;
        font-size: 18px;
        font-weight: 700;
        color: @fcolor;
      }
      .crewCard-icon {
        font-size: 18px;
        font-weight: 400;
        color: @fcolor;
      }
    }
    .crewCard-text {
      height: 100%;
      color: @fcolor;
    }
  }
  .CrewCardDebPlus {
    background: @boxcolor;
    width: 100%;
    height: 100%;
    .crewCard-head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 45px;
      padding: 3px 15px;
      border: 1px solid black;
      border-image: linear-gradient(to top left, rgba(255, 255, 255, 0) 0%, #0060c1 20%, rgba(255, 255, 255, 0) 99%) 21;
      border-width: 1px 0;
      .crewCard-title {
        display: flex;
        align-items: center;
        width: 100%;
        height: 45px;
        font-size: 18px;
        font-weight: 700;
        color: @fcolor;
      }
      .crewCard-icon {
        font-size: 18px;
        font-weight: 400;
        color: @fcolor;
      }
    }
    .crewCard-text {
      height: 100%;
      color: @fcolor;
    }
    .flexBox {
      background: @flexboxcolor;
      border-radius: 5px;
    }
  }
  .CrewEchart {
    display: flex;
    justify-content: space-between;
    width: 49%;
    height: 135px;
    background-color: @backcolor;
  }
  .CrewToolTip {
    width: 100%;
    height: 180px;
    margin-bottom: 10px;
    background-color: @backcolor;
    display: flex;
  }
  .cardSlick {
    .CrewToolTip {
      width: 100%;
      height: 180px;
      margin-bottom: 10px;
      background-color: @backcolor;
      display: flex;
    }
  }
  .check-large {
    background-color: @backcolor;
    margin-bottom: 10px;
  }
}
