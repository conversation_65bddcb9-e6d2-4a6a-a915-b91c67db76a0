import store from '@/store/index'

// 引入模块
import app from '@/store/modules/app'
import user from '@/store/modules/user'
import tags from '@/store/modules/tags'
import pages from '@/store//modules/pages'
import permission from '@/store/modules/async-router'

// 静态路由
import { constantRouterMap } from './router.constant'

// 初始化路由
permission.state.routers = constantRouterMap

// 注册模块
store.registerModule('app', app)
store.registerModule('user', user)
store.registerModule('tags', tags)
store.registerModule('pages', pages)
store.registerModule('permission', permission)

export default store
