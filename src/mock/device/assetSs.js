import Mock from 'mockjs2'
import api from '@/api/device/assetSs'
import { toRegex, builder } from '../util'

Mock.mock(toRegex(api.queryFacility), 'post', options => {
  const arr = []
  const DTO = {
    uuid: 1,
    surfaceStructure: '面层结构',
    remark: '备注',
    cdbg: '场地标高',
    createBy: '创建人',
    createDate: '2020-08-05', // '创建时间',
    assetSsNum: '202008051123xx1', // '设施编号',
    djjc: '地基基础',
    dytdzh: '对应土地证号',
    gettype: '获得方式',
    completeDate: '2019-09-11', // '建成投产日期',
    structure: '结构形式',
    completedQuaGrade: '竣工验收质量等级',
    floorage: 1001, // '建筑面积',
    machineLoad: '流动机械荷载',
    area: 965, // '面积',
    techGrade: '当前技术等级',
    wharfLoad: '码头均布荷载',
    wharfWidth: 100, // '码头宽',
    wharfElevation: '码头面高程',
    orgid: '组织机构',
    equipmentName: '配备机械名称',
    equipmentNum: 20, // '配备机械数量',
    equipmentModel: '配备机械型号',
    platformLoad: '平台均布荷载',
    platformWidth: '平台宽',
    frontWaterDepth: 70, // '前沿水深',
    certificateNo: '20200805112xxaa12', // '权证号',
    constructionUnit: '施工单位',
    designUnit: '设计单位',
    designLoad: '设计荷载',
    designCapacity: '设计靠泊能力',
    assetSsName: '设施名称',
    assetSsDl: '大类',
    assetSsZl: '子类',
    ssdw: '所属单位',
    status: '状态',
    useUnits: '使用单位',
    tenureType: '使用权类型',
    useHolder: '使用权人',
    address: '所在地址',
    type: '设施类型',
    seaUse: '用海方式',
    seaUseProject: '用海项目',
    approBridgeLength: '引桥长',
    approBridgeTop: '引桥顶标高',
    approBridgeLoad: '引桥均布荷载',
    approBridgeWidth: 60, // '引桥宽',
    approBridgeFlowLoad: '引桥流动机械荷载',
    approBridgeNum: 42, // '引桥数量',
    useFor: '用途',
    activeArea: 600, // '有效面积',
    totalLength: 1200, // '总长',
    assetFinNum: 'ZC20200801xx01', // '资产编码',
    initialValue: 3600, // '资产原值',
    storageCapacity: 5000, // '总堆存能力',
    totalWidth: 320, // '总宽',
    location: '坐落',
    wharfLength: 512, // '码头长',
    averageLoad: '均部荷载',
    wharfNum: 667, // '码头数量',
    topPost: '顶标杆',
    finAssetNum: '资产编号',
    frame: '结构形式',
    houseCertificateNo: 'FZ2427xx232', // '房产证号',
    seaUseHolder: '海域使用权人',
    landUseHolder: '土地使用权人',
    carryingArea: 100, // '证载面积（㎡）',
    designCarryCapacity: '设计通过能力',
    modifyBy: '修改者',
    modifyDate: '2019-05-05', // '修改时间',
    orgId: '组织机构'
  }

  for (let i = 0; i < 20; i++) {
    const dto = {}
    for (const key in DTO) {
      if (['createDate', 'completeDate', 'modifyDate'].includes(key)) {
        dto[key] = DTO[key]
      } else if (!['uuid'].includes(key)) {
        dto[key] = DTO[key] + DTO.uuid
      }
    }
    dto.uuid = DTO.uuid++
    arr.push(dto)
  }
  return builder({
    data: arr,
    pageNo: 1,
    pageSize: 0,
    totalCount: 0,
    totalPage: 0
  })
})

Mock.mock(toRegex(api.deleteFacility), 'post', options => {
  return builder()
})

Mock.mock(toRegex(api.modifyFacility), 'post', options => {
  return builder()
})
