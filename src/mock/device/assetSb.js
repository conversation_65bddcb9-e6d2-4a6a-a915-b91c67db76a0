import Mock from 'mockjs2'
import api from '@/api/device/assetSb'
import { toRegex, builder, getBody } from '../util'

// 设备分类
Mock.mock(toRegex(api.queryCategory), 'post', options => {
  const body = getBody(options)
  const param = body.param
  const arr = [
    {
      uuid: '设备大类001-key',
      categoryName: '设备大类001',
      activity: 'Y',
      children: [
        {
          uuid: '设备小类001-key',
          categoryName: '设备小类001',
          activity: 'Y'
        }
      ]
    },
    {
      uuid: '设备大类002-key',
      categoryName: '设备大类002',
      activity: 'Y',
      children: [
        {
          uuid: '设备小类002-key',
          categoryName: '设备小类002',
          activity: 'Y'
        }
      ]
    },
    {
      uuid: '设备大类003-key',
      categoryName: '设备大类003',
      activity: 'Y',
      children: [
        {
          uuid: '设备小类003-key',
          categoryName: '设备小类003',
          activity: 'Y'
        }
      ]
    }
  ]
  console.log(param)
  return builder(arr)
})

Mock.mock(toRegex(api.modifyCategory), 'post', options => {
  const body = getBody(options)
  const param = body.param
  console.log(param)
  return builder()
})

Mock.mock(toRegex(api.deleteCategory), 'post', options => {
  const body = getBody(options)
  const param = body.param
  console.log(param)
  return builder()
})

// 设备台账
// Mock.mock(toRegex(api.queryEquipment), 'post', options => {
//   const body = getBody(options)
//   const param = body.param
//   const arr = [
//     {
//       uuid: 'uuidxxxxxxxx001',
//       sbdl: '设备大类001-key',
//       sbxl: '设备小类001-key',
//       description: '设备名称001',
//       assetSbNum: '设备编号001',
//       assetStatus: '设备启用中',
//       locationCode: '',
//       locationName: '',
//       cqdw: '产权单位001',
//       ssdw: '所属单位001',
//       sydw: '使用单位001',
//       source: '来源001',
//       purdate: '2018-03-13',
//       originValue: '原值001',
//       model: '规格-中型',
//       sccj: '生产厂家001',
//       produceDate: '2017-12',
//       dkpp: '电信品牌',
//       jszb: '技术指标001',
//       remark: '备注说明001'
//     },
//     {
//       uuid: 'uuidxxxxxxxx002',
//       sbdl: '设备大类002-key',
//       sbxl: '设备小类002-key',
//       description: '设备名称002',
//       assetSbNum: '设备编号002',
//       assetStatus: '设备启用中',
//       locationCode: 'code01-01-02-01',
//       locationName: 'name01-01-02-01',
//       cqdw: '产权单位002',
//       ssdw: '所属单位002',
//       sydw: '使用单位002',
//       source: '来源002',
//       purdate: '2018-03-10',
//       originValue: '原值002',
//       model: '规格-大型',
//       sccj: '生产厂家002',
//       produceDate: '2017-09',
//       dkpp: '移动品牌',
//       jszb: '技术指标002',
//       remark: '备注说明002'
//     }
//   ]
//   console.log(param)
//   return builder({
//     data: arr,
//     pageNo: 1,
//     pageSize: 0,
//     totalCount: 0,
//     totalPage: 0
//   })
// })

// Mock.mock(toRegex(api.modifyEquipment), 'post', options => {
//   const body = getBody(options)
//   const param = body.param
//   console.log(param)
//   return builder()
// })

// Mock.mock(toRegex(api.deleteEquipment), 'post', options => {
//   const body = getBody(options)
//   const param = body.param
//   console.log(param)
//   return builder()
// })
