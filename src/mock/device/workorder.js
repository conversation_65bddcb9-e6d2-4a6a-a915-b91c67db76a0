import Mock from 'mockjs2'
import api from '@/api/device/workorder'
import { toRegex, builder, getBody, getParam } from '../util'

// Mock.mock(toRegex(api.queryWorkorder), 'post', options => {
//   const body = getBody(options)
//   const param = body.param
//   const arr = [
//     {
//       uuid: 'GD0001',
//       description: '工单描述001',
//       assetSbUuid: '26723041501253803',
//       assetSbName: 'A库01号设备',
//       acceptTime: '2020-08-11',
//       workorderType: 'checkPlanType',
//       errorType: 'error',
//       errorCode: '故障代码001',
//       errorDescription: '故障描述001',
//       startTime: '2020-09-01',
//       endTime: '2020-09-05',
//       create_by: '创建人001',
//       create_date: '2020-08-01 11:12:33',
//       modify_by: '修改人001',
//       modify_date: '2020-08-02 08:32:31',
//       org_id: '1.100.118',
//       status: 'approval1009',
//       workorderPersonEntityList: [
//         {
//           workorderUuid: 'GD0001',
//           personSysId: 'maintain001',
//           actualStartTime: '2020-09-13',
//           actualEndTime: '2020-09-20',
//           actualCost: 50
//         },
//         {
//           workorderUuid: 'GD0001',
//           personSysId: 'maintain002',
//           actualStartTime: '2020-09-15',
//           actualEndTime: '2020-09-18',
//           actualCost: 21
//         }
//       ],
//       workorderMaterialEntityList: [
//         {
//           workorderUuid: 'GD0001',
//           materialSysId: '26715531750932839',
//           materialNum: '08064816',
//           materialName: '交换机模块',
//           orderUnit: 'unit0022',
//           quantity: 10
//         },
//         {
//           workorderUuid: 'GD0001',
//           materialSysId: '26715531750932742',
//           materialNum: '08063236',
//           materialName: '红外灯',
//           orderUnit: 'unit0031',
//           quantity: 7
//         },
//         {
//           workorderUuid: 'GD0001',
//           materialSysId: '26715531750932753',
//           materialNum: '08063246',
//           materialName: '控制台终端',
//           orderUnit: 'unit0031',
//           quantity: 5
//         }
//       ]
//     },
//     {
//       uuid: 'GD0002',
//       description: '工单描述002',
//       assetSbUuid: '26723041501253824',
//       assetSbName: '设备名称003',
//       acceptTime: '2020-08-10',
//       workorderType: 'maintainPlanType',
//       errorType: 'warning',
//       errorCode: '故障代码002',
//       errorDescription: '故障描述002',
//       startTime: '2020-09-05',
//       endTime: '2020-09-16',
//       create_by: '创建人002',
//       create_date: '2020-08-23 08:51:06',
//       modify_by: '修改人001',
//       modify_date: '2020-08-24 14:22:19',
//       org_id: '1.100.118',
//       status: 'approval1007',
//       workorderPersonEntityList: [
//         {
//           workorderUuid: 'GD0002',
//           personSysId: 'maintain002',
//           actualStartTime: '2020-09-04',
//           actualEndTime: '2020-09-06',
//           actualCost: 14
//         },
//         {
//           workorderUuid: 'GD0002',
//           personSysId: 'maintain005',
//           actualStartTime: '2020-09-20',
//           actualEndTime: '2020-09-27',
//           actualCost: 18
//         }
//       ],
//       workorderMaterialEntityList: [
//         {
//           workorderUuid: 'GD0001',
//           materialSysId: '26715531750932758',
//           materialNum: '08063250',
//           materialName: '闪光灯',
//           orderUnit: 'unit0039',
//           quantity: 22
//         },
//         {
//           workorderUuid: 'GD0001',
//           materialSysId: '26715531750932762',
//           materialNum: '08063254',
//           materialName: '球芯',
//           orderUnit: 'unit0031',
//           quantity: 7
//         },
//         {
//           workorderUuid: 'GD0001',
//           materialSysId: '26715531750932753',
//           materialNum: '08063246',
//           materialName: '控制台终端',
//           orderUnit: 'unit0031',
//           quantity: 5
//         }
//       ]
//     }
//   ]
//   console.log(param)
//   return builder({
//     data: arr,
//     pageNo: 1,
//     pageSize: 0,
//     totalCount: 0,
//     totalPage: 0
//   })
// })

// Mock.mock(toRegex(api.modifyWorkorder), 'post', options => {
//   const body = getBody(options)
//   const param = body.param
//   console.log(param)
//   return builder()
// })

// Mock.mock(toRegex(api.deleteWorkorder), 'post', options => {
//   const body = getBody(options)
//   const param = body.param
//   console.log(param)
//   return builder()
// })

Mock.mock(toRegex(api.queryWorkorderRecord), 'post', options => {
  const param = getParam(options)
  const arr = [
    {
      flag: '同意',
      message: '第三次审批',
      createBy: '审批人3',
      createDate: '2020-09-12'
    },
    {
      flag: '同意',
      message: '第二次审批',
      createBy: '审批人2',
      createDate: '2020-09-01'
    },
    {
      flag: '同意',
      message: '第一次审批',
      createBy: '审批人1',
      createDate: '2020-08-21'
    }
  ]
  console.log(param)
  return builder(arr)
})

Mock.mock(toRegex(api.approveWorkorder), 'post', options => {
  const body = getBody(options)
  const param = body.param
  console.log(param)
  return builder()
})
