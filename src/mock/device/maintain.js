import Mock from 'mockjs2'
import api from '@/api/device/assetMaintain'
import { toRegex, builder, getBody } from '../util'

Mock.mock(toRegex(api.queryAssetMainArea), 'post', options => {
  const body = getBody(options)
  const param = body.param
  const arr = [
    {
      activity: 'Y',
      areaName: '保养区域0001',
      children: [],
      createBy: '26715531750932568',
      createDate: 1598276876000,
      equipmentEntityList: [],
      modifyBy: '26715531750932568',
      modifyDate: 1598535962000,
      orgId: '1.100.118',
      uuid: '26723041501253541',
      uuidParent: '0'
    },
    {
      activity: 'N',
      areaName: '保养区域0002',
      children: [],
      createBy: '26715531750932568',
      createDate: 1598276876000,
      equipmentEntityList: [],
      modifyBy: '26715531750932568',
      modifyDate: 1598535962000,
      orgId: '1.100.118',
      uuid: '26723041501253542',
      uuidParent: '0'
    }
  ]
  console.log(param)
  return builder(arr)
})

Mock.mock(toRegex(api.queryAssetMainEquipment), 'post', options => {
  const body = getBody(options)
  const param = body.param
  const arr = [
    {
      activity: 'Y',
      assetSbNum: '设备编号001',
      assetStatus: '',
      cqdw: '所属单位001',
      createBy: '26715531750932568',
      createDate: '2020-09-10  15:47:13',
      description: 'A库01号设备',
      dkpp: '121',
      jszb: '1',
      locationName: '',
      model: '规格',
      originValue: 555,
      produceDate: '2020-08',
      purdate: '2020-09-09',
      remark: '2',
      sbdl: '设备大类001-key',
      sbxl: '设备小类001-key',
      sccj: '11212',
      selected: true,
      source: '来源001',
      ssdw: '所属单位001',
      sydw: '使用单位001',
      uuid: '26723041501253803'
    },
    {
      activity: 'Y',
      assetSbNum: '设备编号003',
      assetStatus: '',
      cqdw: '所属单位003',
      createBy: '26715531750932568',
      createDate: '2020-09-13  13:46:15',
      description: '设备名称003',
      dkpp: '电信品牌',
      jszb: '技术指标。。。',
      locationName: '',
      model: '规格-中型',
      originValue: 50002,
      produceDate: '2020-05',
      purdate: '2020-09-09',
      remark: '备注描述。。。',
      sbdl: '设备大类003-key',
      sbxl: '设备小类003-key',
      sccj: '生产厂家003',
      selected: true,
      source: '来源003',
      ssdw: '所属单位003',
      sydw: '使用单位003',
      uuid: '26723041501253824'
    }
  ]
  console.log(param)
  return builder(arr)
})

Mock.mock(toRegex(api.queryAssetMainPart), 'post', options => {
  const body = getBody(options)
  const param = body.param
  const arr = [
    {
      activity: 'Y',
      createBy: '26715531750932568',
      createDate: '2020-09-14 22:19:13',
      equipmentUuid: '26723041501253803',
      modifyBy: '26715531750932568',
      modifyDate: '2020-09-14 22:24:17',
      orgId: '1.100.118',
      partName: '01设备01-1部件',
      uuid: '26723041501253888'
    }
  ]
  console.log(param)
  return builder(arr)
})

Mock.mock(toRegex(api.queryAssetMainItem), 'post', options => {
  const body = getBody(options)
  const param = body.param
  const arr = [
    {
      activity: 'Y',
      checkMethod: '点检方法 - 加油',
      checkStandard: '点检标准 - 加油',
      createBy: '26715531750932568',
      createDate: '2020-09-15 16:39:37',
      itemName: '01设备01-01部件-01项目',
      modifyBy: '26715531750932568',
      modifyDate: '2020-09-15 20:42:01',
      orgId: '1.100.118',
      partUuid: '26723041501253888',
      planTag: false,
      uuid: '26723041501253911'
    }
  ]
  console.log(param)
  return builder(arr)
})
