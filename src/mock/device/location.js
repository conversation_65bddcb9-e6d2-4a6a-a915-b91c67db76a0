import Mock from 'mockjs2'
import api from '@/api/device/location'
import { toRegex, builder, getBody } from '../util'

Mock.mock(toRegex(api.queryLocation), 'post', options => {
  const body = getBody(options)
  const param = body.param
  const arr = [
    {
      uuid: '0001',
      locationName: '位置名称001',
      locationCode: '位置代码001',
      create_by: '创建人001',
      create_date: '2020-08-30',
      type: '普通',
      modify_by: '修改人001',
      modify_date: '2020-08-30',
      parent: '',
      orgid: '组织机构',
      activity: 'Y',
      children: [
        {
          uuid: '0011',
          locationName: '位置名称011',
          locationCode: '位置代码011',
          create_by: '创建人011',
          create_date: '2020-08-31',
          type: '普通',
          modify_by: '修改人011',
          modify_date: '2020-08-31',
          parent: '0001',
          orgid: '组织机构',
          activity: 'N'
        },
        {
          uuid: '0012',
          locationName: '位置名称012',
          locationCode: '位置代码012',
          create_by: '创建人012',
          create_date: '2020-08-31',
          type: '特殊',
          modify_by: '修改人012',
          modify_date: '2020-08-31',
          parent: '0001',
          orgid: '组织机构',
          activity: 'N',
          children: [
            {
              uuid: '0121',
              locationName: '位置名称0121',
              locationCode: '位置代码0121',
              create_by: '创建人0121',
              create_date: '2020-08-31',
              type: '特殊',
              modify_by: '修改人0121',
              modify_date: '2020-08-31',
              parent: '0012',
              orgid: '组织机构',
              activity: 'N'
            },
            {
              uuid: '0122',
              locationName: '位置名称0122',
              locationCode: '位置代码0122',
              create_by: '创建人0122',
              create_date: '2020-08-31',
              type: '特殊',
              modify_by: '修改人0122',
              modify_date: '2020-08-31',
              parent: '0012',
              orgid: '组织机构',
              activity: 'Y'
            },
            {
              uuid: '0123',
              locationName: '位置名称0123',
              locationCode: '位置代码0123',
              create_by: '创建人0123',
              create_date: '2020-08-31',
              type: '特殊',
              modify_by: '修改人0123',
              modify_date: '2020-08-31',
              parent: '0012',
              orgid: '组织机构',
              activity: 'Y'
            }
          ]
        },
        {
          uuid: '0013',
          locationName: '位置名称013',
          locationCode: '位置代码013',
          create_by: '创建人013',
          create_date: '2020-08-31',
          type: '特殊',
          modify_by: '修改人013',
          modify_date: '2020-08-31',
          parent: '0001',
          orgid: '组织机构',
          activity: 'Y'
        }
      ]
    },
    {
      uuid: '0002',
      locationName: '位置名称002',
      locationCode: '位置代码002',
      create_by: '创建人002',
      create_date: '2020-08-30',
      type: '特殊',
      modify_by: '修改人002',
      modify_date: '2020-08-30',
      parent: '',
      orgid: '组织机构',
      activity: 'Y',
      children: [
        {
          uuid: '0021',
          locationName: '位置名称021',
          locationCode: '位置代码021',
          create_by: '创建人021',
          create_date: '2020-08-31',
          type: '普通',
          modify_by: '修改人021',
          modify_date: '2020-08-31',
          parent: '0002',
          orgid: '组织机构',
          activity: 'N'
        },
        {
          uuid: '0022',
          locationName: '位置名称022',
          locationCode: '位置代码022',
          create_by: '创建人022',
          create_date: '2020-08-31',
          type: '特殊',
          modify_by: '修改人022',
          modify_date: '2020-08-31',
          parent: '0002',
          orgid: '组织机构',
          activity: 'Y',
          children: [
            {
              uuid: '0221',
              locationName: '位置名称0221',
              locationCode: '位置代码0221',
              create_by: '创建人0221',
              create_date: '2020-08-31',
              type: '特殊',
              modify_by: '修改人0221',
              modify_date: '2020-08-31',
              parent: '0022',
              orgid: '组织机构',
              activity: 'N'
            },
            {
              uuid: '0222',
              locationName: '位置名称0222',
              locationCode: '位置代码0222',
              create_by: '创建人0222',
              create_date: '2020-08-31',
              type: '特殊',
              modify_by: '修改人0222',
              modify_date: '2020-08-31',
              parent: '0022',
              orgid: '组织机构',
              activity: 'Y'
            },
            {
              uuid: '0223',
              locationName: '位置名称0223',
              locationCode: '位置代码0223',
              create_by: '创建人0223',
              create_date: '2020-08-31',
              type: '特殊',
              modify_by: '修改人0223',
              modify_date: '2020-08-31',
              parent: '0022',
              orgid: '组织机构',
              activity: 'Y'
            }
          ]
        },
        {
          uuid: '0023',
          locationName: '位置名称023',
          locationCode: '位置代码023',
          create_by: '创建人023',
          create_date: '2020-08-31',
          type: '特殊',
          modify_by: '修改人023',
          modify_date: '2020-08-31',
          parent: '0002',
          orgid: '组织机构',
          activity: 'Y'
        }
      ]
    },
    {
      uuid: '0003',
      locationName: '位置名称003',
      locationCode: '位置代码003',
      create_by: '创建人003',
      create_date: '2020-08-30',
      type: '普通',
      modify_by: '修改人003',
      modify_date: '2020-08-30',
      parent: '',
      orgid: '组织机构',
      activity: 'Y'
    }
  ]
  console.log(param)
  return builder(arr)
})

Mock.mock(toRegex(api.modifyLocation), 'post', options => {
  const body = getBody(options)
  const param = body.param
  console.log(param)
  return builder()
})
