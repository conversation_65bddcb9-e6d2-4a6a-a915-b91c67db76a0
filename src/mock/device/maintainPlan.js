import Mock from 'mockjs2'
import api from '@/api/device/assetMaintainPlan'
import { toRegex, builder, getBody } from '../util'

Mock.mock(toRegex(api.queryAssetMainPlan), 'post', options => {
  const body = getBody(options)
  const param = body.param
  const arr = [
    {
      checkDept: '26534169307775126',
      createBy: '26715531750932568',
      createDate: '2020-09-15 21:16:14',
      executeTime: '2020-09-10',
      modifyBy: '26715531750932568',
      period: '10',
      planName: '测试点检计划001',
      uuid: '26723041501253916'
    },
    {
      activity: 'Y',
      checkDept: '26534169307775130',
      createBy: '26715531750932568',
      createDate: '2020-09-15 22:05:32',
      executeTime: '2020-09-02',
      modifyBy: '26715531750932568',
      period: '10',
      planName: '测试点检计划002',
      uuid: '26723041501253949'
    }
  ]
  console.log(param)
  return builder(arr)
})

Mock.mock(toRegex(api.queryAssetMainItem), 'post', options => {
  const body = getBody(options)
  const param = body.param
  const arr = [
    {
      activity: 'Y',
      checkMethod: '点检方法 - 加油1',
      checkStandard: '点检标准 - 加油3',
      createBy: '26715531750932568',
      createDate: '2020-09-15 16:39:37',
      itemName: '01设备01-01部件-01项目',
      modifyBy: '26715531750932568',
      modifyDate: '2020-09-15 20:42:01',
      orgId: '1.100.118',
      planTag: false,
      uuid: '26723041501253911'
    }
  ]
  console.log(param)
  return builder(arr)
})

Mock.mock(toRegex(api.queryAssetMainTask), 'post', options => {
  const body = getBody(options)
  const param = body.param
  const arr = [
    {
      checkDept: '26534169307775128',
      executeTime: '2020-09-17 13:40:22',
      period: '3',
      planName: '计划1',
      planUuid: '26723041501254336',
      taskUuid: '26847145218801705'
    },
    {
      checkDept: '26534169307775128',
      executeTime: '2020-09-09 10:40:29',
      period: '3',
      planName: '计划1',
      planUuid: '26723041501254336',
      taskUuid: '26847145218801706'
    }
  ]
  console.log(1)
  console.log(api.queryAssetMainTask)
  console.log(param)
  return builder(arr)
})

Mock.mock(toRegex(api.queryAssetMainFeedback), 'post', options => {
  const body = getBody(options)
  const param = body.param
  const arr = [
    {
      activity: 'Y',
      areaName: '区域1',
      checkMethod: 'fff',
      checkStandard: 'eeee',
      createBy: '26715531750932568',
      createDate: '2020-09-18 21:24:17',
      description: 'A库01号设备',
      feedbackRemarks: '222',
      isNormal: 'Y',
      itemName: '项目1-1',
      orgId: '1.100.118',
      partName: '部件1',
      planTag: false,
      uuid: '26723041501254332'
    }
  ]
  console.log(2)
  console.log(api.queryAssetMainFeedback)
  console.log(param)
  return builder(arr)
})
