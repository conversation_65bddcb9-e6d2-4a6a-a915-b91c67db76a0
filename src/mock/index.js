// 判断环境不是 prod 或者 preview 是 true 时，加载 mock 服务
if (process.env.NODE_ENV !== 'production' || process.env.VUE_APP_PREVIEW === 'true') {
  // 使用同步加载依赖
  const Mock = require('mockjs2')

  // 物资接口 mock
  // require('./material/auth')
  // require('./material/user')
  // require('./material/manage')
  // require('./material/other')
  // require('./material/base')
  // require('./material/tagCloud')
  // require('./material/article')
  // require('./material/usedfor')
  // require('./material/material')
  // require('./material/prline')
  // require('./material/matu')

  // 设备接口 mock
  // require('./device/assetSs')
  // require('./device/location')
  // require('./device/assetSb')
  // require('./device/maintain')
  // require('./device/workorder')
  // require('./device/maintainPlan')

  Mock.setup({
    timeout: 200
  })
}
