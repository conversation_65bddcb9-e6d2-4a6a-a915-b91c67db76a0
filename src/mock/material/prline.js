import Mock from 'mockjs2'
import api from '@/api/material/prline'
import { toRegex, builder, getBody, getParam } from '../util'

Mock.mock(toRegex(api.getProcurePlanByPageNo), 'post', options => {
  const body = getBody(options)
  const param = body.param
  const response = {
    data: [],
    pageNo: 1,
    pageSize: 5,
    totalCount: 5,
    totalPage: 1
  }
  const array = [
    {
      agent: '1',
      createBy: '测试1',
      createDate: 1585112400000,
      isIssue: '1',
      jModel: 'ji 1.2.33',
      jwspOrderQty: 11,
      lineCost: 200,
      materialName: '车辆1',
      materialNum: 'BGYP5',
      orderQty: 10,
      orderUnit: '件',
      orgId: '1',
      prlineNum: '',
      departmentName: '软件开发部',
      prlineSysId: '26534169307775186',
      status: '1001',
      unitCost: 200,
      usedFor: '9982372321',
      planMonth: '2020-01'
    },
    {
      agent: '2',
      createBy: '测试2',
      createDate: 1585112400000,
      isIssue: '1',
      jModel: 'juh 1.2.13',
      jwspOrderQty: 5,
      lineCost: 200,
      materialName: '配件2',
      materialNum: 'BGYP2',
      modifyBy: '测试',
      modifyDate: 1585112400000,
      orderQty: 2,
      orderUnit: '件',
      orgId: '1',
      prlineNum: '12345678',
      departmentName: '公司领导',
      prlineSysId: '26534169307775187',
      status: '1002',
      unitCost: 200,
      usedFor: '26534169307775109',
      planMonth: '2020-02'
    },
    {
      agent: '3',
      createBy: '测试3',
      createDate: 1585112400000,
      isIssue: '1',
      jModel: 'juh 1.2.13',
      jwspOrderQty: 5,
      lineCost: 200,
      materialName: '配件3',
      materialNum: 'BGYP3',
      modifyBy: '测试3',
      modifyDate: 1585112400000,
      orderQty: 3,
      orderUnit: '件',
      orgId: '1',
      prlineNum: '12345678',
      departmentName: '软件开发部',
      prlineSysId: '26534169307775188',
      status: '1002',
      unitCost: 200,
      usedFor: '26534169307775109',
      planMonth: '2020-03'
    },
    {
      agent: '4',
      createBy: '测试4',
      createDate: 1585112400000,
      isIssue: '1',
      jModel: 'juh 1.2.13',
      jwspOrderQty: 5,
      lineCost: 200,
      materialName: '配件4',
      materialNum: 'BGYP3',
      modifyBy: '测试3',
      modifyDate: 1585112400000,
      orderQty: 3,
      orderUnit: '件',
      orgId: '1',
      prlineNum: '123456789',
      departmentName: '软件开发部',
      prlineSysId: '26534169307775189',
      status: '1002',
      unitCost: 200,
      usedFor: '26534169307775109',
      planMonth: '2020-03'
    },
    {
      agent: '5',
      createBy: '测试5',
      createDate: 1585112400000,
      isIssue: '1',
      jModel: 'juh 1.2.13',
      jwspOrderQty: 5,
      lineCost: 200,
      materialName: '配件5',
      materialNum: 'BGYP3',
      modifyBy: '测试3',
      modifyDate: 1585112400000,
      orderQty: 3,
      orderUnit: '件',
      orgId: '1',
      prlineNum: '12345678910121212121',
      departmentName: '软件开发部',
      prlineSysId: '26534169307775190',
      status: '1002',
      unitCost: 200,
      usedFor: '26534169307775109',
      planMonth: '2020-03'
    }
  ]
  if (param.prlineNum !== '') {
    const data = []
    for (const item of array) {
      if (item.prlineNum === param.prlineNum) {
        data.push(item)
      }
    }
    return builder(Object.assign(response, { data }))
  }
  return builder(Object.assign(response, { data: array }))
})

Mock.mock(toRegex(api.getApproveRecordByNum), 'get', options => {
  console.log(getParam(options))
  const response = [
    {
      time: '2020-04-01 08:12',
      suggest: '',
      auditors: '王xx',
      status: '同意'
    },
    {
      time: '2020-03-29 13:41',
      suggest: '该采购单已核实，可以采购！',
      auditors: '许x',
      status: '同意'
    },
    {
      time: '2020-03-28 10:21',
      suggest: '该采购单信息确认无误，予以同意！',
      auditors: '李xx',
      status: '同意'
    }
  ]
  return builder(response)
})

Mock.mock(toRegex(api.approveProcurePlan), 'post', options => {
  console.log(getBody(options))
  return builder({}, '审批成功', '0000')
})

Mock.mock(toRegex(api.deleteProcurePlan), 'delete', options => {
  const response = { code: '0000', message: '请求成功' }
  return builder(response)
})

Mock.mock(toRegex(api.modifyProcurePlan), 'post', options => {
  console.log(getBody(options))
  const response = { code: '0000', message: '请求成功' }
  return builder(response)
})
