import Mock from 'mockjs2'
import { builder, getBody } from '../util'

const username = ['admin', 'super']
const password = ['0192023a7bbd73250516f069df18b500']

const login = options => {
  const body = getBody(options)

  if (!username.includes(body.param.username) || !password.includes(body.param.password)) {
    return builder({ isLogin: true }, '账户或密码错误', 401)
  }

  return builder(
    {
      id: Mock.mock('@guid'),
      name: Mock.mock('@name'),
      username: 'admin',
      password: '',
      avatar: 'https://cdn2.ettoday.net/images/2194/d2194378.jpg',
      status: 1,
      telephone: '',
      lastLoginIp: '*************',
      lastLoginTime: 1534837621348,
      creatorId: 'admin',
      createTime: 1497160610259,
      deleted: 0,
      roleId: 'admin',
      lang: 'zh-CN',
      token: '4291d7da9005377ec9aec4a71ea837f',
      data: {
        orgId: '0111',
        userNo: 'admin'
      }
    },
    '',
    '0000',
    { 'Custom-Header': Mock.mock('@guid') }
  )
}

const logout = () => {
  return builder({}, '[测试接口] 注销成功')
}

const smsCaptcha = () => {
  return builder({ captcha: Mock.mock('@integer(10000, 99999)') })
}

const twofactor = () => {
  return builder({ stepCode: Mock.mock('@integer(0, 1)') })
}

Mock.mock(/\/api\/auth\/login/, 'post', login)
Mock.mock(/\/api\/auth\/logout/, 'post', logout)
Mock.mock(/\/auth\/2step-code/, 'post', twofactor)
Mock.mock(/\/account\/sms/, 'post', smsCaptcha)
