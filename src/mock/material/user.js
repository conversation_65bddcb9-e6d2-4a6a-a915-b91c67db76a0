import Mock from 'mockjs2'
import { builder } from '../util'

const info = options => {
  const userInfo = {
    id: '4291d7da9005377ec9aec4a71ea837f',
    name: '周吉',
    username: 'admin',
    password: '',
    avatar: 'https://cdn2.ettoday.net/images/2194/d2194378.jpg',
    status: 1,
    telephone: '',
    lastLoginIp: '*************',
    lastLoginTime: 1534837621348,
    creatorId: 'admin',
    createTime: 1497160610259,
    merchantCode: 'TLif2btpzg079h15bk',
    deleted: 0,
    roleId: 'admin',
    role: {}
  }
  // role
  const roleObj = {
    id: 'admin',
    name: '管理员',
    describe: '拥有所有权限',
    status: 1,
    creatorId: 'system',
    createTime: 1497160610259,
    deleted: 0,
    permissions: [
      {
        roleId: 'admin',
        permissionId: 'dashboard',
        permissionName: '仪表盘',
        actions: [],
        actionEntitySet: [
          {
            action: 'add',
            describe: '新增',
            defaultCheck: false
          },
          {
            action: 'query',
            describe: '查询',
            defaultCheck: false
          },
          {
            action: 'get',
            describe: '详情',
            defaultCheck: false
          },
          {
            action: 'update',
            describe: '修改',
            defaultCheck: false
          },
          {
            action: 'delete',
            describe: '删除',
            defaultCheck: false
          }
        ],
        actionList: null,
        dataAccess: null
      },
      {
        roleId: 'admin',
        permissionId: 'TableDemo',
        permissionName: '查询Demo',
        actions: [],
        actionEntitySet: [
          {
            action: 'add',
            defaultCheck: false,
            describe: '新增'
          },
          {
            action: 'edit',
            defaultCheck: false,
            describe: '编辑'
          },
          {
            action: 'del',
            describe: '删除',
            defaultCheck: false
          },
          {
            action: 'query',
            describe: '查询',
            defaultCheck: false
          }
        ],
        actionList: null,
        dataAccess: null
      },
      {
        roleId: 'admin',
        permissionId: 'TableDemo',
        permissionName: '查询Demo',
        actions: [],
        actionEntitySet: [
          {
            action: 'add',
            defaultCheck: false,
            describe: '新增'
          },
          {
            action: 'edit',
            defaultCheck: false,
            describe: '编辑'
          },
          {
            action: 'del',
            describe: '删除',
            defaultCheck: false
          },
          {
            action: 'query',
            describe: '查询',
            defaultCheck: false
          }
        ],
        actionList: null,
        dataAccess: null
      },
      {
        roleId: 'admin',
        permissionId: 'CodeBas',
        permissionName: '公共代码维护',
        actions: [],
        actionEntitySet: [
          {
            action: 'add',
            defaultCheck: false,
            describe: '新增'
          },
          {
            action: 'edit',
            defaultCheck: false,
            describe: '编辑'
          },
          {
            action: 'del',
            describe: '删除',
            defaultCheck: false
          },
          {
            action: 'query',
            describe: '查询',
            defaultCheck: false
          }
        ],
        actionList: null,
        dataAccess: null
      },
      {
        roleId: 'admin',
        permissionId: 'Vendor',
        permissionName: '供应商维护',
        actions: [],
        actionEntitySet: [
          {
            action: 'add',
            defaultCheck: false,
            describe: '新增'
          },
          {
            action: 'edit',
            defaultCheck: false,
            describe: '编辑'
          },
          {
            action: 'del',
            describe: '删除',
            defaultCheck: false
          },
          {
            action: 'query',
            describe: '查询',
            defaultCheck: false
          },
          {
            action: 'approve',
            describe: '审批',
            defaultCheck: false
          },
          {
            action: 'enable',
            describe: '启用',
            defaultCheck: false
          }
        ],
        actionList: null,
        dataAccess: null
      },
      {
        roleId: 'admin',
        permissionId: 'ProcurePlan',
        permissionName: '采购计划',
        actions: [],
        actionEntitySet: [
          {
            action: 'add',
            defaultCheck: true,
            describe: '新增'
          },
          {
            action: 'edit',
            defaultCheck: true,
            describe: '编辑'
          },
          {
            action: 'del',
            describe: '删除',
            defaultCheck: true
          },
          {
            action: 'query',
            describe: '查询',
            defaultCheck: true
          },
          {
            action: 'export',
            describe: '导出',
            defaultCheck: true
          },
          {
            action: 'approve',
            describe: '审批',
            defaultCheck: true
          }
        ],
        actionList: null,
        dataAccess: null
      },
      {
        roleId: 'admin',
        permissionId: 'ProcuretManagement',
        permissionName: '采购生成与核销',
        actions: [],
        actionEntitySet: [
          {
            action: 'add',
            defaultCheck: true,
            describe: '新增'
          },
          {
            action: 'edit',
            defaultCheck: true,
            describe: '编辑'
          },
          {
            action: 'del',
            describe: '删除',
            defaultCheck: true
          },
          {
            action: 'query',
            describe: '查询',
            defaultCheck: true
          }
        ],
        actionList: null,
        dataAccess: null
      },
      {
        roleId: 'admin',
        permissionId: 'QuotationManagement',
        permissionName: '报价单管理',
        actions: [],
        actionEntitySet: [
          {
            action: 'add',
            defaultCheck: true,
            describe: '新增'
          },
          {
            action: 'edit',
            defaultCheck: true,
            describe: '编辑'
          },
          {
            action: 'query',
            describe: '查询',
            defaultCheck: true
          },
          {
            action: 'export',
            describe: '导出',
            defaultCheck: true
          },
          {
            action: 'save',
            describe: '保存',
            defaultCheck: true
          },
          {
            action: 'approve',
            describe: '审批',
            defaultCheck: true
          }
        ],
        actionList: null,
        dataAccess: null
      },
      {
        roleId: 'admin',
        permissionId: 'JobConfiguration',
        permissionName: '工种配置',
        actionEntitySet: [
          {
            action: 'add',
            defaultCheck: true,
            describe: '新增'
          },
          {
            action: 'edit',
            defaultCheck: true,
            describe: '编辑'
          },
          {
            action: 'del',
            describe: '删除',
            defaultCheck: true
          },
          {
            action: 'query',
            describe: '查询',
            defaultCheck: true
          }
        ],
        'actionList': null,
        'dataAccess': null
      },
      {
        roleId: 'admin',
        permissionId: 'Usedfor',
        permissionName: '使用方向管理',
        actionEntitySet: [
          {
            action: 'add',
            defaultCheck: true,
            describe: '新增'
          },
          {
            action: 'edit',
            defaultCheck: true,
            describe: '编辑'
          },
          {
            action: 'del',
            describe: '删除',
            defaultCheck: true
          },
          {
            action: 'query',
            describe: '查询',
            defaultCheck: true
          }
        ],
        'actionList': null,
        'dataAccess': null
      },
      {
        roleId: 'admin',
        permissionId: 'ContractManagement',
        permissionName: '合同管理',
        actionEntitySet: [
          {
            action: 'add',
            defaultCheck: true,
            describe: '新增'
          },
          {
            action: 'edit',
            defaultCheck: true,
            describe: '编辑'
          },
          {
            action: 'del',
            describe: '删除',
            defaultCheck: true
          },
          {
            action: 'query',
            describe: '查询',
            defaultCheck: true
          }
        ],
        'actionList': null,
        'dataAccess': null
      },
      {
        roleId: 'admin',
        permissionId: 'Invoice',
        permissionName: '发票管理',
        actionEntitySet: [
          {
            action: 'add',
            defaultCheck: true,
            describe: '新增'
          },
          {
            action: 'edit',
            defaultCheck: true,
            describe: '编辑'
          },
          {
            action: 'del',
            describe: '删除',
            defaultCheck: true
          },
          {
            action: 'query',
            describe: '查询',
            defaultCheck: true
          }
        ],
        'actionList': null,
        'dataAccess': null
      },
      {
        roleId: 'admin',
        permissionId: 'Material',
        permissionName: '物资代码维护',
        actionEntitySet: [
          {
            action: 'add',
            defaultCheck: true,
            describe: '新增'
          },
          {
            action: 'edit',
            defaultCheck: true,
            describe: '编辑'
          },
          {
            action: 'del',
            describe: '删除',
            defaultCheck: true
          },
          {
            action: 'query',
            describe: '查询',
            defaultCheck: true
          },
          {
            action: 'approve',
            describe: '审批',
            defaultCheck: true
          },
          {
            action: 'report',
            describe: '上报',
            defaultCheck: true
          }
        ],
        'actionList': null,
        'dataAccess': null
      }
    ]
  }

  roleObj.permissions.push({
    roleId: 'admin',
    permissionId: 'support',
    permissionName: '超级模块',
    actions: [],
    actionEntitySet: [
      {
        action: 'add',
        describe: '新增',
        defaultCheck: false
      },
      {
        action: 'import',
        describe: '导入',
        defaultCheck: false
      },
      {
        action: 'get',
        describe: '详情',
        defaultCheck: false
      },
      {
        action: 'update',
        describe: '修改',
        defaultCheck: false
      },
      {
        action: 'delete',
        describe: '删除',
        defaultCheck: false
      },
      {
        action: 'export',
        describe: '导出',
        defaultCheck: false
      },
      {
        action: 'approve',
        describe: '审批',
        defaultCheck: false
      }
    ],
    actionList: null,
    dataAccess: null
  })

  userInfo.role = roleObj
  return builder(userInfo)
}

const userNav = options => {
  const nav = [
    // dashboard
    {
      name: 'dashboard',
      parentId: 0,
      id: 1,
      meta: {
        icon: 'dashboard',
        title: '仪表盘',
        show: true
      },
      component: 'PageView',
      redirect: '/dashboard/workplace'
    },
    {
      name: 'workplace',
      parentId: 1,
      id: 7,
      meta: {
        title: '工作台',
        show: true
      },
      component: 'Workplace'
    },
    {
      name: 'Analysis',
      parentId: 1,
      id: 2,
      meta: {
        title: '分析台',
        show: true
      },
      component: 'Analysis',
      path: '/dashboard/analysis'
    },
    // 基础信息模块
    {
      name: 'base',
      parentId: 0,
      id: 10,
      meta: {
        icon: 'form',
        title: '基础信息',
        hiddenHeaderContent: true
      },
      redirect: '/base/base-form',
      component: 'PageView'
    },
    {
      name: 'TableDemo',
      parentId: 10,
      id: 100,
      meta: {
        title: '查询Demo'
      },
      component: 'TableDemo'
    },
    {
      name: 'CodeBas',
      parentId: 10,
      id: 101,
      meta: {
        title: '公共代码维护'
      },
      component: 'CodeBas'
    },
    {
      name: 'Vendor',
      parentId: 10,
      id: 102,
      meta: {
        title: '供应商维护'
      },
      component: 'Vendor'
    },
    {
      name: 'AreaManagement',
      parentId: 10,
      id: 103,
      meta: {
        title: '库区库存管理'
      },
      component: 'AreaManagement'
    },
    {
      name: 'Material',
      parentId: 10,
      id: 104,
      meta: {
        title: '物资代码维护'
      },
      component: 'Material'
    },
    {
      name: 'Usedfor',
      parentId: 10,
      id: 105,
      meta: {
        title: '使用方向管理'
      },
      component: 'Usedfor'
    },
    // 个人页面
    // {
    //   'name': 'account',
    //   'parentId': 0,
    //   'id': 10028,
    //   'meta': {
    //     'title': '个人页',
    //     'icon': 'user',
    //     'show': true
    //   },
    //   'redirect': '/account/center',
    //   'component': 'PageView'
    // },
    // {
    //   'name': 'center',
    //   'parentId': 10028,
    //   'id': 10029,
    //   'meta': {
    //     'title': '个人中心',
    //     'show': true
    //   },
    //   'component': 'AccountCenter'
    // },
    // 系统管理
    {
      name: 'um',
      parentId: 0,
      id: 1024,
      meta: {
        title: '系统管理',
        icon: 'apartment',
        show: true,
        hiddenHeaderContent: true
      },
      redirect: '/um/userManager',
      component: 'PageView'
    },
    {
      name: 'userManager',
      parentId: 1024,
      id: 1025,
      meta: {
        title: '用户管理',
        show: true
      },
      component: 'UserManager'
    },
    {
      name: 'role',
      parentId: 1024,
      id: 1040,
      meta: {
        title: '角色管理',
        show: true
      },
      component: 'RoleManager'
    },
    {
      name: 'resource',
      parentId: 1024,
      id: 1050,
      meta: {
        title: '资源管理',
        show: true
      },
      component: 'ResourceManager'
    },
    {
      name: 'Person',
      parentId: 1024,
      id: 1030,
      meta: {
        title: '员工信息'
      },
      component: 'Person'
    },
    {
      name: 'OrgManager',
      parentId: 1024,
      id: 1035,
      meta: {
        title: '组织管理'
      },
      component: 'OrgManager'
    },
    {
      name: 'declare',
      parentId: 0,
      id: 20,
      meta: {
        icon: 'container',
        title: '申报管理',
        hiddenHeaderContent: true
      },
      component: 'PageView'
    },
    {
      name: 'ProcurePlan',
      parentId: 20,
      id: 200,
      meta: {
        title: '采购计划'
      },
      component: 'ProcurePlan'
    },
    {
      name: 'ProcuretManagement',
      parentId: 20,
      id: 201,
      meta: {
        title: '采购生成与核销'
      },
      component: 'ProcuretManagement'
    },
    {
      name: 'Invoice',
      parentId: 20,
      id: 202,
      meta: {
        title: '发票管理'
      },
      component: 'Invoice'
    },
    // 采购管理模块
    {
      name: 'purchase',
      parentId: 0,
      id: 30,
      meta: {
        icon: 'code-sandbox',
        title: '采购管理',
        hiddenHeaderContent: true
      },
      redirect: '/purchase/ContractManagement',
      component: 'PageView'
    },
    {
      name: 'ContractManagement',
      parentId: 30,
      id: 301,
      meta: {
        title: '合同管理'
      },
      component: 'ContractManagement'
    },
    {
      name: 'RfqManagement',
      parentId: 30,
      id: 302,
      meta: {
        title: '询比价管理'
      },
      component: 'RfqManagement'
    },
    // 报价单管理
    {
      name: 'QuotationManagement',
      parentId: 30,
      id: 303,
      meta: {
        title: '报价单管理'
      },
      component: 'QuotationManagement'
    },
    // 订单管理
    {
      name: 'PolineManagement',
      parentId: 30,
      id: 310,
      meta: {
        title: '订单管理'
      },
      component: 'PolineManagement'
    },
    {
      name: 'lg',
      parentId: 0,
      id: 888,
      meta: {
        icon: 'code-sandbox',
        title: '劳保用品',
        hiddenHeaderContent: true
      },
      redirect: '/craft/JobConfiguration',
      component: 'PageView'
    },
    {
      name: 'JobConfiguration',
      parentId: 888,
      id: 889,
      meta: {
        title: '工种配置'
      },
      component: 'JobConfiguration'
    }
  ]
  return builder(nav)
}

Mock.mock(/\/api\/user\/info/, 'get', info)
Mock.mock(/\/api\/user\/nav/, 'get', userNav)
