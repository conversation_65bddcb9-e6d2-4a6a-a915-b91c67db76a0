import Mock from 'mockjs2'
import api from '@/api/material/usedfor'
import { toRegex, builder } from '../util'

Mock.mock(toRegex(api.getUsedforInfo), 'post', options => {
  return builder({
    data: [
      {
        activity: 'Y',
        createBy: 'test',
        createDate: 1585805204000,
        orgId: '1.100',
        parentId: 'A',
        usedforId: 'A1',
        usedforName: 'A1'
      },
      {
        activity: 'Y',
        createBy: 'test',
        createDate: 1585805204000,
        orgId: '1.100',
        parentId: 'A1',
        usedforId: 'A1B1',
        usedforName: 'A1B1'
      },
      {
        activity: 'Y',
        createBy: 'test',
        createDate: 1585805204000,
        orgId: '1.100',
        parentId: 'A1',
        usedforId: 'A1B2',
        usedforName: 'A1B2'
      },
      {
        activity: 'Y',
        createBy: 'test',
        createDate: 1585805204000,
        orgId: '1.100',
        parentId: 'A',
        usedforId: 'A2',
        usedforName: 'A2'
      },
      {
        activity: 'Y',
        createBy: 'test',
        createDate: 1585805204000,
        orgId: '1.100',
        parentId: 'A2',
        usedforId: 'A2B1',
        usedforName: 'A2B1'
      },
      {
        activity: 'N',
        createBy: 'test',
        createDate: 1585805204000,
        orgId: '1.100',
        parentId: 'A2',
        usedforId: 'A2B2',
        usedforName: 'A2B2'
      },
      {
        activity: 'Y',
        createBy: 'test',
        createDate: 1585805204000,
        orgId: '1.100',
        parentId: 'A2B1',
        usedforId: 'A2B1C2',
        usedforName: 'A2B1C2'
      },
      {
        activity: 'Y',
        createBy: 'test',
        createDate: 1585805204000,
        orgId: '1.100',
        parentId: 'A2B2',
        usedforId: 'A2B2C1',
        usedforName: 'A2B2C1'
      },
      {
        activity: 'Y',
        createBy: 'test',
        createDate: 1585805204000,
        orgId: '1.100',
        parentId: 'A2B2',
        usedforId: 'A2B2C2',
        usedforName: 'A2B2C2'
      },
      {
        activity: 'Y',
        createBy: 'test',
        createDate: 1585805204000,
        orgId: '1.100',
        parentId: 'A',
        usedforId: 'A3',
        usedforName: 'A3'
      }
    ],
    pageNo: 1,
    pageSize: 0,
    totalCount: 0,
    totalPage: 0
  })
})

Mock.mock(toRegex(api.modifyUsedforInfo), 'post', options => {
  return builder()
})
