import Mock from 'mockjs2'
import api from '@/api/material/matu'
import { toRegex, builder, getBody } from '../util'

Mock.mock(toRegex(api.listStorehouseByMatrial), 'post', options => {
  const response = {
    data: [
      {
        selected: false,
        storehouseName: 'A仓库',
        storehouseSysId: '26534169307864555',
        totalCurbal: 8
      },
      {
        selected: false,
        storehouseName: 'B仓库',
        storehouseSysId: '26534169307864556',
        totalCurbal: 15
      },
      {
        selected: false,
        storehouseName: 'C仓库',
        storehouseSysId: '26534169307864557',
        totalCurbal: 50
      }
    ],
    pageNo: 1,
    pageSize: 30,
    totalCount: 5,
    totalPage: 1
  }
  console.log(getBody(options))
  return builder(response)
})

Mock.mock(toRegex(api.listMaterialBalances), 'post', options => {
  const response = [
    {
      createBy: '00001',
      createDate: 1589008166000,
      curbal: 5.0000,
      fifo: '20200509',
      isArrival: 'Y',
      issueUnit: 'unit0048',
      lineCost: 150.0000,
      lotCost: 30.0000,
      lotNum: '10000-20200508002-20200509-17',
      materialBalancesSysId: '26534169307962974',
      materialNum: '003004007',
      matrSysId: '26534169307962973',
      orgId: '0111',
      selected: true,
      storehouseSysId: '26534169307864555',
      vendor: '26534169307775225'
    },
    {
      createBy: '00001',
      createDate: 1589008208000,
      curbal: 5.0000,
      fifo: '20200509',
      isArrival: 'Y',
      issueUnit: 'unit0042',
      lineCost: 15.0000,
      lotCost: 15.0000,
      lotNum: '10000-001-20200509-18',
      materialBalancesSysId: '26534169307962976',
      materialNum: 'BGHC10009',
      matrSysId: '26534169307962975',
      orgId: '0111',
      selected: true,
      storehouseSysId: '26534169307864555',
      vendor: '26534169307960096'
    },
    {
      createBy: '00001',
      createDate: 1589008208001,
      curbal: 5.0000,
      fifo: '20200509',
      isArrival: 'Y',
      issueUnit: 'unit0042',
      lineCost: 20.0000,
      lotCost: 20.0000,
      lotNum: '10000-001-20200509-19',
      materialBalancesSysId: '26534169307962977',
      materialNum: 'BGHC10009',
      matrSysId: '26534169307962975',
      orgId: '0111',
      selected: true,
      storehouseSysId: '26534169307864555',
      vendor: '26534169307960097'
    },
    {
      createBy: '00001',
      createDate: 1589008208000,
      curbal: 10.0000,
      fifo: '20200509',
      isArrival: 'Y',
      issueUnit: 'unit0042',
      lineCost: 30.0000,
      lotCost: 30.0000,
      lotNum: '10000-001-20200509-20',
      materialBalancesSysId: '26534169307962978',
      materialNum: 'BGHC10009',
      matrSysId: '26534169307962975',
      orgId: '0111',
      selected: true,
      storehouseSysId: '26534169307864555',
      vendor: '26534169307960098'
    },
    {
      createBy: '00001',
      createDate: 1589008208000,
      curbal: 10.0000,
      fifo: '20200509',
      isArrival: 'Y',
      issueUnit: 'unit0042',
      lineCost: 20.0000,
      lotCost: 20.0000,
      lotNum: '10000-001-20200509-21',
      materialBalancesSysId: '26534169307962979',
      materialNum: 'BGHC10009',
      matrSysId: '26534169307962975',
      orgId: '0111',
      selected: false,
      storehouseSysId: '26534169307864555',
      vendor: '26534169307960099'
    },
    {
      createBy: '00001',
      createDate: 1589008208000,
      curbal: 10.0000,
      fifo: '20200509',
      isArrival: 'Y',
      issueUnit: 'unit0042',
      lineCost: 20.0000,
      lotCost: 20.0000,
      lotNum: '10000-001-20200509-22',
      materialBalancesSysId: '26534169307962980',
      materialNum: 'BGHC10009',
      matrSysId: '26534169307962975',
      orgId: '0111',
      selected: false,
      storehouseSysId: '26534169307864555',
      vendor: '26534169307960100'
    },
    {
      createBy: '00001',
      createDate: 1589008208000,
      curbal: 10.0000,
      fifo: '20200509',
      isArrival: 'Y',
      issueUnit: 'unit0042',
      lineCost: 20.0000,
      lotCost: 20.0000,
      lotNum: '10000-001-20200509-23',
      materialBalancesSysId: '26534169307962981',
      materialNum: 'BGHC10009',
      matrSysId: '26534169307962975',
      orgId: '0111',
      selected: false,
      storehouseSysId: '26534169307864555',
      vendor: '26534169307960101'
    },
    {
      createBy: '00001',
      createDate: 1589008208000,
      curbal: 10.0000,
      fifo: '20200509',
      isArrival: 'Y',
      issueUnit: 'unit0042',
      lineCost: 20.0000,
      lotCost: 20.0000,
      lotNum: '10000-001-20200509-24',
      materialBalancesSysId: '26534169307962982',
      materialNum: 'BGHC10009',
      matrSysId: '26534169307962975',
      orgId: '0111',
      selected: false,
      storehouseSysId: '26534169307864555',
      vendor: '26534169307960102'
    },
    {
      createBy: '00001',
      createDate: 1589008208000,
      curbal: 10.0000,
      fifo: '20200509',
      isArrival: 'Y',
      issueUnit: 'unit0042',
      lineCost: 20.0000,
      lotCost: 20.0000,
      lotNum: '10000-001-20200509-25',
      materialBalancesSysId: '26534169307962983',
      materialNum: 'BGHC10009',
      matrSysId: '26534169307962975',
      orgId: '0111',
      selected: false,
      storehouseSysId: '26534169307864555',
      vendor: '26534169307960103'
    }
  ]
  console.log(getBody(options))
  return builder(response)
})

Mock.mock(toRegex(api.exitStore), 'post', options => {
  console.log(getBody(options))
  return builder({})
})
