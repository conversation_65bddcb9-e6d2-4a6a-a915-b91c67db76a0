import Mock from 'mockjs2'
import { builder, getParam } from '../util'

const totalCount = 5701

const serverList = options => {
  const parameters = getParam(options)

  const result = []
  const pageNo = parseInt(parameters.pageNo)
  const pageSize = parseInt(parameters.pageSize)
  const totalPage = Math.ceil(totalCount / pageSize)
  const key = (pageNo - 1) * pageSize
  const next = (pageNo >= totalPage ? totalCount % pageSize : pageSize) + 1

  for (let i = 1; i < next; i++) {
    const tmpKey = key + i
    result.push({
      key: tmpKey,
      id: tmpKey,
      no: 'No ' + tmpKey,
      description: '这是一段描述',
      callNo: Mock.mock('@integer(1, 999)'),
      status: Mock.mock('@integer(0, 3)'),
      updatedAt: Mock.mock('@datetime'),
      editable: false
    })
  }

  return builder({
    pageSize: pageSize,
    pageNo: pageNo,
    totalCount: totalCount,
    totalPage: totalPage,
    data: result
  })
}

const serverListDemo = options => {
  // const parameters = getParam(options)
  // const pageNo = parseInt(parameters.pageNo)
  const result1 = []

  for (let i = 1; i < 4; i++) {
    const tmpKey = i
    result1.push({
      id: tmpKey,
      username: 'zhouji' + i,
      cname: '周吉' + i,
      mobile: '13567847619',
      status: Mock.mock('@integer(0, 3)')
    })
  }

  const aa = builder({
    pageSize: 20,
    pageNo: 1,
    totalCount: totalCount,
    totalPage: 20,
    data: result1
  })
  alert(aa)
}

const projects = () => {
  return builder({
    data: [
      {
        id: 1,
        cover: 'https://cn.bing.com/th?id=OIP._8FyU636rP0Ic1I7eyGeXwAAAA&pid=Api&rs=1',
        title: '订单号:JJYS13633',
        description: '自动变速箱油，得乐400NG天然气机油等',
        status: 1,
        updatedAt: '2018-07-26 00:00:00'
      },
      {
        id: 2,
        cover: 'https://cn.bing.com/th?id=OIP._8FyU636rP0Ic1I7eyGeXwAAAA&pid=Api&rs=1',
        title: '订单号:JJYS13634',
        description: '自动变速箱油，得乐400NG天然气机油等',
        status: 1,
        updatedAt: '2018-07-26 00:00:00'
      },
      {
        id: 3,
        cover: 'https://cn.bing.com/th?id=OIP._8FyU636rP0Ic1I7eyGeXwAAAA&pid=Api&rs=1',
        title: '订单号:JJYS13635',
        description: '自动变速箱油【1箱】，得乐400NG天然气机油等',
        status: 1,
        updatedAt: '2018-07-26 00:00:00'
      },
      {
        id: 4,
        cover: 'https://cn.bing.com/th?id=OIP._8FyU636rP0Ic1I7eyGeXwAAAA&pid=Api&rs=1',
        title: '订单号:JJYS13636',
        description: '自动变速箱油，得乐400NG天然气机油等',
        status: 1,
        updatedAt: '2018-07-26 00:00:00'
      },
      {
        id: 5,
        cover: 'https://cn.bing.com/th?id=OIP._8FyU636rP0Ic1I7eyGeXwAAAA&pid=Api&rs=1',
        title: '订单号:JJYS13637',
        description: '自动变速箱油，得乐400NG天然气机油等',
        status: 1,
        updatedAt: '2018-07-26 00:00:00'
      },
      {
        id: 6,
        cover: 'https://cn.bing.com/th?id=OIP._8FyU636rP0Ic1I7eyGeXwAAAA&pid=Api&rs=1',
        title: '订单号:JYS13638',
        description: '自动变速箱油，得乐400NG天然气机油等',
        status: 1,
        updatedAt: '2018-07-26 00:00:00'
      }
    ],
    pageSize: 10,
    pageNo: 0,
    totalPage: 6,
    totalCount: 57
  })
}

const activity = () => {
  return builder([
    {
      id: 1,
      user: {
        nickname: '',
        avatar: 'https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png'
      },
      project: {
        name: 'xxx物资申请单',
        action: '已审批',
        event: '审批人：公司总经理'
      },
      time: '2018-08-23 14:47:00'
    },
    {
      id: 1,
      user: {
        nickname: '',
        avatar: 'https://gw.alipayobjects.com/zos/rmsportal/jZUIxmJycoymBprLOUbT.png'
      },
      project: {
        name: 'xxx物资申请单',
        action: '已审批',
        event: '审批人：公司总经理'
      },
      time: '2018-08-23 09:35:37'
    },
    {
      id: 1,
      user: {
        nickname: '',
        avatar: '@image(64x64)'
      },
      project: {
        name: 'xxx物资申请单',
        action: '已审批',
        event: '审批人：公司总经理'
      },
      time: '2017-05-27 00:00:00'
    },
    {
      id: 1,
      user: {
        nickname: '',
        avatar: '@image(64x64)'
      },
      project: {
        name: 'xxx物资申请单',
        action: '已审批',
        event: '审批人：公司总经理'
      },
      time: '2018-08-23 14:47:00'
    },
    {
      id: 1,
      user: {
        nickname: '',
        avatar: '@image(64x64)'
      },
      project: {
        name: 'xxx物资申请单',
        action: '已审批',
        event: '审批人：公司总经理'
      },
      time: '2018-08-23 14:47:00'
    }
  ])
}

const teams = () => {
  return builder([
    {
      id: 1,
      name: '科学搬砖组',
      avatar: 'https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png'
    },
    {
      id: 2,
      name: '程序员日常',
      avatar: 'https://gw.alipayobjects.com/zos/rmsportal/cnrhVkzwxjPwAaCfPbdc.png'
    },
    {
      id: 1,
      name: '设计天团',
      avatar: 'https://gw.alipayobjects.com/zos/rmsportal/gaOngJwsRYRaVAuXXcmB.png'
    },
    {
      id: 1,
      name: '中二少女团',
      avatar: 'https://gw.alipayobjects.com/zos/rmsportal/ubnKSIfAJTxIgXOKlciN.png'
    },
    {
      id: 1,
      name: '骗你学计算机',
      avatar: 'https://gw.alipayobjects.com/zos/rmsportal/WhxKECPNujWoWEFNdnJE.png'
    }
  ])
}

const radar = () => {
  return builder([
    {
      item: '引用',
      个人: 70,
      团队: 30,
      部门: 40
    },
    {
      item: '口碑',
      个人: 60,
      团队: 70,
      部门: 40
    },
    {
      item: '产量',
      个人: 50,
      团队: 60,
      部门: 40
    },
    {
      item: '贡献',
      个人: 40,
      团队: 50,
      部门: 40
    },
    {
      item: '热度',
      个人: 60,
      团队: 70,
      部门: 40
    },
    {
      item: '引用',
      个人: 70,
      团队: 50,
      部门: 40
    }
  ])
}

Mock.mock(/\/service/, 'get', serverList)
Mock.mock(/\/servicedemo/, 'get', serverListDemo)
Mock.mock(/\/list\/search\/projects/, 'get', projects)
Mock.mock(/\/workplace\/activity/, 'get', activity)
Mock.mock(/\/workplace\/teams/, 'get', teams)
Mock.mock(/\/workplace\/radar/, 'get', radar)
