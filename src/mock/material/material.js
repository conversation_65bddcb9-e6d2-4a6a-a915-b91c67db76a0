
// import Mock from 'mockjs2'
// import api from '@/api/material/material'
// import {
//   toRegex,
//   builder
// } from '../util'

// Mock.mock(toRegex(api.getMaterialInfo), 'post', options => {
//   const response = {
//     data: [
//       {
//         activity: 'Y',
//         bit: '位',
//         buyer: '张三',
//         createBy: '创建者',
//         createDate: 1584625005000,
//         deptno: '软件部',
//         floor: '层',
//         frame: '架',
//         isLabourSupply: 'Y',
//         isResupply: 'Y',
//         isReuse: 'Y',
//         isStop: 'Y',
//         isUniOrder: 'N',
//         issueUnit: 'Y',
//         jModel: 'HJyh 6253',
//         lineCost: 10000,
//         lotType: '批次；类型',
//         materialClass: '1',
//         materialName: '办公用品',
//         materialNum: 'BGYP',
//         materialSysId: '1',
//         materialType: '项目类型',
//         materialnum5: '212',
//         maxAmount: 100,
//         minAmount: 10,
//         modifyBy: '修改者',
//         modifyDate: 1584711402000,
//         orderUnit: '件',
//         orgId: '1111',
//         refCost: 200,
//         status: '1',
//         storeman: '李四',
//         typeDetail: '类型细目',
//         yardId: '库',
//         zone: '区'
//       },
//       {
//         activity: 'N',
//         bit: '件',
//         buyer: '海妹妹',
//         createBy: '创建者',
//         createDate: 1584625005000,
//         deptno: '软件部',
//         floor: '层',
//         frame: '架',
//         isLabourSupply: 'Y',
//         isResupply: 'Y',
//         isReuse: 'Y',
//         isStop: 'Y',
//         isUniOrder: 'N',
//         issueUnit: 'Y',
//         jModel: 'juh 1.2.13',
//         lineCost: 10000,
//         lotType: '批次；类型',
//         materialClass: '1',
//         materialName: '配件',
//         materialNum: 'BGYP1',
//         materialSysId: '2',
//         materialType: '项目类型',
//         materialnum5: '212',
//         maxAmount: 100,
//         minAmount: 10,
//         modifyBy: '修改者',
//         modifyDate: 1584681282417,
//         orderUnit: '件',
//         orgId: '1111',
//         refCost: 200,
//         status: '1',
//         storeman: '李磊',
//         typeDetail: '类型细目',
//         yardId: '库',
//         zone: '区'
//       },
//       {
//         activity: 'Y',
//         bit: '位',
//         buyer: '王五',
//         createBy: 'hj',
//         createDate: 1585099420119,
//         deptno: '软件部',
//         floor: '层',
//         frame: '架',
//         isLabourSupply: 'Y',
//         isResupply: 'Y',
//         isReuse: 'Y',
//         isStop: 'Y',
//         isUniOrder: 'N',
//         issueUnit: 'Y',
//         jModel: 'ji 1.2.33',
//         lineCost: 10000,
//         lotType: '批次；类型',
//         materialClass: '1',
//         materialName: '办公用品',
//         materialNum: 'BGYP7',
//         materialSysId: '26534169307775086',
//         materialType: '项目类型',
//         materialnum5: '212',
//         maxAmount: 100,
//         minAmount: 10,
//         modifyBy: 'admin',
//         modifyDate: 1585199660247,
//         orderUnit: '件',
//         orgId: '0111',
//         refCost: 200,
//         status: '2',
//         storeman: '陈六',
//         typeDetail: '类型细目',
//         yardId: '库',
//         zone: '区'
//       }
//     ],
//     pageNo: 1,
//     pageSize: 15,
//     totalCount: 15,
//     totalPage: 1
//   }
//   return builder(response)
// })
