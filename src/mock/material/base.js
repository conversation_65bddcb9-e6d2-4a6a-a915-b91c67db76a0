
import api from '@/api/material/base'
import Mock from 'mockjs2'
import {
  toRegex,
  getParam,
  builder
} from '../util'

Mock.mock(toRegex(api.getTreeById), 'get', options => {
  const param = getParam(options)
  if (param.id === 'usedfor') {
    const response = [
      {
        children: [
          { label: '测试用', value: '26534169307775107', valueParent: '82372321' },
          { label: '测试用', value: '26534169307775109', valueParent: '82372321' },
          { label: '测试用', value: '26534169307775110', valueParent: '82372321' }
        ],
        label: '生产用',
        value: '82372321',
        valueParent: '0'
      },
      { label: '生产用', value: '82672321', valueParent: '0' },
      { label: '生产用', value: '9982372321', valueParent: '0' }
    ]
    return builder(response)
  }
})

Mock.mock(toRegex(api.getCommboxById), 'get', options => {
  const param = getParam(options)
  if (param.id === 'appro') {
    const response = [
      {
        'label': '新建',
        'value': '1001'
      },
      {
        'label': '审批中',
        'value': '1002'
      },
      {
        'label': '已审批',
        'value': '1003'
      },
      {
        'label': '已结束',
        'value': '1004'
      }
    ]
    return builder(response)
  }
  if (param.id === 'dept') {
    const response = [
      {
        'label': '研发部',
        'value': '1'
      },
      {
        'label': '软件部门',
        'value': '2'
      }
    ]
    return builder(response)
  }
  if (param.id === 'matrtype') {
    const response = [
      {
        'label': '类别1',
        'value': '类别1'
      },
      {
        'label': '类别2',
        'value': '类别2'
      },
      {
        'label': '类别3',
        'value': '类别3'
      },
      {
        'label': '类别4',
        'value': '类别4'
      }
    ]
    return builder(response)
  }
  if (param.id === 'person') {
    const response = [
      {
        'label': '小王',
        'value': '1'
      },
      {
        'label': '小张',
        'value': '2'
      },
      {
        'label': '小徐',
        'value': '3'
      },
      {
        'label': '小丁',
        'value': '4'
      }
    ]
    return builder(response)
  }
  if (param.id === 'personByDept') {
    const response = [
      {
        'label': '审批人1',
        'value': 'userNo0001'
      },
      {
        'label': '审批人2',
        'value': 'userNo0002'
      },
      {
        'label': '审批人3',
        'value': 'userNo0003'
      },
      {
        'label': '审批人4',
        'value': 'userNo0004'
      }
    ]
    return builder(response)
  }
})

Mock.mock(toRegex(api.getMaterialClassInfo), 'get', options => {
  const parameters = getParam(options)
  if (parameters.activity === 'Y') {
    return builder({
      data: [
        {
          codeClassId: 'AcceptType',
          codeClassName: '受理类型',
          showSequence: 1,
          activity: 'Y'
        },
        {
          codeClassId: 'Sex',
          codeClassName: '性别',
          showSequence: 3,
          activity: 'Y'
        }
      ],
      pageSize: 10,
      pageNo: 0,
      totalPage: 6,
      totalCount: 57
    })
  } else if (parameters.activity === 'N') {
    return builder({
      data: [
        {
          codeClassId: 'BusinessType',
          codeClassName: '业务类型',
          showSequence: 2,
          activity: 'N'
        }
      ],
      pageSize: 10,
      pageNo: 0,
      totalPage: 6,
      totalCount: 57
    })
  } else {
    return builder({
      data: [
        {
          codeClassId: 'AcceptType',
          codeClassName: '受理类型',
          showSequence: 1,
          activity: 'Y'
        },
        {
          codeClassId: 'BusinessType',
          codeClassName: '业务类型',
          showSequence: 2,
          activity: 'N'
        },
        {
          codeClassId: 'Sex',
          codeClassName: '性别',
          showSequence: 3,
          activity: 'Y'
        }
      ],
      pageSize: 10,
      pageNo: 0,
      totalPage: 6,
      totalCount: 57
    })
  }
})

Mock.mock(toRegex(api.getMaterialCodeInfo), 'get', options => {
  const parameters = getParam(options)
  if (parameters.codeClassId === 'AcceptType') {
    return builder({
      data: [
        {
          codeId: 'Y',
          codeName: '已受理',
          udf1: '',
          udf2: '',
          udf3: '',
          activity: 'Y'
        },
        {
          codeId: 'N',
          codeName: '未受理',
          udf1: '',
          udf2: '',
          udf3: '',
          activity: 'Y'
        }
      ],
      pageSize: 10,
      pageNo: 0,
      totalPage: 1,
      totalCount: 10
    })
  } else if (parameters.codeClassId === 'BusinessType') {
    return builder({
      data: [
        {
          codeId: '0',
          codeName: '装卸船',
          udf1: 'Vessel',
          udf2: '',
          udf3: '',
          activity: 'Y'
        },
        {
          codeId: '1',
          codeName: '装船',
          udf1: 'Vessel',
          udf2: 'Freight',
          udf3: '',
          activity: 'Y'
        }
      ],
      pageSize: 10,
      pageNo: 0,
      totalPage: 1,
      totalCount: 10
    })
  } else if (parameters.codeClassId === 'Sex') {
    return builder({
      data: [
        {
          codeId: 'F',
          codeName: '女',
          udf1: 'Vessel',
          udf2: '',
          udf3: '',
          activity: 'Y'
        },
        {
          codeId: 'M',
          codeName: '男',
          udf1: 'Vessel',
          udf2: 'Freight',
          udf3: '',
          activity: 'Y'
        }
      ],
      pageSize: 10,
      pageNo: 0,
      totalPage: 1,
      totalCount: 10
    })
  } else {
    return builder({
      data: [],
      pageSize: 10,
      pageNo: 0,
      totalPage: 1,
      totalCount: 0
    })
  }
})
