export const toRegex = string => {
  return new RegExp(
    string.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&') + '(\\?.*)?$'
  )
}

export const getBody = options => {
  return options.body && JSON.parse(options.body)
}

export const getParam = options => {
  const url = options.url
  const search = url.split('?')[1]
  if (!search) {
    return {}
  }
  return JSON.parse(
    '{"' + decodeURIComponent(search)
      .replace(/"/g, '\\"')
      .replace(/&/g, '","')
      .replace(/=/g, '":"') + '"}'
  )
}

export const builder = (data, message = '', code = '0000', headers = {}) => {
  return {
    _headers: headers,
    message: message || '',
    timestamp: new Date().getTime(),
    result: data,
    code: code
  }
}
