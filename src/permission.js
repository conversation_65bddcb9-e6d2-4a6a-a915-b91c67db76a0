import Vue from 'vue'
import router from './router'
import store from './store'

import { generatorComponents as components } from './router.generator'
import { ACCESS_TOKEN, OPERATOR, ORG_ID, PERSON_ID } from '@/store/mutation-types'
import { setDocumentTitle } from '@/utils/domUtil'

import NProgress from 'nprogress'
import Notification from 'ant-design-vue/es/notification'
import '@/components/NProgress/nprogress.less'

NProgress.configure({ showSpinner: false })

const array = ['development', 'test']
const informal = array.includes(process.env.VUE_APP_ENV)
const whiteList = informal ? ['login', 'loginSys'] : ['login']
const loginRoutePath = informal ? '/user/loginSys' : '/user/login'
const toDefaultRoute = ['/user/loginSys', '/user/login']
const defRoutePath = '/dashboard'

router.beforeEach((to, from, next) => {
  NProgress.start()
  if (to.path.startsWith('/sso')) {
    return next()
  }
  const orgId = Vue.ls.get(ORG_ID)
  const operator = Vue.ls.get(OPERATOR)
  const personId = Vue.ls.get(PERSON_ID)
  const authToken = Vue.ls.get(ACCESS_TOKEN)
  if (to.meta && to.meta.title) {
    setDocumentTitle(`${to.meta.title} - ITSM`)
  } else {
    setDocumentTitle(`ITSM`)
  }
  if (authToken && operator && orgId) {
    if (toDefaultRoute.includes(to.path)) {
      next({ path: defRoutePath })
      NProgress.done()
    } else {
      if (store.getters.roles.length === 0) {
        const params = {
          userNo: operator
        }
        store.dispatch('SetTodoTableGroup', { orgId, personId })
        store.dispatch('SettodoCockpitGroup', { orgId, personId })
        store
          .dispatch('GetUserInfo', params)
          .then(res => {
            store.dispatch('GenerateRoutes', { params, components }).then(() => {
              const pathRedirect = to.path
              const queryRedirect = from.query.redirect
              const originalRedirect = queryRedirect || pathRedirect
              const redirect = decodeURIComponent(originalRedirect)
              router.addRoutes(store.getters.addRouters)
              if (to.path === redirect) {
                next({ ...to, replace: true })
              } else {
                next({ path: redirect })
              }
            })
            store.dispatch('SetTodoGroup', res.result.role)
          })
          .catch(() => {
            Notification.error({
              message: '错误',
              description: '请求用户信息失败，请重新登录！',
              duration: 2,
              onClose: function () {
                store.dispatch('Logout').then(() => {
                  next({ path: loginRoutePath, query: { redirect: to.fullPath } })
                })
              }
            })
          })
      } else {
        next()
      }
    }
  } else {
    if (!to || !to.name) {
      next({ path: loginRoutePath })
    } else if (whiteList.includes(to.name)) {
      next()
    } else {
      Notification.warning({
        message: '系统通知',
        description: 'token 已过期, 请重新登录！',
        duration: 2,
        onClose: function () {
          store.dispatch('Logout').then(() => {
            next({ path: loginRoutePath, query: { redirect: to.fullPath } })
          })
        }
      })
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
