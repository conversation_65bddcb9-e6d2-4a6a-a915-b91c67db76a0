<template>
  <print-area ref="printArea">
    <section
      v-for="(list, key) in object"
      class="excel-container"
      :key="key"
    >
      <div
        v-if="!IS_TS"
        class="excel-item"
        v-for="(item, index) of list"
        break-avoid
        break-after
        :key="index"
      >
        <div class="excel-group">
          <div class="excel-header">
            <div class="mainTitle">
              <div class="orgName">{{ item.orgName }}</div>
              <div class="textName">
                <span style="letter-spacing: 1.5pt">{{ (IS_XT_ENV && item.maplist[0].materialNum.substring(0,2) === '45') ? '办公'+ key : key }}</span>
              </div>
            </div>
            <div v-if="IS_YZ_ENV" class="subTitle">
              <div>
                <span style="margin: 0 3pt">计划单号:</span>
                {{ item.prlineNum }}
              </div>
            </div>
            <div class="subTitle">
              <div v-if="!(IS_JX_ENV || IS_MD)">
                <span style="margin: 0 3pt">单证类别:</span>
                {{ item.materialTypeName || '普通物资' }}
              </div>
              <div>
                <span style="margin: 0 3pt">验收单号:</span>
                {{ item.polineNum }}
              </div>
              <div v-if="IS_BYJ_ENV">
                <span style="margin: 0 3pt">账本号:</span>
                {{ item.zbh }}
              </div>
              <div>
                <span style="margin: 0 3pt">供应商:</span>
                {{ item.vendorName }}
              </div>
              <div v-if="IS_BYJ_ENV">
                <span style="margin: 0 3pt">库负责人:</span>
                {{ item.storeman }}
              </div>
              <div v-if="IS_YZ_ENV">
                <span style="margin: 0 3pt">制单时间:</span>
                {{ item.printDate || item.createDate }}
              </div>
            </div>
          </div>
          <div class="excel-content">
            <a-table
              class="table table-th-2 table-td-3"
              :columns="columns"
              :data-source="item.maplist"
              :pagination="false"
              rowKey="index"
              bordered
            >
              <span
                slot="num"
                slot-scope="text"
                :class="{ 'numStyle': IS_MD }"
              >{{ text }}</span>
            </a-table>
          </div>
          <div class="excel-footer">
            <div v-if="!IS_BYJ_ENV && !IS_JX_ENV && !IS_LG">
              <span style="margin: 0 3pt">{{ IS_JYS ? '经办人:' : '制单人:' }}</span>
              {{ IS_JYS ? item.buyer : (IS_TC ? item.storeman : '') }}
              {{ IS_XT_ENV ? item.zdr : '' }}
              {{ IS_SLH ? USER_NAME : '' }}
              {{ IS_DMY ? USER_NAME : '' }}
            </div>
            <div v-if="!IS_JX_ENV">
              <span style="margin: 0 3pt">{{ IS_JYS ? '验收人:' : '采购人:' }}</span>
              {{ IS_JYS ? item.storeman : (IS_TC ? item.buyer : '') }}
              {{ IS_XT_ENV ? item.cgr : '' }}
            </div>
            <div v-if="IS_JX_ENV">
              <span style="margin: 0 3pt">{{ '验收人:' + USER_NAME }}</span>
            </div>
            <div v-if="IS_XT_ENV">
              <span style="margin: 0 3pt">验收人:</span>
              {{ item.ysr }}
            </div>
            <div v-else>
              <span style="margin: 0 3pt">{{ IS_JYS ? '审核人:' : '验收人:' }}</span>
              {{ (IS_TC || IS_SZXD) ? (item.requestedBy !== '计划外入库' ?item.requestedBy :item.buyer) : '' }}
            </div>
            <div v-if="IS_BYJ_ENV">
              <span style="margin: 0 3pt">使用部门:</span>
            </div>
            <div>
              <span style="margin: 0 3pt">{{ IS_BYJ_ENV ||IS_YZ_ENV ? '税前金额:' : '金额:' }}</span>
              <span :class="{ 'numStyle': IS_MD }">{{ item.totalCurrency }}</span>
            </div>
            <div v-if="!IS_YZ_ENV">
              <span style="margin: 0 3pt">{{ IS_BYJ_ENV ? '税前总金额:' : '总金额:' }}</span>
              <span :class="{ 'numStyle': IS_MD }">{{ item.polineNumCurrency }}</span>
            </div>
            <div v-if="IS_BYJ_ENV || IS_YZ_ENV">
              <span style="margin: 0 3pt">税额:</span>
              <span>{{ item.polinese }}</span>
            </div>
            <div v-if="IS_BYJ_ENV || IS_YZ_ENV">
              <span style="margin: 0 3pt">税后总金额:</span>
              <span>{{ item.AfterTaxPolineNumCurrency }}</span>
            </div>
            <div v-if="IS_XT">
              <span style="margin: 0 3pt">使用方向:</span>
              {{ item.usedforName }}
            </div>
            <div v-if="!IS_YZ_ENV">
              <span style="margin: 0 3pt">制单时间:</span>
              {{ item.printDate || item.createDate }}
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="IS_TS"
        class="excel-item"
        v-for="(item, index) of list"
        break-avoid
        break-after
        :key="index"
      >
        <div class="excel-group">
          <div class="excel-header">
            <div class="mainTitle">
              <div class="orgName">{{ item.orgName }}</div>
              <div class="textName">
                <span style="letter-spacing: 1.5pt">{{ (IS_XT_ENV && item.maplist[0].materialNum.substring(0,2) === '45') ? '办公'+ key : key }}</span>
              </div>
            </div>
            <div class="subTitle">
              <span style="margin: 0 3pt">单证类别:</span>
              {{ item.materialTypeName || '普通物资' }}
              <span style="margin: 0 3pt">{{ '验收单号:'+ item.polineNum }}</span>
              <span style="margin: 0 3pt">{{ '供应商:'+ item.vendorName }}</span>
            </div>
          </div>
          <div class="excel-content">
            <a-table
              class="table table-th-2 table-td-3"
              :columns="columns"
              :data-source="item.maplist"
              :pagination="false"
              rowKey="index"
              bordered
            >
              <span slot="totalExclTax" slot-scope="key2">
                <div :style="{fontSize: IS_TS ? '18px' : '14px'}">{{ key2 }}</div>
              </span>
            </a-table>
          </div>
          <div class="excel-footer">
            <div v-if="IS_TS" style="width: 100%;">
              <div style="line-height: 1.2; margin: 10pt 1pt 1pt 1pt">{{ '验收记录:'+item.remarkTS }}</div>
              <span style="margin-right: 70px">制单人:叶琰勋</span>
              <span style="margin-right: 70px">采购人:叶琰勋</span>
              <span style="margin-right: 70px">验收人:吴和明</span>
              <span style="margin-right: 70px">金额:<span :style="{fontSize: IS_TS ? '18px' : '14px'}">{{ item.totalCurrency }}</span></span>
              <span style="margin-right: 70px">制单时间:{{ item.createDate }}</span>
              <!-- <span style="margin: 0 0pt">{{'制单人:叶琰勋\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0
              采购人:叶琰勋\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0
              验收人:吴和明\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0金额:'
              +item.totalCurrency+'\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\
              制单时间:'+item.createDate }}
              </span> -->
            </div>
          </div>
        </div>
      </div>
    </section>
  </print-area>
</template>

<script>
import { PrintArea } from '@/components'
import '@/components/print.less'
import { handleFixedDigit } from '@/utils/util'

// 定制化
import Vue from 'vue'
import Utils from './utils'
import { ORG_ID, PERSON_NAME } from '@/store/mutation-types'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_JX_ENV = USER_ORG_ID === '1.102'
const IS_XT_ENV = USER_ORG_ID === '1.100.118'
const IS_BYJ_ENV = USER_ORG_ID === '1.100.101'
const IS_YZ_ENV = USER_ORG_ID === '1.100.107'
const IS_WZG = USER_ORG_ID.substr(0, 5) === '1.101'
const IS_XT = USER_ORG_ID === '1.100.118'
const IS_LG = USER_ORG_ID === '*************'
const IS_MD = USER_ORG_ID === '1.100.104'
const IS_JYS = USER_ORG_ID === '1.100.117'
const IS_TC = USER_ORG_ID === '1.100.114'
const IS_SZXD = USER_ORG_ID === '1.100.106'
const IS_TS = USER_ORG_ID === '1.100.122'
const IS_DMY = USER_ORG_ID === '1.122.801'
const IS_SLH = USER_ORG_ID === '1.100.111'
const USER_NAME = Vue.ls.get(PERSON_NAME)

export default {
  components: {
    PrintArea
  },
  data () {
    return {
      IS_SLH,
      IS_XT_ENV,
      IS_LG,
      IS_JX_ENV,
      IS_WZG,
      IS_BYJ_ENV,
      IS_YZ_ENV,
      IS_SZXD,
      IS_XT,
      IS_JYS,
      IS_TS,
      IS_MD,
      IS_TC,
      IS_DMY,
      USER_NAME,
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          customRender: (text, row, index) => {
            return {
              children: row.materialNum ? (IS_MD ? (index + 1) : text) : ''
            }
          },
          width: '4%',
          align: 'center'
        },
        ...(IS_BYJ_ENV ? [{
          title: '仓库',
          dataIndex: 'storehouseName',
          customRender: (text, row, index) => {
            return {
              children: row.storehouseName ? text : (row.usedCo ? this.storeList.find(item => item.value === row.usedCo).label : '')
            }
          },
          width: '8%',
          align: 'center'
        }]
          : []),
        {
          title: '物资代码',
          dataIndex: 'materialNum',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '12%',
          align: 'center'
        },
        ...(IS_TS || IS_DMY ? [{
          title: '仓库',
          dataIndex: 'storehouseName',
          customRender: (text, row, index) => {
            return {
              children: row.storehouseName ? text : (row.usedCo ? this.storeList.find(item => item.value === row.usedCo).label : '')
            }
          },
          width: '8%',
          align: 'center'
        }]
          : []),
        ...(IS_JX_ENV ? [{
          title: '类别',
          dataIndex: 'materialTypeName',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '8%',
          align: 'center'
        }] : []),
        ...(IS_MD ? [{
          title: '二级类别',
          dataIndex: 'materialClassName',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '8%',
          align: 'center'
        }] : []),
        {
          title: '物资名称',
          dataIndex: 'materialName',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '9%',
          align: 'center'
        },
        ...(IS_DMY ? [ {
          title: '规格型号',
          dataIndex: 'jModel',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '9%',
          align: 'center'
        }]
          : [ {
            title: '规格型号',
            dataIndex: 'jModel',
            customRender: (text, row, index) => {
              return {
                children: text
              }
            },
            width: '18%',
            align: 'center'
          }]),
        ...(IS_XT_ENV ? [ {
          title: '品牌',
          dataIndex: 'brand',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '6%',
          align: 'center'
        }] : []),
        {
          title: '计量单位',
          dataIndex: 'orderUnit',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '6%',
          align: 'center'
        },
        {
          title: '应收数量',
          dataIndex: 'orderQty',
          width: '7%',
          align: 'center'
        },
        ...(!IS_TS ? [{
          title: '实收数量',
          dataIndex: 'acceptedQty',
          width: '7%',
          align: 'center'
        }]
          : []),
        {
          title: IS_BYJ_ENV ? '税前单价' : '单价',
          dataIndex: 'refCost',
          scopedSlots: { customRender: 'num' },
          width: '7%',
          align: 'center'
        },
        {
          title: IS_BYJ_ENV ? '税前金额' : '\xa0\xa0金\xa0\xa0\xa0\xa0\xa0额\xa0\xa0',
          dataIndex: 'totalExclTax',
          scopedSlots: { customRender: 'num' },
          width: '7%',
          align: 'center'
        },
        {
          title: '发票号码1',
          dataIndex: 'invoiceNum',
          customRender: (text, row, index) => {
            return {
              children: text === '1' ? '随机备件' : text
            }
          },
          width: '12%',
          align: 'center'
        },
        ...(IS_JX_ENV ? [{
          title: '领料单号',
          dataIndex: 'prlineNum',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '10%',
          align: 'center'
        }, {
          title: '仓库',
          dataIndex: 'storehouseName',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '5%',
          align: 'center'
        }] : []),
        ...(IS_BYJ_ENV ? [{
          title: '\xa0税\xa0率\xa0',
          dataIndex: 'taxRate',
          scopedSlots: { customRender: 'num' },
          width: '7%',
          align: 'center'
        }]
          : []),
        ...(IS_DMY ? [{
          title: '使用方向',
          dataIndex: 'usedforName',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '7%',
          align: 'center'
        }]
          : [])
      ],
      // 打印配置
      excelData: '',
      printDate: '',
      storeList: [
        { value: 'YH', label: '涌和库' },
        { value: 'TD', label: '通达库' },
        { value: 'NBCT', label: 'NBCT库' },
        { value: 'BLSTD', label: '北仑山通达库' },
        { value: 'BY', label: '北一库' }
      ]
    }
  },
  computed: {
    object () {
      const excelData = {}
      if (Utils.isObject(this.excelData)) {
        for (const title in this.excelData) {
          Object.assign(excelData, {
            [title]: this.toSort(this.excelData[title])
          })
        }
        return excelData
      }
      if (Utils.isArray(this.excelData)) {
        if (!IS_TS) {
          Object.assign(excelData, {
            物资验收单: this.toSort(this.excelData)
          })
        }
        if (IS_TS) {
          Object.assign(excelData, {
            物资入库单: this.toSort(this.excelData)
          })
        }
      }
      return excelData
    }
  },
  methods: {
    toSort (data) {
      const group = (IS_XT ? 10 : (IS_WZG ? 7 : (IS_JYS ? 30 : 5)))
      const cache = {}
      // 税额计算方式 false 当前页求和  true polineNum一致的求和
      const afterTaxTyep = IS_BYJ_ENV
      for (const item of data) {
        item.oldlist = item.maplist || []
        item.totalCurrency = 0
        item.maplist = []
        if (!cache[item.polineNum]) {
          cache[item.polineNum] = {
            index: 1,
            temp: 1,
            total: 0,
            tax: 0,
            cuslineCost: 0
          }
        }
        for (let i = 0; i < group; i++) {
          item.maplist.push({
            ...(item.oldlist[i] || {}),
            index: item.oldlist[i] ? cache[item.polineNum].index++ : 'temp_' + cache[item.polineNum].temp++
          })

          // 当前table总金额
          item.totalCurrency += (item.maplist[i] || {}).totalExclTax || 0
          item.totalCurrency = +item.totalCurrency.toFixed(8) || 0

          // 当前polineNum总金额
          cache[item.polineNum].total += (item.maplist[i] || {}).totalExclTax || 0
          cache[item.polineNum].total = +cache[item.polineNum].total.toFixed(8) || 0

          if (afterTaxTyep) {
          // 当前polineNum总税额
            cache[item.polineNum].tax += (item.maplist[i] || {}).tax || 0
            cache[item.polineNum].tax = +cache[item.polineNum].tax.toFixed(8) || 0
            // 当前polineNum税后总金额
            cache[item.polineNum].cuslineCost += (item.maplist[i] || {}).cuslineCost || 0
            cache[item.polineNum].cuslineCost = +cache[item.polineNum].cuslineCost.toFixed(8) || 0
          }
        }
      }

      // 赋值总金额
      for (const item of data) {
        item.polineNumCurrency = cache[item.polineNum].total || 0
        if (afterTaxTyep) {
          item.polinese = Math.round(cache[item.polineNum].tax * 1000000) / 1000000 || 0
          item.polinese = Math.round(item.polinese * 100000) / 100000 || 0
          item.polinese = Math.round(item.polinese * 10000) / 10000 || 0
          item.polinese = Math.round(item.polinese * 1000) / 1000 || 0
          item.polinese = Math.round(item.polinese * 100) / 100 || 0
          // 总金额+ 税额 = 税后总金额
          // item.AfterTaxPolineNumCurrency = (cache[item.polineNum].total + cache[item.polineNum].tax).toFixed(2) || 0
          item.AfterTaxPolineNumCurrency = Math.round((item.polineNumCurrency + item.polinese) * 1000000) / 1000000
          item.AfterTaxPolineNumCurrency = Math.round(item.AfterTaxPolineNumCurrency * 100000) / 100000
          item.AfterTaxPolineNumCurrency = Math.round(item.AfterTaxPolineNumCurrency * 10000) / 10000
          item.AfterTaxPolineNumCurrency = Math.round(item.AfterTaxPolineNumCurrency * 1000) / 1000
          item.AfterTaxPolineNumCurrency = Math.round(item.AfterTaxPolineNumCurrency * 100) / 100
        }
      }
      return data
    },
    toHandle (data) {
      let index = 0
      const list = []
      const maps = {}
      const cache = {}
      const array = data.maplist || []

      // 按采购计划分类
      for (const item of array) {
        if (!maps[item.polineNum]) {
          maps[item.polineNum] = {
            polineNum: item.polineNum,
            lotNum: data.lotNum,
            orgName: data.orgName,
            deptName: data.deptName,
            createDate: data.createDate,
            printDate: data.printDate,
            createName: data.createName,
            remarkTS: item.remarkTS,
            totalCurrency: data.totalCurrency,
            maplist: [],
            total: 0,
            cuslineCost: 0
          }
        }
        maps[item.polineNum].maplist.push({ ...item })
      }

      // 按物资数量排版
      for (const key in maps) {
        const item = maps[key]
        const array = item.maplist
        const length = array.length

        let count = length > (IS_XT ? 10 : (IS_WZG ? 7 : (IS_JYS ? 30 : 5))) ? length : (IS_XT ? 10 : (IS_WZG ? 7 : (IS_JYS ? 30 : 5)))

        while (count % (IS_XT ? 10 : (IS_WZG ? 7 : (IS_JYS ? 30 : 5)))) {
          count++
        }

        // 拆分页面
        let xh = 0
        let temp = 0
        for (let i = 0; i < count; i++) {
          const floor = Math.floor(i / (IS_XT ? 10 : (IS_WZG ? 7 : (IS_JYS ? 30 : 5))))
          const code = key + '-' + floor
          if (cache[code] === undefined) {
            cache[code] = index++
          }
          if (!list[cache[code]]) {
            list[cache[code]] = {
              polineNum: item.polineNum,
              lotNum: data.lotNum,
              orgName: item.orgName,
              deptName: item.deptName,
              createDate: item.createDate,
              createName: item.createName,
              remarkTS: item.remarkTS,
              polineNumCurrency: 0,
              totalCurrency: 0,
              maplist: []
            }
          }
          list[cache[code]].maplist.push({
            ...(item.maplist[i] || {}),
            index: item.maplist[i] ? ++xh : 'temp_' + ++temp
          })

          // 当前table总金额
          list[cache[code]].totalCurrency += (item.maplist[i] || {}).totalExclTax || 0
          list[cache[code]].totalCurrency = +list[cache[code]].totalCurrency.toFixed(8) || 0

          // 当前polineNum总金额
          maps[item.polineNum].total += (item.maplist[i] || {}).totalExclTax || 0
          maps[item.polineNum].total = +maps[item.polineNum].total.toFixed(8) || 0
          // 当前polineNum税后总金额
          maps[item.polineNum].cuslineCost += (item.maplist[i] || {}).cuslineCost || 0
          maps[item.polineNum].cuslineCost = +maps[item.polineNum].cuslineCost.toFixed(8) || 0
        }
      }

      // 赋值总金额
      for (const item of list) {
        item.polineNumCurrency = maps[item.polineNum].total || 0
      }

      return list
    },
    toFormat (date, format) {
      function itType (val) {
        return Object.prototype.toString.call(val).replace(/^\[[^\s\]]+\s*([^\s\]]+)]$/, '$1')
      }
      function isDate (date) {
        if (itType(date) === 'Date') {
          return true
        }
        return false
      }
      function isString (str) {
        if (itType(str) === 'String') {
          return true
        }
        return false
      }
      if (isDate(date, true)) {
        const handle = function (i) {
          return (i < 10 ? '0' : '') + i
        }
        if (!isString(format, true)) {
          format = 'yyyy/MM/dd HH:mm:ss'
        }
        return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (f) {
          switch (f) {
            case 'yyyy': {
              return handle(date.getFullYear())
            }
            case 'MM': {
              return handle(date.getMonth() + 1)
            }
            case 'dd': {
              return handle(date.getDate())
            }
            case 'HH': {
              return handle(date.getHours())
            }
            case 'mm': {
              return handle(date.getMinutes())
            }
            case 'ss': {
              return handle(date.getSeconds())
            }
          }
        })
      }
      return ''
    },
    customHeaderRow () {
      return {
        style: {
          fontSize: '30px'
        }
      }
    },
    customRow () {
      return {
        style: {
          fontSize: '30px'
        }
      }
    },
    toPrint (data = [], media = {}) {
      this.excelData = data
      this.printDate = this.toFormat(new Date(), 'yyyy年MM月dd日')
      this.$nextTick(() => {
        switch (USER_ORG_ID) {
          case '1.100.118': {
            this.$refs.printArea.doPrint(
              Object.assign(
                {
                  size: '381mm 139.7mm',
                  margin: '-1cm 1cm 0cm .2cm'
                },
                media
              )
            )
            break
          }
          case '1.100.101': {
            this.$refs.printArea.doPrint(
              Object.assign(
                {
                  size: '381mm 139.7mm',
                  margin: '2cm 3cm .5cm'
                },
                media
              )
            )
            break
          }
          case '1.100.117': {
            this.$refs.printArea.doPrint(
              Object.assign(
                {
                  size: '210mm 297mm',
                  margin: '1cm .6cm .3cm .6cm'
                },
                media
              )
            )
            break
          }
          default: {
            this.$refs.printArea.doPrint(
              Object.assign(
                {
                  size: '241mm 139.7mm',
                  margin: '1.5cm 1.2cm .5cm .5cm'
                },
                media
              )
            )
          }
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.numStyle {
  font-size: 16px;
  font-weight: 700;
}
.excel-container {
  width: 100%;
  & > .excel-item {
    width: 100%;
    & > .excel-group {
      width: 100%;
      position: relative;
      &:not(:last-child) {
        &::after {
          content: '';
          width: calc(100% + 10pt);
          height: 2pt;
          border-bottom: dashed 1pt #000;
          position: absolute;
          bottom: -10pt;
          left: -5pt;
        }
      }
      & > .excel-header {
        width: 100%;
        & > .mainTitle {
          color: #000;
          font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
          text-align: center;
          & > .orgName {
            font-size: 16pt;
            font-weight: 500;
          }
          & > .textName {
            font-size: 14pt;
            font-weight: 500;
          }
        }
        & > .subTitle {
          height: 16pt;
          line-height: 16pt;
          padding: 0 100pt 0 20pt;
          color: #000;
          font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
          font-size: 9pt;
          font-weight: 400;
          display: flex;
          justify-content: space-between;
        }
      }
      & > .excel-content {
        width: 100%;
        display: flex;
        & > .table {
          width: 100%;
        }
      }
      & > .excel-footer {
        width: 100%;
        height: 22pt;
        line-height: 22pt;
        padding: 0 30pt;
        color: #000;
        font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
        font-size: 9pt;
        font-weight: 400;
        display: flex;
        justify-content: space-between;
        & > .div {
          width: 10%;
          flex: 1 0 auto;
        }
      }
    }
  }
}
</style>
