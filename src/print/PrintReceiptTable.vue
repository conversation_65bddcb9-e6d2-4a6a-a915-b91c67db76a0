<template>
  <print-area ref="printArea">
    <section
      class="excel-container"
      v-for="(item, index) in excelData"
      :key="index"
    >
      <div
        class="excel-item"
      >
        <div class="excel-group">
          <div class="excel-header">
            <div class="mainTitle">
              <div class="orgName">物资收发存报表</div>
              <div class="textName">
                <span style="letter-spacing: 1.5pt; width: 200px;">库房：{{item.storehouseName}}</span>
                <span style="letter-spacing: 1.5pt; width: 200px;"></span>
              </div>
            </div>
          </div>
          <div class="excel-content">
            <a-table
              class="table table-th-2 table-td-3"
              :columns="columns"
              :data-source="item.maplist"
              :pagination="false"
              bordered
              rowKey="id"
            />
          </div>
          <template>
            <div class="excel-footer">
              <div>
                <span style="margin: 0 3pt">制表：</span>
                {{ USER_NAME }}
              </div>
              <div/>
              <div/>
              <div/>
              <div/>
              <div/>
              <div>
                <span style="margin: 0 3pt">制单时间：</span>
                {{ moment().format('YYYY-MM-DD') }}
              </div>
            </div>
          </template>
        </div>
      </div>
    </section>
  </print-area>
</template>

<script>
import { PrintArea } from '@/components'
import '@/components/print.less'

// 定制化
import Vue from 'vue'
import Utils from './utils'
import { PERSON_NAME } from '@/store/mutation-types'
import moment from 'moment'
const USER_NAME = Vue.ls.get(PERSON_NAME)

export default {
  components: {
    PrintArea
  },
  data () {
    return {
      USER_NAME,
      moment,
      columns: [
        {
          title: '物资代码',
          dataIndex: 'materialNum',
          width: '10%'
        },
        {
          title: '物资名称',
          dataIndex: 'materialName',
          width: '13%'
        },
        {
          title: '规格型号',
          dataIndex: 'jModel',
          width: '15%'
        },
        {
          title: '单位',
          dataIndex: 'orderUnit',
          width: '6%'
        },
        {
          title: '期初库存',
          children: [
            {
              title: '数量',
              dataIndex: 'qckcQu',
              width: '7%'
            },
            {
              title: '金额(元)',
              dataIndex: 'qckc',
              width: '7%'
            }
          ]
        },
        {
          title: '本月收入',
          children: [
            {
              title: '数量',
              dataIndex: 'srhjQu',
              width: '7%'
            },
            {
              title: '金额(元)',
              dataIndex: 'srhj',
              width: '7%'
            }
          ]
        },
        {
          title: '本月发出',
          children: [
            {
              title: '数量',
              dataIndex: 'fchjQu',
              width: '7%'
            },
            {
              title: '金额(元)',
              dataIndex: 'fchj',
              width: '7%'
            }
          ]
        },
        {
          title: '期末库存',
          children: [
            {
              title: '数量',
              dataIndex: 'qmkcQu',
              width: '7%'
            },
            {
              title: '金额(元)',
              dataIndex: 'qmkc',
              width: '7%'
            }
          ]
        }
      ],
      // 打印配置
      excelData: [],
      printDate: ''
    }
  },
  computed: {
    object () {
      const excelData = {}
      if (Utils.isObject(this.excelData)) {
        for (const title in this.excelData) {
          Object.assign(excelData, {
            [title]: this.toSort(this.excelData[title])
          })
        }
        return excelData
      }
      if (Utils.isArray(this.excelData)) {
        Object.assign(excelData, {
          物资货架信息: this.toSort(this.excelData)
        })
      }
      return excelData
    }
  },
  methods: {
    toSort (data) {
      const cache = {}
      console.log(data)
      for (const item of data) {
        item.oldlist = item.maplist || []
        item.maplist = []
        if (!cache[item.rfqNum]) {
          cache[item.rfqNum] = {
            index: 1,
            temp: 1
          }
        }
        let group
        if (item.type === 2) {
          group = item.oldlist.length >= 16 ? 16 : item.oldlist.length
        } else {
          group = item.oldlist.length
        }
        for (let i = 0; i < group; i++) {
          item.maplist.push({
            ...(item.oldlist[i] || {}),
            index: item.oldlist[i] ? cache[item.rfqNum].index++ : 'temp_' + cache[item.rfqNum].temp++
          })
        }
      }
      return data
    },
    toHandle (data) {
      let index = 0
      const list = []
      const maps = {}
      const cache = {}
      const array = data.maplist || []

      // 按采购计划分类
      for (const item of array) {
        if (!maps[item.rfqNum]) {
          maps[item.rfqNum] = {
            rfqNum: item.rfqNum,
            orgName: data.orgName,
            createDate: data.createDate,
            createName: data.createName,
            maplist: []
          }
        }
        maps[item.rfqNum].maplist.push({ ...item })
      }

      // 按物资数量排版
      for (const key in maps) {
        const item = maps[key]
        const array = item.maplist
        const length = array.length
        if (length <= 16) {
          continue
        }

        let count = length > 16 ? length : 16

        while (count % 16) {
          count++
        }

        // 拆分页面
        let xh = 0
        let temp = 0
        for (let i = 0; i < count; i++) {
          const floor = Math.floor(i / 16)
          const code = key + '-' + floor
          if (cache[code] === undefined) {
            cache[code] = index++
          }
          if (!list[cache[code]]) {
            list[cache[code]] = {
              rfqNum: item.rfqNum,
              orgName: item.orgName,
              createDate: item.createDate,
              createName: item.createName,
              maplist: []
            }
          }
          list[cache[code]].maplist.push({
            ...(item.maplist[i] || {}),
            index: item.maplist[i] ? ++xh : 'temp_' + ++temp
          })
        }
      }

      return list
    },
    toPrint (data = [], media = {}) {
      this.excelData = data
      this.$nextTick(() => {
        this.$refs.printArea.doPrint(
          Object.assign(
            {
              size: '297mm 210mm',
              margin: '1cm .5cm .5cm .5cm'
            },
            media
          )
        )
      })
    }
  }
}
</script>

<style lang="less" scoped>
.excel-container {
  width: 100%;
  & > .excel-item {
    width: 100%;
    & > .excel-group {
      width: 100%;
      position: relative;
      &:not(:last-child) {
        &::after {
          content: '';
          width: calc(100% + 10pt);
          height: 2pt;
          border-bottom: dashed 1pt #000;
          position: absolute;
          bottom: -10pt;
          left: -5pt;
        }
      }
      & > .excel-header {
        width: 100%;
        margin-bottom: 20px;
        & > .mainTitle {
          color: #000;
          font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
          text-align: center;
          & > .orgName {
            font-size: 16pt;
            font-weight: 500;
            margin-bottom: 20px;
          }
          & > .textName {
            font-size: 9pt;
            font-weight: 400;
            display: flex;
            justify-content: space-between;
          }
        }
        & > .subTitle {
          height: 16pt;
          line-height: 16pt;
          padding: 0 100pt 0 20pt;
          color: #000;
          font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
          font-size: 9pt;
          font-weight: 400;
          display: flex;
          justify-content: space-between;
        }
        & > .subTitle1 {
          height: 5pt;
          line-height: 5pt;
          padding: 0 100pt 0 20pt;
          color: #000;
          display: flex;
          justify-content: space-between;
        }
      }
      & > .excel-content {
        width: 100%;
        display: flex;
        & > .table {
          width: 100%;
        }
      }
      & > .excel-footer {
        width: 100%;
        height: 20pt;
        line-height: 20pt;
        padding: 0 30pt;
        color: #000;
        font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
        font-size: 9pt;
        font-weight: 400;
        display: flex;
        justify-content: space-between;
        & > .div {
          width: 10%;
          flex: 1 0 auto;
        }
      }
    }
  }
}
 ::v-deep {
  th.ant-descriptions-item-label.ant-descriptions-item-colon {
      border: 1px solid #000;
      border-top: none;
  }
  .ant-descriptions-item-content {
      border: 1px solid #000;
      border-top: none;
      &:last-child {
        border-right: 1px solid #000;
        border-top: none;
      }
  }
}
</style>
