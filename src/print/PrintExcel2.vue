<template>
  <print-area ref="printArea">
    <section class="excel-container">
      <div
        class="excel-item"
        v-for="(item, index) of list"
        break-avoid
        break-after
        :key="index"
      >
        <div class="excel-group">
          <div class="excel-header">
            <div class="mainTitle">{{ item.orgName }}办公用品领用单 ({{ item.prlineNum }})</div>
            <div class="subTitle">
              <div>
                <span style="margin: 0 3pt">领用部门:</span>
                {{ item.deptName }}
              </div>
              <div>
                <span style="margin: 0 3pt">领用日期:</span>
                {{ item.createDate }}
              </div>
            </div>
          </div>
          <div class="excel-content">
            <a-table
              class="table table-th-2 table-td-2"
              :columns="columns"
              :data-source="item.maplist"
              :pagination="false"
              rowKey="index"
              bordered
            />
            <div class="description">第一联 · 统计记账</div>
          </div>
          <div class="excel-footer">
            <div class="receiveName">领用人签字:</div>
          </div>
        </div>
        <div class="excel-group">
          <div class="excel-header">
            <div class="mainTitle">{{ item.orgName }}办公用品领用单 ({{ item.prlineNum }})</div>
            <div class="subTitle">
              <div>领用部门: {{ item.deptName }}</div>
              <div>领用日期: {{ item.createDate }}</div>
            </div>
          </div>
          <div class="excel-content">
            <a-table
              class="table table-th-2 table-td-2"
              :columns="columns"
              :data-source="item.maplist"
              :pagination="false"
              rowKey="index"
              bordered
            />
            <div class="description">第二联 · 领用人备查</div>
          </div>
          <div class="excel-footer">
            <div class="receiveName">领用人签字:</div>
          </div>
        </div>
        <div class="excel-group">
          <div class="excel-header">
            <div class="mainTitle">{{ item.orgName }}办公用品领用单 ({{ item.prlineNum }})</div>
            <div class="subTitle">
              <div class="deptName">领用部门: {{ item.deptName }}</div>
              <div class="createDate">领用日期: {{ item.createDate }}</div>
            </div>
          </div>
          <div class="excel-content">
            <a-table
              class="table table-th-2 table-td-2"
              :columns="columns"
              :data-source="item.maplist"
              :pagination="false"
              rowKey="index"
              bordered
            />
            <div class="description">第三联 · 仓库记账</div>
          </div>
          <div class="excel-footer">
            <div>
              <span style="margin: 0 3pt">领用人签字:</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  </print-area>
</template>

<script>
import { PrintArea } from '@/components'
import '@/components/print.less'

export default {
  components: {
    PrintArea
  },
  data () {
    return {
      // 表格配置
      columns: [
        {
          title: '用品编号',
          dataIndex: 'materialNum',
          customRender: (text, row, index) => {
            if (index === 5) {
              return {
                attrs: {
                  align: 'right',
                  colSpan: 5
                },
                children: <div style="text-align: right; padding-right: 5pt;">{text}</div>
              }
            }
            return {
              children: text
            }
          },
          width: '12%',
          align: 'center'
        },
        {
          title: '名称',
          dataIndex: 'materialName',
          customRender: (text, row, index) => {
            if (index === 5) {
              return {
                attrs: {
                  colSpan: 0
                },
                children: text
              }
            }
            return {
              children: text
            }
          },
          width: '25%',
          align: 'center'
        },
        {
          title: '单位',
          dataIndex: 'orderUnit',
          customRender: (text, row, index) => {
            if (index === 5) {
              return {
                attrs: {
                  colSpan: 0
                },
                children: text
              }
            }
            return {
              children: text
            }
          },
          width: '8%',
          align: 'center'
        },
        {
          title: '数量',
          dataIndex: 'actReqNum',
          customRender: (text, row, index) => {
            if (index === 5) {
              return {
                attrs: {
                  colSpan: 0
                },
                children: text
              }
            }
            return {
              children: text
            }
          },
          width: '8%',
          align: 'center'
        },
        {
          title: '单价',
          dataIndex: 'unitCost',
          customRender: (text, row, index) => {
            if (index === 5) {
              return {
                attrs: {
                  colSpan: 0
                },
                children: text
              }
            }
            return {
              children: text
            }
          },
          width: '10%',
          align: 'center'
        },
        {
          title: '金额',
          dataIndex: 'lineCost',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '12%',
          align: 'center'
        },
        {
          title: '备注',
          dataIndex: 'remarks',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '25%',
          align: 'center'
        }
      ],
      // 打印配置
      excelData: '',
      printDate: ''
    }
  },
  computed: {
    list () {
      return this.toSort(this.excelData)
    }
  },
  methods: {
    toSort (data) {
      const group = 5
      const cache = {}
      for (const item of data) {
        item.oldlist = item.maplist || []
        item.totalCurrency = 0
        item.maplist = []
        if (!cache[item.prlineNum]) {
          cache[item.prlineNum] = {
            index: 1,
            temp: 1
          }
        }
        for (let i = 0; i < group; i++) {
          item.maplist.push({
            ...(item.oldlist ? item.oldlist[i] : {}),
            index: item.oldlist ? cache[item.prlineNum].index++ : 'temp_' + cache[item.prlineNum].temp++
          })

          item.totalCurrency += (item.maplist[i] || {}).lineCost || 0
          item.totalCurrency = +item.totalCurrency.toFixed(8) || 0

          // 新增合计功能
          if (i % 5 === 4) {
            item.maplist.push({
              materialNum: '合 计:',
              lineCost: item.totalCurrency,
              index: 'total_index'
            })
          }
        }
      }
      return data
    },
    toHandle (data) {
      let index = 0
      const list = []
      const maps = {}
      const cache = {}
      const array = data.maplist || []

      // 按采购计划分类
      for (const item of array) {
        if (!maps[item.prlineNum]) {
          maps[item.prlineNum] = {
            prlineNum: item.prlineNum,
            lotNum: data.lotNum,
            orgName: data.orgName,
            deptName: data.deptName,
            createDate: data.createDate,
            createName: data.createName,
            totalCurrency: data.totalCurrency,
            maplist: []
          }
        }
        maps[item.prlineNum].maplist.push({ ...item })
      }

      // 按物资数量排版
      for (const key in maps) {
        const item = maps[key]
        const array = item.maplist
        const length = array.length

        let count = length > 5 ? length : 5

        while (count % 5) {
          count++
        }

        // 拆分页面
        let xh = 0
        let temp = 0
        for (let i = 0; i < count; i++) {
          const floor = Math.floor(i / 5)
          const code = key + '-' + floor
          if (cache[code] === undefined) {
            cache[code] = index++
          }
          if (!list[cache[code]]) {
            list[cache[code]] = {
              prlineNum: item.prlineNum,
              lotNum: data.lotNum,
              orgName: item.orgName,
              deptName: item.deptName,
              createDate: item.createDate,
              createName: item.createName,
              totalCurrency: 0,
              maplist: []
            }
          }
          list[cache[code]].maplist.push({
            ...(item.maplist[i] || {}),
            index: item.maplist[i] ? ++xh : 'temp_' + ++temp
          })

          list[cache[code]].totalCurrency += (item.maplist[i] || {}).lineCost || 0
          list[cache[code]].totalCurrency = +list[cache[code]].totalCurrency.toFixed(8) || 0

          // 新增合计功能
          if (i % 5 === 4) {
            list[cache[code]].maplist.push({
              materialNum: '合 计:',
              lineCost: list[cache[code]].totalCurrency,
              index: ++xh
            })
          }
        }
      }
      return list
    },
    toFormat (date, format) {
      function itType (val) {
        return Object.prototype.toString.call(val).replace(/^\[[^\s\]]+\s*([^\s\]]+)]$/, '$1')
      }
      function isDate (date) {
        if (itType(date) === 'Date') {
          return true
        }
        return false
      }
      function isString (str) {
        if (itType(str) === 'String') {
          return true
        }
        return false
      }
      if (isDate(date, true)) {
        const handle = function (i) {
          return (i < 10 ? '0' : '') + i
        }
        if (!isString(format, true)) {
          format = 'yyyy/MM/dd HH:mm:ss'
        }
        return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (f) {
          switch (f) {
            case 'yyyy': {
              return handle(date.getFullYear())
            }
            case 'MM': {
              return handle(date.getMonth() + 1)
            }
            case 'dd': {
              return handle(date.getDate())
            }
            case 'HH': {
              return handle(date.getHours())
            }
            case 'mm': {
              return handle(date.getMinutes())
            }
            case 'ss': {
              return handle(date.getSeconds())
            }
          }
        })
      }
      return ''
    },
    toPrint (data = [], media = {}) {
      this.excelData = data
      this.printDate = this.toFormat(new Date(), 'yyyy年MM月dd日')
      this.$nextTick(() => {
        this.$refs.printArea.doPrint(
          Object.assign(
            {
              size: 'A4 portrait',
              margin: '1.5cm 1.2cm .3cm'
            },
            media
          )
        )
      })
    }
  }
}
</script>

<style lang="less" scoped>
.excel-container {
  width: 100%;
  & > .excel-item {
    width: 100%;
    & > .excel-group {
      width: 100%;
      margin-bottom: 30pt;
      position: relative;
      &:not(:last-child) {
        &::after {
          content: '';
          width: calc(100% + 10pt);
          height: 2pt;
          border-bottom: dashed 1pt #000;
          position: absolute;
          bottom: -10pt;
          left: -5pt;
        }
      }
      & > .excel-header {
        width: calc(100% - 26pt);
        & > .mainTitle {
          height: 28pt;
          line-height: 28pt;
          color: #000;
          font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
          font-size: 16pt;
          font-weight: 500;
          text-align: center;
        }
        & > .subTitle {
          height: 16pt;
          line-height: 16pt;
          padding: 0 15pt 0 15pt;
          color: #000;
          font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
          font-size: 9pt;
          font-weight: 400;
          display: flex;
          justify-content: space-between;
        }
      }
      & > .excel-content {
        display: flex;
        & > .table {
          width: calc(100% - 26pt);
        }
        & > .description {
          width: 24pt;
          color: #000;
          line-height: 24pt;
          font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
          font-size: 9pt;
          font-weight: 400;
          text-align: center;
          writing-mode: vertical-lr;
        }
      }
      & > .excel-footer {
        width: calc(100% - 26pt);
        height: 22pt;
        line-height: 22pt;
        padding: 0 100pt 0 100pt;
        color: #000;
        font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
        font-size: 9pt;
        font-weight: 400;
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}
</style>
