
function itType (val) {
  return Object.prototype.toString.call(val).replace(/^\[[^\s\]]+\s*([^\s\]]+)]$/, '$1')
}

function isArray (arr) {
  if (itType(arr) === 'Array') {
    return true
  }
  return false
}

function isObject (obj) {
  if (itType(obj) === 'Object') {
    return true
  }
  return false
}

function isString (str) {
  if (itType(str) === 'String') {
    return true
  }
  return false
}

function isNumber (num) {
  if (itType(num) === 'Number') {
    return true
  }
  return false
}

function isRegExp (reg) {
  if (itType(reg) === 'RegExp') {
    return true
  }
  return false
}

function isBoolean (val) {
  if (itType(val) === 'Boolean') {
    return true
  }
  return false
}

function isFunction (num) {
  if (itType(num) === 'Function') {
    return true
  }
  return false
}

function isNotEmptyArray (arr) {
  if (isArray(arr)) {
    return arr.length > 0
  }
  return false
}

function isNotEmptyObject (obj) {
  if (isObject(obj)) {
    return Object.keys(obj).length > 0
  }
  return false
}

function isNotEmptyString (str) {
  if (isString(str)) {
    return !!str.trim()
  }
  return false
}

function isNotFiniteNumber (num) {
  if (isNumber(num)) {
    return !isFinite(num)
  }
  return false
}

function isFiniteNumber (num) {
  if (isNumber(num)) {
    return isFinite(num)
  }
  return false
}

function isEmptyString (str) {
  if (isString(str)) {
    return !str.trim()
  }
  return false
}

function isEmptyObject (obj) {
  if (isObject(obj)) {
    return Object.keys(obj).length === 0
  }
  return false
}

function isEmptyArray (arr) {
  if (isArray(arr)) {
    return arr.length === 0
  }
  return false
}

function toOwnProperty (own) {
  if (isObject(own)) {
    return Object.keys(own)
  }
  if (isArray(own)) {
    return own.keys()
  }
  return []
}

function toDeepClone (source) {
  function clone (i, o) {
    for (const key of toOwnProperty(o)) {
      const iIsArray = isArray(i[key])
      const oIsArray = isArray(o[key])
      const iIsObject = isObject(i[key])
      const oIsObject = isObject(o[key])
      const ioIsArray = iIsArray && oIsArray
      const ioIsObject = iIsObject && oIsObject
      if (ioIsArray || ioIsObject) {
        clone(i[key], o[key])
      } else if (oIsArray) {
        if (i[key] === undefined) {
          i[key] = []
          clone(i[key], o[key])
        }
      } else if (oIsObject) {
        if (i[key] === undefined) {
          i[key] = {}
          clone(i[key], o[key])
        }
      } else {
        i[key] = o[key]
      }
    }
  }
  let inData
  let inArgs
  if (!isObject(source) && !isArray(source)) {
    return source
  } else if (isObject(source)) {
    inData = {}
    inArgs = [...arguments]
  } else if (isArray(source)) {
    inData = []
    inArgs = [...arguments]
  }
  for (const onData of inArgs) {
    const iIsArray = isArray(inData)
    const oIsArray = isArray(onData)
    const iIsObject = isObject(inData)
    const oIsObject = isObject(onData)
    const ioIsArray = iIsArray && oIsArray
    const ioIsObject = iIsObject && oIsObject
    if (ioIsArray) {
      clone(inData, onData)
    }
    if (ioIsObject) {
      clone(inData, onData)
    }
  }
  return inData
}

export {
  itType,
  isArray,
  isObject,
  isString,
  isNumber,
  isRegExp,
  isBoolean,
  isFunction,
  isEmptyArray,
  isEmptyObject,
  isEmptyString,
  isNotEmptyArray,
  isNotEmptyObject,
  isNotEmptyString,
  isNotFiniteNumber,
  isFiniteNumber,
  toOwnProperty,
  toDeepClone
}

export default {
  itType,
  isArray,
  isObject,
  isString,
  isNumber,
  isRegExp,
  isBoolean,
  isFunction,
  isEmptyArray,
  isEmptyObject,
  isEmptyString,
  isNotEmptyArray,
  isNotEmptyObject,
  isNotEmptyString,
  isNotFiniteNumber,
  isFiniteNumber,
  toOwnProperty,
  toDeepClone
}
