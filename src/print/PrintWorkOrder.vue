<template>
  <print-area ref="printArea">
    <section class="excel-container">
      <div class="sheet" v-if="tabNum === 1">
        <div
          class="table"
          v-for="(item, index) in excelData"
          :key="index">
          <table border="1" cellspacing="0" cellpadding="0">
            <col width="60px" >
            <col width="60px" >
            <col width="120px" >
            <col width="60px" >
            <col width="60px" >
            <col width="120px" >
            <col width="120px" >
            <col width="120px" >
            <col width="60px" >
            <col width="60px" >
            <col width="120px" >
            <col width="60px" >
            <col width="60px" >
            <col width="120px" >
            <col width="120px" >
            <col width="120px" >
            <h2 class="top" style="font-size: 24px">{{ item.formName }}</h2>
            <tr class="top">
              <th class="topTitle" colspan="4">维修编号：{{ item.workOrderNo }}</th>
              <th class="topTitle" colspan="9"/>
              <th class="topTitle" colspan="3">带款提车，谢谢合作！</th>
            </tr>
            <tr class="midtitle">
              <td colspan="3">结算单位</td>
              <td colspan="10">{{ item.sydw }}</td>
              <td colspan="1">结算日期</td>
              <td colspan="2">{{ item.endTime }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="3">车票号码</td>
              <td colspan="4">{{ item.assetSbNum }}</td>
              <td colspan="2">汽车类型</td>
              <td colspan="3">{{ item.fuelType }}</td>
              <td colspan="3">汽车品牌</td>
              <td colspan="2">{{ item.jModel }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="3">检修概况</td>
              <td colspan="13"/>
            </tr>
            <tr class="midtitle betweenClear">
              <td colspan="16" style="text-align: left">维修项目</td>
            </tr>
            <tr class="midtitle">
              <td colspan="1" style="width: 60px">序号</td>
              <td colspan="4">项目名称</td>
              <td colspan="1">数量</td>
              <td colspan="1">工时</td>
              <td colspan="1">金额</td>
              <td colspan="1">序号</td>
              <td colspan="4">项目名称</td>
              <td colspan="1">数量</td>
              <td colspan="1">工时</td>
              <td colspan="1">金额</td>
            </tr>
            <tr v-for="(info, ins) in item.excelArray" :key="ins" class="midtitle">
              <td colspan="1" style="width: 60px">{{ info[0].id }}</td>
              <td colspan="4">{{ info[0].assetSbItemName }}</td>
              <td colspan="1">{{ info[0].quantity }}</td>
              <td colspan="1">{{ info[0].manHour }}</td>
              <td colspan="1">{{ info[0].manHour * 10 }}</td>
              <td colspan="1">{{ info[1] ? info[1].id : '' }}</td>
              <td colspan="4">{{ info[1] ? info[1].assetSbItemName : '' }}</td>
              <td colspan="1">{{ info[1] ? info[1].quantity : '' }}</td>
              <td colspan="1">{{ info[1] ? info[1].manHour : '' }}</td>
              <td colspan="1">{{ info[1] ? info[1].manHour * 10 : '' }}</td>
            </tr>
            <tr class="midtitle betweenClear">
              <td colspan="16" style="text-align: right; padding-right: 5px;">工时费合计：{{ item.manHourCost }}元</td>
            </tr>
            <div v-if="item.materialArr.length > 0">
              <tr class="midtitle betweenClear">
                <td colspan="16" style="text-align: left">领料项目</td>
              </tr>
              <tr class="midtitle">
                <td colspan="1">序号</td>
                <td colspan="3">货品名称</td>
                <td colspan="1">单位</td>
                <td colspan="1">数量</td>
                <td colspan="1">单价</td>
                <td colspan="1">金额</td>
                <td colspan="1">序号</td>
                <td colspan="3">货品名称</td>
                <td colspan="1">单位</td>
                <td colspan="1">数量</td>
                <td colspan="1">单价</td>
                <td colspan="1">金额</td>
              </tr>
              <tr v-for="(info, ins) in item.materialArr" :key="ins" class="midtitle">
                <td colspan="1">{{ info[0].id }}</td>
                <td colspan="3">{{ info[0].materialName }}</td>
                <td colspan="1">{{ info[0].orderUnit }}</td>
                <td colspan="1">{{ info[0].quantity }}</td>
                <td colspan="1">{{ info[0].unitCost }}</td>
                <td colspan="1">{{ info[0].lineCost }}</td>
                <td colspan="1">{{ info[1] ? info[1].id : '' }}</td>
                <td colspan="3">{{ info[1] ? info[1].materialName : '' }}</td>
                <td colspan="1">{{ info[1] ? info[1].orderUnit : '' }}</td>
                <td colspan="1">{{ info[1] ? info[1].quantity : '' }}</td>
                <td colspan="1">{{ info[1] ? info[1].unitCost : '' }}</td>
                <td colspan="1">{{ info[1] ? info[1].lineCost : '' }}</td>
              </tr>
              <tr class="midtitle betweenClear">
                <td colspan="16" style="text-align: right; padding-right: 5px;">材料费合计：{{ item.materialCost }}元</td>
              </tr>
            </div>
            <tr class="midtitle betweenClear">
              <td colspan="16" style="text-align: left;">费用合计</td>
            </tr>
            <tr class="midtitle">
              <td colspan="2">工时费用</td>
              <td colspan="3">{{ item.manHourCost }}元</td>
              <td colspan="1">材料费用</td>
              <td colspan="2">{{ item.materialCost }}元</td>
              <td colspan="2">外加费用</td>
              <td colspan="3">{{ item.outsourceCost }}元</td>
              <td colspan="1">油漆费用</td>
              <td colspan="2">0.00元</td>
            </tr>
            <tr class="midtitle">
              <td colspan="2">代办费用</td>
              <td colspan="3">0.00元</td>
              <td colspan="1">管理费用</td>
              <td colspan="2">0.00元</td>
              <td colspan="2">辅助费用</td>
              <td colspan="3">0.00元</td>
              <td colspan="1">税金</td>
              <td colspan="2">0.00元</td>
            </tr>
            <tr class="midtitle">
              <td colspan="2">维修金额</td>
              <td colspan="11">{{ item.totalCost }}</td>
              <td colspan="1">优惠金额</td>
              <td colspan="2">0.00元</td>
            </tr>
            <tr class="midtitle">
              <td colspan="2">车票号码</td>
              <td colspan="14" style="text-align: right; padding-right: 5px;">{{ item.total }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="2">备注</td>
              <td colspan="14">{{ item.remark }}</td>
            </tr>
            <tr style="height: 40px;">
              <td class="bottomTitle" colspan="5"/>
              <td class="bottomTitle" colspan="5"/>
              <td class="bottomTitle" style="text-align: right; padding-right: 5px;" colspan="6">结算单位核对无误后，请签字_______________</td>
            </tr>
            <tr style="height: 30px;">
              <td class="bottomTitle" colspan="6">开户行：{{ item.handler }}</td>
              <td class="bottomTitle" colspan="5">账号：{{ item.approNames }}</td>
              <td class="bottomTitle" colspan="5">电话：{{ item.storeman }}</td>
            </tr>
            <tr style="height: 30px;">
              <td class="bottomTitle" colspan="6">地址：{{ item.handler }}</td>
              <td class="bottomTitle" colspan="5">税号：{{ item.approNames }}</td>
              <td class="bottomTitle" colspan="5">传真：{{ item.storeman }}</td>
            </tr>
          </table>
        </div>
      </div>
    </section>
  </print-area>
</template>

<script>
import { PrintArea } from '@/components'
import '@/components/print.less'
import EvaluationSheet from '@/views/material/store/component/evaluationSheet.vue'

// 定制化
import Vue from 'vue'
import Utils from './utils'
import { ORG_ID, PERSON_NAME } from '@/store/mutation-types'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const USER_NAME = Vue.ls.get(PERSON_NAME)

export default {
  name: 'PrintExcel4',
  components: {
    PrintArea,
    EvaluationSheet
  },
  data () {
    return {
      USER_ORG_ID,
      USER_NAME,
      // 打印配置
      excelData: '',
      excelDataSe: {},
      infoTitle: '',
      printDate: '',
      tabNum: 1,
      excelArray: [],
      materialArr: []
    }
  },
  computed: {
    object () {
      const excelData = {}
      if (Utils.isObject(this.excelData)) {
        for (const title in this.excelData) {
          Object.assign(excelData, {
            [title]: this.toSort(this.excelData[title])
          })
        }
        return excelData
      }
      return excelData
    }
  },
  methods: {
    toSort (data) {
      const group = 10
      const cache = {}
      for (const item of data) {
        item.oldlist = item.maplist || []
        item.totalCurrency = 0
        item.maplist = []
        if (!cache[item.polineNum]) {
          cache[item.polineNum] = {
            index: 1,
            temp: 1,
            total: 0,
            tax: 0,
            cuslineCost: 0
          }
        }
        for (let i = 0; i < group; i++) {
          item.maplist.push({
            ...(item.oldlist[i] || {}),
            index: item.oldlist[i] ? cache[item.polineNum].index++ : 'temp_' + cache[item.polineNum].temp++
          })
        }
      }
      return data
    },
    toHandle (data) {
      let index = 0
      const list = []
      const maps = {}
      const cache = {}
      const array = data.maplist || []

      // 按采购计划分类
      for (const item of array) {
        if (!maps[item.polineNum]) {
          maps[item.polineNum] = {
            polineNum: item.polineNum,
            lotNum: data.lotNum,
            orgName: data.orgName,
            deptName: data.deptName,
            createDate: data.createDate,
            printDate: data.printDate,
            createName: data.createName,
            remarkTS: item.remarkTS,
            totalCurrency: data.totalCurrency,
            maplist: [],
            total: 0,
            cuslineCost: 0
          }
        }
        maps[item.polineNum].maplist.push({ ...item })
      }

      // 按物资数量排版
      for (const key in maps) {
        const item = maps[key]
        const array = item.maplist
        const length = array.length

        let count = length > 10

        while (count % (10)) {
          count++
        }

        // 拆分页面
        let xh = 0
        let temp = 0
        for (let i = 0; i < count; i++) {
          const floor = Math.floor(i / (10))
          const code = key + '-' + floor
          if (cache[code] === undefined) {
            cache[code] = index++
          }
          if (!list[cache[code]]) {
            list[cache[code]] = {
              polineNum: item.polineNum,
              lotNum: data.lotNum,
              orgName: item.orgName,
              deptName: item.deptName,
              createDate: item.createDate,
              createName: item.createName,
              remarkTS: item.remarkTS,
              totalCurrency: 0,
              maplist: []
            }
          }
          list[cache[code]].maplist.push({
            ...(item.maplist[i] || {}),
            index: item.maplist[i] ? ++xh : 'temp_' + ++temp
          })
        }
      }
      return list
    },
    toFormat (date, format) {
      function itType (val) {
        return Object.prototype.toString.call(val).replace(/^\[[^\s\]]+\s*([^\s\]]+)]$/, '$1')
      }
      function isDate (date) {
        if (itType(date) === 'Date') {
          return true
        }
        return false
      }
      function isString (str) {
        if (itType(str) === 'String') {
          return true
        }
        return false
      }
      if (isDate(date, true)) {
        const handle = function (i) {
          return (i < 10 ? '0' : '') + i
        }
        if (!isString(format, true)) {
          format = 'yyyy/MM/dd HH:mm:ss'
        }
        return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (f) {
          switch (f) {
            case 'yyyy': {
              return handle(date.getFullYear())
            }
            case 'MM': {
              return handle(date.getMonth() + 1)
            }
            case 'dd': {
              return handle(date.getDate())
            }
            case 'HH': {
              return handle(date.getHours())
            }
            case 'mm': {
              return handle(date.getMinutes())
            }
            case 'ss': {
              return handle(date.getSeconds())
            }
          }
        })
      }
      return ''
    },
    toPrint (data = [], media = {}, tabNum = 0) {
      this.excelData = data
      this.printDate = this.toFormat(new Date(), 'yyyy年MM月dd日')
      this.tabNum = tabNum
      if (tabNum === 1) {
        for (let i = 0; i < this.excelData.length; i++) {
          const length = Math.ceil(this.excelData[i].maplist.length / 2)
          const materialLength = Math.ceil(this.excelData[i].materialList.length / 2)
          const excelArray = []
          const materialArr = []
          for (let i = 0; i < length; i++) {
            excelArray.push([])
          }
          for (let i = 0; i < materialLength; i++) {
            materialArr.push([])
          }
          this.excelData[i].maplist.forEach((item, index) => {
            item.id = index + 1
            excelArray[Math.floor(+index / 2)].push(item)
          })
          this.excelData[i].materialList.forEach((item, index) => {
            item.id = index + 1
            materialArr[Math.floor(+index / 2)].push(item)
          })
          this.excelData[i].excelArray = excelArray
          this.excelData[i].materialArr = materialArr
        }
      }
      this.$nextTick(() => {
        this.$refs.printArea.doPrint(
          Object.assign(
            {
              size: '280mm 230.7mm',
              margin: '2cm 1.2cm .5cm .5cm'
            },
            media
          )
        )
      })
    }
  }
}
</script>

<style lang="less" scoped>
.sheet {
  width: 100%;
  font-size: 12px;
  margin: auto auto;
  text-align: center;
  font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
  overflow: auto;
  .topTitle {
    border-top: 1px solid transparent;
    border-right: 1px solid transparent;
    border-left: 1px solid transparent;
    font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
    min-height: 30px;
  }
  .bottomTitle {
    border-bottom: 1px solid transparent;
    border-right: 1px solid transparent;
    border-left: 1px solid transparent;
    font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
    min-height: 30px;
  }
  .top {
    text-align: center;
    font-size: 18px;
    font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
  }
  .midtitle {
    height: 30px;
    font-size: 15px;
    text-align: center;
    font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
  }
  .midtitletow {
    height: 30px;
    font-size: 22px;
    text-align: center;
    font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
  }
  .table {
    height:  589.7pt;
  }
  .betweenClear {
    border-right: 1px solid transparent;
    border-left: 1px solid transparent;
  }
}
</style>
