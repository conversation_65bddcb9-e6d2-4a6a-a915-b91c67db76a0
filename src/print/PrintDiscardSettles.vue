<template>
  <print-area ref="printArea">
    <section class="excel-container">
      <div class="sheet">
        <div
          class="table"
          v-for="(item, index) in excelData"
          :key="index">
          <table border="1" cellspacing="0" cellpadding="0" v-if="!item.isFqwz">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <h2 class="top" style="font-size: 22px; text-align: center;">{{ item.title }}</h2>
            <div v-if="item.isSk">
              <tr class="midtitle">
                <td colspan="1">收款单位</td>
                <td colspan="3">{{ item.companyName }}</td>
                <td colspan="2">仓库保管员签字</td>
                <td colspan="3"/>
                <td colspan="2">报废物资管理员签字</td>
                <td colspan="2"/>
              </tr>
              <tr class="midtitle">
                <td colspan="1">付款单位</td>
                <td colspan="3">{{ item.czdwName }}</td>
                <td colspan="2">结算人员签字</td>
                <td colspan="7"/>
              </tr>
            </div>
            <div v-else>
              <tr class="midtitle">
                <td colspan="1">收款单位</td>
                <td colspan="3">{{ item.czdwName }}</td>
                <td colspan="2">结算人员签字</td>
                <td colspan="7"/>
              </tr>
              <tr class="midtitle">
                <td colspan="1">付款单位</td>
                <td colspan="3">{{ item.companyName }}</td>
                <td colspan="2">仓库保管员签字</td>
                <td colspan="3"/>
                <td colspan="2">报废物资管理员签字</td>
                <td colspan="2"/>
              </tr>
            </div>
            <tr class="midtitle">
              <td colspan="1">序号</td>
              <td colspan="3">名称</td>
              <td colspan="1">回收时间</td>
              <td colspan="1">车次</td>
              <td colspan="1">重量(吨)</td>
              <td colspan="1">包装数量</td>
              <td colspan="1">结算依据</td>
              <td colspan="2">含税回收单价</td>
              <td colspan="2">含税回收总价(元)</td>
            </tr>
            <div v-for="(ele, ins) in item.list" :key="ins">
              <tr class="midtitle" v-show="!ele.isHj">
                <td colspan="1">{{ ele.xh }}</td>
                <td colspan="3">{{ ele.materialName }}</td>
                <td colspan="1">{{ sub(ele.matuDate || '') }}</td>
                <td colspan="1">{{ ele.carCount }}</td>
                <td colspan="1">{{ ele.settleWeight }}</td>
                <td colspan="1">{{ ele.orderQty }}</td>
                <td colspan="1">{{ ele.settleMethod }}</td>
                <td colspan="2">{{ ele.czdwUnitcostAftertax }}</td>
                <td colspan="2">{{ ele.czdwAmount }}</td>
              </tr>
              <tr class="midtitle" v-show="ele.isHj">
                <td colspan="5">{{ ele.materialName }}</td>
                <td colspan="1">{{ ele.carCount }}</td>
                <td colspan="1">{{ ele.settleWeight }}</td>
                <td colspan="1">{{ ele.orderQty }}</td>
                <td colspan="1">/</td>
                <td colspan="2">/</td>
                <td colspan="2">{{ ele.czdwAmount }}</td>
              </tr>
            </div>
          </table>
        </div>
      </div>
    </section>
  </print-area>
</template>

<script>
import { PrintArea } from '@/components'
import { takeTreeByKey } from '@/utils/util'
import * as baseApi from '@/api/material/base'
import { genWaterMark } from '../utils/watermark.js'
import '@/components/print.less'
// 定制化
import Vue from 'vue'
import Utils from './utils'
import { ORG_ID, PERSON_NAME } from '@/store/mutation-types'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_JX_ENV = USER_ORG_ID === '1.102'
const IS_BYJ_ENV = USER_ORG_ID === '1.100.101'
const IS_YZ_ENV = USER_ORG_ID === '1.100.107'
const IS_WZG = USER_ORG_ID.substr(0, 5) === '1.101'
const IS_XT = USER_ORG_ID === '1.100.118'
const IS_MD = USER_ORG_ID === '1.100.104'
const IS_JYS = USER_ORG_ID === '1.100.117'
const IS_HJ = USER_ORG_ID === '1.100.131'
const IS_TCWG = USER_ORG_ID === '1.100.114'
const IS_TS = USER_ORG_ID === '1.100.122'
const USER_NAME = Vue.ls.get(PERSON_NAME)
export default {
  name: 'PrintDiscardMatr',
  components: {
    PrintArea
  },
  data () {
    return {
      IS_JX_ENV,
      IS_WZG,
      IS_BYJ_ENV,
      IS_YZ_ENV,
      IS_XT,
      IS_JYS,
      IS_TS,
      IS_MD,
      IS_HJ,
      IS_TCWG,
      USER_NAME,
      // 打印配置
      excelData: '',
      excelDataSe: {},
      infoTitle: '',
      printDate: '',
      tabNum: 1,
      renderComponent: []
    }
  },
  computed: {
    object () {
      const excelData = {}
      if (Utils.isObject(this.excelData)) {
        for (const title in this.excelData) {
          Object.assign(excelData, {
            [title]: this.toSort(this.excelData[title])
          })
        }
        return excelData
      }
      return excelData
    }
  },
  created () {
    this.initApi()
  },
  methods: {
    initApi () {
    },
    // 获取下拉框选项文本
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || ''
    },
    toSort (data) {
      const group = (IS_WZG ? 7 : (IS_JYS ? 30 : 5))
      const cache = {}
      // 税额计算方式 false 当前页求和  true polineNum一致的求和
      const afterTaxTyep = IS_BYJ_ENV
      for (const item of data) {
        item.oldlist = item.maplist || []
        item.totalCurrency = 0
        item.maplist = []
        if (!cache[item.polineNum]) {
          cache[item.polineNum] = {
            index: 1,
            temp: 1,
            total: 0,
            tax: 0,
            cuslineCost: 0
          }
        }
        for (let i = 0; i < group; i++) {
          item.maplist.push({
            ...(item.oldlist[i] || {}),
            index: item.oldlist[i] ? cache[item.polineNum].index++ : 'temp_' + cache[item.polineNum].temp++
          })
          // 当前table总金额
          item.totalCurrency += (item.maplist[i] || {}).totalExclTax || 0
          item.totalCurrency = +item.totalCurrency.toFixed(8) || 0
          // 当前polineNum总金额
          cache[item.polineNum].total += (item.maplist[i] || {}).totalExclTax || 0
          cache[item.polineNum].total = +cache[item.polineNum].total.toFixed(8) || 0
          if (afterTaxTyep) {
            // 当前polineNum总税额
            cache[item.polineNum].tax += (item.maplist[i] || {}).tax || 0
            cache[item.polineNum].tax = +cache[item.polineNum].tax.toFixed(8) || 0
            // 当前polineNum税后总金额
            cache[item.polineNum].cuslineCost += (item.maplist[i] || {}).cuslineCost || 0
            cache[item.polineNum].cuslineCost = +cache[item.polineNum].cuslineCost.toFixed(8) || 0
          }
        }
      }
      // 赋值总金额
      for (const item of data) {
        item.polineNumCurrency = cache[item.polineNum].total || 0
        if (afterTaxTyep) {
          item.polinese = Math.round(cache[item.polineNum].tax * 1000000) / 1000000 || 0
          item.polinese = Math.round(item.polinese * 100000) / 100000 || 0
          item.polinese = Math.round(item.polinese * 10000) / 10000 || 0
          item.polinese = Math.round(item.polinese * 1000) / 1000 || 0
          item.polinese = Math.round(item.polinese * 100) / 100 || 0
          // 总金额+ 税额 = 税后总金额
          // item.AfterTaxPolineNumCurrency = (cache[item.polineNum].total + cache[item.polineNum].tax).toFixed(2) || 0
          item.AfterTaxPolineNumCurrency = Math.round((item.polineNumCurrency + item.polinese) * 1000000) / 1000000
          item.AfterTaxPolineNumCurrency = Math.round(item.AfterTaxPolineNumCurrency * 100000) / 100000
          item.AfterTaxPolineNumCurrency = Math.round(item.AfterTaxPolineNumCurrency * 10000) / 10000
          item.AfterTaxPolineNumCurrency = Math.round(item.AfterTaxPolineNumCurrency * 1000) / 1000
          item.AfterTaxPolineNumCurrency = Math.round(item.AfterTaxPolineNumCurrency * 100) / 100
        }
      }
      return data
    },
    sub (str) {
      console.log(str, 'str')
      return str.substring(0, 10)
    },
    curData (num) {
      return num <= 4 ? 4 : num
    },
    curDataFQ (num) {
      return num <= 10 ? 10 : num
    },
    toPrint (data = [], media = {}, tabNum = 0) {
      this.excelData = data
      console.log(data, 'data')
      this.tabNum = tabNum
      this.$nextTick(() => {
        switch (this.tabNum) {
          default: {
            // genWaterMark({
            //   className: 'containers',
            //   content: '宁波梅东集装箱码头有限公司',
            //   font: '24px Microsoft YaHei',
            //   color: 'rgba(156, 162, 169, 0.4)',
            //   rotate: -35,
            //   position: 'absolute'
            // })
            this.$refs.printArea.doPrint(
              Object.assign(
                {
                  size: '297mm 210mm',
                  margin: '2.5cm 1.5cm .5cm 1.5cm'
                },
                media
              )
            )
          }
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.containers {
  position: relative;
}
.sheet {
  width: 100%;
  font-size: 12px;
  margin: auto auto;
  height: auto;
  min-height: 600px;
  text-align: center;
  font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
  overflow: auto;
  .topTitle {
    border-top: 1px solid transparent;
    border-right: 1px solid transparent;
    border-left: 1px solid transparent;
    font-family: Microsoft YaHei;
    color: #000;
    min-height: 60px;
  }
  .bottomTitle {
    border-bottom: 1px solid transparent;
    border-right: 1px solid transparent;
    border-left: 1px solid transparent;
    font-family: Microsoft YaHei;
    color: #000;
    min-height: 30px;
  }
  .top {
    text-align: center;
    font-size: 18px;
    font-family: Microsoft YaHei;
    color: #000;
  }
  .midtitle {
    height: 30px;
    font-size: 12px;
    text-align: center;
    font-family: Microsoft YaHei;
    color: #000;
  }
  .midTitleB {
    height: 30px;
    font-size: 12px;
    font-family: Microsoft YaHei;
    color: #000;
  }
  .table {
    page-break-after: always;
  }
}
</style>
