<template>
  <print-area ref="printArea">
    <section class="excel-container">
      <div class="sheet">
        <div
          class="table"
          :style="{'height': 550 + 529*(item.x - 1) + 'pt'}"
          v-for="(item, index) in excelData"
          :key="index">
          <table border="1" cellspacing="0" cellpadding="0">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <col width="100px">
            <h2 class="top" style="font-size: 22px; text-align: center;">{{ item.companyName }}</h2>
            <h4 class="top" style="font-size: 18px; text-align: center;">{{ item.title }}</h4>
            <tr style="height: 16px;">
              <td class="topTitle" colspan="7">日期：{{ item.date }}</td>
              <td class="topTitle" colspan="7">编号：{{ item.prlineNum }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="1">序号</td>
              <td colspan="2">物资编码</td>
              <td colspan="2">物资名称</td>
              <td colspan="2">型号</td>
              <td colspan="1">数量</td>
              <td colspan="2">申请时间</td>
              <td colspan="2">报废原因</td>
            </tr>
            <tr v-for="(ele, eleins) in item.details" :key="eleins" class="midtitle">
              <td colspan="1">{{ eleins + 1 }}</td>
              <td colspan="2">{{ ele.materialNum }}</td>
              <td colspan="2">{{ ele.materialName }}</td>
              <td colspan="2">{{ ele.model }}</td>
              <td colspan="1">{{ ele.qty }}</td>
              <td colspan="2">{{ ele.requestedDate }}</td>
              <td colspan="2">{{ ele.discardReason }}</td>
            </tr>
            <tr class="midTitleB">
              <td style="text-align: center;" colspan="2">备注</td>
              <td colspan="10">{{ item.remark }}</td>
            </tr>
            <tr class="midTitleB">
              <td style="text-align: center;" colspan="2">使用部门（班组）</td>
              <td colspan="4">{{ item.userDeptAppr }}</td>
              <td style="text-align: center;" colspan="2">职能部门</td>
              <td colspan="4">{{ item.functionDeptAppr }}</td>
            </tr>
            <tr class="midTitleB">
              <td style="text-align: center;" colspan="2">物资组</td>
              <td colspan="10">{{ item.materialAppr }}</td>
            </tr>
            <tr class="midTitleB">
              <td style="text-align: center;" colspan="2">工程技术部</td>
              <td colspan="10">{{ item.techAppr }}</td>
            </tr>
            <tr class="midTitleB">
              <td style="text-align: center;" colspan="2">计划财务部</td>
              <td colspan="10">{{ item.financialAppr }}</td>
            </tr>
            <tr class="midTitleB">
              <td style="text-align: center;" colspan="2">公司分管领导意见</td>
              <td colspan="10">{{ item.assitManagerAppr }}</td>
            </tr>
            <tr class="midTitleB">
              <td style="text-align: center;" colspan="2">总经理意见</td>
              <td colspan="10">{{ item.topManagerAppr }}</td>
            </tr>
          </table>
        </div>
      </div>
    </section>
  </print-area>
</template>

<script>
import { PrintArea } from '@/components'
import { takeTreeByKey } from '@/utils/util'
import * as baseApi from '@/api/material/base'
import '@/components/print.less'
// 定制化
import Vue from 'vue'
import Utils from './utils'
import { ORG_ID, PERSON_NAME } from '@/store/mutation-types'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_JX_ENV = USER_ORG_ID === '1.102'
const IS_BYJ_ENV = USER_ORG_ID === '1.100.101'
const IS_YZ_ENV = USER_ORG_ID === '1.100.107'
const IS_WZG = USER_ORG_ID.substr(0, 5) === '1.101'
const IS_XT = USER_ORG_ID === '1.100.118'
const IS_MD = USER_ORG_ID === '1.100.104'
const IS_JYS = USER_ORG_ID === '1.100.117'
const IS_HJ = USER_ORG_ID === '1.100.131'
const IS_TCWG = USER_ORG_ID === '1.100.114'
const IS_TS = USER_ORG_ID === '1.100.122'
const USER_NAME = Vue.ls.get(PERSON_NAME)
export default {
  name: 'PrintDiscard',
  components: {
    PrintArea
  },
  data () {
    return {
      IS_JX_ENV,
      IS_WZG,
      IS_BYJ_ENV,
      IS_YZ_ENV,
      IS_XT,
      IS_JYS,
      IS_TS,
      IS_MD,
      IS_HJ,
      IS_TCWG,
      USER_NAME,
      // 打印配置
      excelData: '',
      excelDataSe: {},
      infoTitle: '',
      printDate: '',
      tabNum: 1,
      useCompanys: [],
      renderComponent: []
    }
  },
  computed: {
    object () {
      const excelData = {}
      if (Utils.isObject(this.excelData)) {
        for (const title in this.excelData) {
          Object.assign(excelData, {
            [title]: this.toSort(this.excelData[title])
          })
        }
        return excelData
      }
      if (Utils.isArray(this.excelData)) {
        if (!IS_TS) {
          Object.assign(excelData, {
            物资验收单: this.toSort(this.excelData)
          })
        }
        if (IS_TS) {
          Object.assign(excelData, {
            物资入库单: this.toSort(this.excelData)
          })
        }
      }
      return excelData
    },
    scrollerHeight () {
      return function (index) {
        return this.getHeight(index)
      }
    }
  },
  created () {
    this.initApi()
  },
  methods: {
    initApi () {
      baseApi.getCommboxById({ id: 'useCompany' }).then(res => {
        this.useCompanys = res.result || []
      })
    },
    // 获取下拉框选项文本
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || ''
    },
    toSort (data) {
      const group = (IS_WZG ? 7 : (IS_JYS ? 30 : 5))
      const cache = {}
      // 税额计算方式 false 当前页求和  true polineNum一致的求和
      const afterTaxTyep = IS_BYJ_ENV
      for (const item of data) {
        item.oldlist = item.maplist || []
        item.totalCurrency = 0
        item.maplist = []
        if (!cache[item.polineNum]) {
          cache[item.polineNum] = {
            index: 1,
            temp: 1,
            total: 0,
            tax: 0,
            cuslineCost: 0
          }
        }
        for (let i = 0; i < group; i++) {
          item.maplist.push({
            ...(item.oldlist[i] || {}),
            index: item.oldlist[i] ? cache[item.polineNum].index++ : 'temp_' + cache[item.polineNum].temp++
          })
          // 当前table总金额
          item.totalCurrency += (item.maplist[i] || {}).totalExclTax || 0
          item.totalCurrency = +item.totalCurrency.toFixed(8) || 0
          // 当前polineNum总金额
          cache[item.polineNum].total += (item.maplist[i] || {}).totalExclTax || 0
          cache[item.polineNum].total = +cache[item.polineNum].total.toFixed(8) || 0
          if (afterTaxTyep) {
            // 当前polineNum总税额
            cache[item.polineNum].tax += (item.maplist[i] || {}).tax || 0
            cache[item.polineNum].tax = +cache[item.polineNum].tax.toFixed(8) || 0
            // 当前polineNum税后总金额
            cache[item.polineNum].cuslineCost += (item.maplist[i] || {}).cuslineCost || 0
            cache[item.polineNum].cuslineCost = +cache[item.polineNum].cuslineCost.toFixed(8) || 0
          }
        }
      }
      // 赋值总金额
      for (const item of data) {
        item.polineNumCurrency = cache[item.polineNum].total || 0
        if (afterTaxTyep) {
          item.polinese = Math.round(cache[item.polineNum].tax * 1000000) / 1000000 || 0
          item.polinese = Math.round(item.polinese * 100000) / 100000 || 0
          item.polinese = Math.round(item.polinese * 10000) / 10000 || 0
          item.polinese = Math.round(item.polinese * 1000) / 1000 || 0
          item.polinese = Math.round(item.polinese * 100) / 100 || 0
          // 总金额+ 税额 = 税后总金额
          // item.AfterTaxPolineNumCurrency = (cache[item.polineNum].total + cache[item.polineNum].tax).toFixed(2) || 0
          item.AfterTaxPolineNumCurrency = Math.round((item.polineNumCurrency + item.polinese) * 1000000) / 1000000
          item.AfterTaxPolineNumCurrency = Math.round(item.AfterTaxPolineNumCurrency * 100000) / 100000
          item.AfterTaxPolineNumCurrency = Math.round(item.AfterTaxPolineNumCurrency * 10000) / 10000
          item.AfterTaxPolineNumCurrency = Math.round(item.AfterTaxPolineNumCurrency * 1000) / 1000
          item.AfterTaxPolineNumCurrency = Math.round(item.AfterTaxPolineNumCurrency * 100) / 100
        }
      }
      return data
    },
    toHandle (data) {
      let index = 0
      const list = []
      const maps = {}
      const cache = {}
      const array = data.maplist || []
      // 按采购计划分类
      for (const item of array) {
        if (!maps[item.polineNum]) {
          maps[item.polineNum] = {
            polineNum: item.polineNum,
            lotNum: data.lotNum,
            orgName: data.orgName,
            deptName: data.deptName,
            createDate: data.createDate,
            printDate: data.printDate,
            createName: data.createName,
            remark: item.remarks,
            totalCurrency: data.totalCurrency,
            maplist: [],
            total: 0,
            cuslineCost: 0
          }
        }
        maps[item.polineNum].maplist.push({ ...item })
      }
      // 按物资数量排版
      for (const key in maps) {
        const item = maps[key]
        const array = item.maplist
        const length = array.length
        let count = length > (IS_WZG ? 7 : (IS_JYS ? 30 : 5)) ? length : (IS_WZG ? 7 : (IS_JYS ? 30 : 5))
        while (count % (IS_WZG ? 7 : (IS_JYS ? 30 : 5))) {
          count++
        }
        // 拆分页面
        let xh = 0
        let temp = 0
        for (let i = 0; i < count; i++) {
          const floor = Math.floor(i / (IS_WZG ? 7 : (IS_JYS ? 30 : 5)))
          const code = key + '-' + floor
          if (cache[code] === undefined) {
            cache[code] = index++
          }
          if (!list[cache[code]]) {
            list[cache[code]] = {
              polineNum: item.polineNum,
              lotNum: data.lotNum,
              orgName: item.orgName,
              deptName: item.deptName,
              createDate: item.createDate,
              createName: item.createName,
              remark: item.remarks,
              polineNumCurrency: 0,
              totalCurrency: 0,
              maplist: []
            }
          }
          list[cache[code]].maplist.push({
            ...(item.maplist[i] || {}),
            index: item.maplist[i] ? ++xh : 'temp_' + ++temp
          })
          // 当前table总金额
          list[cache[code]].totalCurrency += (item.maplist[i] || {}).totalExclTax || 0
          list[cache[code]].totalCurrency = +list[cache[code]].totalCurrency.toFixed(8) || 0
          // 当前polineNum总金额
          maps[item.polineNum].total += (item.maplist[i] || {}).totalExclTax || 0
          maps[item.polineNum].total = +maps[item.polineNum].total.toFixed(8) || 0
          // 当前polineNum税后总金额
          maps[item.polineNum].cuslineCost += (item.maplist[i] || {}).cuslineCost || 0
          maps[item.polineNum].cuslineCost = +maps[item.polineNum].cuslineCost.toFixed(8) || 0
        }
      }
      // 赋值总金额
      for (const item of list) {
        item.polineNumCurrency = maps[item.polineNum].total || 0
      }
      return list
    },
    toFormat (date, format) {
      function itType (val) {
        return Object.prototype.toString.call(val).replace(/^\[[^\s\]]+\s*([^\s\]]+)]$/, '$1')
      }
      function isDate (date) {
        if (itType(date) === 'Date') {
          return true
        }
        return false
      }
      function isString (str) {
        if (itType(str) === 'String') {
          return true
        }
        return false
      }
      if (isDate(date, true)) {
        const handle = function (i) {
          return (i < 10 ? '0' : '') + i
        }
        if (!isString(format, true)) {
          format = 'yyyy/MM/dd HH:mm:ss'
        }
        return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (f) {
          switch (f) {
            case 'yyyy': {
              return handle(date.getFullYear())
            }
            case 'MM': {
              return handle(date.getMonth() + 1)
            }
            case 'dd': {
              return handle(date.getDate())
            }
            case 'HH': {
              return handle(date.getHours())
            }
            case 'mm': {
              return handle(date.getMinutes())
            }
            case 'ss': {
              return handle(date.getSeconds())
            }
          }
        })
      }
      return ''
    },
    getHeight (index) {
      this.$nextTick(() => {
        this.renderComponent[index] = false
        console.log(this.$refs.table[index].style)
        if (!this.$refs.table[index].style.height) {
          this.$refs.table[index].style.height = '550pt'
        }
        if (+this.$refs.table[index].style.height.substr(0, this.$refs.table[index].style.height.length - 2) > 550) {
          this.$refs.table[index].style.height = Math.ceil(+this.$refs.table[index].style.height.substr(0, this.$refs.table[index].style.height.length - 2) / 550) * 550 + 'pt'
        }
        console.log(this.$refs.table[index].style)
        this.renderComponent[index] = true
        return this.$refs.table[index].style.height
      })
    },
    toPrint (data = [], media = {}, tabNum = 0) {
      this.excelData = data
      this.tabNum = tabNum
      this.$nextTick(() => {
        switch (this.tabNum) {
          default: {
            this.$refs.printArea.doPrint(
              Object.assign(
                {
                  size: '210mm 297mm',
                  margin: '2.5cm 1.5cm 1.5cm 1.5cm'
                },
                media
              )
            )
          }
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.sheet {
  width: 100%;
  font-size: 12px;
  margin: auto auto;
  height: auto;
  text-align: center;
  font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
  overflow: auto;
  .topTitle {
    border-top: 1px solid transparent;
    border-right: 1px solid transparent;
    border-left: 1px solid transparent;
    font-family: Microsoft YaHei;
    color: #000;
    min-height: 60px;
  }
  .bottomTitle {
    border-bottom: 1px solid transparent;
    border-right: 1px solid transparent;
    border-left: 1px solid transparent;
    font-family: Microsoft YaHei;
    color: #000;
    min-height: 60px;
  }
  .top {
    text-align: center;
    font-size: 18px;
    font-family: Microsoft YaHei;
    color: #000;
  }
  .midtitle {
    height: 50px;
    font-size: 12px;
    text-align: center;
    font-family: Microsoft YaHei;
    color: #000;
  }
  .midTitleB {
    height: 60px;
    font-size: 12px;
    font-family: Microsoft YaHei;
    color: #000;
    white-space: pre-line;
  }
  .table {
    page-break-after: always;
  }
}
</style>
