<template>
  <print-area ref="printArea">
    <section class="excel-container">
      <div class="sheet" v-if="tabNum === 2 && IS_HJ">
        <div
          class="table"
          v-for="(item, index) in excelData"
          :key="index">
          <table
            border="1"
            cellspacing="0"
            cellpadding="0">
            <col width="150px" >
            <col width="150px" >
            <col width="120px" >
            <col width="120px" >
            <col width="120px" >
            <col width="120px" >
            <col width="120px" >
            <col width="120px" >
            <col width="120px" >
            <col width="120px" >
            <div class="top" style="font-size: 22px; font-weight: 700;">{{ item.orgName }}</div>
            <tr class="top">
              <th class="topTitle" style="text-align: center;" colspan="10">{{ item.subTitle }}</th>
            </tr>
            <tr style="height: 30px;">
              <td class="topTitle" colspan="1">申请单号：</td>
              <td class="topTitle" colspan="1">{{ item.prlineNum }}</td>
              <td class="topTitle" colspan="1">发货仓库：</td>
              <td class="topTitle" colspan="2">{{ item.storehouseName }}</td>
              <td class="topTitle" colspan="1">制单日期：</td>
              <td class="topTitle" colspan="2">{{ item.createDate }}</td>
              <td class="topTitle" colspan="1">发货日期：</td>
              <td class="topTitle" colspan="1">{{ item.OutStoreDate }}</td>
            </tr>
            <tr class="top">
              <td class="topTitle" style="text-align: left; font-size: 16px;" colspan="10">合同段名称：</td>
            </tr>
            <tr class="midtitle">
              <td colspan="1">使用单位</td>
              <td colspan="4">{{ item.useCompany }}</td>
              <td colspan="1">合同号</td>
              <td colspan="1">{{ item.contractNum }}</td>
              <td colspan="1">项目</td>
              <td colspan="2">{{ item.approNames }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="1" rowspan="2">物资名称</td>
              <td colspan="1" rowspan="2">型号规格</td>
              <td colspan="1" rowspan="2">计量单位</td>
              <td colspan="2">数量</td>
              <td colspan="2">实际价</td>
              <td colspan="1" rowspan="2">销售单价</td>
              <td colspan="1" rowspan="2">销售总价</td>
              <td colspan="1" rowspan="2">备注</td>
            </tr>
            <tr class="midtitle">
              <td colspan="1">应发</td>
              <td colspan="1">实发</td>
              <td colspan="1">核算单价</td>
              <td colspan="1">核算总价</td>
            </tr>
            <tr v-for="(info, ins) in item.matuEntityHjList" :key="ins" class="midtitle">
              <td colspan="1">{{ info.materialName }}</td>
              <td colspan="1">{{ info.jModel }}</td>
              <td colspan="1">{{ info.orderUnit }}</td>
              <td colspan="1">{{ info.quantity }}</td>
              <td colspan="1">{{ info.actReqNum }}</td>
              <td colspan="1">{{ info.accountingUnitPrice }}</td>
              <td colspan="1">{{ info.accountingSumPrice }}</td>
              <td colspan="1">{{ info.saleUnitPrice }}</td>
              <td colspan="1">{{ info.saleSumPrice }}</td>
              <td colspan="1">{{ info.sumCost }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="1">合计</td>
              <td colspan="1"/>
              <td colspan="1"/>
              <td colspan="1">{{ item.sumShouldQty }}</td>
              <td colspan="1">{{ item.sumActQty }}</td>
              <td colspan="1"/>
              <td colspan="1">{{ item.sumAccountingPrice }}</td>
              <td colspan="1"/>
              <td colspan="1">{{ item.sumSalePrice }}</td>
              <td colspan="1"/>
            </tr>
            <tr class="midtitle">
              <td colspan="1">生产厂家</td>
              <td colspan="3">{{ item.projName }}</td>
              <td colspan="1">出厂日期</td>
              <td colspan="1">{{ item.handler }}</td>
              <td colspan="1">出厂编号</td>
              <td colspan="1">{{ item.storeman }}</td>
              <td colspan="1">主机编号</td>
              <td colspan="1">{{ item.storeman }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="1">备注</td>
              <td colspan="9">{{ item.remarkTS }}</td>
            </tr>
            <tr style="height: 30px;">
              <td class="bottomTitle" colspan="1">主管人：</td>
              <td class="bottomTitle" colspan="1">{{ item.handler }}</td>
              <td class="bottomTitle" colspan="1">制单人：</td>
              <td class="bottomTitle" colspan="2">{{ item.printCreateName }}</td>
              <td class="bottomTitle" colspan="1">发货人：</td>
              <td class="bottomTitle" colspan="2">{{ item.matuCreateName }}</td>
              <td class="bottomTitle" colspan="1">提货人：</td>
              <td class="bottomTitle" colspan="1">{{ item.handler }}</td>
            </tr>
          </table>
        </div>
      </div>
      <div class="sheet" v-if="tabNum === 1 && IS_HJ">
        <div
          class="table"
          v-for="(item, index) in excelData"
          :key="index">
          <table border="1" cellspacing="0" cellpadding="0">
            <col width="150px" >
            <col width="150px" >
            <col width="120px" >
            <col width="120px" >
            <col width="120px" >
            <col width="120px" >
            <col width="120px" >
            <col width="120px" >
            <col width="120px" >
            <col width="120px" >
            <col width="120px" >
            <col width="120px" >
            <h2 class="top" style="font-size: 22px">宁波港物资有限公司</h2>
            <tr class="top">
              <th class="topTitle" style="text-align: center;" colspan="12">物资验收单</th>
            </tr>
            <tr style="height: 30px; font-weight: 400;">
              <td class="topTitle" colspan="3">入库单号：{{ item.polineNum }}</td>
              <td class="topTitle" colspan="3">验收仓库：{{ item.storehouse }}</td>
              <td class="topTitle" colspan="3">制单日期：{{ item.createDate }}</td>
              <td class="topTitle" colspan="3">验收日期：{{ item.checkDate }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="1">供货单位</td>
              <td colspan="4">{{ item.vendorName }}</td>
              <td colspan="1">合同号码</td>
              <td colspan="2">{{ item.contractNum }}</td>
              <td colspan="1">发票号码</td>
              <td colspan="3">{{ item.approNames }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="1">物资名称</td>
              <td colspan="1">型号规格</td>
              <td colspan="1">计量单位</td>
              <td colspan="1">应发数量</td>
              <td colspan="1">实发数量</td>
              <td colspan="1">不含税单价</td>
              <td colspan="1">不含税金额</td>
              <td colspan="1">税额</td>
              <td colspan="1">运杂费</td>
              <td colspan="1">合计</td>
              <td colspan="1">项目单体</td>
              <td colspan="1">备注</td>
            </tr>
            <tr v-for="(info, ins) in item.matrEntityHJList" :key="ins" class="midtitle">
              <td colspan="1">{{ info.materialName }}</td>
              <td colspan="1">{{ info.jModel }}</td>
              <td colspan="1">{{ info.orderUnit }}</td>
              <td colspan="1">{{ info.orderQty }}</td>
              <td colspan="1">{{ info.acceptedQty }}</td>
              <td colspan="1">{{ info.unitCost }}</td>
              <td colspan="1">{{ info.lineCost }}</td>
              <td colspan="1">{{ info.tax }}</td>
              <td colspan="1">{{ info.otherCharges }}</td>
              <td colspan="1">{{ info.sumCost }}</td>
              <td colspan="1">{{ info.htdt }}</td>
              <td colspan="1">{{ info.remark }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="1">合计</td>
              <td colspan="1"/>
              <td colspan="1"/>
              <td colspan="1"/>
              <td colspan="1"/>
              <td colspan="1"/>
              <td colspan="1"/>
              <td colspan="1"/>
              <td colspan="1"/>
              <td colspan="1"/>
              <td colspan="1"/>
              <td colspan="1"/>
            </tr>
            <tr class="midtitle">
              <td colspan="1">备注</td>
              <td colspan="11">{{ item.remarkTS }}</td>
            </tr>
            <tr style="height: 30px;">
              <td class="bottomTitle" colspan="4">经办人：{{ item.handler }}</td>
              <td class="bottomTitle" colspan="4">审核人：{{ item.approNames }}</td>
              <td class="bottomTitle" colspan="4">仓管员：{{ item.storeman }}</td>
            </tr>
          </table>
        </div>
      </div>
      <div class="sheet" v-if="tabNum === 3 && IS_HJ">
        <div
          class="table"
          v-for="(item, index) in excelData"
          :key="index">
          <table border="1" cellspacing="0" cellpadding="0">
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <h2 class="top" style="font-size: 22px">{{ item.orgName }}</h2>
            <!-- <h2 class="top" style="font-size: 22px">宁波保税区海升物流有限公司</h2> -->
            <tr class="top">
              <h2 class="topTitle" style="text-align: center;">{{ item.title || '标题未知' }}</h2>
            </tr>
            <tr class="top">
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
            </tr>
            <tr style="height: 30px; font-weight: 400;">
              <td class="topTitle" colspan="3">调度日计划：{{ item.prlineNum }}</td>
              <td class="topTitle" colspan="3">行号：1</td>
              <td class="topTitle" colspan="3">计量单位：{{ item.orderUnit }}</td>
              <td class="topTitle" colspan="3">NO：{{ item.number }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="6">发往单位</td>
              <td colspan="3">发往地址</td>
              <td colspan="3">项目名称</td>
            </tr>
            <tr class="midtitle">
              <td colspan="6">{{ item.sendCompanyName }}</td>
              <td colspan="3">{{ item.targetPlace }}</td>
              <td colspan="3">{{ item.projName }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="3">库号</td>
              <td colspan="3">物资代码</td>
              <td colspan="6">品种规格</td>
            </tr>
            <tr class="midtitle">
              <td colspan="3">{{ item.storehouseNum }}</td>
              <td colspan="3">{{ item.materialNum }}</td>
              <td colspan="6">{{ item.jModel }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="3">重车重量</td>
              <td colspan="3">空车重量</td>
              <td colspan="2">预发重量</td>
              <td colspan="2">回空重量</td>
              <td colspan="2">实发重量</td>
            </tr>
            <tr class="midtitle">
              <td colspan="3">{{ item.heavyWeight }}</td>
              <td colspan="3">{{ item.emptyWeight }}</td>
              <td colspan="2">{{ item.preprovideWeight }}</td>
              <td colspan="2">{{ item.returnWeight }}</td>
              <td colspan="2">{{ item.actualWeight }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="3">车牌号</td>
              <td colspan="3">驾驶员姓名</td>
              <td colspan="2">吨公里</td>
              <td colspan="2">重车过磅时间</td>
              <td colspan="2">空车过磅时间</td>
            </tr>
            <tr class="midtitle">
              <td colspan="3">{{ item.carNumber }}</td>
              <td colspan="3">{{ item.driver }}</td>
              <td colspan="2">{{ item.approNames }}</td>
              <td colspan="2">{{ item.heavyArriveTime }}</td>
              <td colspan="2">{{ item.emptyArriveTime }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="3">联系方式</td>
              <td colspan="9">{{ item.contractNum }}</td>
            </tr>
            <tr class="midtitle" style="height: 30px;">
              <td class="bottomTitle" colspan="4">收料人：{{ item.handler }}</td>
              <td class="bottomTitle" colspan="4"/>
              <td class="bottomTitle" colspan="4">发货人：{{ item.storeman }}</td>
            </tr>
          </table>
        </div>

      </div>
      <div class="sheet" v-if="tabNum === 4 && IS_HJ">
        <div
          class="table"
          v-for="(item, index) in excelData"
          :key="index">
          <table border="1" cellspacing="0" cellpadding="0">
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <col width="100px" >
            <!-- <h2 class="top" style="font-size: 22px">{{ item.orgName }}</h2> -->
            <h2 class="top" style="font-size: 22px; letter-spacing: 30px; text-indent: 30px">海建公司</h2>
            <tr class="top">
              <h2 class="topTitle" style="text-align: center;">{{ item.title || '标题未知' }}</h2>
            </tr>
            <tr class="top">
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
              <td class="topTitle" colspan="1"/>
            </tr>
            <tr style="height: 30px; font-weight: 400;">
              <td class="topTitle" colspan="4">行号：</td>
              <td class="topTitle" colspan="4">计量单位：{{ item.orderUnit }}</td>
              <td class="topTitle" colspan="4">NO：{{ item.number }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="6">供货单位</td>
              <td colspan="3">发往地址</td>
              <td colspan="3">项目名称</td>
            </tr>
            <tr class="midtitle">
              <td colspan="6">{{ item.vendorName }}</td>
              <td colspan="3">{{ item.targetPlace }}</td>
              <td colspan="3">{{ item.projName }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="3">库号</td>
              <td colspan="3">物资代码</td>
              <td colspan="6">品种规格</td>
            </tr>
            <tr class="midtitle">
              <td colspan="3">{{ item.storehouseName }}</td>
              <td colspan="3">{{ item.materialNum }}</td>
              <td colspan="6">{{ item.jModel }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="3">重车重量</td>
              <td colspan="3">空车重量</td>
              <td colspan="2">预收重量</td>
              <td colspan="2">回空重量</td>
              <td colspan="2">实收重量</td>
            </tr>
            <tr class="midtitle">
              <td colspan="3">{{ item.heavyWeight }}</td>
              <td colspan="3">{{ item.emptyWeight }}</td>
              <td colspan="2">{{ item.preprovideWeight }}</td>
              <td colspan="2">{{ item.returnWeight }}</td>
              <td colspan="2">{{ item.actualWeight }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="3">车牌号</td>
              <td colspan="3">驾驶员姓名</td>
              <td colspan="2">火车/汽车</td>
              <td colspan="2">重车过磅时间</td>
              <td colspan="2">空车过磅时间</td>
            </tr>
            <tr class="midtitle">
              <td colspan="3">{{ item.isTrain === 'Y' ? item.trainNumber : item.carNumber }}</td>
              <td colspan="3">{{ item.driver }}</td>
              <td colspan="2">{{ item.isTrain === 'Y' ? '火车' : '汽车' }}</td>
              <td colspan="2">{{ item.heavyArriveTime }}</td>
              <td colspan="2">{{ item.emptyArriveTime }}</td>
            </tr>
            <tr class="midtitle">
              <td colspan="3">联系方式</td>
              <td colspan="9">{{ item.contractNum }}</td>
            </tr>
            <tr class="midtitle" style="height: 30px;">
              <td class="bottomTitle" colspan="4">收料人：{{ item.handler }}</td>
              <td class="bottomTitle" colspan="4"/>
              <td class="bottomTitle" colspan="4">发货人：{{ item.storeman }}</td>
            </tr>
          </table>
        </div>

      </div>
    </section>
    <section v-if="IS_TCWG">
      <EvaluationSheet :data="excelDataSe" :printDate="printDate"/>
    </section>
  </print-area>
</template>

<script>
import { PrintArea } from '@/components'
import '@/components/print.less'
import EvaluationSheet from '@/views/material/store/component/evaluationSheet.vue'

// 定制化
import Vue from 'vue'
import Utils from './utils'
import { ORG_ID, PERSON_NAME } from '@/store/mutation-types'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_JX_ENV = USER_ORG_ID === '1.102'
const IS_BYJ_ENV = USER_ORG_ID === '1.100.101'
const IS_YZ_ENV = USER_ORG_ID === '1.100.107'
const IS_WZG = USER_ORG_ID.substr(0, 5) === '1.101'
const IS_XT = USER_ORG_ID === '1.100.118'
const IS_MD = USER_ORG_ID === '1.100.104'
const IS_JYS = USER_ORG_ID === '1.100.117'
const IS_HJ = USER_ORG_ID === '1.100.131'
const IS_TCWG = USER_ORG_ID === '1.100.114'
const IS_TS = USER_ORG_ID === '1.100.122'
const USER_NAME = Vue.ls.get(PERSON_NAME)

export default {
  name: 'PrintExcel4',
  components: {
    PrintArea,
    EvaluationSheet
  },
  data () {
    return {
      IS_JX_ENV,
      IS_WZG,
      IS_BYJ_ENV,
      IS_YZ_ENV,
      IS_XT,
      IS_JYS,
      IS_TS,
      IS_MD,
      IS_HJ,
      IS_TCWG,
      USER_NAME,
      // 打印配置
      excelData: '',
      excelDataSe: {},
      infoTitle: '',
      printDate: '',
      tabNum: 1
    }
  },
  computed: {
    object () {
      const excelData = {}
      if (Utils.isObject(this.excelData)) {
        for (const title in this.excelData) {
          Object.assign(excelData, {
            [title]: this.toSort(this.excelData[title])
          })
        }
        return excelData
      }
      if (Utils.isArray(this.excelData)) {
        if (!IS_TS) {
          Object.assign(excelData, {
            物资验收单: this.toSort(this.excelData)
          })
        }
        if (IS_TS) {
          Object.assign(excelData, {
            物资入库单: this.toSort(this.excelData)
          })
        }
      }
      return excelData
    }
  },
  methods: {
    toSort (data) {
      const group = (IS_WZG ? 7 : (IS_JYS ? 30 : 5))
      const cache = {}
      // 税额计算方式 false 当前页求和  true polineNum一致的求和
      const afterTaxTyep = IS_BYJ_ENV
      for (const item of data) {
        item.oldlist = item.maplist || []
        item.totalCurrency = 0
        item.maplist = []
        if (!cache[item.polineNum]) {
          cache[item.polineNum] = {
            index: 1,
            temp: 1,
            total: 0,
            tax: 0,
            cuslineCost: 0
          }
        }
        for (let i = 0; i < group; i++) {
          item.maplist.push({
            ...(item.oldlist[i] || {}),
            index: item.oldlist[i] ? cache[item.polineNum].index++ : 'temp_' + cache[item.polineNum].temp++
          })

          // 当前table总金额
          item.totalCurrency += (item.maplist[i] || {}).totalExclTax || 0
          item.totalCurrency = +item.totalCurrency.toFixed(8) || 0

          // 当前polineNum总金额
          cache[item.polineNum].total += (item.maplist[i] || {}).totalExclTax || 0
          cache[item.polineNum].total = +cache[item.polineNum].total.toFixed(8) || 0

          if (afterTaxTyep) {
          // 当前polineNum总税额
            cache[item.polineNum].tax += (item.maplist[i] || {}).tax || 0
            cache[item.polineNum].tax = +cache[item.polineNum].tax.toFixed(8) || 0
            // 当前polineNum税后总金额
            cache[item.polineNum].cuslineCost += (item.maplist[i] || {}).cuslineCost || 0
            cache[item.polineNum].cuslineCost = +cache[item.polineNum].cuslineCost.toFixed(8) || 0
          }
        }
      }

      // 赋值总金额
      for (const item of data) {
        item.polineNumCurrency = cache[item.polineNum].total || 0
        if (afterTaxTyep) {
          item.polinese = Math.round(cache[item.polineNum].tax * 1000000) / 1000000 || 0
          item.polinese = Math.round(item.polinese * 100000) / 100000 || 0
          item.polinese = Math.round(item.polinese * 10000) / 10000 || 0
          item.polinese = Math.round(item.polinese * 1000) / 1000 || 0
          item.polinese = Math.round(item.polinese * 100) / 100 || 0
          // 总金额+ 税额 = 税后总金额
          // item.AfterTaxPolineNumCurrency = (cache[item.polineNum].total + cache[item.polineNum].tax).toFixed(2) || 0
          item.AfterTaxPolineNumCurrency = Math.round((item.polineNumCurrency + item.polinese) * 1000000) / 1000000
          item.AfterTaxPolineNumCurrency = Math.round(item.AfterTaxPolineNumCurrency * 100000) / 100000
          item.AfterTaxPolineNumCurrency = Math.round(item.AfterTaxPolineNumCurrency * 10000) / 10000
          item.AfterTaxPolineNumCurrency = Math.round(item.AfterTaxPolineNumCurrency * 1000) / 1000
          item.AfterTaxPolineNumCurrency = Math.round(item.AfterTaxPolineNumCurrency * 100) / 100
        }
      }
      return data
    },
    toHandle (data) {
      let index = 0
      const list = []
      const maps = {}
      const cache = {}
      const array = data.maplist || []

      // 按采购计划分类
      for (const item of array) {
        if (!maps[item.polineNum]) {
          maps[item.polineNum] = {
            polineNum: item.polineNum,
            lotNum: data.lotNum,
            orgName: data.orgName,
            deptName: data.deptName,
            createDate: data.createDate,
            printDate: data.printDate,
            createName: data.createName,
            remarkTS: item.remarkTS,
            totalCurrency: data.totalCurrency,
            maplist: [],
            total: 0,
            cuslineCost: 0
          }
        }
        maps[item.polineNum].maplist.push({ ...item })
      }

      // 按物资数量排版
      for (const key in maps) {
        const item = maps[key]
        const array = item.maplist
        const length = array.length

        let count = length > (IS_WZG ? 7 : (IS_JYS ? 30 : 5)) ? length : (IS_WZG ? 7 : (IS_JYS ? 30 : 5))

        while (count % (IS_WZG ? 7 : (IS_JYS ? 30 : 5))) {
          count++
        }

        // 拆分页面
        let xh = 0
        let temp = 0
        for (let i = 0; i < count; i++) {
          const floor = Math.floor(i / (IS_WZG ? 7 : (IS_JYS ? 30 : 5)))
          const code = key + '-' + floor
          if (cache[code] === undefined) {
            cache[code] = index++
          }
          if (!list[cache[code]]) {
            list[cache[code]] = {
              polineNum: item.polineNum,
              lotNum: data.lotNum,
              orgName: item.orgName,
              deptName: item.deptName,
              createDate: item.createDate,
              createName: item.createName,
              remarkTS: item.remarkTS,
              polineNumCurrency: 0,
              totalCurrency: 0,
              maplist: []
            }
          }
          list[cache[code]].maplist.push({
            ...(item.maplist[i] || {}),
            index: item.maplist[i] ? ++xh : 'temp_' + ++temp
          })

          // 当前table总金额
          list[cache[code]].totalCurrency += (item.maplist[i] || {}).totalExclTax || 0
          list[cache[code]].totalCurrency = +list[cache[code]].totalCurrency.toFixed(8) || 0

          // 当前polineNum总金额
          maps[item.polineNum].total += (item.maplist[i] || {}).totalExclTax || 0
          maps[item.polineNum].total = +maps[item.polineNum].total.toFixed(8) || 0
          // 当前polineNum税后总金额
          maps[item.polineNum].cuslineCost += (item.maplist[i] || {}).cuslineCost || 0
          maps[item.polineNum].cuslineCost = +maps[item.polineNum].cuslineCost.toFixed(8) || 0
        }
      }

      // 赋值总金额
      for (const item of list) {
        item.polineNumCurrency = maps[item.polineNum].total || 0
      }

      return list
    },
    toFormat (date, format) {
      function itType (val) {
        return Object.prototype.toString.call(val).replace(/^\[[^\s\]]+\s*([^\s\]]+)]$/, '$1')
      }
      function isDate (date) {
        if (itType(date) === 'Date') {
          return true
        }
        return false
      }
      function isString (str) {
        if (itType(str) === 'String') {
          return true
        }
        return false
      }
      if (isDate(date, true)) {
        const handle = function (i) {
          return (i < 10 ? '0' : '') + i
        }
        if (!isString(format, true)) {
          format = 'yyyy/MM/dd HH:mm:ss'
        }
        return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (f) {
          switch (f) {
            case 'yyyy': {
              return handle(date.getFullYear())
            }
            case 'MM': {
              return handle(date.getMonth() + 1)
            }
            case 'dd': {
              return handle(date.getDate())
            }
            case 'HH': {
              return handle(date.getHours())
            }
            case 'mm': {
              return handle(date.getMinutes())
            }
            case 'ss': {
              return handle(date.getSeconds())
            }
          }
        })
      }
      return ''
    },
    toPrint (data = [], media = {}, tabNum = 0) {
      if (IS_TCWG) {
        this.excelDataSe = data
      } else {
        this.excelData = data
        this.printDate = this.toFormat(new Date(), 'yyyy年MM月dd日')
      }
      this.tabNum = tabNum
      if (tabNum === 1) {
        for (let i = 0; i < 3; i++) {
          if (this.excelData[0].matrEntityHJList.length < 3) {
            this.excelData[0].matrEntityHJList.push([])
          }
        }
      } else if (tabNum === 2) {
        this.excelData.forEach((item, index) => {
          for (let i = 0; i < 3; i++) {
            if (item.matuEntityHjList.length < 3) {
              this.excelData[index].matuEntityHjList.push([])
            }
          }
        })
      }
      // else if (tabNum === 3) {
      //   this.infoTitle = '送货通知单'
      // } else if (tabNum === 4) {
      //   this.infoTitle = '入库通知单'
      //   this.tabNum = 4
      // }
      this.$nextTick(() => {
        switch (USER_ORG_ID) {
          case '1.100.114': {
            this.$refs.printArea.doPrint(
              Object.assign(
                {
                  size: '210mm 297mm',
                  margin: '1cm .6cm .3cm .6cm'
                },
                media
              )
            )
            break
          }
          default: {
            this.$refs.printArea.doPrint(
              Object.assign(
                {
                  size: '241mm 139.7mm',
                  margin: '1.5cm 1.2cm .5cm .5cm'
                },
                media
              )
            )
          }
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.sheet {
  width: 100%;
  font-size: 12px;
  margin: 0 auto;
  text-align: center;
  overflow: auto;
  .topTitle {
    border-top: 1px solid transparent;
    border-right: 1px solid transparent;
    border-left: 1px solid transparent;
    min-height: 30px;
  }
  .bottomTitle {
    border-bottom: 1px solid transparent;
    border-right: 1px solid transparent;
    border-left: 1px solid transparent;
    min-height: 30px;
  }
  .top {
    text-align: center;
    font-size: 18px;
  }
  .midtitle {
    height: 30px;
    font-size: 12px;
    text-align: center;
  }
  .table {
    height:  446px;
  }
}
</style>
