<template>
  <print-area ref="printArea">
    <section
      v-for="(list, key) in object"
      class="excel-container"
      :key="key"
    >
      <div
        class="excel-item"
        v-for="(item, index) of list"
        break-avoid
        break-after
        :key="index"
      >
        <div class="excel-group">
          <div class="excel-header">
            <div class="mainTitle">
              <div class="textName">
                <span style="letter-spacing: 1.5pt">{{ key }}</span>
              </div>
            </div>
          </div>
          <div class="excel-content">
            <a-table
              class="table table-th-2 table-td-3"
              :columns="columns"
              :data-source="item.maplist"
              :pagination="false"
              bordered
            />
          </div>
          <template>
            <div class="excel-footer">
              <div/>
              <div/>
              <div/>
              <div/>
              <div/>
              <div>
                <span style="margin: 0 3pt">制表：</span>
                {{ USER_NAME }}
              </div>
              <div/>
            </div>
          </template>
        </div>
      </div>
    </section>
  </print-area>
</template>

<script>
import { PrintArea } from '@/components'
import '@/components/print.less'

// 定制化
import Vue from 'vue'
import Utils from './utils'
import { PERSON_NAME } from '@/store/mutation-types'
const USER_NAME = Vue.ls.get(PERSON_NAME)

export default {
  components: {
    PrintArea
  },
  data () {
    return {
      USER_NAME,
      columns: [
        {
          title: '序号',
          dataIndex: 'xh',
          customRender: (text, row, index) => {
            return {
              children: index + 1
            }
          },
          width: '4%',
          align: 'center'
        },
        {
          title: '物资编码',
          dataIndex: 'materialNum',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '12%',
          align: 'center'
        },
        {
          title: '物资名称',
          dataIndex: 'materialName',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '12%',
          align: 'center'
        },
        {
          title: '规格型号',
          dataIndex: 'jmodel',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '12%',
          align: 'center'
        },
        {
          title: '计量单位',
          dataIndex: 'orderUnit',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '6%',
          align: 'center'
        },
        {
          title: '数量',
          dataIndex: 'lineCost',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '8%',
          align: 'center'
        },
        {
          title: '区',
          dataIndex: 'zone',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '8%',
          align: 'center'
        },
        {
          title: '架',
          dataIndex: 'frame',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '8%',
          align: 'center'
        },
        {
          title: '层',
          dataIndex: 'floor',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '8%',
          align: 'center'
        },
        {
          title: '位',
          dataIndex: 'bit',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '8%',
          align: 'center'
        },
        {
          title: '是否有图片',
          dataIndex: 'hasPic',
          customRender: (text, row, index) => {
            return {
              children: text === 'Y' ? '是' : '否'
            }
          },
          width: '10%',
          align: 'center'
        }
      ],
      // 打印配置
      excelData: '',
      printDate: ''
    }
  },
  computed: {
    object () {
      const excelData = {}
      if (Utils.isObject(this.excelData)) {
        for (const title in this.excelData) {
          Object.assign(excelData, {
            [title]: this.toSort(this.excelData[title])
          })
        }
        return excelData
      }
      if (Utils.isArray(this.excelData)) {
        Object.assign(excelData, {
          物资货架信息: this.toSort(this.excelData)
        })
      }
      return excelData
    }
  },
  methods: {
    toSort (data) {
      const cache = {}
      console.log(data)
      for (const item of data) {
        item.oldlist = item.maplist || []
        item.maplist = []
        if (!cache[item.rfqNum]) {
          cache[item.rfqNum] = {
            index: 1,
            temp: 1
          }
        }
        let group
        if (item.type === 2) {
          group = item.oldlist.length >= 16 ? 16 : item.oldlist.length
        } else {
          group = item.oldlist.length
        }
        for (let i = 0; i < group; i++) {
          item.maplist.push({
            ...(item.oldlist[i] || {}),
            index: item.oldlist[i] ? cache[item.rfqNum].index++ : 'temp_' + cache[item.rfqNum].temp++
          })
        }
      }
      return data
    },
    toHandle (data) {
      let index = 0
      const list = []
      const maps = {}
      const cache = {}
      const array = data.maplist || []

      // 按采购计划分类
      for (const item of array) {
        if (!maps[item.rfqNum]) {
          maps[item.rfqNum] = {
            rfqNum: item.rfqNum,
            orgName: data.orgName,
            createDate: data.createDate,
            createName: data.createName,
            maplist: []
          }
        }
        maps[item.rfqNum].maplist.push({ ...item })
      }

      // 按物资数量排版
      for (const key in maps) {
        const item = maps[key]
        const array = item.maplist
        const length = array.length
        if (length <= 16) {
          continue
        }

        let count = length > 16 ? length : 16

        while (count % 16) {
          count++
        }

        // 拆分页面
        let xh = 0
        let temp = 0
        for (let i = 0; i < count; i++) {
          const floor = Math.floor(i / 16)
          const code = key + '-' + floor
          if (cache[code] === undefined) {
            cache[code] = index++
          }
          if (!list[cache[code]]) {
            list[cache[code]] = {
              rfqNum: item.rfqNum,
              orgName: item.orgName,
              createDate: item.createDate,
              createName: item.createName,
              maplist: []
            }
          }
          list[cache[code]].maplist.push({
            ...(item.maplist[i] || {}),
            index: item.maplist[i] ? ++xh : 'temp_' + ++temp
          })
        }
      }

      return list
    },
    toFormat (date, format) {
      function itType (val) {
        return Object.prototype.toString.call(val).replace(/^\[[^\s\]]+\s*([^\s\]]+)]$/, '$1')
      }
      function isDate (date) {
        if (itType(date) === 'Date') {
          return true
        }
        return false
      }
      function isString (str) {
        if (itType(str) === 'String') {
          return true
        }
        return false
      }
      if (isDate(date, true)) {
        const handle = function (i) {
          return (i < 10 ? '0' : '') + i
        }
        if (!isString(format, true)) {
          format = 'yyyy/MM/dd HH:mm:ss'
        }
        return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (f) {
          switch (f) {
            case 'yyyy': {
              return handle(date.getFullYear())
            }
            case 'MM': {
              return handle(date.getMonth() + 1)
            }
            case 'dd': {
              return handle(date.getDate())
            }
            case 'HH': {
              return handle(date.getHours())
            }
            case 'mm': {
              return handle(date.getMinutes())
            }
            case 'ss': {
              return handle(date.getSeconds())
            }
          }
        })
      }
      return ''
    },
    toPrint (data = [], media = {}) {
      this.excelData = data
      this.printDate = this.toFormat(new Date(), 'yyyy年MM月dd日')
      this.$nextTick(() => {
        this.$refs.printArea.doPrint(
          Object.assign(
            {
              size: '241mm 139.7mm',
              margin: '.1cm 1.2cm .1cm .5cm'
            },
            media
          )
        )
      })
    }
  }
}
</script>

<style lang="less" scoped>
.excel-container {
  width: 100%;
  & > .excel-item {
    width: 100%;
    & > .excel-group {
      width: 100%;
      position: relative;
      &:not(:last-child) {
        &::after {
          content: '';
          width: calc(100% + 10pt);
          height: 2pt;
          border-bottom: dashed 1pt #000;
          position: absolute;
          bottom: -10pt;
          left: -5pt;
        }
      }
      & > .excel-header {
        width: 100%;
        & > .mainTitle {
          color: #000;
          font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
          text-align: center;
          & > .orgName {
            font-size: 16pt;
            font-weight: 500;
          }
          & > .textName {
            font-size: 14pt;
            font-weight: 500;
          }
        }
        & > .subTitle {
          height: 16pt;
          line-height: 16pt;
          padding: 0 100pt 0 20pt;
          color: #000;
          font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
          font-size: 9pt;
          font-weight: 400;
          display: flex;
          justify-content: space-between;
        }
        & > .subTitle1 {
          height: 5pt;
          line-height: 5pt;
          padding: 0 100pt 0 20pt;
          color: #000;
          display: flex;
          justify-content: space-between;
        }
      }
      & > .excel-content {
        width: 100%;
        display: flex;
        & > .table {
          width: 100%;
        }
      }
      & > .excel-footer {
        width: 100%;
        height: 20pt;
        line-height: 20pt;
        padding: 0 30pt;
        color: #000;
        font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
        font-size: 9pt;
        font-weight: 400;
        display: flex;
        justify-content: space-between;
        & > .div {
          width: 10%;
          flex: 1 0 auto;
        }
      }
    }
  }
}
 ::v-deep {
  th.ant-descriptions-item-label.ant-descriptions-item-colon {
      border: 1px solid #000;
      border-top: none;
  }
  .ant-descriptions-item-content {
      border: 1px solid #000;
      border-top: none;
      &:last-child {
        border-right: 1px solid #000;
        border-top: none;
      }
  }
}
</style>
