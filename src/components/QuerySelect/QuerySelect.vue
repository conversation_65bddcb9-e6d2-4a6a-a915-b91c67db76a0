<template>
  <section>
    <a-select
      ref="aSelect"
      v-model="valueCur"
      :loading="loading"
      :disabled="disabled"
      :showSearch="showSearch"
      :default-active-first-option="defaultActiveFirstOption"
      :show-arrow="showArrow"
      :filter-option="filterOption"
      :not-found-content="notFoundContent"
      @search="handleSearch"
      @select="handleChange"
      @input="handleInput"
    >
      <a-select-option v-for="(info, index) in columns" :value="info[searchValue]" :label="info[searchLabel]" :key="index">
        {{ info[searchLabel] }}
      </a-select-option>
    </a-select>
  </section>
</template>

<script>
import _ from 'lodash'
import { axios } from '@/utils/request'
import { requestBuilder } from '@/utils/util'
export default {
  name: 'QuerySelect',
  props: {
    // 双向绑定
    value: {
      type: [String, Number, Array],
      default: null
    },
    // 查询label
    searchLabel: {
      type: String,
      default: 'label'
    },
    // 查询value
    searchValue: {
      type: String,
      default: 'value'
    },
    // 禁用
    disabled: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    showSearch: {
      type: Boolean,
      default: () => {
        return true
      }
    },
    defaultActiveFirstOption: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    showArrow: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    filterOption: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    notFoundContent: {
      type: String,
      default: '请输入查询'
    },
    immediate: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    // 查询接口
    urlName: {
      type: String,
      default: '/vendor/queryVendorInfoForSelectOption'
    },
    // 查询获取数据条数
    queryNum: {
      type: Number,
      default: 20
    },
    queryParam: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  data () {
    return {
      valueCur: this.value,
      columns: [],
      zcColumns: [],
      loading: false
    }
  },
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler (newVal, oldVal) {
        console.log(newVal, oldVal, 'watch')
        if (newVal) {
          this.valueCur = newVal
          const index = this.columns.findIndex(item => item[this.searchValue] === newVal)
          if (index === -1) {
            this.searchByUrl(newVal, this.searchValue)
          }
          this.$emit('change', newVal)
        }
      }
    }
  },
  mounted () {
    if (this.immediate) {
      this.searchByUrl()
    }
  },
  methods: {
    searchByUrl (value = '', key = '') {
      this.loading = true
      const obj = {
        ...this.queryParam
      }
      obj[key] = value
      const param = requestBuilder('', obj, 1, this.queryNum)
      axios.post(this.urlName, param).then(res => {
        const data = res.result.data.map(item => {
          const dx = {}
          dx[this.searchLabel] = item[this.searchLabel]
          dx[this.searchValue] = item[this.searchValue]
          return dx
        })
        const arr = this.unique([...data, ...this.zcColumns])
        this.columns = arr || []
        // this.zcColumns = this.unique([...arr, ...this.zcColumns])
      }).then(() => {
        // this.$nextTick(() => {
        //   const selectDropdown = this.$el.querySelector('.ant-select-dropdown')
        //   console.log(selectDropdown, 'selectDropdown')
        //   if (selectDropdown) {
        //     selectDropdown.scrollTop = 0
        //   }
        // })
      }).finally(() => {
        this.loading = false
      })
    },
    fetch: _.debounce(function (value) {
      this.searchByUrl(value, this.searchLabel)
    }, 1000),
    handleSearch (value) {
      if (value) {
        this.columns = []
        this.fetch(value)
      }
    },
    handleChange (value) {
      this.valueCur = value
      this.$emit('input', value)
      const obj = this.columns.find(item => {
        return item[this.searchValue] === value
      })
      const arr = [...this.zcColumns, obj]
      this.zcColumns = this.unique(arr) || []
      this.$emit('change', value)
    },
    handleInput (event) {
      this.$emit('change', event.target.value)
    },
    unique (arr) {
      const list = []
      arr.forEach(item => {
        if (list.findIndex(info => info[this.searchValue] === item[this.searchValue]) === -1) {
          list.push(item)
        } else {
          // console.log(item)
        }
      })
      return list
    }
  }
}
</script>

<style lang="less" scoped>
</style>
