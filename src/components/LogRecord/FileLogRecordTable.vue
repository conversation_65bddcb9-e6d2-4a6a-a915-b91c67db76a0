<template>
  <div>
    <div>
      <s-table
        :data="queryLoadData"
        :scroll="scroll"
        :columns="compheadColumns"
        :components="resizeComponents(headColumns)"
        :immediate="true"
        :bordered="false"
        :showPagination="true"
        ref="head"
        rowKey="uuid"
      >
        <!-- 自定义帅选组件 -->
        <template slot="title">
          <div class="tableTitle">
            <a-popover v-model="popVisible" trigger="click">
              <template slot="content">
                <DropdownChecked
                  :showTitle="false"
                  title="自定义表格排序"
                  tableId="FileLogRecordTable"
                  v-model="headColumns" />
              </template>
              <span class="title-icons" >
                <a-icon type="setting" theme="filled" />
              </span>
            </a-popover>
            <span class="title-icons" style="border: none;">
              <a-tooltip>
                <template slot="title">
                  刷新
                </template>
                <a-icon type="sync" :spin="popLoading" @click="reColumns"/>
              </a-tooltip>
            </span>
          </div>
        </template>
        <span
          slot="serial"
          slot-scope="text, record, index"
        >{{ index + 1 }}</span>
        <span
          slot="filename"
          slot-scope="text,record"
        >{{ record.updatedata[0].newValue||record.updatedata[0].oldValue }}</span>

        <span
          slot="operation"
          slot-scope="text"
        >{{ text=='update'?'修改':text=='insert'?'上传':text=='delete'?'删除':'' }}</span>

      </s-table>

    </div>
  </div>

</template>
<script>
import { STable, DropdownChecked } from '@/components'
import { requestBuilder, deepUpdate, getNowDate } from '@/utils/util'
import * as logDataRecordApi from '@/api/system/logDataRecord'
import Vue from 'vue'
import { ORG_ID, PERSON_ID } from '@/store/mutation-types'

import { tableMixin } from '@/utils/tableMixin'

const USER_ORG_ID = Vue.ls.get(ORG_ID)
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)
export default {
  name: 'FileLogRecordTable',
  components: {
    STable,
    DropdownChecked
  },

  mixins: [tableMixin],
  data () {
    return {
      popLoading: false,
      popVisible: false,
      visible: false,
      queryParam: {
        tableName: null,
        keyValue: null
      },
      scroll: {
        y: '35vh',
        x: '100%'
      },
      // 表格配置
      headColumns: [
        {
          title: '序号',
          dataIndex: 'serial',
          scopedSlots: { customRender: 'serial' },
          check: true,
          align: 'center',
          width: 50
        },
        {
          title: '文件名',
          dataIndex: 'filename',
          scopedSlots: { customRender: 'filename' },
          check: true,
          width: 200,
          align: 'center'
        },
        {
          title: '操作',
          dataIndex: 'operation',
          scopedSlots: { customRender: 'operation' },
          ellipsis: true,
          check: true,
          align: 'center',
          width: 100

        }, {
          title: '修改人',
          dataIndex: 'personName',
          check: true,
          align: 'center',
          width: 100

        }, {
          title: '修改部门',
          dataIndex: 'departmentName',
          check: true,
          align: 'center',
          width: 100
        }, {
          title: '修改时间',
          dataIndex: 'createDate',
          check: true,
          align: 'center',
          width: 140
        }

      ],
      // 加载数据方法 必须为 Promise 对象
      queryLoadData: parameter => {
        if (this.visible) {
          if (this.isLocalLoad) {
            return this.dataSource
          } else {
            const param = requestBuilder(
              '',
              this.queryParam,
              parameter.pageNo,
              parameter.pageSize
            )
            return logDataRecordApi.queryLog(param).then(res => {
              if (res.code !== '0000') {
                this.$notification.error({
                  message: '系统消息',
                  description: res.message || '查询失败！'
                })
                return Promise.reject(res)
              }
              const report = res.result
              return report
            })
          }
        } else {
          return new Promise((resolve, reject) => {
            resolve([])
          })
        }
      },

      dataSource: [],
      isLocalLoad: false
    }
  },
  computed: {
    compheadColumns () {
      const arr = this.headColumns.filter(item => {
        return item.check
      })
      return arr
    }
  },
  created () {
    this.initTable('headColumns', 'FileLogRecordTable')
  },
  methods: {
    reColumns () {
      this.popLoading = true
      this.$store.dispatch('SetTodoTableGroup', { USER_ORG_ID, USER_PERSON_ID })
      setTimeout(() => {
        this.initTable('headColumns', 'FileLogRecordTable')
        this.popLoading = false
      }, 500)
    },

    show (params) {
      this.isLocalLoad = params.isLocalLoad || false
      if (this.isLocalLoad) {
        this.dataSource = params
      } else {
        this.queryParam = deepUpdate(this.queryParam, params)
        this.visible = true
        if (this.$refs.head) {
          this.$refs.head.refresh()
        }
      }
    },
    clear () {
      this.isLocalLoad = true
      this.dataSource = []
      this.$refs.head.refresh()
    },
    handleCancel (e) {
      this.visible = false
      this.dataSource = []
    }
  }
}
</script>
<style lang="less" scoped>
 ::v-deep {
   .selected-row{
      td {
        background: #f0f4ff !important;
      }
    }
    .row {
      td {
        height: auto !important;
      }
    }
 }
</style>
