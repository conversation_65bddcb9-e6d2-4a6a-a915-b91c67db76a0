<template>
  <div>
    <div>
      <s-table
        :data="queryLoadData"
        :scroll="scroll"
        :columns="compheadColumns"
        :components="resizeComponents(headColumns)"
        :immediate="true"
        :bordered="false"
        :showPagination="true"
        :rowClassName="headRowClassName"
        :customRow="headRowClick"
        ref="head"
        rowKey="uuid"
      >
        <!-- 自定义帅选组件 -->
        <template slot="title">
          <div class="tableTitle">
            <a-popover v-model="popVisible" trigger="click">
              <template slot="content">
                <DropdownChecked
                  :showTitle="false"
                  title="自定义表格排序"
                  tableId="logHeadTable"
                  v-model="headColumns" />
              </template>
              <span class="title-icons" >
                <a-icon type="setting" theme="filled" />
              </span>
            </a-popover>
            <span class="title-icons" style="border: none;">
              <a-tooltip>
                <template slot="title">
                  刷新
                </template>
                <a-icon
                  type="sync"
                  :spin="popLoading"
                  @click="reColumns"/>
              </a-tooltip>
            </span>
          </div>
        </template>
        <span
          slot="serial"
          slot-scope="text, record, index"
        >{{ index + 1 }}</span>
        <span
          slot="operation"
          slot-scope="text"
        >{{ text=='update'?'修改':text=='insert'?'新增':text=='delete'?'删除':'' }}</span>

      </s-table>
      <s-table
        ref="body"
        :data="bodyLoadData"
        :scroll="scroll"
        :columns="compbodyColumns"
        :components="resizeComponents(bodyColumns)"

        :immediate="true"
        :bordered="false"
        :showPagination="false"
        :rowClassName="()=>{return 'row'}"
        :rowKey="(record,index)=>{return index}"
      >
        <!-- 自定义帅选组件 -->
        <template slot="title">
          <div class="tableTitle">
            <a-popover v-model="popVisible2" trigger="click">
              <template slot="content">
                <DropdownChecked
                  :showTitle="false"
                  title="自定义表格排序"
                  tableId="logBodyTable"
                  v-model="bodyColumns" />
              </template>
              <span class="title-icons" >
                <a-icon type="setting" theme="filled" />
              </span>
            </a-popover>
            <span class="title-icons" style="border: none;">
              <a-tooltip>
                <template slot="title">
                  刷新
                </template>
                <a-icon
                  type="sync"
                  :spin="popLoading"
                  @click="reColumns"/>
              </a-tooltip>
            </span>
          </div>
        </template>
        <span
          slot="serial"
          slot-scope="text, record, index"
        >{{ index + 1 }}</span>
      </s-table>
    </div>
  </div>

</template>
<script>
import { STable, DropdownChecked } from '@/components'
import { requestBuilder, deepUpdate, getNowDate } from '@/utils/util'
import * as logDataRecordApi from '@/api/system/logDataRecord'
import Vue from 'vue'
import { ORG_ID, PERSON_ID } from '@/store/mutation-types'
import { tableMixin } from '@/utils/tableMixin'

const USER_ORG_ID = Vue.ls.get(ORG_ID)
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)
export default {
  name: 'LogRecordTable',
  components: {
    STable, DropdownChecked
  },
  mixins: [tableMixin],
  data () {
    return {
      visible: false,
      popVisible: false,
      popVisible2: false,
      popLoading: false,
      queryParam: {
        tableName: null,
        keyValue: null,
        ignoreStatus: null
      },
      scroll: {
        y: '35vh',
        scrollToFirstRowOnChange: false
      },
      headRowClassName: (record, index) => {
        return this.selectedHeadRowIndex === index ? 'selected-row row' : 'row'
      },
      // 表格配置
      headColumns: [
        {
          title: '序号',
          dataIndex: 'serial',
          scopedSlots: { customRender: 'serial' },
          align: 'center',
          check: true,
          width: 50
        },
        {
          title: '操作',
          dataIndex: 'operation',
          scopedSlots: { customRender: 'operation' },
          align: 'center',
          check: true,
          width: 100

        }, {
          title: '修改人',
          dataIndex: 'personName',
          align: 'center',
          check: true,
          width: 100

        }, {
          title: '修改部门',
          dataIndex: 'departmentName',
          align: 'center',
          check: true,
          width: 100
        }, {
          title: '修改时间',
          dataIndex: 'createDate',
          align: 'center',
          check: true,
          width: 100
        }

      ],
      clickTimer: null,
      selectedHeadRow: null,
      selectedHeadRowIndex: -1,
      headRowClick: (record, index) => ({
        on: {
          click: () => {
            clearTimeout(this.clickTimer)
            this.clickTimer = setTimeout(() => {
              this.selectedHeadRow = record
              this.selectedHeadRowIndex = index
              this.$refs.body.refresh()
            }, 300)
          }
        }
      }),

      bodyColumns: [
        {
          title: '序号',
          dataIndex: 'serial',
          scopedSlots: { customRender: 'serial' },
          check: true,
          width: 60,
          align: 'center'
        },
        {
          title: '字段',
          dataIndex: 'field',
          check: true,
          ellipsis: true,
          width: 120,
          align: 'center'

        }, {
          title: '修改前',
          dataIndex: 'oldValue',
          check: true,
          width: 120,
          align: 'center'

        }, {
          title: '修改后',
          dataIndex: 'newValue',
          check: true,
          width: 120,
          align: 'center'
        }
      ],
      // 加载数据方法 必须为 Promise 对象
      queryLoadData: parameter => {
        if (this.visible) {
          if (this.isLocalLoad) {
            return this.dataSource
          } else {
            const param = requestBuilder(
              '',
              this.queryParam,
              parameter.pageNo,
              parameter.pageSize
            )
            return logDataRecordApi.queryLog(param).then(res => {
              if (res.code !== '0000') {
                this.$notification.error({
                  message: '系统消息',
                  description: res.message || '查询失败！'
                })
                return Promise.reject(res)
              }
              const report = res.result
              return report
            })
          }
        } else {
          return new Promise((resolve, reject) => {
            resolve([])
          })
        }
      },
      // 加载数据方法 必须为 Promise 对象
      bodyLoadData: parameter => {
        return new Promise((resolve, reject) => {
          if (this.selectedHeadRow) {
            resolve(this.selectedHeadRow.updatedata)
          } else {
            resolve([])
          }
        })
      },
      dataSource: [],
      isLocalLoad: false
    }
  },
  computed: {
    compheadColumns () {
      const arr = this.headColumns.filter(item => {
        return item.check
      })
      return arr
    },
    compbodyColumns () {
      const arr = this.bodyColumns.filter(item => {
        return item.check
      })
      return arr
    }
  },
  created () {
    this.initTable('headColumns', 'logHeadTable')
    this.initTable('bodyColumns', 'logBodyTable')
  },
  methods: {
    reColumns () {
      this.popLoading = true
      this.$store.dispatch('SetTodoTableGroup', { USER_ORG_ID, USER_PERSON_ID })
      setTimeout(() => {
        this.initTable('headColumns', 'logHeadTable')
        this.initTable('bodyColumns', 'logBodyTable')
        this.popLoading = false
      }, 500)
    },
    show (params) {
      this.isLocalLoad = params.isLocalLoad || false
      if (this.isLocalLoad) {
        this.dataSource = params
      } else {
        this.queryParam = deepUpdate(this.queryParam, params)
        this.visible = true
        this.selectedHeadRow = null
        this.selectedHeadRowIndex = -1
        if (this.$refs.head) {
          this.$refs.head.refresh()
        }
        if (this.$refs.body) {
          this.$refs.body.refresh()
        }
      }
    },
    clear () {
      this.isLocalLoad = true
      this.dataSource = []
      this.selectedHeadRow = null
      this.selectedHeadRowIndex = -1
      this.$refs.head.refresh()
      this.$refs.body.refresh()
    },
    handleCancel (e) {
      this.visible = false
      this.dataSource = []
      this.selectedHeadRow = null
      this.selectedHeadRowIndex = -1
    }
  }
}
</script>
<style lang="less" scoped>
 ::v-deep {
   .selected-row{
      td {
        background: #f0f4ff !important;
      }
    }
    .row {
      td {
        height: auto !important;
      }
    }
 }
</style>
