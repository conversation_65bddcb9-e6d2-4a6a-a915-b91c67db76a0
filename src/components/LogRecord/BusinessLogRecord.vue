<template>
  <a-modal
    title="审计"
    :visible="visible"
    :footer="null"
    width="70vw"
    @cancel="handleCancel"
  >
    <a-row :gutter="12">
      <a-col :span="8">

        <s-table
          v-if="columns.length"
          ref="businessTable"
          :data="loadData"
          :scroll="scroll"
          :columns="compcolumns"
          :components="resizeComponents(columns)"
          :immediate="true"
          :bordered="false"
          :showPagination="false"
          :customRow="rowClick"
          :rowClassName="rowClassName"
          :rowKey="(record,index)=>{return record[recordKey]}"
        >
          <!-- 自定义帅选组件 -->
          <template slot="title">
            <div class="tableTitle">
              <a-popover v-model="popVisible" trigger="click">
                <template slot="content">
                  <DropdownChecked
                    :showTitle="false"
                    title="自定义表格排序"
                    :tableId="'BusinessLogRecord'+ tableName"
                    v-model="columns" />
                </template>
                <span class="title-icons" >
                  <a-icon type="setting" theme="filled" />
                </span>
              </a-popover>
              <span class="title-icons" style="border: none;">
                <a-tooltip>
                  <template slot="title">
                    刷新
                  </template>
                  <a-icon type="sync" :spin="popLoading" @click="reColumns"/>
                </a-tooltip>
              </span>
            </div>
          </template>
          <span
            slot="serial"
            slot-scope="text, record, index"
          >{{ index + 1 }}</span>
          <div
            slot="filterDropdown"
            slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
            style="padding: 8px"
          >
            <a-input
              :placeholder="`按${column.title}筛选`"
              v-model="searchText"
              style="width: 188px; margin-bottom: 8px; display: block;"
              @pressEnter="() => handleSearch(column.dataIndex ,false)"
            />
            <a-button
              type="primary"
              size="small"
              style="width: 80px; margin-right: 20px"
              @click="() => handleSearch(column.dataIndex,false)"
            >
              筛选
            </a-button>
            <a-button
              size="small"
              style="width: 80px"
              @click="() => handleSearch(column.dataIndex,true)"
            >
              重置
            </a-button>
          </div>
          <a-icon
            slot="filterIcon"
            slot-scope="filtered"
            type="search"
            :style="{ color: filtered ? '#108ee9' : undefined }"
          />
        </s-table>
      </a-col>
      <a-col :span="16">
        <LogRecordTable ref="LogRecordTable" />
        <div v-if="hasFile">
          <label class="title">附件：</label>
          <FileLogRecordTable ref="FileLogRecordTable" />
        </div>
      </a-col>
    </a-row>

  </a-modal>
</template>
<script>
import { STable, DropdownChecked } from '@/components'
import LogRecordTable from './LogRecordTable'
import { requestBuilder, deepUpdate, getNowDate } from '@/utils/util'
import * as logDataRecordApi from '@/api/system/logDataRecord'
import FileLogRecordTable from './FileLogRecordTable'
import Vue from 'vue'
import { ORG_ID, PERSON_ID } from '@/store/mutation-types'

import { tableMixin } from '@/utils/tableMixin'

const USER_ORG_ID = Vue.ls.get(ORG_ID)
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)

export default {
  name: 'BusinessLogRecord',

  mixins: [tableMixin],
  components: {
    STable, LogRecordTable, FileLogRecordTable, DropdownChecked
  },
  data () {
    return {
      popLoading: false,
      popVisible: false,
      visible: false,
      hasFile: false,
      queryParam: null,
      scroll: {
        y: '70vh',
        x: '100%'
      },
      rowClassName: (record, index) => {
        return this.selectedRowIndex === index ? 'selected-row row' : 'row'
      },
      // 表格配置
      columns: [ ],
      clickTimer: null,
      selectedRow: null,
      selectedRowIndex: -1,
      rowClick: (record, index) => ({
        on: {
          click: () => {
            clearTimeout(this.clickTimer)
            this.clickTimer = setTimeout(() => {
              this.selectedRow = record
              this.selectedRowIndex = index
              this.$refs.LogRecordTable.show({
                tableName: this.tableName,
                keyValue: record[this.recordKey]
              })
              if (this.hasFile && this.$refs.FileLogRecordTable) {
                if (this.recordFileKey) {
                  this.$refs.FileLogRecordTable.show({
                    tableName: 'file',
                    keyValue: record[this.recordFileKey]
                  })
                } else if (this.fileKeyValue) {
                  this.$refs.FileLogRecordTable.show({
                    tableName: 'file',
                    keyValue: this.fileKeyValue
                  })
                }
              }
            }, 300)
          }
        }
      }),
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        if (this.visible) {
          if (this.isLocalLoad) {
            return this.dataSource
          } else {
            const param = requestBuilder(
              '',
              this.queryParam
            )
            return logDataRecordApi.queryBusiness(this.url, param).then(res => {
              if (res.code !== '0000') {
                this.$notification.error({
                  message: '系统消息',
                  description: res.message || '查询失败！'
                })
                return Promise.reject(res)
              }
              const report = res.result.data
              return report
            })
          }
        } else {
          return new Promise((resolve, reject) => {
            resolve([])
          })
        }
      },
      dataSource: [],
      isLocalLoad: false,
      recordKey: null,
      recordFileKey: null,
      fileKeyValue: null,
      tableName: null,
      searchText: ''
    }
  },
  computed: {
    compcolumns () {
      const arr = this.columns.filter(item => {
        return item.check
      })
      return arr
    }
  },
  created () {

  },
  methods: {
    reColumns () {
      this.popLoading = true
      this.$store.dispatch('SetTodoTableGroup', { USER_ORG_ID, USER_PERSON_ID })
      setTimeout(() => {
        this.initTable('columns', 'BusinessLogRecord' + this.tableName)
        this.popLoading = false
      }, 500)
    },

    handleSearch (dataIndex, isReset) {
      if (isReset) {
        this.searchText = null
      }
      this.selectedRow = null
      this.selectedRowIndex = -1
      this.$refs.LogRecordTable.clear()
      this.queryParam[dataIndex] = this.searchText
      this.$refs.businessTable.refresh()
    },
    showModal (params) {
      this.$nextTick(() => {
        this.visible = true
        this.hasFile = params.hasFile || false
        this.isLocalLoad = params.isLocalLoad || false
        if (this.isLocalLoad) {
          const { dataSource, columns, recordKey, tableName, recordFileKey, fileKeyValue } = params
          this.dataSource = dataSource
          this.columns = columns
          this.recordKey = recordKey
          this.recordFileKey = recordFileKey
          this.fileKeyValue = fileKeyValue
          this.tableName = tableName
        } else {
          const { url, queryParam, columns, recordKey, tableName, recordFileKey, fileKeyValue } = params
          this.url = url
          this.queryParam = queryParam
          this.columns = columns
          this.recordKey = recordKey
          this.recordFileKey = recordFileKey
          this.fileKeyValue = fileKeyValue
          this.tableName = tableName
        }
        this.initTable('columns', 'BusinessLogRecord' + this.tableName)
        if (this.$refs.businessTable) {
          this.$refs.businessTable.refresh()
        }
      })
    },
    handleCancel (e) {
      this.visible = false
      this.dataSource = []
      this.$refs.LogRecordTable.clear()
      if (this.$refs.FileLogRecordTable) {
        this.$refs.FileLogRecordTable.clear()
      }
    }
  }
}
</script>
<style lang="less" scoped>
.title {
  font-size: 15px;
    line-height: 3;
    font-weight: 600;
}
 ::v-deep {
   .selected-row{
      td {
        background: #f0f4ff !important;
      }
    }
    .row {
      td {
        height: auto !important;
      }
    }
 }
</style>
