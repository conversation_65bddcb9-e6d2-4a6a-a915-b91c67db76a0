<template>
  <component
    :is="$name"
    v-bind="$props"
  />
</template>

<script>
import Library from './library'

export default {
  name: 'SFormItem',
  components: {
    ...Library
  },
  props: {
    extender: {
      type: Object,
      default: function () {
        return {}
      }
    },
    group: {
      type: Object,
      default: function () {
        return {}
      }
    },
    row: {
      type: Object,
      default: function () {
        return {}
      }
    },
    col: {
      type: Object,
      default: function () {
        return {}
      }
    },
    item: {
      type: Object,
      default: function () {
        return {}
      }
    },
    form: {
      type: Object,
      default: function () {
        return {}
      }
    },
    type: {
      type: String,
      default: ''
    },
    field: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      default: ''
    },
    binds: {
      type: Object,
      default: function () {
        return {}
      }
    },
    events: {
      type: Object,
      default: function () {
        return {}
      }
    },
    decorator: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  computed: {
    $name () {
      return this.item.type.replace(/^A(.+)/, 'S$1')
    }
  }
}
</script>
