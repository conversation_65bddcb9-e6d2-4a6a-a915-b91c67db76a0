<template>
  <div class="s-form-container">
    <a-spin :spinning="spinning">
      <a-form
        :form="sForm"
        v-bind="handleFormBind(form)"
      >
        <slot
          name="before"
          :options="options"
          :form="form"
        />
        <div
          class="s-form-group-container"
          v-if="handleRenderGroup(groupItem, form)"
          v-show="handleShowGroup(groupItem, form)"
          v-for="(groupItem, groupIndex) of sGroups"
          v-bind="handleGroupBind(groupItem, form)"
          :key="groupIndex"
        >
          <div
            v-if="!!groupItem.title || !!groupItem.slot"
            class="s-form-group-item-header"
          >
            <slot
              :name="groupItem.slot"
              :options="options"
              :groupIndex="groupIndex"
              :groupData="groupItem"
              :group="groupItem"
              :index="groupIndex"
              :item="groupItem"
              :form="form"
            >
              <div class="s-form-group-item-header-title">{{ groupItem.title }}</div>
            </slot>
          </div>
          <div class="s-form-group-item-content">
            <div
              v-if="handleRenderRowItem(rowItem, groupItem, form)"
              v-show="handleShowRowItem(rowItem, groupItem, form)"
              v-for="(rowItem, rowIndex) in groupItem.items"
              :key="rowIndex"
            >
              <a-row v-bind="handleRowGridBind(rowItem, groupItem, form)">
                <a-col
                  v-if="handleRenderColItem(colItem, rowItem, groupItem, form)"
                  v-show="handleShowColItem(colItem, rowItem, groupItem, form)"
                  v-bind="handleColGridBind(colItem, rowItem, groupItem, form)"
                  v-for="(colItem, colIndex) of rowItem.items"
                  :key="colIndex"
                >
                  <div
                    class="s-form-group-item-template"
                    v-bind="handleColBind(colItem, rowItem, groupItem, form)"
                  >
                    <slot
                      :name="colItem.slot"
                      :type="colItem.type"
                      :field="colItem.field"
                      :label="colItem.label"
                      :decorator="colItem.decorator"
                      :binds="handleTemplateBind(colItem, rowItem, groupItem, form)"
                      :events="handleTemplateEvent(colItem, rowItem, groupItem, form)"
                      :extender="handleTemplateExtender(colItem, rowItem, groupItem, form)"
                      :groupIndex="groupIndex"
                      :groupData="groupItem"
                      :group="groupItem"
                      :rowIndex="rowIndex"
                      :rowData="rowItem"
                      :row="rowItem"
                      :colIndex="colIndex"
                      :colData="colItem"
                      :col="colItem"
                      :index="colIndex"
                      :item="colItem"
                      :form="form"
                    >
                      <s-form-item
                        :type="colItem.type"
                        :field="colItem.field"
                        :label="colItem.label"
                        :decorator="colItem.decorator"
                        :binds="handleTemplateBind(colItem, rowItem, groupItem, form)"
                        :events="handleTemplateEvent(colItem, rowItem, groupItem, form)"
                        :extender="handleTemplateExtender(colItem, rowItem, groupItem, form)"
                        :group="groupItem"
                        :row="rowItem"
                        :col="colItem"
                        :item="colItem"
                        :form="form"
                      />
                    </slot>
                  </div>
                </a-col>
              </a-row>
            </div>
          </div>
        </div>
        <slot
          name="after"
          :options="options"
          :form="form"
        />
      </a-form>
    </a-spin>
  </div>
</template>

<script>
import Utils from './utils'
import Initial from './initial'
import SFormItem from './FormItem'

export default {
  name: 'SForm',
  components: {
    SFormItem
  },
  props: {
    grid: {
      type: Object,
      default: function () {
        return {}
      }
    },
    watch: {
      type: Object,
      default: function () {
        return {}
      }
    },
    attrs: {
      type: Object,
      default: function () {
        return {}
      }
    },
    groups: {
      type: Array,
      default: function () {
        return []
      }
    },
    options: {
      type: Object,
      default: function () {
        return {}
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    spinning: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      sForm: this.$form.createForm(this, this.handleWatch()),
      sGroups: [],
      sItems: []
    }
  },
  computed: {
    form () {
      return this
    }
  },
  methods: {

    handleWatch () {
      if (Utils.isObject(this.watch)) {
        for (const key of Object.keys(this.watch)) {
          if (Utils.isFunction(this.watch[key])) {
            this.watch[key] = this.watch[key].bind(this)
          }
        }
      }
      return {
        ...this.watch,
        onFieldsChange: (...rest) => {
          if (Utils.isFunction(this.watch.onFieldsChange)) {
            this.watch.onFieldsChange(...rest)
          }
        }
      }
    },

    handleGroups () {
      this.sGroups = []
      this.sItems = []
      let group = {
        type: 'AGroup',
        slot: '',
        field: '',
        title: '',
        attrs: {
          disabled: false,
          readonly: false
        },
        items: [],
        grid: {}
      }
      // console.log('#######groups######ddd#', this.groups)
      for (const node of this.groups) {
        if (Utils.isObject(node)) {
          if (node.type === 'AGroup') {
            const disabled = Utils.isObject(node.attrs) && node.attrs.disabled === true
            const readonly = Utils.isObject(node.attrs) && node.attrs.readonly === true
            const attrs = { disabled, readonly }
            group = {
              type: 'AGroup',
              slot: (Utils.isString(node.slot) && node.slot) || '',
              field: (Utils.isString(node.field) && node.field) || '',
              title: (Utils.isString(node.title) && node.title) || '',
              attrs: Utils.isObject(node.attrs) ? { ...node.attrs, ...attrs } : { ...attrs },
              items: Utils.isArray(node.items) ? [...node.items] : [],
              grid: Utils.isObject(node.grid) ? { ...node.grid } : {},
              render: node.render,
              show: node.show
            }
            this.sGroups.push(group)
          } else {
            if (!this.sGroups.includes(group)) {
              this.sGroups.push(group)
            }
            group.items.push(node)
          }
        }
      }
      for (const item of this.sGroups) {
        this.handleEverys(item)
      }
      return this.sGroups
    },
    handleEverys (group) {
      let row = {
        grid: {},
        items: []
      }
      const oldItems = group.items || []
      const newItems = (group.items = [])
      for (const oldItem of oldItems) {
        if (Utils.isObject(oldItem)) {
          if (Utils.isObject(oldItem.grid)) {
            if (oldItem.grid.newline === true) {
              row = {
                grid: {},
                items: []
              }
            }
          }
          if (!newItems.includes(row)) {
            newItems.push(row)
          }
          if (Utils.isObject(Initial[oldItem.type])) {
            const col = Utils.toDeepClone({}, Initial[oldItem.type], oldItem)
            const item = Utils.toDeepClone({}, Initial[oldItem.type], oldItem)
            this.sItems.push(
              Object.assign(item, {
                group,
                row,
                col
              })
            )
            row.items.push(col)
          }
        }
      }
      return group
    },
    handleCompute (items) {
      if (!Utils.isArray(items)) {
        items = this.sItems
      }
      const form = this.form
      const options = this.options
      for (const item of items) {
        const col = item.col
        const row = item.row
        const group = item.group
        const field = item.field
        const configs = options[field]

        if (![true, false].includes(col.render)) {
          if (Utils.isObject(configs)) {
            Utils.isFunction(configs.handleRender)
              ? (item.render = configs.handleRender(col, { col, row, group, form, Utils }) !== false)
              : configs.handleRender !== undefined
                ? (item.render = configs.handleRender !== false)
                : (item.render = undefined)
          }
          if (item.render === undefined) {
            Utils.isFunction(col.render)
              ? (item.render = col.render(col, { col, row, group, form, Utils }) !== false)
              : (item.render = col.render !== false)
          }
        }

        if (![true, false].includes(col.show)) {
          if (Utils.isObject(configs)) {
            Utils.isFunction(configs.handleShow)
              ? (item.show = configs.handleShow(col, { col, row, group, form, Utils }) !== false)
              : configs.handleShow !== undefined
                ? (item.show = configs.handleShow !== false)
                : (item.show = undefined)
          }
          if (item.show === undefined) {
            Utils.isFunction(col.show)
              ? (item.show = col.show(col, { col, row, group, form, Utils }) !== false)
              : (item.show = col.show !== false)
          }
        }

        if (item.render === undefined) {
          item.render = col.render
        }

        if (item.show === undefined) {
          item.show = col.show
        }
      }
      return items
    },
    handleFormBind (form) {
      const disabled = [form.disabled].includes(true)
      const readonly = [form.readonly].includes(true)
      return {
        ...form.attrs,
        'disabled': readonly || disabled,
        'off-disabled': readonly && !disabled
      }
    },
    handleGroupBind (group, form) {
      const disabled = [form.disabled, group.attrs.disabled].includes(true)
      const readonly = [form.readonly, group.attrs.readonly].includes(true)
      return {
        ...group.attrs,
        'disabled': readonly || disabled,
        'off-disabled': readonly && !disabled
      }
    },
    handleShowGroup (group, form) {
      const field = group.field
      const options = this.options
      const configs = options[field]

      if (group.show === false) {
        return false
      }

      if (group.show === undefined) {
        if (Utils.isObject(configs) && Utils.isFunction(configs.handleShow)) {
          if (configs.handleShow(group, { group, form, Utils }) === false) {
            return false
          }
        }
      }

      if (Utils.isFunction(group.show)) {
        if (group.show(group, { group, form, Utils }) === false) {
          return false
        }
      }

      this.handleCompute(this.sItems)

      for (const row of group.items) {
        if (Utils.isObject(row)) {
          if (Utils.isArray(row.items)) {
            for (const col of row.items) {
              if (Utils.isObject(col)) {
                if (this.sItems.some(item => item.field === col.field)) {
                  if (this.sItems.find(item => item.field === col.field).show === true) {
                    return true
                  }
                }
              }
            }
          }
        }
      }
      return false
    },
    handleRenderGroup (group, form) {
      const field = group.field
      const options = this.options
      const configs = options[field]

      if (group.render === false) {
        return false
      }

      if (group.render === undefined) {
        if (Utils.isObject(configs) && Utils.isFunction(configs.handleRender)) {
          if (configs.handleRender(group, { group, form, Utils }) === false) {
            return false
          }
        }
      }

      if (Utils.isFunction(group.render)) {
        if (group.render(group, { group, form, Utils }) === false) {
          return false
        }
      }

      this.handleCompute(this.sItems)

      for (const row of group.items) {
        if (Utils.isObject(row)) {
          if (Utils.isArray(row.items)) {
            for (const col of row.items) {
              if (Utils.isObject(col)) {
                if (this.sItems.some(item => item.field === col.field)) {
                  if (this.sItems.find(item => item.field === col.field).render === true) {
                    return true
                  }
                }
              }
            }
          }
        }
      }
      return false
    },
    handleRowGridBind (row, group, form) {
      const rowAttr = {}
      const setAttr = grid => {
        const align = grid.align
        const gutter = grid.gutter
        const justify = grid.justify
        const type = grid.type
        if (['top', 'middle', 'bottom'].includes(align)) {
          rowAttr.align = align
        }
        if (Utils.isObject(gutter) || Utils.isArray(gutter) || Utils.isNumber(gutter)) {
          rowAttr.gutter = gutter
        }
        if (['start', 'end', 'center', 'space-around', 'space-between'].includes(justify)) {
          rowAttr.justify = justify
        }
        if (['flex'].includes(type)) {
          rowAttr.type = type
        }
      }
      if (Utils.isObject(form) && Utils.isObject(form.grid)) {
        setAttr(form.grid)
      }
      if (Utils.isObject(group) && Utils.isObject(group.grid)) {
        setAttr(group.grid)
      }
      if (Utils.isObject(row) && Utils.isObject(row.grid)) {
        setAttr(row.grid)
      }
      return rowAttr
    },
    handleShowRowItem (row, group, form) {
      for (const col of row.items) {
        if (Utils.isObject(col)) {
          if (this.sItems.some(item => item.field === col.field)) {
            if (this.sItems.find(item => item.field === col.field).show === true) {
              return true
            }
          }
        }
      }
      return false
    },
    handleRenderRowItem (row, group, form) {
      for (const col of row.items) {
        if (Utils.isObject(col)) {
          if (this.sItems.some(item => item.field === col.field)) {
            if (this.sItems.find(item => item.field === col.field).render === true) {
              return true
            }
          }
        }
      }
      return false
    },
    handleColBind (col, row, group, form) {
      const disabled = [
        form.disabled,
        group.attrs.disabled,
        Utils.isFunction(col.attrs.disabled)
          ? col.attrs.disabled(col, { col, row, group, form, Utils })
          : col.attrs.disabled
      ].includes(true)

      const readonly = [
        form.readonly,
        group.attrs.readonly,
        Utils.isFunction(col.attrs.readonly)
          ? col.attrs.readonly(col, { col, row, group, form, Utils })
          : col.attrs.readonly
      ].includes(true)

      return {
        'disabled': readonly || disabled,
        'off-disabled': readonly && !disabled
      }
    },
    handleColGridBind (col, row, group, form) {
      const colAttr = {}
      const setAttr = grid => {
        const flex = grid.flex
        const offset = grid.offset
        const order = grid.order
        const pull = grid.pull
        const push = grid.push
        const span = grid.span
        const xs = grid.xs
        const sm = grid.sm
        const md = grid.md
        const lg = grid.lg
        const xl = grid.xl
        const xxl = grid.xxl
        if (Utils.isNumber(flex) || Utils.isString(flex)) {
          colAttr.flex = flex
        }
        if (Utils.isNumber(offset)) {
          colAttr.offset = offset
        }
        if (Utils.isNumber(order)) {
          colAttr.order = order
        }
        if (Utils.isNumber(pull)) {
          colAttr.pull = pull
        }
        if (Utils.isNumber(push)) {
          colAttr.push = push
        }
        if (Utils.isNumber(span)) {
          colAttr.span = span
        }
        if (Utils.isNumber(xs) || Utils.isObject(xs)) {
          colAttr.xs = xs
        }
        if (Utils.isNumber(sm) || Utils.isObject(sm)) {
          colAttr.sm = sm
        }
        if (Utils.isNumber(md) || Utils.isObject(md)) {
          colAttr.md = md
        }
        if (Utils.isNumber(lg) || Utils.isObject(lg)) {
          colAttr.lg = lg
        }
        if (Utils.isNumber(xl) || Utils.isObject(xl)) {
          colAttr.xl = xl
        }
        if (Utils.isNumber(xxl) || Utils.isObject(xxl)) {
          colAttr.xxl = xxl
        }
      }
      if (Utils.isObject(form) && Utils.isObject(form.grid)) {
        setAttr(form.grid)
      }
      if (Utils.isObject(group) && Utils.isObject(group.grid)) {
        setAttr(group.grid)
      }
      if (Utils.isObject(row) && Utils.isObject(row.grid)) {
        setAttr(row.grid)
      }
      if (Utils.isObject(col) && Utils.isObject(col.grid)) {
        setAttr(col.grid)
      }
      return colAttr
    },
    handleShowColItem (col, row, group, form) {
      if (Utils.isObject(col)) {
        if (this.sItems.some(item => item.field === col.field)) {
          if (this.sItems.find(item => item.field === col.field).show === true) {
            return true
          }
        }
      }
      return false
    },
    handleRenderColItem (col, row, group, form) {
      if (Utils.isObject(col)) {
        if (this.sItems.some(item => item.field === col.field)) {
          if (this.sItems.find(item => item.field === col.field).render === true) {
            return true
          }
        }
      }
      return false
    },
    handleTemplateBind (col, row, group, form) {
      const field = col.field
      const options = this.options || {}
      const configs = options[field] || {}
      const attrs = {}

      if (!Utils.isObject(configs.handleAttrs)) {
        configs.handleAttrs = {}
      }

      const disabled = [
        form.disabled,
        group.attrs.disabled,
        configs.handleAttrs.disabled,
        Utils.isFunction(col.attrs.disabled)
          ? col.attrs.disabled(col, { col, row, group, form, Utils })
          : col.attrs.disabled
      ].includes(true)

      const readonly = [
        form.readonly,
        group.attrs.readonly,
        configs.handleAttrs.readonly,
        Utils.isFunction(col.attrs.readonly)
          ? col.attrs.readonly(col, { col, row, group, form, Utils })
          : col.attrs.readonly
      ].includes(true)

      if (col.attrs) {
        Object.assign(attrs, {
          ...col.attrs,
          ...configs.handleAttrs
        })
      }

      return Object.assign(attrs, {
        disabled: readonly || disabled
      })
    },
    handleTemplateEvent (col, row, group, form) {
      const field = col.field
      const options = this.options || {}
      const configs = options[field] || {}
      const events = { ...col.events }

      if (Utils.isObject(configs) && Utils.isObject(configs.handleEvents)) {
        Object.assign(events, {
          ...configs.handleEvents
        })
      }

      return events
    },
    handleTemplateExtender (col, row, group, form) {
      const field = col.field
      const options = this.options || {}
      const configs = options[field] || {}
      const extender = { ...configs }

      if (Utils.isObject(configs) && Utils.isFunction(configs.selectOptions)) {
        Object.assign(extender, {
          selectOptions: configs.selectOptions(col, { col, row, group, form, Utils })
        })
      }

      return extender
    },
    getFieldValue (field, base = {}) {
      if (Utils.isString(field) && !!field.trim()) {
        // 初始化
        const form = this.form
        const sForm = this.sForm
        const sItems = this.sItems
        const fieldsValue = { [field]: sForm.getFieldValue(field) }

        // 遍历处理
        if (sItems.some(item => item.field === field)) {
          const item = sItems.find(item => item.field === field)
          const itemDefault = item.default
          const itemTransfer = item.transfer
          const baseValue = base[field]
          let outputDefValue

          if (fieldsValue[field] === undefined) {
            if (Utils.isObject(itemDefault)) {
              const col = item.col
              const row = item.row
              const group = item.group
              Utils.isFunction(itemDefault.output)
                ? (outputDefValue = itemDefault.output({ col, row, group, form, Utils }))
                : (outputDefValue = itemDefault.output)
            }

            if (outputDefValue !== undefined) {
              fieldsValue[field] = outputDefValue
            }

            if (baseValue !== undefined) {
              fieldsValue[field] = baseValue
            }
          }

          if (Utils.isObject(itemTransfer)) {
            if (Utils.isFunction(itemTransfer.output)) {
              const col = item.col
              const row = item.row
              const group = item.group
              fieldsValue[field] = itemTransfer.output(fieldsValue[field], { col, row, group, form, Utils })
            }
          }
        }

        // 返回数据
        return fieldsValue[field]
      }
    },
    getFieldsValue (fields = [], base = {}) {
      // 兼容处理
      if (Utils.isObject(fields)) {
        base = fields
        fields = undefined
      }

      // 初始化
      let fieldsValue = {}
      const form = this.form
      const sForm = this.sForm
      const sItems = this.sItems

      // 预处理
      Utils.isNotEmptyArray(fields)
        ? (fieldsValue = sForm.getFieldsValue(fields))
        : (fieldsValue = sForm.getFieldsValue())

      // 遍历处理
      for (const field of Object.keys(fieldsValue)) {
        if (sItems.some(item => item.field === field)) {
          const item = sItems.find(item => item.field === field)
          const itemField = item.field
          const itemDefault = item.default
          const itemTransfer = item.transfer
          const baseValue = base[itemField]
          let outputDefValue

          if (fieldsValue[field] === undefined) {
            if (Utils.isObject(itemDefault)) {
              const col = item.col
              const row = item.row
              const group = item.group
              Utils.isFunction(itemDefault.output)
                ? (outputDefValue = itemDefault.output({ col, row, group, form, Utils }))
                : (outputDefValue = itemDefault.output)
            }

            if (outputDefValue !== undefined) {
              fieldsValue[itemField] = outputDefValue
            }

            if (baseValue !== undefined) {
              fieldsValue[itemField] = baseValue
            }
          }

          if (Utils.isObject(itemTransfer)) {
            if (Utils.isFunction(itemTransfer.output)) {
              const col = item.col
              const row = item.row
              const group = item.group
              fieldsValue[itemField] = itemTransfer.output(fieldsValue[itemField], { col, row, group, form, Utils })
            }
          }
        }
      }

      // 返回数据
      return fieldsValue
    },
    setFieldsValue (data = {}, base = {}) {
      if (Utils.isObject(data)) {
        // 初始化
        let fieldsKeys = []
        const fieldsValue = {}
        const form = this.form
        const sForm = this.sForm
        const sItems = this.sItems
        const sFilter = sItems.filter(item => item.field)
        const sKeys = sFilter.map(item => item.field)

        // 预处理
        Utils.isNotEmptyObject(data)
          ? (fieldsKeys = Object.keys(data).filter(field => sKeys.includes(field)))
          : (fieldsKeys = sFilter.map(item => item.field))

        // 遍历处理
        for (const field of fieldsKeys) {
          if (sItems.some(item => item.field === field)) {
            const item = sItems.find(item => item.field === field)
            const itemField = item.field
            const itemDefault = item.default
            const itemTransfer = item.transfer
            const baseValue = base[itemField]
            const dataValue = data[itemField]
            let inputDefValue

            // 未定义
            fieldsValue[itemField] = undefined

            if (Utils.isObject(itemDefault)) {
              const col = item.col
              const row = item.row
              const group = item.group
              Utils.isFunction(itemDefault.input)
                ? (inputDefValue = itemDefault.input({ col, row, group, form, Utils }))
                : (inputDefValue = itemDefault.input)
            }

            if (inputDefValue !== undefined) {
              fieldsValue[itemField] = inputDefValue
            }

            if (baseValue !== undefined) {
              fieldsValue[itemField] = baseValue
            }

            if (dataValue !== undefined) {
              fieldsValue[itemField] = dataValue
            }

            if (Utils.isObject(itemTransfer)) {
              if (Utils.isFunction(itemTransfer.input)) {
                const col = item.col
                const row = item.row
                const group = item.group
                fieldsValue[itemField] = itemTransfer.input(fieldsValue[itemField], { col, row, group, form, Utils })
              }
            }
          }
        }

        sForm.setFieldsValue({
          ...fieldsValue
        })
      }
    },
    validateFieldsAndScroll () {
      this.sForm.validateFieldsAndScroll(...arguments)
    },
    validateFields () {
      this.sForm.validateFields(...arguments)
    },
    resetFields () {
      this.sForm.resetFields(...arguments)
    }
  },
  watch: {
    groups: {
      handler (groups) {
        if (Utils.isArray(groups)) {
          this.handleGroups()
        }
      },
      immediate: true,
      deep: true
    }

  }
}
</script>

<style lang="less" scoped>
.s-form-container {
  width: 100%;
  height: auto;
  .s-form-group-container {
    width: 100%;
    height: auto;
    margin-bottom: 20px;
    .s-form-group-item-header {
      display: flex;
      width: 100%;
      height: 30px;
      padding-left: 5px;
      margin-bottom: 10px;
      line-height: 30px;
      font-size: 13px;
      color: #303133;
      border-bottom: dashed 1px #cfcfcf;
      & > .s-form-group-item-header-title {
        flex: 1 1 auto;
        padding-left: 8px;
        line-height: 32px;
      }
    }
    .s-form-group-item-content {
      padding: 0 3px;
      margin-bottom: 10px;
    }
    .s-form-group-item-template {
      width: 100%;
      height: auto;
      min-height: 45px;
    }
  }
  ::v-deep {
    .ant-form-horizontal {
      .ant-form-item {
        display: flex;
        margin: 2px 0 16.5px;
        padding: 0;
        & > .ant-form-item-label {
          display: inline-block;
          flex: 0 0 auto;
          width: 75px;
          margin-right: 2px;
          line-height: 27px;
        }
        & > .ant-form-item-control-wrapper {
          display: inline-block;
          flex: 1 1 auto;
          vertical-align: middle;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          .ant-form-item-control {
            line-height: 27px;
            .ant-form-item-children {
              .ant-calendar-picker {
                width: 100%;
                min-width: 0 !important;
              }
            }
            .ant-form-explain {
              width: 100%;
              height: 20px;
              min-height: 20px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
        &.ant-form-item-with-help {
          margin-bottom: 0;
        }
      }
    }
    .ant-form-vertical {
      .ant-form-item {
        display: flex;
        flex-flow: row wrap;
        margin: 2px 0 14.5px;
        padding: 0;
        & > .ant-form-item-label {
          display: inline-block;
          flex: 0 0 auto;
          width: 100%;
          padding: 0;
          margin-right: 2px;
          line-height: 27px;
        }
        & > .ant-form-item-control-wrapper {
          display: inline-block;
          flex: 1 1 auto;
          vertical-align: middle;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          .ant-form-item-control {
            line-height: 27px;
            .ant-form-item-children {
              .ant-calendar-picker {
                width: 100%;
                min-width: 0 !important;
              }
            }
            .ant-form-explain {
              width: 100%;
              height: 18px;
              min-height: 18px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
        &.ant-form-item-with-help {
          margin-bottom: 0;
        }
      }
    }
    .ant-form-inline {
      .ant-form-item {
        display: flex;
        margin: 2px 0 16.5px;
        padding: 0;
        & > .ant-form-item-label {
          display: inline-block;
          flex: 0 0 auto;
          width: 75px;
          margin-right: 2px;
          line-height: 27px;
        }
        & > .ant-form-item-control-wrapper {
          display: inline-block;
          flex: 1 1 auto;
          vertical-align: middle;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          .ant-form-item-control {
            line-height: 27px;
            .ant-form-item-children {
              .ant-calendar-picker {
                width: 100%;
                min-width: 0 !important;
              }
            }
            .ant-form-explain {
              width: 100%;
              height: 20px;
              min-height: 20px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
        &.ant-form-item-with-help {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
