import {
  isString,
  isObject,
  isRegExp,
  isFunction,
  toDeepClone
} from './utils'

// 校验器
export const validator = {
  number (options) {
    const rule = {}
    const pattern = options.pattern
    const message = options.message
    const validator = options.validator

    if (isObject(options)) {
      Object.assign(rule, options)
    }

    if (!isRegExp(pattern)) {
      Object.assign(rule, {
        pattern: /^[+-]?\d+\.?\d*$/i
      })
    }

    if (isString(validator)) {
      rule.validator = (rule, value, callback) => {
        if (!value && value !== 0) {
          return callback(new Error(message || '该项必填'))
        }
        if (!rule.pattern.test(value)) {
          return callback(new Error(validator || message || '格式有误'))
        }
        return callback()
      }
      Object.assign(rule, { message: '' })
    }
    return rule
  }
}

// 扩展器
export const extender = {
  groups (groups = [], fn = null, options = {}) {
    if (isFunction(fn)) {
      let region = { type: 'AGroup' }
      const clones = toDeepClone(groups)
      const entries = groups.entries()
      for (const [index, group] of entries) {
        group.type === 'AGroup'
          ? region = toDeepClone(group)
          : region = toDeepClone(region)

        const result = fn(group, index, clones, region)

        if (isObject(result)) {
          const clone = clones[index]
          const part = toDeepClone(result)
          Object.assign(group, result)
          Object.assign(clone, part)
        }
      }
    }
    return groups
  },
  options (groups = [], fn = null, options = {}) {
    if (isFunction(fn)) {
      let region = { type: 'AGroup' }
      const clones = toDeepClone(groups)
      const entries = groups.entries()
      for (const [index, group] of entries) {
        group.type === 'AGroup'
          ? region = toDeepClone(group)
          : region = toDeepClone(region)

        const result = fn(group, index, clones, region)

        if (isObject(result)) {
          if (group.field) {
            options[group.field] = Object.assign(
              options[group.field] || {},
              result
            )
          }
        }
      }
    }
    return options
  }
}
