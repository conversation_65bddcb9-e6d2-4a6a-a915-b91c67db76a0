import moment from 'moment'

export default {
  AInput: {
    type: 'AInput',
    slot: '',
    field: '',
    label: '',
    decorator: {},
    attrs: {},
    events: {},
    default: {
      input: '',
      ouput: ''
    },
    transfer: {
      input (value, { Utils }) {
        return value !== undefined && value !== null ? value : ''
      },
      output (value, { Utils }) {
        return value !== undefined && value !== null ? value : ''
      }
    },
    grid: {}
  },

  ASelect: {
    type: 'ASelect',
    slot: '',
    field: '',
    label: '',
    decorator: {},
    attrs: {},
    events: {},
    default: {
      input ({ Utils, col }) {
        return ['multiple', 'combobox'].includes(col.attrs.mode) ? [] : undefined
      },
      output: ''
    },
    transfer: {
      input (value, { Utils, col }) {
        if (['multiple', 'combobox'].includes(col.attrs.mode)) {
          if (Utils.isString(value)) {
            return value.trim() ? value.trim().split(',') : []
          }
          return Utils.isArray(value) ? value : []
        }
        return value
      },
      output (value, { Utils, col }) {
        if (['multiple', 'combobox'].includes(col.attrs.mode)) {
          if (Utils.isArray(value)) {
            return value.join(',')
          }
          return Utils.isString(value) ? value.trim() : ''
        }
        return value
      }
    },
    grid: {}
  },

  ATreeSelect: {
    type: 'ATreeSelect',
    slot: '',
    field: '',
    label: '',
    decorator: {},
    attrs: {},
    events: {},
    default: {
      input ({ Utils, col }) {
        return col.attrs.multiple === true ? [] : undefined
      },
      output: ''
    },
    transfer: {
      input (value, { Utils, col }) {
        if (col.attrs.multiple === true) {
          if (Utils.isString(value)) {
            return value.trim() ? value.trim().split(',') : []
          }
          return Utils.isArray(value) ? value : []
        }
        return value
      },
      output (value, { Utils, col }) {
        if (col.attrs.multiple === true) {
          if (Utils.isArray(value)) {
            return value.join(',')
          }
          return Utils.isString(value) ? value.trim() : ''
        }
        return value
      }
    },
    grid: {}
  },

  AMonthPicker: {
    type: 'AMonthPicker',
    slot: '',
    field: '',
    label: '',
    decorator: {},
    attrs: {},
    events: {},
    default: {
      input: null,
      output: ''
    },
    transfer: {
      input (value, { Utils, col }) {
        if (value === undefined || Utils.isString(value)) {
          if (value) {
            return moment(value, col.attrs.format || 'YYYY-MM')
          }
          return null
        }
        return value
      },
      output (value, { Utils, col }) {
        if (Utils.isObject(value)) {
          return value.format(col.attrs.valueFormat || col.attrs.format || 'YYYY-MM')
        }
        return value !== null && value !== undefined ? value : ''
      }
    },
    grid: {}
  },

  ARangePicker: {
    type: 'ARangePicker',
    slot: '',
    field: '',
    label: '',
    decorator: {},
    attrs: {},
    events: {},
    default: {
      input: [],
      output: []
    },
    transfer: {
      input (value, { Utils, col }) {
        if (Utils.isArray(value)) {
          return value
            .filter(every => Utils.isString(every))
            .map(every => Utils.isString(every)
              ? moment(every, col.attrs.format || 'YYYY-MM-DD')
              : null
            )
        }
        return []
      },
      output (value, { Utils, col }) {
        if (Utils.isArray(value)) {
          return value.map(every =>
            Utils.isObject(every)
              ? every.format(col.attrs.valueFormat || col.attrs.format || 'YYYY-MM-DD')
              : ''
          )
        }
        return []
      }
    },
    grid: {}
  },

  ADatePicker: {
    type: 'ADatePicker',
    slot: '',
    field: '',
    label: '',
    decorator: {},
    attrs: {},
    events: {},
    default: {
      input: null,
      output: ''
    },
    transfer: {
      input (value, { Utils, col }) {
        if (value === undefined || Utils.isString(value)) {
          if (value) {
            return moment(value, col.attrs.format || (col.attrs.showTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'))
          }
          return null
        }
        return value
      },
      output (value, { Utils, col }) {
        if (Utils.isObject(value)) {
          return value.format(
            col.attrs.valueFormat || col.attrs.format || (col.attrs.showTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD')
          )
        }
        return value !== null && value !== undefined ? value : ''
      }
    },
    grid: {}
  },

  ATextarea: {
    type: 'ATextarea',
    slot: '',
    field: '',
    label: '',
    decorator: {},
    attrs: {},
    events: {},
    default: {
      input: '',
      output: ''
    },
    transfer: {
      input (value, { Utils }) {
        return value !== undefined && value !== null ? value : ''
      },
      output (value, { Utils }) {
        return value !== undefined && value !== null ? value : ''
      }
    },
    grid: {}
  }
}
