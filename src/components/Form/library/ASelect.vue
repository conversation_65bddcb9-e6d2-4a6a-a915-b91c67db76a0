<template>
  <a-form-item :label="$attrs.label">
    <a-select
      ref="component"
      v-decorator="[$attrs.field, $attrs.decorator]"
      v-bind="handleScope($attrs.binds)"
      v-on="handleScope($attrs.events)"
    >
      <a-select-option
        v-if="optionRender(option, $attrs.col)"
        v-show="optionShow(option, $attrs.col)"
        v-for="(option, index) of optionsRender($attrs.col)"
        :disabled="optionDisabled(option, $attrs.col)"
        :label="option.label"
        :value="option.value"
        :key="index"
      >{{ option.label }}</a-select-option>
    </a-select>
  </a-form-item>
</template>

<script>
import Utils from '../utils'

export default {
  name: 'SSelect',
  inheritAttrs: false,
  methods: {
    handleScope (obj) {
      const scope = { ...obj }
      const names = Object.keys(obj)
      for (const name of names) {
        if (Utils.isFunction(scope[name])) {
          scope[name] = scope[name].bind(this)
        }
      }
      return scope
    },
    optionsRender (col) {
      const selectOptions = []
      const row = this.$attrs.row
      const form = this.$attrs.form
      const group = this.$attrs.group
      const extender = this.$attrs.extender

      if (Utils.isObject(extender)) {
        if (Utils.isArray(extender.selectOptions)) {
          selectOptions.push(...extender.selectOptions)
        }

        if (Utils.isFunction(extender.selectOptionsRender)) {
          const result = extender.selectOptionsRender(selectOptions, { col, row, group, form, Utils })
          return Utils.isArray(result) ? result : selectOptions
        }
      }

      return selectOptions
    },
    optionDisabled (opt, col) {
      const selectOptions = []
      const row = this.$attrs.row
      const form = this.$attrs.form
      const group = this.$attrs.group
      const extender = this.$attrs.extender

      if (Utils.isObject(extender)) {
        if (Utils.isArray(extender.selectOptions)) {
          selectOptions.push(...extender.selectOptions)
        }
        if (Utils.isFunction(extender.selectOptionItemDisabled)) {
          const result = extender.selectOptionItemDisabled(opt, {
            options: selectOptions,
            col,
            row,
            group,
            form,
            Utils
          })
          return Utils.isBoolean(result) ? result : opt.disabled === true
        }
      }

      return opt.disabled === true
    },
    optionRender (opt, col) {
      const selectOptions = []
      const row = this.$attrs.row
      const form = this.$attrs.form
      const group = this.$attrs.group
      const extender = this.$attrs.extender

      if (Utils.isObject(extender)) {
        if (Utils.isArray(extender.selectOptions)) {
          selectOptions.push(...extender.selectOptions)
        }
        if (Utils.isFunction(extender.selectOptionItemRender)) {
          const result = extender.selectOptionItemRender(opt, {
            options: selectOptions,
            col,
            row,
            group,
            form,
            Utils
          })
          return Utils.isBoolean(result) ? result : opt.render !== false
        }
      }

      return opt.render !== false
    },
    optionShow (opt, col) {
      const selectOptions = []
      const row = this.$attrs.row
      const form = this.$attrs.form
      const group = this.$attrs.group
      const extender = this.$attrs.extender

      if (Utils.isObject(extender)) {
        if (Utils.isArray(extender.selectOptions)) {
          selectOptions.push(...extender.selectOptions)
        }
        if (Utils.isFunction(extender.selectOptionItemShow)) {
          const result = extender.selectOptionItemShow(opt, {
            options: selectOptions,
            col,
            row,
            group,
            form,
            Utils
          })
          return Utils.isBoolean(result) ? result : opt.show !== false
        }
      }

      return opt.show !== false
    }
  }
}
</script>

<style lang="less">
[disabled] {
  .ant-select {
    .ant-select-selection--multiple .ant-select-search--inline,
    .ant-select-selection--multiple .ant-select-search--inline {
      display: none;
    }
  }
}
[off-disabled] {
  .ant-select[disabled],
  .ant-select-disabled {
    .ant-select-selection {
      color: rgba(0, 0, 0, 0.65);
      background-color: #ffffff;
      cursor: default;
      opacity: 1;
    }
    .ant-select-selection--multiple .ant-select-selection__choice {
      padding-right: 20px;
      color: rgba(0, 0, 0, 0.65);
      background-color: #fafafa;
    }
  }
}
</style>
