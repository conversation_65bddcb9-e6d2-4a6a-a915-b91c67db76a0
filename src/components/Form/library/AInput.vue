<template>
  <a-form-item :label="$attrs.label">
    <a-input
      ref="component"
      v-decorator="[$attrs.field, $attrs.decorator]"
      v-bind="handleScope($attrs.binds)"
      v-on="handleScope($attrs.events)"
    />
  </a-form-item>
</template>

<script>
import Utils from '../utils'

export default {
  name: 'SInput',
  inheritAttrs: false,
  methods: {
    handleScope (obj) {
      const scope = { ...obj }
      const names = Object.keys(obj)
      for (const name of names) {
        if (Utils.isFunction(scope[name])) {
          scope[name] = scope[name].bind(this)
        }
      }
      return scope
    }
  }
}
</script>

<style lang="less">
[off-disabled] {
  .ant-input[disabled],
  .ant-input-disabled {
    color: rgba(0, 0, 0, 0.65);
    background-color: #ffffff;
    cursor: default;
    opacity: 1;
  }
}
</style>
