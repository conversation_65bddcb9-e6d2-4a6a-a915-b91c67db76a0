<template>
  <div class="user-wrapper">
    <div class="content-box">
      <a
        v-if="true"
        href="javascript:;"
        @click="showDrawer"
      >
        <span class="action">个性化</span>
      </a>
      <a
        v-if="true"
        @click="openFile()"
        target="_blank"
      >
        <span class="action">用户操作速成手册</span>
      </a>
      <a-dropdown>
        <span class="action ant-dropdown-link user-dropdown-menu">
          <a-avatar
            class="avatar"
            size="small"
            :src="avatar"
          />
          <span>{{ nickname }}</span>
        </span>

        <a-menu
          slot="overlay"
          class="user-dropdown-menu-wrapper"
        >
          <a-menu-item
            v-if="false"
            key="0"
          >
            <router-link :to="{ name: 'center' }">
              <a-icon type="user" />
              <span>个人中心</span>
            </router-link>
          </a-menu-item>

          <a-menu-item
            v-if="tenant"
            key="1"
          >
            <a
              href="javascript:;"
              @click="chooseCompany"
            >
              <a-icon type="edit" />
              <span>单位切换</span>
            </a>
          </a-menu-item>

          <a-menu-divider v-if="false" />

          <a-menu-item key="2">
            <a
              href="javascript:;"
              @click="showModal"
            >
              <a-icon type="setting" />
              <span>修改密码</span>
            </a>
          </a-menu-item>

          <a-menu-item key="3">
            <a
              href="javascript:;"
              @click="handleLogout"
            >
              <a-icon type="logout" />
              <span>退出登录</span>
            </a>
          </a-menu-item>
        </a-menu>
      </a-dropdown>

      <a-modal
        v-model="visible"
        title="修改密码"
        @click="showModal"
      >
        <template slot="footer">
          <a-button
            key="back"
            @click="handleCancel"
          >取消</a-button>
          <a-button
            key="submit"
            type="primary"
            @click="handleOk()"
          >提交</a-button>
        </template>

        <a-form
          :form="form"
          layout="vertical"
          hide-required-mark
        >
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="原密码">
                <a-input-password
                  v-decorator="['password', {
                    rules: [{ required: true, message: '原密码不能为空' }],
                  }]"
                  placeholder="请输入原密码"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item
                label="新密码"
                has-feedback
              >
                <a-input-password
                  v-decorator="['newPassword', {
                    rules: [
                      { required: true, message: '密码不能为空' },
                      { validator: validateToNextPassword }
                    ]
                  }]"
                  placeholder="请输入新密码"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item
                label="确认新密码"
                has-feedback
              >
                <a-input-password
                  v-decorator="['confirmPassword', {
                    rules: [
                      { required: true, message: '密码不能为空' },
                      { validator: compareToFirstPassword }
                    ]
                  }]"
                  placeholder="请确认新密码"
                  @blur="handleConfirmBlur"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-modal>
    </div>
    <a-drawer
      title="个性化配置"
      width="360"
      placement="right"
      :closable="false"
      :visible="visibleDrawer"
      @close="onCloseDrawer"
    >
      <div>
        <div style="margin-bottom: 30px;">主题色切换</div>
        <div class="topicCol">
          <div class="lights" @click="changeTheme('light')"/>
          <div class="darks" @click="changeTheme('dark')"/>
        </div>
      </div>
    </a-drawer>
    <ChooseCompanyModal ref="modal" :list="userInfoList"/>
  </div>
</template>

<script>
// import md5 from 'md5'
import Vue from 'vue'
import * as baseApi from '@/api/system/base'
import { mapActions, mapGetters } from 'vuex'
import { OPERATOR, DEFAULT_THEME, ACCESS_TOKEN, TENANT, ORG_NAME, MOBILE_PHONE } from '@/store/mutation-types'
import NoticeIcon from '@/components/NoticeIcon'
import { changePassword } from '@/api/system/login'
import ChooseCompanyModal from '@/views/system/components/ChooseCompanyModal.vue'
import { mixin } from '@/utils/mixin'
import { requestBuilder } from '@/utils/util'
const BASE_URL = process.env.VUE_APP_API_BASE_URL
export default {
  name: 'UserMenu',
  mixins: [mixin],
  data () {
    return {
      BASE_URL,
      tenant: Vue.ls.get(TENANT) === 'Y',
      orgName: Vue.ls.get(ORG_NAME),
      confirmDirty: false,
      visible: false,
      visible2: false,
      visibleDrawer: false,
      form: this.$form.createForm(this),
      userInfoList: []
      // password: '',
      // newpassword: '',
      // confirmPassword: ''
    }
  },
  components: {
    NoticeIcon,
    ChooseCompanyModal
  },
  computed: {
    ...mapGetters(['nickname', 'avatar'])
  },
  methods: {
    chooseCompany () {
      this.userInfoList = []
      const mobilePhone = Vue.ls.get(MOBILE_PHONE)
      if (!mobilePhone) {
        return this.$message.warn('未找到用户信息，请刷新页面！')
      }
      baseApi.queryTenantInfo(requestBuilder('', { mobilePhone: mobilePhone })).then(res => {
        if (res.result && res.result.length > 0) {
          // 让用户选择单位进行登录
          this.userInfoList = res.result
          this.$refs.modal.showModal(true)
        } else {
          // 虽然是多单位但是当前用户只在一个单位中有数据，则默认使用当前信息
          return this.$message.warn('未找到对应单位信息！')
        }
      })
    },
    showError () {
      this.visible2 = true
    },
    changeTheme (style) {
      Vue.ls.set(DEFAULT_THEME, style)
      this.doChangeTheme(style)
    },
    showDrawer () {
      this.visibleDrawer = true
    },
    onCloseDrawer () {
      this.visibleDrawer = false
    },
    handleOk2 (e) {
      this.visible2 = false
    },
    showModal () {
      this.visible = true
    },
    handleConfirmBlur (e) {
      const value = e.target.value
      this.confirmDirty = this.confirmDirty || !!value
    },
    compareToFirstPassword (rule, value, callback) {
      const form = this.form
      if (value && value !== form.getFieldValue('newPassword')) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    },
    validateToNextPassword (rule, value, callback) {
      const form = this.form
      var regex = new RegExp('(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z]).{8,30}')
      if (!regex.test(value)) {
        callback(new Error('您的密码复杂度太低，密码长度必须8位以上，包含大小写字母和数字！'))
      }
      if (value && this.confirmDirty) {
        form.validateFields(['confirmPassword'], { force: true })
      }
      callback()
    },
    handleOk () {
      const param = {
        userNo: Vue.ls.get(OPERATOR),
        password: this.form.getFieldValue('password'),
        newPassword: this.form.getFieldValue('newPassword')
      }
      changePassword({ param: param })
        .then(res => {
          if (res.code === '0000') {
            this.$message.success('修改成功，即将返回登录界面')
            this.Logout({})
              .then(() => {
                setTimeout(() => {
                  window.location.reload()
                }, 16)
              })
              .catch(err => {
                this.$message.error({
                  title: '错误',
                  description: err.message
                })
              })
          } else {
            this.$message.error(res.message)
          }
        })
        .catch(err => {
          this.$message.error({
            title: '错误',
            description: err.message
          })
        })
    },
    handleCancel (e) {
      this.visible = false
    },
    openFile () {
      const TOKEN = Vue.ls.get(ACCESS_TOKEN)
      if (TOKEN) {
        // 验证通过，执行导航
        window.open(`${BASE_URL}/file/books/wzsc.pdf`, '_blank')
      } else {
        // 验证失败，可以执行其他操作，比如弹出警告
        this.$message.error('当前状态不允许下载用户手册！')
      }
    },
    ...mapActions(['Logout']),
    handleLogout () {
      this.$confirm({
        title: '提示',
        content: '真的要注销登录吗 ?',
        onOk: () => {
          return this.Logout({})
            .then(() => {
              setTimeout(() => {
                window.location.reload()
              }, 16)
            })
            .catch(err => {
              this.$message.error({
                title: '错误',
                description: err.message
              })
            })
        },
        onCancel () {}
      })
    }
  }
}
</script>

<style lang="less" scoped>
  .topicCol {
    display: flex;
  }
  .lights {
    background-color: #ececec;
    width: 50px;
    height: 50px;
    border-radius: 5px;
    box-shadow: 0px 1px 4px rgba(0,0,0,0.3),0px 0px 50px #eaeaea inset;
    cursor: pointer;
    margin-right: 15px;
  }
  .darks {
    background-color: #292d5e;
    width: 50px;
    height: 50px;
    border-radius: 5px;
    box-shadow: 0px 1px 4px rgba(0,0,0,0.3),0px 0px 50px #000b3e inset;
    cursor: pointer;
  }
  .dark {
    color: red;
  }
</style>
