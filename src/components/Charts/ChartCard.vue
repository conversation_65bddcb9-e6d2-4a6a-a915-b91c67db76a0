<template>
  <a-card :loading="loading" :body-style="{ padding: '13px 20px 8px' }" :bordered="false">
    <div class="chart-card-header">
      <div class="meta">
        <span class="chart-card-title">
          <slot name="title">
            {{ title }}
          </slot>
        </span>
        <span class="chart-card-action">
          <slot name="action"/>
        </span>
      </div>
      <div class="total">
        <slot name="total">
          <countTo
            :startVal="0"
            :endVal="total"
            :prefix="prefix"
            :suffix="suffix"
            :decimals="decimals"
            :duration="duration"/>
        </slot>
      </div>
    </div>
<!--    <div class="chart-card-content">-->
<!--      <div class="content-fix">-->
<!--        <slot/>-->
<!--      </div>-->
<!--    </div>-->
    <div class="chart-card-footer">
      <div class="field">
        <slot name="footer"/>
      </div>
    </div>
  </a-card>
</template>

<script>
import countTo from 'vue-count-to'

export default {
  name: 'ChartCard',
  components: {
    countTo
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    prefix: {
      type: String,
      default: ''
    },
    suffix: {
      type: String,
      default: ''
    },
    total: {
      type: Number,
      default: null
    },
    duration: {
      type: Number,
      default: 1000
    },
    // 小数
    decimals: {
      type: Number,
      default: 0
    },
    loading: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="less" scoped>
  .chart-card-header {
    width: 100%;
    position: relative;
    overflow: hidden;
    .meta {
      width: 100%;
      font-size: 14px;
      color: rgba(0, 0, 0, .45);
      line-height: 18px;
      position: relative;
      overflow: hidden;
    }
  }

  .chart-card-action {
    cursor: pointer;
    margin-right:10px;
    top: 0;
    right: 0;
  }

  .chart-card-footer {
    border-top: 1px solid #e8e8e8;
    padding-left: 1px;
    padding-top: 5px;
    margin-top: 8px;
    > * {
      position: relative;
    }
    .field {
      margin: 0;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  .chart-card-content {
    width: 100%;
    height: 40px;
    position: relative;
    .content-fix {
      width: 100%;
      position: absolute;
      left: 0;
      bottom: 0;
    }
  }

  .total {
    height: 38px;
    padding-top: 2px;
    margin-bottom: 1px;
    color: #000;
    font-size: 28px;
    line-height: 38px;
    // width: 60%;
    text-overflow: ellipsis;
    word-break: break-all;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
