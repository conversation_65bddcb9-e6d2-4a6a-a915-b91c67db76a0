s-table组件结合'vue-draggable-resizable'完成自由拖拽表格
<template>
  <s-table
    bordered
    :columns="columns"
    :components="components"
    :data="loadData"
    rowKey="key">
    <template v-slot:action>
      <a href="javascript:;">Delete</a>
    </template>
  </s-table>
</template>

<script>
  // 局部引入
import { STable } from '@/components'
import Vue from 'vue'
import VueDraggableResizable from 'vue-draggable-resizable'

Vue.component('vue-draggable-resizable', VueDraggableResizable)

export default {
  name: 'Test',
  components: {
    STable
  },
  data () {
    this.components = {
      header: {
        cell: (h, props, children) => {
          const { key, ...restProps } = props
          console.log('ResizeableTitle', key)
          const col = this.columns.find(col => {
            const k = col.dataIndex || col.key
            return k === key
          })

          if (!col || !col.width) {
            return h('th', { ...restProps }, [...children])
          }

          const dragProps = {
            key: col.dataIndex || col.key,
            class: 'table-draggable-handle',
            attrs: {
              w: 10,
              x: col.width,
              z: 1,
              axis: 'x',
              draggable: true,
              resizable: false
            },
            on: {
              dragging: (x, y) => {
                col.width = Math.max(x, 1)
              }
            }
          }
          const drag = h('vue-draggable-resizable', { ...dragProps })
          return h('th', { ...restProps, class: 'resize-table-th' }, [...children, drag])
        }
      }
    }
    return {
      data: [
        {
          key: 0,
          date: '2018-02-11',
          amount: 120,
          type: 'income',
          note: 'transfer'
        },
        {
          key: 1,
          date: '2018-03-11',
          amount: 243,
          type: 'income',
          note: 'transfer'
        },
        {
          key: 2,
          date: '2018-04-11',
          amount: 98,
          type: 'income',
          note: 'transfer'
        }
      ],
      columns: [
        {
          title: 'Date',
          dataIndex: 'date',
          width: 200,
          ellipsis: true
        },
        {
          title: 'Amount',
          dataIndex: 'amount',
          width: 100,
          ellipsis: true
        },
        {
          title: 'Type',
          dataIndex: 'type',
          width: 100,
          ellipsis: true
        },
        {
          title: 'Note',
          dataIndex: 'note',
          width: 100,
          ellipsis: true
        },
        {
          title: 'Action',
          key: 'action',
          scopedSlots: { customRender: 'action' }
        }
      ],
      loadData: parameter => {
        return Promise.resolve(this.data)
      }
    }
  }
}
</script>
<style>
.resize-table-th {
  position: relative;
  .ant-table-header-column {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .table-draggable-handle {
    position: absolute !important;
    transform: translate(-50%, -50%) !important;
    width: 10px !important;
    height: 100% !important;
    right: -5px;
    background: #000;
    cursor: col-resize;
    touch-action: none;
    border: none;
  }
}
</style>
