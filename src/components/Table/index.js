import T from 'ant-design-vue/es/table/Table'
import get from 'lodash.get'

/**
 * 工具类
 */
const itString = function (str) {
  return Object.prototype.toString.call(str)
}
const itType = function (val) {
  return itString(val).replace(/^\[[^\s\]]+\s*([^\s\]]+)]$/, '$1')
}
const isArray = function (arr) {
  return itType(arr) === 'Array'
}
const isObject = function (obj) {
  return itType(obj) === 'Object'
}

/**
 * 组件类
 */
export default {
  data () {
    return {
      optionColumns: [],
      selectColumns: [],
      needTotalList: [],
      oldDataSource: [],
      oldPagination: {},
      selectedRows: [],
      selectedRowKeys: [],
      localDataSource: [],
      localLoading: false,
      localPagination: { ...this.pagination },
      localFilters: {},
      localSorter: {}
    }
  },
  props: {
    // 组件
    ...T.props,
    // 自定义
    data: {
      type: Function,
      required: true
    },
    alert: {
      type: [Object, Boolean],
      default: null
    },
    rowSelection: {
      type: Object,
      default: null
    },
    clearExpansion: {
      type: Boolean,
      default: true
    },
    clearSelection: {
      type: Boolean,
      default: true
    },
    expandedRowKeys: {
      type: Array,
      default: function () {
        return []
      }
    },
    showAlertInfo: {
      type: Boolean,
      default: false
    },
    showPagination: {
      type: [String, Boolean],
      default: 'auto'
    },
    hideOnSinglePage: {
      type: Boolean,
      default: false
    },
    showSizeChanger: {
      type: Boolean,
      default: true
    },
    pageSizeOptions: {
      type: Array,
      default: function () {
        return ['10', '20', '30', '40']
      }
    },
    pageSize: {
      type: Number,
      default: 10
    },
    pageNum: {
      type: Number,
      default: 1
    },
    pageURI: {
      type: Boolean,
      default: false
    },
    immediate: {
      type: Boolean,
      default: true
    },
    noloading: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    'pageNum': {
      immediate: true,
      handler (val) {
        Object.assign(this.localPagination, {
          current: val
        })
      }
    },
    'pageSize': {
      immediate: true,
      handler (val) {
        Object.assign(this.localPagination, {
          pageSize: val
        })
      }
    },
    'pageSizeOptions': {
      immediate: true,
      handler (options) {
        Object.assign(this.localPagination, {
          pageSizeOptions: options
        })
      }
    },
    'showSizeChanger': {
      immediate: true,
      handler (val) {
        Object.assign(this.localPagination, {
          showSizeChanger: val
        })
      }
    },
    'localPagination.current': {
      immediate: true,
      handler (val) {
        this.pageURI &&
          this.$router.push({
            ...this.$route,
            name: this.$route.name,
            params: Object.assign({}, this.$route.params, {
              pageNo: val
            })
          })
      }
    }
  },
  created () {
    const { pageNo } = this.$route.params
    const localPageNum = (this.pageURI && pageNo && parseInt(pageNo)) || this.pageNum
    this.localPagination = (
      ['auto', 'imitate', true].includes(this.showPagination) &&
      Object.assign({}, this.localPagination, {
        current: localPageNum,
        pageSize: this.pageSize,
        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条 (共 ${total} 条)`,
        showSizeChanger: this.showSizeChanger
      })
    )
    this.needTotalList = this.initColumnTotal(this.columns)
    this.immediate && this.loadData()
  },
  methods: {
    /**
     * 准备中
     */
    ready (state) {
      this.localLoading = state !== false
    },

    /**
     * 重置数据
     */
    reset () {
      this.localDataSource = this.oldDataSource
      this.localPagination = this.oldPagination
    },

    /**
     * 选择数据
     */
    select (data) {
      this.localDataSource = [...data]
      this.localPagination = this.showPagination === 'imitate'
        ? Object.assign({}, this.localPagination, {
          current: 1,
          total: data.length
        })
        : false
    },

    /**
     * 清空数据
     */
    clear () {
      this.localLoading = false
      this.localDataSource = []
      this.localPagination = this.showPagination
        ? Object.assign({}, this.localPagination, {
          current: 1,
          total: 0
        })
        : false
      this.oldPagination = this.localPagination
      this.oldDataSource = this.localDataSource
    },

    /**
     * 刷新数据
     * @param bool 是否跳到第一页
     */
    refresh (bool = false, delay = 0) {
      if (bool === true) {
        this.localPagination = Object.assign(
          {},
          {
            current: 1,
            pageSize: (this.showPagination && this.localPagination.pageSize) || this.pageSize
          }
        )
      }
      delay > 0
        ? setTimeout(() => this.loadData(), delay)
        : this.loadData()
    },

    /**
     * 加载数据
     */
    loadData (pagination, filters, sorter) {
      this.clearExpansion && this.clearExpanded()
      this.clearSelection && this.clearSelected()
      this.localLoading = !this.noloading
      const parameter = Object.assign(
        {
          pageNo:
            (pagination && pagination.current) || (this.showPagination && this.localPagination.current) || this.pageNum,
          pageSize:
            (pagination && pagination.pageSize) ||
            (this.showPagination && this.localPagination.pageSize) ||
            this.pageSize
        },
        (sorter && sorter.field && { sortField: sorter.field }) ||
          (this.localSorter && this.localSorter.field && { sortField: this.localSorter.field }) ||
          {},
        (sorter && sorter.order && { sortOrder: sorter.order }) ||
          (this.localSorter && this.localSorter.order && { sortOrder: this.localSorter.order }) ||
          {},
        {
          ...this.localFilters,
          ...filters
        }
      )
      Promise.resolve(this.data(parameter))
        .then(r => {
          if (isObject(r)) {
            // 分页数据
            this.localPagination = this.showPagination
              ? Object.assign({}, this.localPagination, {
                total: r.totalCount || r.data.length,
                current: r.pageNo || 1,
                pageSize: parameter.pageSize,
                showSizeChanger: this.showSizeChanger
              })
              : false

            // 数据长度不匹配时 ,自动翻页到上一页
            if (this.showPagination && this.localPagination.pageSize) {
              const remainder = this.localPagination.total % this.localPagination.pageSize
              const quotient = this.localPagination.total / this.localPagination.pageSize
              const totalValue = remainder ? parseInt(quotient) + 1 : parseInt(quotient)
              if (this.localPagination.current > totalValue && this.localPagination.current > 1) {
                this.localPagination.current--
                this.loadData()
                return
              }
            }

            // 是否关闭 table 分页功能
            try {
              if (
                ['auto', 'imitate', true].includes(this.showPagination) &&
                r.totalCount <= r.pageNo * this.localPagination.pageSize
              ) {
                this.localPagination.hideOnSinglePage = this.hideOnSinglePage
              }
            } catch (e) {
              this.localPagination = false
            }

            // table 数据
            this.localDataSource = r.data
            this.oldDataSource = this.localDataSource
            this.oldPagination = this.localPagination
          }
          if (isArray(r)) {
            // 分页数据
            this.localPagination = this.showPagination
              ? Object.assign({}, this.localPagination, {
                total: r.length,
                current: parameter.pageNo || 1,
                pageSize: parameter.pageSize,
                showSizeChanger: this.showSizeChanger
              })
              : false

            // 数据长度不匹配时 ,自动翻页到上一页
            if (this.showPagination && this.localPagination.pageSize) {
              const remainder = this.localPagination.total % this.localPagination.pageSize
              const quotient = this.localPagination.total / this.localPagination.pageSize
              const totalValue = remainder ? parseInt(quotient) + 1 : parseInt(quotient)
              if (this.localPagination.current > totalValue && this.localPagination.current > 1) {
                this.localPagination.current--
                this.loadData()
                return
              }
            }

            // 是否关闭 table 分页功能
            try {
              if (['auto', 'imitate', true].includes(this.showPagination)) {
                this.localPagination.hideOnSinglePage = this.hideOnSinglePage
              }
            } catch (e) {
              this.localPagination = false
            }

            // table 数据
            this.localDataSource = r
            this.oldDataSource = this.localDataSource
            this.oldPagination = this.localPagination
          }
        })
        .finally(() => {
          // loading 结束
          if (!this.noloading) {
            this.localLoading = false
          }
          // 默认展开项
          if (this.defaultExpandedRowKeys && this.defaultExpandedRowKeys.length > 0) {
            for (const rowKey of this.defaultExpandedRowKeys) {
              if (this.localDataSource.some(record => record[this.rowKey] === rowKey)) {
                if (!this.expandedRowKeys.includes(rowKey)) {
                  this.expandedRowKeys.push(rowKey)
                }
              }
            }
          } else if (this.defaultExpandAllRows === true) {
            for (const record of this.localDataSource) {
              if (!this.expandedRowKeys.includes(record[this.rowKey])) {
                this.expandedRowKeys.push(record[this.rowKey])
              }
            }
          }
        })
    },

    /**
     * 更新 table 已选中项
     */
    updateSelect (selectedRowKeys, selectedRows) {
      this.selectedRows = selectedRows
      this.selectedRowKeys = selectedRowKeys
      const list = this.needTotalList
      this.needTotalList = list.map(item => {
        return {
          ...item,
          total: selectedRows.reduce((sum, val) => {
            const total = sum + parseInt(get(val, item.dataIndex))
            return isNaN(total) ? 0 : total
          }, 0)
        }
      })
    },

    /**
     * 更新 table 展开项
     */
    updateExpand (expandedRowKeys) {
      if (isArray(this.expandedRowKeys) && isArray(expandedRowKeys)) {
        this.expandedRowKeys.splice(0, this.expandedRowKeys.length, ...expandedRowKeys)
      }
    },

    /**
     * 触发 table 选中项
     */
    triggerSelect (selectedRowKeys, selectedRows) {
      if (this.rowSelection) {
        this.rowSelection.onChange(selectedRowKeys, selectedRows)
        this.updateSelect(selectedRowKeys, selectedRows)
      }
    },

    /**
     * 触发 table 展开项
     */
    triggerExpand (expandedRowKeys) {
      this.updateExpand(expandedRowKeys)
    },

    /**
     * 清空 table 已选中项
     */
    clearSelected () {
      if (this.rowSelection) {
        this.rowSelection.onChange([], [])
        this.updateSelect([], [])
      }
    },

    /**
     * 清空 table 展开项
     */
    clearExpanded () {
      this.triggerExpand([])
    },

    /**
     * 统计列总计
     */
    initColumnTotal (columns) {
      const totalList = []
      columns &&
        columns instanceof Array &&
        columns.forEach(column => {
          if (column.needTotal) {
            totalList.push({
              ...column,
              total: 0
            })
          }
        })
      return totalList
    },

    /**
     * 统计列字段
     */
    initColumnField (columns) {
      for (const column of columns) {
        if (!isArray(column.children) || column.children.length === 0) {
          if (this.optionColumns.every(option => option.value !== column.dataIndex)) {
            if (column.colFilter !== false) {
              this.optionColumns.push({
                label: column.title,
                value: column.dataIndex
              })
            }
          }
        } else if (column.children.length > 0) {
          this.initColumnField(column.children)
        }
      }
    },

    /**
     * 过滤列字段
     */
    filterColumnField (columns) {
      const columnArr = []
      for (const column of columns) {
        if (!isArray(column.children) || column.children.length === 0) {
          if (this.optionColumns.some(option => option.value === column.dataIndex)) {
            if (this.selectColumns.length === 0 || this.selectColumns.some(select => {
              return select.value === column.dataIndex && select.filter !== false
            })) {
              columnArr.push({
                ...column
              })
            }
          } else {
            columnArr.push({
              ...column
            })
          }
        } else {
          const children = this.filterColumnField(column.children)
          if (children.length > 0) {
            columnArr.push({
              ...column,
              children
            })
          }
        }
      }
      return columnArr
    },

    /**
     * 处理 clear 事件
     */
    renderClear (callback) {
      if (this.selectedRowKeys.length <= 0) return null
      return (
        <a
          style="margin-left: 24px"
          onClick={() => {
            callback()
            this.clearSelected()
          }}
        >
          清空
        </a>
      )
    },

    /**
     * 处理 alert 渲染
     */
    renderAlert () {
      // 绘制统计列数据
      const needTotalItems = this.needTotalList.map(item => {
        return (
          <span style="margin-right: 12px">
            {item.title}总计{' '}
            <a style="font-weight: 600">{!item.customRender ? item.total : item.customRender(item.total)}</a>
          </span>
        )
      })
      // 绘制 清空 按钮
      const clearItem =
        typeof this.alert.clear === 'boolean' && this.alert.clear
          ? this.renderClear(this.clearSelected)
          : this.alert !== null && typeof this.alert.clear === 'function'
            ? this.renderClear(this.alert.clear)
            : null

      // 绘制 alert 组件
      return (
        <a-alert showIcon={true} style="margin-bottom: 16px">
          <template slot="message">
            <span style="margin-right: 12px">
              已选择: <a style="font-weight: 600">{this.selectedRows.length}</a>
            </span>
            {needTotalItems}
            {clearItem}
          </template>
        </a-alert>
      )
    }
  },

  /**
   * 渲染组件
   */
  render () {
    const props = {}
    const localKeys = Object.keys(this.$data)
    const showAlertInfo = typeof this.alert === 'object' && this.alert !== null && this.alert.show
    const onChange = (pagination, filters, sorter) => {
      if (this.showPagination && pagination && pagination.pageSize) {
        const changed = pagination.pageSize !== this.localPagination.pageSize
        this.localPagination.current = pagination.current = !changed ? pagination.current : 1
        this.localPagination.pageSize = pagination.pageSize
      }
      // 数据长度不匹配时 ,自动翻页到上一页
      if (this.showPagination && this.localPagination.pageSize) {
        const remainder = this.localPagination.total % this.localPagination.pageSize
        const quotient = this.localPagination.total / this.localPagination.pageSize
        const totalValue = remainder ? parseInt(quotient) + 1 : parseInt(quotient)
        while (this.localPagination.current > totalValue && this.localPagination.current > 1) {
          this.localPagination.current--
        }
      }
      this.localSorter = sorter
      this.localFilters = filters
      this.showPagination !== 'imitate' && this.loadData(pagination, filters, sorter)
    }
    const onExpandedRowsChange = expandedRowKeys => {
      this.updateExpand(expandedRowKeys)
    }
    const onExpand = (expanded, record) => {
      // console.log('####onExpand##', expanded, record)
      this.$emit('expand', { expanded, record })
    }
    // 处理 组件参数
    Object.keys(T.props).forEach(k => {
      const localKey = `local${k.substring(0, 1).toUpperCase()}${k.substring(1)}`
      if (localKeys.includes(localKey)) {
        props[k] = this[localKey]
        return props[k]
      }
      if (k === 'rowSelection') {
        if (this.rowSelection) {
          props[k] = {
            ...this.rowSelection,
            selectedRows: this.selectedRows,
            selectedRowKeys: this.selectedRowKeys,
            onChange: (selectedRowKeys, selectedRows) => {
              this.updateSelect(selectedRowKeys, selectedRows)
              typeof this[k].onChange !== 'undefined' && this[k].onChange(selectedRowKeys, selectedRows)
            }
          }
          return props[k]
        } else if (!this.rowSelection) {
          props[k] = null
          return props[k]
        }
      }
      if (k === 'getPopupContainer') {
        if (this[k] === undefined) {
          props[k] = () => this.$el
          return props[k]
        }
      }
      props[k] = this[k]
      return props[k]
    })

    // 处理 列字段
    this.initColumnField(props.columns)

    // 处理 列筛选
    props.columns = this.filterColumnField(props.columns)

    // 处理 列选项
    for (const option of this.optionColumns) {
      if (this.selectColumns.every(select => select.value !== option.value)) {
        this.selectColumns.push({
          value: option.value,
          filter: true
        })
      }
    }

    const table = (
      <a-table
        {...{ props, scopedSlots: { ...this.$scopedSlots } }}
        onChange={onChange}
        onExpand={onExpand}
        onExpandedRowsChange={onExpandedRowsChange}
      >
        {Object.keys(this.$slots).map(name => (
          <template slot={name}>{this.$slots[name]}</template>
        ))}
      </a-table>
    )

    return (
      <div class="table-wrapper">
        {showAlertInfo ? this.renderAlert() : null}
        {table}
      </div>
    )
  }
}
