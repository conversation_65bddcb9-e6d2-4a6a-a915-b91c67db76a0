<template>
  <a-select
    :value="value"
    :showSearch="showSearch"
    :allowClear="allowClear"
    optionFilterProp="label"
    dropdownClassName="icon-dropdown-wrapper"
    @dblclick.native.stop="() => {}"
    @click.native.stop="() => {}"
    @change="doChange"
  >
    <a-select-opt-group
      v-for="group of iconOptions"
      :label="group.title"
      :key="group.key"
    >
      <span
        slot="label"
        style="font-size: 14px"
      >{{ group.title }}</span>
      <a-select-option
        v-for="item of group.children"
        :value="item.value"
        :title="item.label"
        :label="item.label"
        :key="item.key"
      >
        <a-icon
          :type="item.value"
          style="font-size: 18px; vertical-align: middle;"
        />
        <span
          class="icon-dropdown-hidden"
          style="padding: 0 3px; font-size: 12px; vertical-align: middle;"
        >{{ item.label }}</span>
      </a-select-option>
    </a-select-opt-group>
  </a-select>
</template>

<script>
import iconOptions from './iconOptions'

export default {
  name: 'IconSelector',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    showSearch: {
      type: Boolean,
      default: true
    },
    allowClear: {
      type: Boolean,
      default: true
    },
    // eslint-disable-next-line vue/require-default-prop
    value: {
      type: String
    }
  },
  data () {
    return {}
  },
  computed: {
    iconOptions () {
      const options = []
      for (const item of iconOptions) {
        const option = {
          key: item.key,
          title: item.title,
          children: []
        }
        for (const child of item.icons) {
          option.children.push({
            key: child,
            label: child,
            value: child
          })
        }
        options.push(option)
      }
      return options
    }
  },
  methods: {
    doChange (value) {
      this.$emit('change', value)
    }
  }
}
</script>

<style lang="less">
.icon-dropdown-wrapper {
  .ant-select-dropdown-menu-item-group-list {
    padding-left: 8px;
    margin-bottom: 4px;
    & > .ant-select-dropdown-menu-item-selected {
      background-color: #b1d6fe;
    }
    & > .ant-select-dropdown-menu-item {
      margin: 3px 5px;
      padding: 5px 7px;
      line-height: 18px;
      display: inline-block;
      .icon-dropdown-hidden {
        display: none !important;
      }
    }
  }
}
</style>
