<!-- Watermark.vue -->
<template>
  <div class="watermark" ref="watermark"/>
</template>

<script>
export default {
  // props: {
  //   text: {
  //     type: String,
  //     required: true
  //   }
  // },
  mounted () {
    // this.addWatermark(this.text)
  },
  methods: {
    addWatermark (text) {
      const watermarkDiv = this.$refs.watermark
      const canvas = document.createElement('canvas')
      canvas.width = text.length * 24 + 40
      canvas.height = text.length * 24 + 100

      const ctx = canvas.getContext('2d')
      if (ctx) {
        ctx.shadowOffsetX = 2
        ctx.shadowOffsetY = 2
        ctx.shadowBlur = 2
        ctx.font = '24px Microsoft YaHei'
        ctx.color = 'rgba(156, 162, 169, 0.4)'
        ctx.textAlign = 'center'
        ctx.translate(canvas.width / 2, canvas.height / 2)
        ctx.rotate(-35 * Math.PI / 180)
        ctx.fillText(text, 0, 0)
      }
      watermarkDiv.style.backgroundImage = `url(${canvas.toDataURL('image/png')})`
      watermarkDiv.style.backgroundSize = ` ${canvas.width}px ${canvas.height}px`
      watermarkDiv.style.backgroundRepeat = 'repeat'
      watermarkDiv.style.pointerEvents = 'none'
      watermarkDiv.style.position = 'fixed'
      watermarkDiv.style.top = 0
      watermarkDiv.style.left = 0
      watermarkDiv.style.width = '100%'
      watermarkDiv.style.height = '100%'
      watermarkDiv.style.zIndex = 9999
    }
  }
}
</script>

<style scoped>
.watermark {
  background-repeat: repeat;
  opacity: 0.2;
}
</style>
