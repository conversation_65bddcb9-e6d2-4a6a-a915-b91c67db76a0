<template>
  <a-layout-sider
    width="256px"
    v-model="collapsed"
    :theme="theme"
    :class="['sider', isDesktop() ? null : 'shadow', theme, fixSiderbar ? 'ant-fixed-sidemenu' : null ]"
    :collapsible="collapsible"
    :collapsedWidth="60"
    :trigger="null"
  >
    <logo
      :layoutMode="layoutMode"
      :collapsed="collapsed"
      :device="device"
      @toggle="toggle"
    />
    <s-menu
      class="side-menu"
      :collapsed="collapsed"
      :menu="menus"
      :theme="theme"
      :mode="mode"
      @select="onSelect"
      style="padding: 10px 0px;"
    />
  </a-layout-sider>
</template>

<script>
import SMenu from './index'
import Logo from '@/components/tools/Logo'
import { mixin, mixinDevice } from '@/utils/mixin'

export default {
  name: 'SideMenu',
  components: { Logo, SMenu },
  mixins: [mixin, mixinDevice],
  props: {
    mode: {
      type: String,
      required: false,
      default: 'vertical'
    },
    theme: {
      type: String,
      required: false,
      default: 'dark'
    },
    collapsible: {
      type: Boolean,
      required: false,
      default: false
    },
    collapsed: {
      type: Boolean,
      required: false,
      default: false
    },
    menus: {
      type: Array,
      required: true
    }
  },
  methods: {
    onSelect (obj) {
      this.$emit('menuSelect', obj)
    },
    toggle () {
      this.$emit('toggle')
    }
  }
}
</script>
