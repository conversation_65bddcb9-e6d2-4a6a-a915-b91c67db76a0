// 打印媒体查询
@media print {
  // 表格
  .ant-table,
  .ant-table-small,
  .ant-table-large {
    &.ant-table-bordered .ant-table-header > table,
    &.ant-table-bordered .ant-table-body > table,
    &.ant-table-bordered .ant-table-fixed-left table,
    &.ant-table-bordered .ant-table-fixed-right table {
      border-width: 1pt;
      border-color: #000;
      border-radius: 0;
    }
  }

  // 表格单元格
  .ant-table,
  .ant-table-small,
  .ant-table-large {
    & > .ant-table-content > .ant-table-header > table > .ant-table-thead > tr > th,
    & > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th,
    & > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-thead > tr > th,
    & > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-thead > tr > th,
    & > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-thead > tr > th,
    & > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-thead > tr > th,
    &
      > .ant-table-content
      > .ant-table-fixed-left
      > .ant-table-body-outer
      > .ant-table-body-inner
      > table
      > .ant-table-thead
      > tr
      > th,
    &
      > .ant-table-content
      > .ant-table-fixed-right
      > .ant-table-body-outer
      > .ant-table-body-inner
      > table
      > .ant-table-thead
      > tr
      > th {
      background-color: transparent;
      border-color: #000;
    }

    & > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th,
    &
      > .ant-table-content
      > .ant-table-fixed-left
      > .ant-table-body-outer
      > .ant-table-body-inner
      > table
      > .ant-table-thead
      > tr
      > th,
    & > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-thead > tr > th,
    &
      > .ant-table-content
      > .ant-table-fixed-right
      > .ant-table-body-outer
      > .ant-table-body-inner
      > table
      > .ant-table-thead
      > tr
      > th,
    & > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-thead > tr > th,
    & > .ant-table-content > .ant-table-header > table > .ant-table-thead > tr > th,
    & > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-thead > tr > th,
    & > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-thead > tr > th {
      height: 11pt;
      padding: 1pt 5pt;
      color: #000;
      font-size: 10pt;
      line-height: 1;
      font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
      font-weight: 400;
      border-width: 1pt;
      border-color: #000;
      border-radius: 0;
      word-break: break-all;
      word-wrap: break-word;
    }

    & > .ant-table-content > .ant-table-body > table > .ant-table-tbody > tr > td,
    &
      > .ant-table-content
      > .ant-table-fixed-left
      > .ant-table-body-outer
      > .ant-table-body-inner
      > table
      > .ant-table-tbody
      > tr
      > td,
    & > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-tbody > tr > td,
    &
      > .ant-table-content
      > .ant-table-fixed-right
      > .ant-table-body-outer
      > .ant-table-body-inner
      > table
      > .ant-table-tbody
      > tr
      > td,
    & > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-tbody > tr > td,
    & > .ant-table-content > .ant-table-header > table > .ant-table-tbody > tr > td,
    & > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-tbody > tr > td,
    & > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-tbody > tr > td {
      height: 11pt;
      padding: 1pt 5pt;
      color: #000;
      font-size: 10pt;
      line-height: 1;
      font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
      font-weight: 400;
      border-width: 1pt;
      border-color: #000;
      border-radius: 0;
      word-break: break-all;
      word-wrap: break-word;
    }
  }

  // 表格单元格行高
  .ant-table-wrapper {
    // 单元格2倍行高
    &.table-th-2 {
      .ant-table table tr > th,
      .ant-table-small table tr > th,
      .ant-table-large table tr > th {
        height: 25pt !important;
        font-size: 12px !important;
      }
    }
    &.table-td-2 {
      .ant-table table tr > td,
      .ant-table-small table tr > td,
      .ant-table-large table tr > td {
        height: 24pt !important;
        font-size: 12px !important;
      }
    }
    // 单元格3倍行高
    &.table-th-3 {
      .ant-table table tr > th,
      .ant-table-small table tr > th,
      .ant-table-large table tr > th {
        height: 36pt !important;
        font-size: 12px !important;
      }
    }
    &.table-td-3 {
      .ant-table table tr > td,
      .ant-table-small table tr > td,
      .ant-table-large table tr > td {
        height: 35pt !important;
        font-size: 12px !important;
      }
    }
  }
}
