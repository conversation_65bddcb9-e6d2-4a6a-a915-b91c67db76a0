<template>
  <div class="timeline body-bgcolor">
    <!--时间线-->
    <div style="display:flex;">
      <div class="timeline-container">
        <div style="display:flex;">
          <div v-for="(item, index) in list" :key="index" style="flex:1; display:flex; flex-direction:column;">
            <div class="lis" :style="{ 'width': width }" @click="changeActive(index)">
              <div :class="{ titleActive: index === value }" style="font-size: 12px; opacity: 0.9;">{{ item.title }}</div>
              <div class="line">
                <div class="dot" @click="changeActive(index)" :class="{ active: index <= value }" />
                <div :style="{ 'visibility': index !== 0 ? 'visible' : 'hidden' }" class="left-line" />
                <div :style="{ 'visibility': index !== list.length - 1 ? 'visible' : 'hidden' }" class="right-line" />
              </div>
              <!-- <div class="item" v-show="item.isShow" /> -->
              <div
                :style="{ 'fontSize': '12px' }"
                v-for="(info, ins) in item.descriptions"
                :key="ins"
              >{{ info }}</div>
            </div>
            <div class="item_bottom" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'Timeline',
  props: {
    value: {
      type: String,
      default: () => {
        return -1
      }
    },
    width: {
      type: String,
      default: () => {
        return 'auto'
      }
    },
    disabled: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    list: {
      type: Array,
      default: () => {
        return [
          {
            title: '任务接收',
            descriptions: ['2022-06-08']
          },
          {
            title: '招标申请',
            descriptions: ['2022-06-08']
          },
          {
            title: '招标审批完成',
            descriptions: ['2022-06-08', '2023-06-08', '2024-06-08']
          },
          {
            title: '招标公告发布',
            descriptions: ['2022-06-08']
          },
          {
            title: '开标',
            descriptions: ['2022']
          },
          {
            title: '评标',
            descriptions: ['2022']
          },
          {
            title: '定标',
            descriptions: ['2022']
          }
        ]
      }
    }
  },
  data () {
    return {
      timeLineList: [
        {
          title: '任务接收',
          description: ['2022-06-08']
        },
        {
          title: '招标申请',
          description: ['2022-06-08']
        },
        {
          title: '招标审批完成',
          description: ['2022-06-08', '2023-06-08', '2024-06-08']
        },
        {
          title: '招标公告发布',
          description: ['2022-06-08']
        },
        {
          title: '开标',
          description: ['2022']
        },
        {
          title: '评标',
          description: ['2022']
        },
        {
          title: '定标',
          description: ['2022']
        }]
    }
  },
  methods: {
    changeActive (index) {
      if (!this.disabled) {
        this.$emit('input', index)
      } else {
        this.$emit('click', index)
      }
      console.log('点击了时间点', index)
    }
    // titleClick (item, click) {
    //   this.$emit('titleClick', item, click)
    // }
  }
}
</script>
<style scoped>
.timeline-container {
  width: 800px;
  height: auto;
  /* margin-left: 50px; */
  margin-top: 30px;
}
.dot{
  border: 2px solid #DCDFE6;
  width: 10px;
  height: 10px;
  border-radius: 10px;
  background: white;
  box-sizing: border-box;
  position: absolute;
  top: 20%;
  right: 40%;
}

.item{
  flex:1;
  border-bottom: 6px solid #DCDFE6;
  box-sizing: border-box;
}

.item_bottom{
  flex:1;
  text-align:center;
  height: 15px;
  margin-top:7px;
  font-size: 14px;
}

.active{
  background-color: rgb(32, 83, 248) !important;
  /*border: 5px solid #67C23A;*/
  box-sizing: content-box;
  font-weight: 700;
}
.lis {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.line {
  display: flex;
  width: 100%;
  justify-content: space-between;
  position: relative;
}
.left-line {
  width: 50%;
  border-bottom: 1px solid #6983bf;
  margin: 10px 0;
}
.right-line {
  width: 50%;
  border-bottom: 1px solid #6983bf;
  margin: 10px 0;
}
.titleActive {
  font-weight: 700;
  opacity: 1;
}
</style>
