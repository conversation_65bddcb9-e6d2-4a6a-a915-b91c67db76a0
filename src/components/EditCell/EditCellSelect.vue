/** 可编辑单元格组件*/
<template>
  <div
    :class="{ editabled: editable }"
    :style="cellStyle.container"
    class="editable-cell"
  >
    <div
      v-if="!disabled && editable"
      :style="cellStyle.selectWrapper"
      :class="{ 'disabled-icon': disabled || !check }"
      class="editable-cell-select-wrapper"
    >
      <a-select
        v-if="!mode"
        v-model="selected"
        :style="cellStyle.select"
        :showSearch="showSearch"
        :allowClear="allowClear"
        optionFilterProp="label"
        class="editable-cell-select"
        @dblclick.native.stop="() => {}"
        @click.native.stop="() => {}"
        @change="doChange"
      >
        <a-select-option
          v-for="(item, index) in options"
          :key="index"
          :label="item.label"
          :value="item.value"
        >{{ item.label }}</a-select-option>
      </a-select>
      <a-select
        v-else
        v-model="selected"
        :style="cellStyle.select"
        :showSearch="showSearch"
        :allowClear="allowClear"
        optionFilterProp="label"
        :mode="mode"
        class="editable-cell-select"
        @dblclick.native.stop="() => {}"
        @click.native.stop="() => {}"
        @change="doChange"
      >
        <a-select-option
          v-for="(item, index) in options"
          :key="index"
          :label="item.label"
          :value="item.value"
        >{{ item.label }}</a-select-option>
      </a-select>
      <a-icon
        v-if="!disabled && check"
        :style="cellStyle.check"
        class="editable-cell-icon-check"
        type="check"
        @click="doConfirm"
      />
    </div>
    <div
      v-else
      :title="(takeTreeByKey(options, value) || {})['label'] || value"
      :class="{ 'disabled-icon': disabled || !edit }"
      :style="cellStyle.valueWrapper"
      class="editable-cell-value-wrapper"
    >
      <span v-if="mode === 'multiple'">
        <span v-for="(item, index) in value" :key="index">{{ (takeTreeByKey(options, item) || {})['label'] || item }}<i v-show="value.length - 1 !== index">,</i></span>
      </span>
      <span v-if="mode !== 'multiple'">
        {{ (takeTreeByKey(options, value) || {})['label'] || value }}
      </span>
      <a-icon
        v-if="!disabled && edit"
        :style="cellStyle.edit"
        class="editable-cell-icon-edit"
        type="edit"
        @click="doEdit"
      />
    </div>
  </div>
</template>

<script>
import { takeTreeByKey } from '@/utils/util'

export default {
  name: 'EditCellSelect',
  props: {
    mode: {
      type: String,
      default: null
    },
    showSearch: {
      type: Boolean,
      default: false
    },
    allowClear: {
      type: Boolean,
      default: false
    },
    options: {
      type: Array,
      default: function () {
        return []
      }
    },
    value: {
      type: [String, Number, Array],
      default: ''
    },
    edit: {
      type: Boolean,
      default: true
    },
    check: {
      type: Boolean,
      default: true
    },
    opened: {
      type: Boolean,
      default: false
    },
    status: {
      type: Boolean,
      default: false
    },
    synced: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    cellStyle: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data () {
    return {
      selected: this.value,
      editable: false
    }
  },
  created () {
    this.selected = this.value
  },
  watch: {
    status: {
      handler (status) {
        if (!this.disabled && status === false) {
          this.editable = false
        }
      }
    },
    opened: {
      immediate: true,
      handler (opened) {
        if (this.disabled) {
          this.editable = false
        } else {
          this.selected = this.value
          this.editable = opened
        }
      }
    },
    disabled: {
      immediate: true,
      handler (disabled) {
        if (disabled) {
          this.editable = false
        } else {
          this.editable = this.opened
        }
      }
    }
  },
  methods: {
    doEdit (e) {
      const ev = e || window.event
      this.$emit('update:status', true)
      this.selected = this.value
      this.editable = true
      this.$emit('edit', this)
      ev.stopPropagation()
    },
    doChange (e) {
      this.$emit('change', this)
    },
    doConfirm (e) {
      const ev = e || window.event
      if (!this.opened) {
        this.editable = false
      }
      this.$emit('confirm', this)
      ev.stopPropagation()
    },
    takeTreeByKey
  },
  mounted () {}
}
</script>

<style lang="less" scoped>
.editable-cell {
  width: 100%;
  position: relative;
  &.editabled {
    min-width: 90px;
  }
  & > .editable-cell-value-wrapper {
    min-height: 20px;
    &:not(.disabled-icon) {
      padding-right: 24px;
    }
    &.disabled-icon {
      padding-right: 2px;
    }
    width: 100%;
    position: relative;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: break-all;
    overflow: hidden;
    & > .editable-cell-icon-edit {
      color: #63aef2;
      position: absolute;
      top: 52%;
      right: 8px;
      width: 20px;
      transform: translateY(-50%);
      cursor: pointer;
    }
    & > .editable-cell-icon-edit:hover {
      color: #108ee9;
    }
  }
  & > .editable-cell-select-wrapper {
    &:not(.disabled-icon) {
      padding-right: 24px;
    }
    &.disabled-icon {
      padding-right: 5px;
    }
    & > .editable-cell-select {
      width: 100%;
    }
    & > .editable-cell-icon-check {
      color: #63aef2;
      position: absolute;
      top: 52%;
      right: 2px;
      width: 20px;
      transform: translateY(-50%);
      cursor: pointer;
    }
    & > .editable-cell-icon-check:hover {
      color: #108ee9;
    }
  }
}

</style>
