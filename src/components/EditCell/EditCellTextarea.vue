/** 可编辑单元格组件*/
<template>
  <div
    :class="{ editabled: editable }"
    :style="cellStyle.container"
    class="editable-cell"
  >
    <div
      v-if="!disabled && editable"
      :style="cellStyle.textareaWrapper"
      :class="{ 'disabled-icon': disabled || !check }"
      class="editable-cell-textarea-wrapper"
    >
      <a-textarea
        v-model="value"
        :autoSize="autoSize"
        :allowClear="allowClear"
        :style="cellStyle.textarea"
        @dblclick.native.stop="() => {}"
        @click.native.stop="() => {}"
        @pressEnter="doConfirm"
        @change="doChange"
        @focus="doFocus"
        @blur="doBlur"
      />
      <a-icon
        v-if="!disabled && check"
        :style="cellStyle.check"
        class="editable-cell-icon-check"
        type="check"
        @click="doConfirm"
      />
    </div>
    <div
      v-else
      :title="text"
      :class="{ 'disabled-icon': disabled || !edit }"
      :style="cellStyle.textWrapper"
      class="editable-cell-text-wrapper"
    >
      {{ text }}
      <a-icon
        v-if="!disabled && edit"
        :style="cellStyle.edit"
        class="editable-cell-icon-edit"
        type="edit"
        @click="doEdit"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'EditCellTextarea',
  props: {
    text: {
      type: [String, Number],
      default: ''
    },
    edit: {
      type: Boolean,
      default: true
    },
    check: {
      type: Boolean,
      default: true
    },
    opened: {
      type: Boolean,
      default: false
    },
    status: {
      type: Boolean,
      default: false
    },
    synced: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    cellStyle: {
      type: Object,
      default: function () {
        return {}
      }
    },
    allowClear: {
      type: Boolean,
      default: false
    },
    autoSize: {
      type: [Object, Boolean],
      default: function () {
        return {
          minRows: 1
        }
      }
    }
  },
  data () {
    return {
      value: this.text,
      editable: false
    }
  },
  created () {
    this.value = this.text
  },
  watch: {
    text: {
      handler (text) {
        if (!this.disabled && this.synced) {
          this.value = this.text
        }
      }
    },
    status: {
      handler (status) {
        if (!this.disabled && status === false) {
          this.editable = false
        }
      }
    },
    opened: {
      immediate: true,
      handler (opened) {
        if (this.disabled) {
          this.editable = false
        } else {
          this.value = this.text
          this.editable = opened
        }
      }
    },
    disabled: {
      immediate: true,
      handler (disabled) {
        if (disabled) {
          this.editable = false
        } else {
          this.editable = this.opened
        }
      }
    }
  },
  methods: {
    doEdit (e) {
      const ev = e || window.event
      this.$emit('update:status', true)
      this.value = this.text
      this.editable = true
      this.$emit('edit', this)
      ev.stopPropagation()
    },
    doChange (e) {
      this.$emit('change', this)
    },
    doConfirm (e) {
      const ev = e || window.event
      if (!this.opened) {
        this.editable = false
      }
      this.$emit('confirm', this)
      ev.stopPropagation()
    },
    doFocus (e) {
      const ev = e || window.event
      this.$emit('focus', this)
      ev.stopPropagation()
    },
    doBlur (e) {
      const ev = e || window.event
      this.$emit('blur', this)
      ev.stopPropagation()
    }
  },
  mounted () {}
}
</script>

<style lang="less" scoped>
.editable-cell {
  width: 100%;
  position: relative;
  &.editabled {
    min-width: 90px;
  }
  & > .editable-cell-text-wrapper {
    min-height: 20px;
    &:not(.disabled-icon) {
      padding-right: 24px;
    }
    &.disabled-icon {
      padding-right: 5px;
    }
    width: 100%;
    position: relative;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: break-all;
    overflow: hidden;
    & > .editable-cell-icon-edit {
      color: #63aef2;
      position: absolute;
      top: 52%;
      right: 8px;
      width: 20px;
      transform: translateY(-50%);
      cursor: pointer;
    }
    & > .editable-cell-icon-edit:hover {
      color: #108ee9;
    }
  }
  & > .editable-cell-textarea-wrapper {
    &:not(.disabled-icon) {
      padding-right: 24px;
    }
    &.disabled-icon {
      padding-right: 2px;
    }
    & > .editable-cell-textarea {
      width: 100%;
    }
    & > .editable-cell-icon-check {
      color: #63aef2;
      position: absolute;
      top: 52%;
      right: 2px;
      width: 20px;
      transform: translateY(-50%);
      cursor: pointer;
    }
    & > .editable-cell-icon-check:hover {
      color: #108ee9;
    }
  }
}
</style>
