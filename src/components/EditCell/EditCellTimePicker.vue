/** 可编辑单元格组件*/
<template>
  <div
    :class="{ editabled: editable }"
    :style="cellStyle.container"
    class="editable-cell"
  >
    <div
      v-if="!disabled && editable"
      :style="cellStyle.selectWrapper"
      :class="{ 'disabled-icon': disabled || !check }"
      class="editable-cell-date-picker-wrapper"
    >
      <a-time-picker
        v-model="date"
        :format="format"
        :style="cellStyle.select"
        :allowClear="allowClear"
        :minute-step="minuteStep"
        :inputReadOnly="inputReadOnly"
        class="editable-cell-date-picker"
        @dblclick.native.stop="() => {}"
        @click.native.stop="() => {}"
        @change="doChange"
      />
      <a-icon
        v-if="!disabled && check"
        :style="cellStyle.check"
        class="editable-cell-icon-check"
        type="check"
        @click="doConfirm"
      />
    </div>
    <div
      v-else
      :title="text"
      :class="{ 'disabled-icon': disabled || !edit }"
      :style="cellStyle.valueWrapper"
      class="editable-cell-value-wrapper"
    >
      {{ text }}
      <a-icon
        v-if="!disabled && edit"
        :style="cellStyle.edit"
        class="editable-cell-icon-edit"
        type="edit"
        @click="doEdit"
      />
    </div>
  </div>
</template>

<script>
import moment from 'moment'

export default {
  name: 'EditCellTimePicker',
  props: {
    format: {
      type: String,
      default: 'HH:mm:ss'
    },
    minuteStep: {
      type: Number,
      default: 1
    },
    allowClear: {
      type: Boolean,
      default: false
    },
    inputReadOnly: {
      type: Boolean,
      default: false
    },
    text: {
      type: [String, Number],
      default: ''
    },
    edit: {
      type: Boolean,
      default: true
    },
    check: {
      type: Boolean,
      default: true
    },
    opened: {
      type: Boolean,
      default: false
    },
    status: {
      type: Boolean,
      default: false
    },
    synced: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    cellStyle: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data () {
    return {
      date: this.doMoment(this.text),
      value: this.text,
      editable: false
    }
  },
  created () {
    this.date = this.doMoment(this.text)
    this.value = this.text
  },
  watch: {
    status: {
      handler (status) {
        if (!this.disabled && status === false) {
          this.editable = false
        }
      }
    },
    opened: {
      immediate: true,
      handler (opened) {
        if (this.disabled) {
          this.editable = false
        } else {
          this.date = this.doMoment(this.text)
          this.value = this.text
          this.editable = opened
        }
      }
    },
    disabled: {
      immediate: true,
      handler (disabled) {
        if (disabled) {
          this.editable = false
        } else {
          this.editable = this.opened
        }
      }
    }
  },
  methods: {
    doMoment (v) {
      return v ? moment(v, this.format) : null
    },
    doFormat (v) {
      return v ? moment(v).format(this.format) : ''
    },
    doEdit (e) {
      const ev = e || window.event
      this.$emit('update:status', true)
      this.date = this.doMoment(this.text)
      this.value = this.text
      this.editable = true
      this.$emit('edit', this)
      ev.stopPropagation()
    },
    doChange (e) {
      this.value = this.doFormat(this.date)
      this.$emit('change', this)
    },
    doConfirm (e) {
      const ev = e || window.event
      if (!this.opened) {
        this.editable = false
      }
      this.$emit('confirm', this)
      ev.stopPropagation()
    }
  },
  mounted () {}
}
</script>

<style lang="less" scoped>
.editable-cell {
  width: 100%;
  position: relative;
  &.editabled {
    min-width: 90px;
  }
  & > .editable-cell-value-wrapper {
    min-height: 20px;
    &:not(.disabled-icon) {
      padding-right: 24px;
    }
    &.disabled-icon {
      padding-right: 2px;
    }
    width: 100%;
    position: relative;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: break-all;
    overflow: hidden;
    & > .editable-cell-icon-edit {
      color: #63aef2;
      position: absolute;
      top: 52%;
      right: 8px;
      width: 20px;
      transform: translateY(-50%);
      cursor: pointer;
    }
    & > .editable-cell-icon-edit:hover {
      color: #108ee9;
    }
  }
  & > .editable-cell-date-picker-wrapper {
    &:not(.disabled-icon) {
      padding-right: 24px;
    }
    &.disabled-icon {
      padding-right: 5px;
    }
    & > .editable-cell-date-picker {
      width: 100%;
    }
    & > .editable-cell-icon-check {
      color: #63aef2;
      position: absolute;
      top: 52%;
      right: 2px;
      width: 20px;
      transform: translateY(-50%);
      cursor: pointer;
    }
    & > .editable-cell-icon-check:hover {
      color: #108ee9;
    }
  }
}
</style>
