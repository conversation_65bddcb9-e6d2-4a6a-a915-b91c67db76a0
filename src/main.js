// with polyfills
import 'core-js/stable'
import 'regenerator-runtime/runtime'

// vue 模块
import Vue from 'vue'
import App from './App'
import store from './store'
import router from './router'

// 控制模块
import './permission'

// 组件模块
import '@/utils/filter'
import '@/core/lazy_use'
import '@/components/global.less'
import bootstrap from '@/core/bootstrap'
import { FormModel, pagination } from 'ant-design-vue'
import fullscreen from 'vue-fullscreen'

// 第三方模块
import { VueAxios } from '@/utils/request'
import * as echarts from 'echarts'
import moment from 'moment'
import { requestBuilder } from '@/utils/util'
// 全局引入vue-draggable-resizable
import VueDraggableResizable from 'vue-draggable-resizable'
Vue.component('vue-draggable-resizable', VueDraggableResizable)

Vue.use(FormModel)
Vue.use(pagination)
Vue.use(fullscreen)
Vue.use(requestBuilder)
// 初始化处理
Vue.use(VueAxios)
Vue.prototype.$requestBuilder = requestBuilder
Vue.prototype.$moment = moment
Vue.prototype.$echarts = echarts
Vue.config.productionTip = false

// 实例化
new Vue({
  router,
  store,
  created: bootstrap,
  render: h => h(App)
}).$mount('#app')
