<template>
  <div>
    <a-upload
      :data="uploadData"
      :file-list="fileList"
      list-type="picture"
      :max-count="1"
      :before-upload="beforeUpload"
      :action="uploadAction"
      :disabled="disabled"
      @change="uploadChange"
      :remove="uploadRemove"
    >
      <a-button :disabled="disabled"> <a-icon type="upload" /> {{ label }} </a-button>
    </a-upload>
  </div>
</template>

<script>
import Vue from 'vue'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import * as fileApi from '@/api/system/file'

// 上传接口
const TOKEN = Vue.ls.get(ACCESS_TOKEN)
const BASE_URL = process.env.VUE_APP_API_BASE_URL

export default {
  name: 'PicUpload',
  props: {
    uploadData: {
      type: Object,
      default: function () {
        return {}
      }
    },
    keyId: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    fieldName: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      default: '图片'
    }
  },
  data () {
    return {
      fileList: [],
      name: '',
      uploadVisible: false,
      uploadAction: BASE_URL + '/file/fileUploadMinio',
      uploadHeaders: { token: TOKEN },
      isShowUpload: true
    }
  },
  methods: {
    beforeUpload (file) {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
      if (!isJpgOrPng) {
        this.$message.error('只支持图片!')
        return false
      }
      if (file.size / 1024 / 1024 > 2) {
        this.$message.error('上传图片大小不可超过2M')
        return false
      }
      this.name = file.name
      if (this.fileList.length > 0) {
        this.deleteOld(this.fileList[0].uid)
      }
      this.fileList.push(file)
    },
    uploadChange (options) {
      if (options.file && ['removed', 'error'].includes(options.file.status)) {
        this.fileList = this.fileList.filter(file => {
          return file.uid !== options.file.uid
        })
      }
      if (options.file && options.file.status === 'done') {
        if (options.file.response && options.file.response.code === '0000') {
          const data = {
            fileNames: [options.file.response.message]
          }
          fileApi.getPicMinio(data).then(res => {
            if (res.code === '0000') {
              this.$set(this.fileList, 0, {
                'name': this.name,
                'uid': options.file.response.message,
                'status': options.file.status,
                'url': res.result[options.file.response.message],
                'thumbUrl': res.result[options.file.response.message]
              })
              this.fileList = [this.fileList[0]]
            }
          }).finally(() => {
            this.$emit('updatePic', this.fieldName, options.file.response.message)
          })
        } else {
          this.fileList = this.fileList.filter(file => {
            return file.uid !== options.file.uid
          })
          this.$notification.error({
            message: '系统消息',
            description: options.file.response.message
          })
        }
      }
    },
    uploadRemove () {
      this.deleteOld(this.fileList[0].uid)
    },
    deleteOld (fileName) {
      fileApi.deleteFileMinio({ fileName: fileName }).then(res => {
        if (res.code === '0000') {
          this.$emit('updatePic', this.fieldName, '')
        }
      })
    }
  }
}
</script>
<style scoped>
.upload-list-inline >>> .ant-upload-list-item {
  float: left;
  width: 400px;
  margin-right: 8px;
}
.upload-list-inline >>> .ant-upload-animate-enter {
  animation-name: uploadAnimateInlineIn;
}
.upload-list-inline >>> .ant-upload-animate-leave {
  animation-name: uploadAnimateInlineOut;
}
</style>
