<template>
  <section>
    <a-drawer
      width="360"
      title="审批"
      :mask="true"
      :maskClosable="true"
      :getContainer="false"
      :visible="visible"
      @close="doClose"
    >
      <a-spin :spinning="loading">
        <a-form
          layout="vertical"
          style="position: relative; z-index: 0"
        >
          <a-form-item label="审批模式:">
            <a-select v-model="approveParam.selectMode">
              <a-select-option
                v-for="(item, index) in approveModeList"
                :disabled="item.disabled"
                :value="item.value"
                :key="index"
              >{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="审批操作:">
            <a-select v-model="approveParam.action">
              <a-select-option value="1">同意</a-select-option>
              <a-select-option
                value="0"
                :disabled="approveReturnDisabled"
              >退回</a-select-option>
              <a-select-option
                value="9"
                :disabled="approveCancelDisabled"
              >取消</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="审批意见:">
            <a-textarea
              placeholder="最多可输入200个文本"
              v-model="approveParam.approveSuggest"
              :autoSize="{ minRows: 3 }"
              :maxLength="200"
            />
          </a-form-item>
          <a-form-item
            v-if="approveParam.showApprove && approveParam.action !== '9'"
            :label="labelApprove"
          >
            <a-select
              v-model="approveParam.selectApprove"
              :mode="approveAutoTag === 'checkbox' ? 'multiple' : 'default'"
              allowClear
            >
              <a-select-option
                v-for="(item, index) in approveList"
                :value="item.value"
                :key="index"
              >{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
        <div v-if="recordSysId">
          <a-divider style="width: calc(100% + 20px); margin: 20px -10px 15px;" />
          <div class="approve-records-select">
            <a-select
              class="border-only-bottom"
              v-model="recordSysId"
              style="width: 100%"
            >
              <a-select-option
                v-for="(item, index) in sysIdOptions"
                :value="item.value"
                :key="index"
              >{{ item.label }}</a-select-option>
            </a-select>
          </div>
          <div class="approve-records-line">
            <div
              v-if="!sysIdRecord"
              style="padding: 8px 14px; font-size: 14px; color: #cccccc"
            >正在查询中...</div>
            <div
              v-else-if="!sysIdRecord.length"
              style="padding: 8px 14px; font-size: 14px; color: #909399"
            >暂无审批记录</div>
            <a-timeline
              v-else
              style="padding: 12px"
            >
              <a-timeline-item
                v-for="(item, index) in sysIdRecord"
                :key="index"
                color="green"
              >
                <div>{{ item.msg }}</div>
                <div>{{ item.flag }}</div>
                <div>{{ item.createBy }}</div>
                <div>{{ item.createDate }}</div>
              </a-timeline-item>
              <a-timeline-item>
                <span slot="dot" />
              </a-timeline-item>
            </a-timeline>
          </div>
        </div>
      </a-spin>
      <div class="drawer-footer">
        <div class="footer-fixed">
          <a-button @click="doClose">返回</a-button>
          <a-button
            type="primary"
            :loading="loading"
            @click="doApprove"
          >提交</a-button>
        </div>
      </div>
    </a-drawer>
  </section>
</template>

<script>
import {
  // 填报状态
  APPROVE_STATUS_READY_CODE,
  APPROVE_STATUS_CONFIRM_CODE,
  // 审批节点
  APPROVE_NODES_NOT_RETURN,
  APPROVE_NODES_ALLOW_APPROVE,
  APPROVE_NODES_ALLOW_CANCEL
} from '@/store/variable-types'

import { STable } from '@/components'
import { OPERATOR, PERSON_ID } from '@/store/mutation-types'
import { requestBuilder } from '@/utils/util'
import * as approveApi from '@/api/system/approve'
import Vue from 'vue'
// 操作人 userId
const USER_OPERATOR = Vue.ls.get(OPERATOR)
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)

export default {
  name: 'ApproveDrawer',
  components: {
    STable
  },
  props: {
    dataSource: {
      type: Array,
      default: function () {
        return []
      }
    },
    selectedRows: {
      type: Array,
      default: function () {
        return []
      }
    },
    dataChanged: {
      type: Function,
      default: function () {}
    },
    drawerClosed: {
      type: Function,
      default: function () {}
    },
    selectChanged: {
      type: Function,
      default: function () {}
    },
    getApproveId: {
      type: Function,
      default: function () {}
    },
    keyName: {
      type: String,
      default: ''
    },
    origin: {
      type: String,
      default: ''
    },
    statusName: {
      type: String,
      default: 'status'
    }
  },
  data () {
    return {
      labelApprove: '',

      // 审批抽屉框
      approveList: [],
      approveModeList: [
        {
          label: '审批选中数据',
          value: '1'
        }
      ],
      approveParam: {
        showApprove: false,
        selectMode: '',
        selectSysIds: [],
        selectApprove: '',
        approveSuggest: '',
        action: '1'
      },
      approveAutoTag: null,
      visible: false,
      loading: false,
      refresh: false,

      // 审批记录缓存
      sysIdRecords: {},
      recordSysId: ''
    }
  },
  computed: {
    // 审批记录下拉框
    sysIdOptions () {
      return this.approveParam.selectSysIds.map(item => {
        return {
          label: '申报单号:' + item[this.keyName],
          value: item[this.keyName]
        }
      })
    },
    // 审批记录查询
    sysIdRecord () {
      if (!this.sysIdRecords.hasOwnProperty(this.recordSysId)) {
        this.queryRecords(this.recordSysId)
        return false
      }
      return this.sysIdRecords[this.recordSysId] || []
    },
    // 审批退回禁用与否
    approveReturnDisabled () {
      if (this.approveParam.selectMode !== '1') {
        return true
      }
      for (const select of this.approveParam.selectSysIds) {
        const item = this.selectedRows.find(item => item[this.keyName] === select[this.keyName])
        if (!item) {
          return true
        }
        if (APPROVE_NODES_NOT_RETURN.includes(this.getApproveId(item[this.statusName]))) {
          return true
        }
      }
      return false
    },
    // 审批取消禁用与否
    approveCancelDisabled () {
      if (this.origin === 'discardMatu') {
        return false
      }
      if (this.approveParam.selectMode !== '1') {
        return true
      }
      for (const select of this.approveParam.selectSysIds) {
        const item = this.dataSource.find(item => item[this.keyName] === select[this.keyName])
        if (!APPROVE_NODES_ALLOW_CANCEL.includes(this.getApproveId(item[this.statusName]))) {
          return true
        }
      }
      return false
    }
  },
  created () {
    // 初始化选项获取
    this.initOptions()
  },
  watch: {
    // 监听 - 审批选定采购计划更改时 (默认查询审批记录下拉框中第一条)
    'approveParam.selectSysIds': {
      handler (list) {
        if (!list.includes(this.recordSysId)) {
          this.recordSysId = ''
        }
        if (!this.recordSysId) {
          this.recordSysId = list.length > 0 ? list[0][this.keyName] : ''
        }
      }
    },
    // 监听 - 审批退回禁用时 (如果当时审批操作是“退回”， 则更改为“同意“)
    'approveReturnDisabled': {
      handler (disabled) {
        if (disabled && this.approveParam.action === '0') {
          this.approveParam.action = '1'
        }
      }
    },
    // 监听 - 审批取消禁用时 (如果当时审批操作是“取消”， 则更改为“同意“)
    'approveCancelDisabled': {
      handler (disabled) {
        if (disabled && this.approveParam.action === '9') {
          this.approveParam.action = '1'
        }
      }
    },
    // 监听 - 审批操作更改时 (重置下一个审批人选项)
    'approveParam.action': {
      handler () {
        if (this.approveAutoTag === 'checkbox') {
          this.approveParam.selectApprove = []
        }
        this.approveParam.selectApprove = ''
      }
    }
  },
  methods: {
    // 下拉框数据获取
    initOptions () {
    },
    // 查询下个审批人
    queryPerson (param) {
      approveApi.queryAssigee(requestBuilder('', param)).then(res => {
        if (res.code !== '0000') {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '审批人获取失败！'
          })
          return Promise.reject(res.result)
        }
        this.approveList = res.result
      })
      this.approveList = []
    },
    // 根据单号查询审批记录
    queryRecords (keyName) {
      approveApi.queryApproHis({ origin: this.origin, businessNum: keyName }).then(res => {
        this.sysIdRecords = {
          ...this.sysIdRecords,
          [keyName]: res.result || []
        }
      })
      this.sysIdRecords[keyName] = false
    },
    // 指定 数据弹框
    doRecord (record) {
      this.refresh = true
      this.dataSource.splice(0, this.dataSource.length, record)
      this.selectedRows.splice(0, this.selectedRows.length, record)
      this.doOpen()
    },
    // 打开 审批弹框
    doOpen () {
      // 审批人类型
      let autoTag = null
      const origin = this.origin

      // 过滤审批模式选项
      this.approveModeList.forEach(item => {
        if (item.value === '1') {
          item.disabled = !this.dataSource.some(item => {
            const statusId = this.getApproveId(item[this.statusName])
            console.log(item[this.statusName], 'item[this.statusName]')
            console.log(statusId, 'statusId')
            const isApproveUser = !!item.userNext && item.userNext.split(',').includes(USER_PERSON_ID)
            // console.log('###########APPROVE_STATUS_READY_CODE', APPROVE_STATUS_READY_CODE, statusId, this.statusName)
            const isApproveReady = [APPROVE_STATUS_READY_CODE].includes(statusId)
            const isApproveAllow = APPROVE_NODES_ALLOW_APPROVE.includes(statusId)
            return isApproveReady || (isApproveAllow && isApproveUser)
          })
        }
      })
      // 如当前页无需申报，则提示
      if (!this.approveModeList.some(item => !item.disabled)) {
        this.$message.error('当前页没有需要审批的！')
        return
      }

      // 过滤无权限审批
      const selectedRows = this.selectedRows.filter(item => {
        const statusId = this.getApproveId(item[this.statusName])
        const isApproveUser = !!item.userNext && item.userNext.split(',').includes(USER_PERSON_ID)
        const isApproveReady = [APPROVE_STATUS_READY_CODE].includes(statusId)
        const isApproveAllow = APPROVE_NODES_ALLOW_APPROVE.includes(statusId)
        return isApproveReady || (isApproveAllow && isApproveUser)
      })
      const selectedRowKeys = selectedRows.map(item => item[this.keyName])

      // 触发更改当前选项
      this.selectChanged(selectedRowKeys, selectedRows)

      // 如当前页仅剩含申报单号，且未选择则提示
      if (selectedRows.length === 0) {
        this.$message.error('请选择需要审批的条目！')
        return
      }

      // 如当前页审批人类型不一致则提示
      for (const item of selectedRows) {
        if (autoTag === null || autoTag === item.autoTag) {
          autoTag = item.autoTag
        } else {
          this.$message.error('当前所选审批人模式不一致！')
          return
        }
      }
      this.labelApprove = '下个审批人:'
      // 设置 autoTag
      if (autoTag !== null && autoTag !== undefined && autoTag !== '1') {
        if (autoTag === 'checkbox') {
          this.approveParam.selectApprove = []
        }
        this.approveAutoTag = autoTag
        this.approveParam.showApprove = true
        this.queryPerson({ autoTag, origin, uuids: [selectedRowKeys[0]] })
      }

      // 初始化已选择的申报单号
      this.approveParam.selectSysIds = [...selectedRows]
      this.approveParam.selectMode = selectedRowKeys.length > 0 ? '1' : '2'
      this.visible = true
    },
    // 关闭 审批弹窗
    doClose () {
      // 是否刷新
      if (this.refresh) {
        this.refresh = false
        this.dataChanged()
      } else {
        this.drawerClosed()
      }
      this.approveList = []
      this.sysIdRecords = {}
      this.recordSysId = ''
      this.approveParam = {
        showApprove: false,
        selectMode: '',
        selectSysIds: [],
        selectApprove: '',
        approveSuggest: '',
        action: '1'
      }
      this.approveAutoTag = null
      this.visible = false
    },
    // 审批调用
    doApprove () {
      const approveParam = this.approveParam
      if (!approveParam.selectMode) {
        this.$message.error('请选择审批模式！')
        return
      }
      // showApprove 为 true时，须指定审批人
      if (approveParam.showApprove && approveParam.selectApprove.length === 0) {
        if (
          approveParam.selectSysIds.some(item => {
            const statusId = this.getApproveId(item[this.statusName])
            return ![APPROVE_STATUS_CONFIRM_CODE].includes(statusId)
          }) &&
          !['0', '9'].includes(approveParam.action)
        ) {
          this.$message.error('请指定下个审批人！')
          return
        }
      }

      const pattern = 'approByNum'
      const needUserId = approveParam.showApprove && approveParam.action !== '9'
      const selectApproveArr = [].concat(approveParam.selectApprove)
      const userId = needUserId ? selectApproveArr.join(',') : ''
      const list = approveParam.selectSysIds.map(item => item[this.keyName])
      const msg = approveParam.approveSuggest
      const flag = approveParam.action

      const param = {
        op: USER_OPERATOR,
        pattern: pattern,
        userId: userId,
        list: list,
        msg: msg,
        flag: flag,
        origin: this.origin
      }
      this.loading = true
      this.$emit('toApprove', param)
    }
  }
}
</script>
