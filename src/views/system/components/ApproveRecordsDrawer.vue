<template>
  <section>
    <a-drawer
      width="360"
      title="审批记录"
      :mask="true"
      :maskClosable="true"
      :getContainer="false"
      :visible="visible"
      @close="doClose"
    >
      <div class="approve-records-select">
        <a-select
          class="border-only-bottom"
          v-model="recordSysId"
          style="width: 100%"
        >
          <a-select-option
            v-for="(item, index) in sysIdOptions"
            :value="item.value"
            :key="index"
          >
            {{
              item.label
            }}
          </a-select-option>
        </a-select>
      </div>
      <div class="approve-records-line">
        <div
          v-if="!sysIdRecord"
          style="padding: 8px 14px; font-size: 14px; color: #cccccc"
        >正在查询中...</div>
        <div
          v-else-if="!sysIdRecord.length"
          style="padding: 8px 14px; font-size: 14px; color: #909399"
        >暂无审批记录</div>
        <a-timeline
          v-else
          style="padding: 12px"
        >
          <a-timeline-item
            v-for="(item, index) in sysIdRecord"
            :key="index"
            color="green"
          >
            <div>{{ item.msg }}</div>
            <div>{{ item.flag }}</div>
            <div>{{ item.createBy }}</div>
            <div>{{ item.createDate }}</div>
          </a-timeline-item>
          <a-timeline-item>
            <span slot="dot" />
          </a-timeline-item>
        </a-timeline>
      </div>
      <div class="drawer-footer">
        <div class="footer-fixed">
          <a-button @click="doClose">返回</a-button>
        </div>
      </div>
    </a-drawer>
  </section>
</template>

<script>
import * as approveApi from '@/api/system/approve'

export default {
  name: 'ApproveRecordsDrawer',
  props: {
    recordSysId: {
      type: String,
      default: ''
    },
    origin: {
      type: String,
      default: ''
    }
  },
  data () {
    return {

      // 审批记录
      sysIdRecords: {},
      visible: false
    }
  },
  computed: {
    // 审批记录下拉框
    sysIdOptions () {
      return [
        {
          label: '申报单号:' + this.recordSysId,
          value: this.recordSysId
        }
      ]
    },
    // 审批记录查询
    sysIdRecord () {
      if (!this.sysIdRecords.hasOwnProperty(this.recordSysId)) {
        this.doQuery(this.recordSysId)
        return false
      }
      return this.sysIdRecords[this.recordSysId] || []
    }
  },
  watch: {
    recordSysId: {
      handler (recordSysId) {
        recordSysId ? this.doOpen() : this.doClose()
      }
    }
  },
  methods: {
    // 查询 审批记录
    doQuery (key) {
      approveApi.queryApproHis({ origin: this.origin, businessNum: key }).then(res => {
        this.sysIdRecords = {
          ...this.sysIdRecords,
          [key]: res.result || []
        }
      })
      this.sysIdRecords[key] = false
    },
    // 打开 审批记录
    doOpen () {
      this.visible = true
    },
    // 关闭 审批记录
    doClose () {
      this.$emit('update:recordSysId', '')
      this.visible = false
    }
  }
}
</script>
