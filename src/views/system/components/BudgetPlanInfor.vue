<template>
  <section>
    <a-modal v-model="visible" title="计划信息" width="1000px" centered>
      <template slot="footer">
        <a-button type="primary" @click="() => {this.visible = false}">
          关闭
        </a-button>
      </template>
      <a-form layout="inline">
        <a-row :gutter="Grid.gutter">
          <a-col
            :xl="Grid.xl"
            :md="Grid.md"
            :sm="Grid.sm"
          >
            <a-form-item label="预算编号">
              <a-input
                v-model="queryParam.budgetId"
                @pressEnter="doPlanSearch"
              />
            </a-form-item>
          </a-col>
          <a-col
            :xl="Grid.xl"
            :md="Grid.md"
            :sm="Grid.sm"
          >
            <a-form-item label="计划描述">
              <a-input
                v-model="queryParam.description"
                @pressEnter="doPlanSearch"
              />
            </a-form-item>
          </a-col>
          <a-col
            :xl="Grid.xl"
            :md="Grid.md"
            :sm="Grid.sm"
          >
            <a-form-item label="计划单号">
              <a-input
                v-model="queryParam.prlineNum"
                @pressEnter="doPlanSearch"
              />
            </a-form-item>
          </a-col>
          <a-col
            :xl="Grid.xl"
            :md="Grid.md"
            :sm="Grid.sm"
          >
            <a-form-item label="类型">
              <a-select
                v-model="queryParam.type"
                @change="doPlanSearch"
              >
                <a-select-option value="prline">计划</a-select-option>
                <a-select-option value="outsource">委外</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :xl="Grid.xl"
            :md="Grid.md"
            :sm="Grid.sm"
          >
            <a-button
              type="primary"
              icon="search"
              @click="doPlanSearch"
            >查询</a-button>
          </a-col>
        </a-row>
      </a-form>
      <a-card :bordered="false">
        <s-table
          ref="planTable"
          :data="PlanLoadData"
          :scroll="scroll"
          :columns="PlanColumns"
          :rowClassName="rowClassName"
          :showPagination="true"
          :pageSizeOptions="pageSizeOptions"
          :pageSize="20"
          :immediate="true"
          :bordered="false"
          :rowKey="(record) => (record.uuid || record.prlineSysId)"
        >
          <span slot="serial" slot-scope="text, record, index">
            {{ index + 1 }}
          </span>
          <template slot="footer">
            <div class="footer-group">
              <div class="footer-item">
                <span class="name">预算金额:</span>
                <span class="name">{{ total }}</span>
              </div>
              <div class="footer-item">
                <span class="name">已用金额:</span>
                <span class="name">{{ used }}</span>
              </div>
            </div>
          </template>
        </s-table>
      </a-card>
    </a-modal>
  </section>
</template>

<script>
import { requestBuilder } from '@/utils/util'
import * as budgetApi from '@/api/device/budget'
import { STable } from '@/components'
export default {
  name: 'BudgetPlanInfor',
  components: {
    STable
  },
  data () {
    return {
      Grid: {
        xl: 6,
        md: 6,
        sm: 24,
        gutter: 10
      },
      // 计划信息页面
      visible: false,
      used: 0,
      total: 0,
      queryParam: {
        budgetId: '',
        type: ''
      },
      scroll: {
        x: '100%',
        y: 'calc(50vh)',
        scrollToFirstRowOnChange: false
      },
      rowClassName: record => {
        return ''
      },
      pageSizeOptions: ['10', '15', '20', '25', '30', '50'],
      PlanLoadData: parameter => {
        return budgetApi.queryPlanInfo(requestBuilder('', this.queryParam,
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder)).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '计划获取失败！'
            })
            return Promise.reject(res.result)
          }
          return res.result
        })
      },
      PlanColumns: [

      ],
      prlineColumns: [
        {
          title: '预算编号',
          dataIndex: 'budgetId',
          align: 'center',
          ellipsis: true,
          fixed: true,
          width: 150
        },
        {
          title: '预算名称',
          dataIndex: 'budgetName',
          ellipsis: true,
          align: 'center',
          fixed: true,
          width: 150
        },
        {
          title: '计划类型',
          dataIndex: 'lineType',
          align: 'center',
          width: 100
        },
        {
          title: '计划单号',
          dataIndex: 'prlineNum',
          align: 'center',
          width: 100
        },
        {
          title: '计划描述',
          dataIndex: 'description',
          ellipsis: true,
          align: 'center',
          width: 100
        },
        {
          title: '设备',
          dataIndex: 'equipment',
          ellipsis: true,
          align: 'center',
          width: 100
        },
        {
          title: '物资编码',
          dataIndex: 'materialNum',
          ellipsis: true,
          align: 'center',
          width: 80
        },
        {
          title: '物资名称',
          dataIndex: 'materialName',
          ellipsis: true,
          align: 'center',
          width: 80
        },
        {
          title: '数量',
          dataIndex: 'orderQty',
          ellipsis: true,
          align: 'center',
          width: 80
        },
        {
          title: '单价',
          dataIndex: 'unitCost',
          ellipsis: true,
          align: 'center',
          width: 80
        },
        {
          title: '单位',
          dataIndex: 'orderUnit',
          ellipsis: true,
          align: 'center',
          width: 80
        },
        {
          title: '总价',
          dataIndex: 'lineCost',
          ellipsis: true,
          align: 'center',
          width: 80
        },
        {
          title: '状态',
          dataIndex: 'status',
          ellipsis: true,
          align: 'center',
          width: 80
        }
      ],
      OutsourceColumns: [
        {
          title: '预算编号',
          dataIndex: 'budgetId',
          align: 'center',
          ellipsis: true,
          fixed: true,
          width: 150
        },
        {
          title: '预算名称',
          dataIndex: 'budgetName',
          ellipsis: true,
          align: 'center',
          fixed: true,
          width: 150
        },
        {
          title: '计划类型',
          dataIndex: 'lineType',
          align: 'center',
          width: 100
        },
        {
          title: '委外单号',
          dataIndex: 'outsourceNum',
          align: 'center',
          width: 100
        },
        {
          title: '委外描述',
          dataIndex: 'description',
          ellipsis: true,
          align: 'center',
          width: 100
        },
        {
          title: '设备',
          dataIndex: 'equipment',
          ellipsis: true,
          align: 'center',
          width: 100
        },
        {
          title: '计划名称',
          dataIndex: 'outsourceName',
          ellipsis: true,
          align: 'center',
          width: 80
        },
        {
          title: '委外类型',
          dataIndex: 'outsourceType',
          ellipsis: true,
          align: 'center',
          width: 80
        },
        {
          title: '数量',
          dataIndex: 'orderQty',
          ellipsis: true,
          align: 'center',
          width: 80
        },
        {
          title: '单价',
          dataIndex: 'unitCost',
          ellipsis: true,
          align: 'center',
          width: 80
        },
        {
          title: '单位',
          dataIndex: 'orderUnit',
          ellipsis: true,
          align: 'center',
          width: 80
        },
        {
          title: '总价',
          dataIndex: 'lineCost',
          ellipsis: true,
          align: 'center',
          width: 80
        },
        {
          title: '状态',
          dataIndex: 'status',
          ellipsis: true,
          align: 'center',
          width: 80
        }
      ]
    }
  },
  watch: {
    'queryParam.type': {
      handler (type) {
        this.PlanColumns = type === 'prline' ? this.prlineColumns : this.OutsourceColumns
      }
    }
  },
  methods: {
    doPlanSearch () {
      this.$refs.planTable.refresh()
    },
    planInfoShow (record) {
      this.queryParam.budgetId = record.budgetId
      this.queryParam.type = 'prline'
      this.visible = true
      this.getGridAmountInfor(record.budgetId)
      this.$refs.planTable.refresh()
    },
    getGridAmountInfor (budgetId) {
      budgetApi.getGridAmountInfor({ budgetId: budgetId }).then(res => {
        this.total = res.result.total
        this.used = res.result.Used
      })
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep {
  .ant-table-footer {
    padding: 8px 50px;
    .footer-group {
      display: flex;
      flex-flow: row nowrap;
      justify-content: center;
      & > .footer-item {
        flex: 0 0 auto;
        margin: 0 30px;
        font-size: 12px;
        color: #606266;
      }
    }
  }
}
</style>
