<template>
  <section>
    <a-modal
      title="请选择单位"
      width="880px"
      :visible="visible"
      :confirm-loading="confirmLoading"
      :closable="closable"
      @cancel="handleCancel"
    >
      <s-table
        ref="table"
        rowKey="id"
        :columns="columns"
        :data="loadData"
        :rowSelection="rowSelection"
        :customRow="rowClick"
        :scroll="scroll"
        :showPagination="false" />
      <template slot="footer">
        <a-button key="back" @click="handleCancel" v-if="closable">返回</a-button>
        <a-button key="submit" type="primary" :loading="confirmLoading" @click="handleOk">
          登录
        </a-button>
      </template>
    </a-modal>
  </section>
</template>

<script>
import { requestBuilder } from '@/utils/util'
import { STable, Ellipsis } from '@/components'
import * as loginApi from '@/api/system/login'
import { OPERATOR, ORG_ID, ACCESS_TOKEN, PERSON_ID, CC_TOKEN, TENANT, MOBILE_PHONE } from '@/store/mutation-types'
export default {
  name: 'ChooseCompanyModal',
  components: {
    STable
  },
  props: {
    list: {
      type: Array,
      default () {
        return []
      }
    }
  },
  data () {
    return {
      visible: false,
      closable: false,
      confirmLoading: false,
      selectedRowKeys: [],
      selectedRows: [],
      rowSelection: {
        type: 'radio',
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      },
      scroll: {
        x: 'max-content',
        scrollToFirstRowOnChange: false
      },
      // 故障管理表格行
      columns: [
        {
          title: '单位名称',
          dataIndex: 'orgName',
          width: 200
        },
        {
          title: '组织id',
          dataIndex: 'orgId',
          width: 120
        },
        {
          title: '所属部门',
          dataIndex: 'departmentName',
          width: 120
        },
        {
          title: '用户姓名',
          dataIndex: 'personName',
          width: 120
        },
        {
          title: '上次登录日期',
          dataIndex: 'loginDate',
          width: 200
        }
      ],
      loadData: () => {
        return this.list
      },
      rowClick: record => ({
        on: {
          // 点击事件
          click: () => {
            this.selectedRows = [record]
            this.selectedRowKeys = [record.id]
            this.$refs.table.triggerSelect(this.selectedRowKeys, this.selectedRows)
          }
        }
      })
    }
  },
  created () {
    this.initOptions()
  },
  methods: {
    // 初始化下拉框数据获取
    initOptions () {
    },
    showModal (closable) {
      this.visible = true
      this.closable = closable
    },
    handleOk (e) {
      if (!this.selectedRows.length > 0) {
        return this.$message.warn('请选择单位！')
      }
      this.confirmLoading = true
      loginApi.tenantLogin(requestBuilder('', this.selectedRows[0])).then(res => {
        if (res.code !== '0000') {
          localStorage.clear()
          this.$router.push({ name: 'dashboard' })
          return this.$notification.error({
            message: '系统消息',
            description: '登录失败，请联系管理员！'
          })
        }
        this.handle(res)
      }).finally(() => {
        this.confirmLoading = false
      })
    },
    handleCancel (e) {
      this.visible = false
      this.confirmLoading = false
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    handle (res) {
      this.$store.dispatch('DelAllTags', [])
      localStorage.clear()
      const DATA = res.result
      var DATA_ACCESS_TOKEN = DATA.ACCESS_TOKEN
      var DATA_PERSON_ID = DATA.PERSON_ID
      var DATA_OPERATOR = DATA.OPERATOR
      var DATA_ORG_ID = DATA.ORG_ID
      var DATA_CC_TOKEN = DATA.CC_TOKEN
      var DATA_TENANT = DATA.TENANT
      var DATA_MOBILE_PHONE = DATA.MOBILE_PHONE
      const a = { 'value': DATA_ACCESS_TOKEN, 'expire': null }
      const b = { 'value': DATA_PERSON_ID, 'expire': null }
      const c = { 'value': DATA_OPERATOR, 'expire': null }
      const d = { 'value': DATA_ORG_ID, 'expire': null }
      const e = { 'value': DATA_CC_TOKEN, 'expire': null }
      const f = { 'value': DATA_TENANT, 'expire': null }
      const g = { 'value': DATA_MOBILE_PHONE, 'expire': null }
      localStorage.setItem('nbggoods__' + ACCESS_TOKEN, JSON.stringify(a))
      localStorage.setItem('nbggoods__' + PERSON_ID, JSON.stringify(b))
      localStorage.setItem('nbggoods__' + OPERATOR, JSON.stringify(c))
      localStorage.setItem('nbggoods__' + ORG_ID, JSON.stringify(d))
      localStorage.setItem('nbggoods__' + CC_TOKEN, JSON.stringify(e))
      localStorage.setItem('nbggoods__' + TENANT, JSON.stringify(f))
      localStorage.setItem('nbggoods__' + MOBILE_PHONE, JSON.stringify(g))
      this.$router.push({ name: 'dashboard' })
      // 单点进来的不需要刷新，手动菜单切换单位的需要刷新当前页面
      if (this.closable) {
        window.location.reload()
      }
      this.handleCancel()
    }
  }
}
</script>

<style lang="less" scoped>
</style>
