<template>
  <section>
    <a-drawer
      v-if="!directDisplay && !tableDisplay"
      width="360"
      title="附件"
      :mask="true"
      :maskClosable="false"
      :getContainer="false"
      :visible="uploadVisible"
      @close="hiddenUploadDrawer"
      :zIndex="1000"
    >
      <div class="drawer-content">
        <a-spin :spinning="fileListLoading" :tip="tip">
          <a-upload
            class="drawer-content-upload"
            :multiple="true"
            :data="uploadData"
            :action="uploadAction"
            :headers="uploadHeaders"
            :disabled="!isShowUpload"
            :file-list="uploadFileList"
            :beforeUpload="uploadBeforeUpload"
            :remove="uploadRemove"
            @change="uploadChange"
            @preview="uploadPreview"
          >
            {{ text }}
            <a-button
              v-if="isShowUpload">
              <a-icon type="upload" />上传
            </a-button>
          </a-upload>
        </a-spin>
      </div>
      <div class="drawer-footer">
        <div class="footer-fixed">
          <a-button @click="hiddenUploadDrawer">返回</a-button>
        </div>
      </div>
    </a-drawer>
    <div class="drawer-content" v-if="directDisplay">
      <a-spin :spinning="fileListLoading" :tip="tip">
        <a-upload
          class="drawer-content-upload"
          :multiple="true"
          :data="uploadData"
          :action="uploadAction"
          :headers="uploadHeaders"
          :disabled="!isShowUpload"
          :file-list="uploadFileList"
          :beforeUpload="uploadBeforeUpload"
          :remove="uploadRemove"
          @change="uploadChange"
          @preview="uploadPreview"
        >
          {{ text }}
          <a-button
            v-if="isShowUpload">
            <a-icon type="upload" />上传
          </a-button>
        </a-upload>
      </a-spin>
    </div>
    <div class="drawer-content" v-if="tableDisplay">
      <a-spin :spinning="fileListLoading" :tip="tip">
        <a-upload
          class="drawer-content-upload"
          :multiple="true"
          :data="uploadData"
          :action="uploadAction"
          :showUploadList="false"
          :headers="uploadHeaders"
          :file-list="uploadFileList"
          :openFileDialogOnClick="openFileDialogOnClick"
          :disabled="!isShowUpload"
          :beforeUpload="uploadBeforeUpload"
          :remove="uploadRemove"
          @change="uploadChange"
          @preview="uploadPreview"
        >
          {{ text }}
          <!-- <a-button
            v-if="isShowUpload">
            <a-icon type="upload" />上传
          </a-button> -->
          <a-dropdown>
            <template #overlay>
              <a-menu @click="handleMenuClick">
                <a-menu-item v-for="item in fileTypes" :key="item.value">
                  {{ item.label }}
                </a-menu-item>
              </a-menu>
            </template>
            <a-button v-if="isShowUpload" type="primary" ref="uploadBtn">
              <a-icon type="upload" />附件上传
            </a-button>
          </a-dropdown>
        </a-upload>
      </a-spin>
      <s-table
        ref="fileTable"
        :data="fileData"
        :columns="fileColumns"
        :immediate="immediate"
        :bordered="bordered"
        :showPagination="false"
        rowKey="uuid"
      >
        <span
          slot="fileType"
          slot-scope="text2"
        >{{ takeSelectLabel(fileTypes,text2) }}</span>
        <span
          slot="createBy"
          slot-scope="text2"
        >{{ takeSelectLabel(persons,text2) }}</span>
        <span
          slot="fileName"
          slot-scope="text2,record"
        ><a href="javascript:void(0)" @click.stop="uploadPreview(record)">
          <ellipsis
            :length="30"
            tooltip
          >{{ text2 }}</ellipsis>
        </a></span>
        <span
          slot="action"
          slot-scope="text2, record"
        >
          <template>
            <a
              style="margin: 0 5px;color: red;"
              @click.stop="removeFile(record)"
            >删除</a>
          </template>
        </span>
      </s-table>
    </div>
  </section>
</template>

<script>
import Vue from 'vue'
// import { Base64 } from 'js-base64'
import { ACCESS_TOKEN, ORG_ID, PERSON_ID } from '@/store/mutation-types'
import { takeTreeByKey, requestBuilder } from '@/utils/util'
import { STable, Ellipsis } from '@/components'
import * as fileApi from '@/api/system/file'
import * as baseApi from '@/api/system/base'

// 上传附件允许类型
const uploadAccept = [
  // Excel
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel',
  // WORD
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
  'application/vnd.ms-word.document.macroEnabled.12',
  'application/vnd.ms-word.template.macroEnabled.12',
  'application/msword',
  // PDF
  'application/pdf',
  // IMG
  'image/png',
  'image/jpeg',
  'image/bmp',
  'image/gif',
  // zip
  'application/zip',
  'application/x-zip-compressed',
  'multipart/x-zip'
]

// 上传接口
const TOKEN = Vue.ls.get(ACCESS_TOKEN)
const BASE_URL = process.env.VUE_APP_API_BASE_URL
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)
// const VUE_APP_UPLOAD_PREVIEW_URL = process.env.VUE_APP_UPLOAD_PREVIEW_URL

export default {
  name: 'Appendix',
  components: {
    STable,
    Ellipsis
  },
  props: {
    // 模块名
    businessName: {
      type: String,
      default: ''
    },
    // 可否对附件做出增删
    isShowDeleteAndUpload: {
      type: Function,
      default: function () {
        return true
      }
    },
    directDisplay: {
      type: Boolean,
      default: false
    },
    tableDisplay: {
      type: Boolean,
      default: false
    },
    text: {
      type: String,
      default: ''
    },
    // 唯一编码
    keyId: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      USER_ORG_ID,
      // 上传参数
      uploadData: {},
      uploadFileList: [],
      uploadVisible: false,
      uploadAction: BASE_URL + '/file/minio/fileUpload',
      uploadAccept: uploadAccept.join(','),
      uploadHeaders: { token: TOKEN, personId: USER_PERSON_ID, fileType: '', orgId: USER_ORG_ID },
      fileListLoading: false,
      tip: '加载中',
      isShowUpload: true,
      selectFileType: '',
      fileTypes: [],
      persons: [],
      immediate: true,
      bordered: false,
      openFileDialogOnClick: false,
      relateUuid: '',
      fileDataList: [],
      cacheBusinessName: '',
      cacheKeyId: '',
      fileColumns: [
        {
          title: '附件类型',
          dataIndex: 'fileType',
          scopedSlots: { customRender: 'fileType' },
          align: 'center',
          width: 120
        },
        {
          title: '附件名称',
          dataIndex: 'fileName',
          scopedSlots: { customRender: 'fileName' },
          align: 'center',
          width: 200
        },
        {
          title: '附件格式',
          dataIndex: 'fileFormat',
          align: 'center',
          width: 80
        },
        {
          title: '上传时间',
          dataIndex: 'createDate',
          align: 'center',
          width: 180
        },
        {
          title: '附件大小(K)',
          dataIndex: 'fileSize',
          align: 'center',
          width: 100
        },
        {
          title: '上传用户',
          dataIndex: 'createBy',
          scopedSlots: { customRender: 'createBy' },
          align: 'center',
          width: 100
        },
        {
          title: '操作',
          key: 'action',
          scopedSlots: { customRender: 'action' },
          align: 'center',
          fixed: 'right',
          width: 80
        }
      ]
    }
  },
  created () {
    this.initOptions()
  },
  watch: {
    uploadFileList: {
      handler (uploadFileList) {
        if (this.tableDisplay && uploadFileList.length > 0) {
          this.queryUploadFile(this.cacheBusinessName, this.cacheKeyId)
        }
      }
    },
    fileTypes: {
      handler (fileTypes) {
        if (this.tableDisplay && fileTypes.length > 0) {
          this.selectFileType = fileTypes[0].value
          console.log('this.selectFileType', this.selectFileType)
        }
      }
    }
  },
  methods: {
    initOptions () {
      baseApi.getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'file_type' } }).then(res => {
        if (res.code === '0000') {
          this.fileTypes = res.result
        }
      })
      baseApi.getCommboxById({ id: 'applyPerson' }).then(res => {
        if (res.code === '0000') {
          this.persons = res.result
        }
      })
    },
    fileData () {
      return this.fileDataList
    },
    uploadBeforeUpload (file) {
      console.log('file.type', file.type)
      // if (!uploadAccept.includes(file.type)) {
      //   this.$message.error('仅支持上传word、excel、图片与zip格式压缩包等附件')
      //   return false
      // }
      if (file.size / 1024 / 1024 > 50) {
        this.$message.error('上传附件大小不可超过50M')
        return false
      }
      if (file.name.indexOf('_') !== -1) {
        this.$message.error('上传附件文件名不允许包含字符\'_\',请重新命名')
        return false
      }
      this.tip = '附件上传中...'
      this.fileListLoading = true
      this.uploadFileList.push(file)
    },
    uploadChange (options) {
      if (options.file && ['removed', 'error'].includes(options.file.status)) {
        this.uploadFileList = this.uploadFileList.filter(file => {
          return file.uid !== options.file.uid
        })
      }
      if (options.file && options.file.status === 'done') {
        if (options.file.response && options.file.response.code === '0000') {
          this.uploadFileList = this.uploadFileList.map(file => {
            if (file.uid === options.file.uid) {
              console.log('options', options)
              console.log('file', file)
              file.url = options.file.response.result.fileUrl
              file.uid = options.file.response.result.fileUrl
              file.fulleName = options.file.response.result.fullFileName
              file.status = options.file.status
              this.fileListLoading = false
              this.tip = '加载中'
            }
            return file
          })
        } else {
          this.uploadFileList = this.uploadFileList.filter(file => {
            return file.uid !== options.file.uid
          })
          this.$notification.error({
            message: '系统消息',
            description: '上传附件失败！'
          })
          this.fileListLoading = false
        }
      }
      if (options.file && options.file.status === 'error') {
        this.$notification.error({
          message: '系统消息',
          description: '上传附件失败！'
        })
        this.fileListLoading = false
      }
      this.openFileDialogOnClick = false
    },
    uploadPreview (file) {
      this.$confirm({
        content: '打开方式',
        zIndex: 10001,
        // cancelText: '预览',
        okText: '下载',
        onOk () {
          // url请求地址替换
          // console.log('BASE_PREVIEW_URL', BASE_PREVIEW_URL)
          // // fileApi.downFile(file).then(res => {
          // //   console.log('res', res)
          // // })
          window.open(`${BASE_URL}${file.url}`)
        }
        // onCancel () {
        //   window.open(`${VUE_APP_UPLOAD_PREVIEW_URL}/onlinePreview/onlinePreview?url=${encodeURIComponent(Base64.encode(`${BASE_URL}${file.url}`))}`)
        // }
      })
    },
    uploadRemove (file) {
      return new Promise((resolve, reject) => {
        this.$confirm({
          title: '确认?',
          content: '是否删除该附件',
          zIndex: 10002,
          onOk: () => {
            // 处理参数
            const fileName = file.fullFileName || ''
            // const orgId = this.uploadData.params.split('_')[0] || ''
            const orgId = USER_ORG_ID
            const businessName = this.uploadData.params.split('_')[0] || ''
            const keyId = this.uploadData.params.split('_')[1] || ''
            // // table展示时的file的uuid
            // const uuid = file.uuid
            this.tip = '正在删除附件...'
            this.fileListLoading = true
            console.log('file', file)
            // 调用接口
            fileApi.deleteFileMinioNew({ keyId, businessName, fileName, orgId }).then(res => {
              if (res.code !== '0000') {
                return this.$notification.error({
                  message: '系统消息',
                  description: '删除附件失败！'
                })
              }
              this.uploadFileList = this.uploadFileList.filter(item => {
                return item.uid !== file.uid
              })
              this.$notification.success({
                message: '系统消息',
                description: '删除附件成功！'
              })
              resolve()
            }).finally(() => {
              this.fileListLoading = false
              this.tip = '加载中'
            })
          },
          onCancel () {
            reject(new Error())
          }
        })
      })
    },
    // 打开附件弹框(抽屉模式),用哪个方法取决于directDisplay
    openUploadDrawer (record, businessName) {
      const keyId = this.getKeyIdByBusinessName(businessName)
      if (!record[keyId]) {
        return null
      }
      if (!this.fileListLoading) {
        const { isShowDeleteAndUpload } = this
        this.isShowUpload = isShowDeleteAndUpload(record)
        fileApi
          .listFileMinio({
            orgId: USER_ORG_ID,
            keyId: record[keyId],
            businessName: businessName
          })
          .then(res => {
            if (res.code !== '0000') {
              this.$notification.error({
                message: '系统消息',
                description: res.message || '获取附件列表失败！'
              })
            }
            for (const item of res.result) {
              this.uploadFileList.push({
                uid: item.fullFileName,
                url: item.fileUrl,
                name: item.fileName,
                fulleName: item.fullFileName,
                // name: item.replace(/_[^_]+$/, ''),
                status: 'done'
              })
            }
          })
          .finally(() => {
            this.fileListLoading = false
          })
        this.fileListLoading = true
      }
      this.uploadData = {
        // params: USER_ORG_ID + '_' + businessName + '_' + record[keyId]
        params: businessName + '_' + record[keyId]
      }
      this.uploadFileList = []
      this.uploadVisible = true
    },
    // 关闭附件弹框
    hiddenUploadDrawer () {
      console.log('this.uploadFileList', this.uploadFileList)
      for (const file of this.uploadFileList) {
        if (file && (file.status === 'uploading' || file.status === undefined)) {
          this.$message.error('有文件正在上传中..., 请稍后再试！')
          return false
        }
      }
      this.uploadData = {}
      this.uploadFileList = []
      this.uploadVisible = false
      return true
    },
    getKeyIdByBusinessName (businessName) {
      let keyId = null
      switch (businessName) {
        case 'contractAttach':
          keyId = 'contractSysId'
          break
        default :
          keyId = 'uuid'
      }
      return keyId
    },
    // 非抽屉模式,直接显示
    doCreateUpload (businessName = '', record = {}) {
      const keyId = this.getKeyIdByBusinessName(businessName)
      const { isShowDeleteAndUpload } = this
      this.isShowUpload = isShowDeleteAndUpload(record)
      if (!record[keyId]) {
        return null
      }
      fileApi
        .listFileMinio({
          keyId: record[keyId],
          businessName: businessName
        })
        .then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '获取附件列表失败！'
            })
          }
          for (const item of res.result) {
            this.uploadFileList.push({
              uid: item.fullFileName,
              url: item.fileUrl,
              name: item.fileName,
              fulleName: item.fullFileName,
              // name: item.replace(/_[^_]+$/, ''),
              status: 'done'
            })
          }
          // 表格形式展示
          if (this.tableDisplay) {
            this.queryUploadFile(businessName, record[keyId])
          }
        })
      this.uploadData = {
        // params: USER_ORG_ID + '_' + businessName + '_' + record[keyId]
        params: businessName + '_' + record[keyId]
      }
      this.uploadFileList = []
      this.uploadVisible = true
    },
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || ''
    },
    queryUploadFile (businessName, keyId) {
      this.fileDataList = []
      this.cacheBusinessName = businessName
      this.cacheKeyId = keyId
      {
        const param = requestBuilder(
          '',
          {
            businessName: this.cacheBusinessName,
            keyId: this.cacheKeyId,
            orgId: USER_ORG_ID
          }
        )
        return fileApi.queryUploadFile(param).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '附件查询失败！'
            })
            return null
          }

          console.log('this.uploadFileList', this.uploadFileList)
          // 赋值url地址,后续可以考虑点击文件名获取预览地址
          for (const item of res.result) {
            for (const file of this.uploadFileList) {
              if (item.fullFileName === file.fulleName) {
                item.url = file.url
                item.fulleName = file.fulleName
              }
            }
          }
          this.fileDataList = res.result
          this.$refs.fileTable.refresh()
        })
      }
    },
    removeFile (file) {
      this.uploadRemove(file)
    },
    handleMenuClick (value) {
      this.selectFileType = value.key
      this.$set(this.uploadHeaders, 'fileType', this.selectFileType)
      this.openFileDialogOnClick = true
      this.$nextTick(() => {
        this.$refs['uploadBtn'].$el.click()
      })
      setTimeout(() => {
        this.openFileDialogOnClick = false
      }, 1000)
    }
  }
}
</script>
