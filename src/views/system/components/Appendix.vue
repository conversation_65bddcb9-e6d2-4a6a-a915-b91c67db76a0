<template>
  <section>
    <a-drawer
      width="360"
      title="附件"
      :mask="true"
      :zIndex="1100"
      :maskClosable="false"
      :visible="uploadVisible"
      @close="hiddenUploadDrawer"
    >
      <div class="drawer-content">
        <a-spin :spinning="fileListLoading">
          <a-upload
            class="drawer-content-upload"
            :multiple="true"
            :data="uploadData"
            :action="uploadAction"
            :headers="uploadHeaders"
            :disabled="!isShowUpload"
            :file-list="uploadFileList"
            :beforeUpload="uploadBeforeUpload"
            :remove="uploadRemove"
            @change="uploadChange"
            @preview="uploadPreview"
          >
            <a-button
              v-if="isShowUpload">
              <a-icon type="export" />上传
            </a-button>
          </a-upload>
          <a-button type="primary" :disabled="uploadFileList.length === 0" @click="installAll">
            <a-icon type="upload"/>下载全部
          </a-button>
          <template v-if="uploadFileList.length > 0">
            <div v-for="(item, index) in uploadFileList" :key="index" class="file-list-box">
              <div style="flex: 1; display: flex;">
                <a-icon class="upload-icon-view" type="file" />
                <a-tooltip :title="item.name">
                  <span class="upload-name-view">{{ item.name }}</span>
                </a-tooltip>
              </div>
              <!-- <img v-if="isImg(item.type)" :src="item.url" class="upload-icon-view" /> -->
              <div>
                <a-tooltip title="上移">
                  <a href="javascript:void(0)" style=" padding: 5px;" :disabled="index === 0" @click="turnUp(item)"><a-icon type="arrow-up" /></a>
                </a-tooltip>
                <a-tooltip title="下移">
                  <a href="javascript:void(0)" style=" padding: 5px;" :disabled="index === uploadFileList.length - 1" @click="turnDown(item)"><a-icon type="arrow-down"/></a>
                </a-tooltip>
                <a-tooltip title="预览">
                  <a href="javascript:void(0)" style="padding: 5px;" @click="uploadPreview(item)" ><a-icon type="eye"/></a>
                </a-tooltip>
                <a-tooltip title="删除">
                  <a href="javascript:void(0)" style="color: #f5222d; padding: 5px;" v-if="isShowDeleteAndUpload()" @click="uploadRemove(item)" ><a-icon type="delete"/></a>
                </a-tooltip>
              </div>
            </div>
          </template>
        </a-spin>
      </div>
      <div class="drawer-footer">
        <div class="footer-fixed">
          <a-button @click="hiddenUploadDrawer">返回</a-button>
        </div>
      </div>
    </a-drawer>
  </section>
</template>

<script>
import Vue from 'vue'
import { Base64 } from 'js-base64'
import { ACCESS_TOKEN, PERSON_ID, ORG_ID } from '@/store/mutation-types'
import * as fileApi from '@/api/system/file'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)

// 上传附件允许类型
const uploadAccept = [
  // Excel
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel',
  // WORD
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
  'application/vnd.ms-word.document.macroEnabled.12',
  'application/vnd.ms-word.template.macroEnabled.12',
  'application/msword',
  // PDF
  'application/pdf',
  // IMG
  'image/png',
  'image/jpeg',
  'image/bmp',
  'image/gif',
  'image/vnd.dwg',
  'application/x-zip-compressed',
  'application/x-compressed',
  ''
]

// 上传接口
const TOKEN = Vue.ls.get(ACCESS_TOKEN)
const BASE_URL = process.env.VUE_APP_API_BASE_URL
const BASE_PREVIEW_URL = process.env.VUE_APP_PREVIEW_BASE_URL
const VUE_APP_UPLOAD_PREVIEW_URL = process.env.VUE_APP_UPLOAD_PREVIEW_URL

export default {
  name: 'Appendix',
  props: {
    // 模块名
    businessName: {
      type: String,
      default: ''
    },
    // 可否对附件做出增删
    isShowDeleteAndUpload: {
      type: Function,
      default: function () {
        return true
      }
    },
    // 唯一编码
    keyId: {
      type: String,
      default: ''
    },
    allowRemove: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data () {
    return {
      // 上传参数
      USER_PERSON_ID,
      USER_ORG_ID,
      uploadData: {},
      uploadFileList: [],
      uploadVisible: false,
      uploadAction: BASE_URL + '/file/fileUpload',
      uploadAccept: uploadAccept.join(','),
      uploadHeaders: { token: TOKEN },
      fileListLoading: false,
      isShowUpload: true,
      infoRecord: {},
      uploadChange: options => {
        console.log(options, 'options')
        if (options.file && ['removed', 'error'].includes(options.file.status)) {
          this.uploadFileList = this.uploadFileList.filter(file => {
            return file.uid !== options.file.uid
          })
        }
        if (options.file && options.file.status === 'done') {
          if (options.file.response && options.file.response.code === '0000') {
            this.uploadFileList = this.uploadFileList.map(file => {
              if (file.uid === options.file.uid) {
                file.url = options.file.response.message
                file.uid = options.file.response.message
                file.status = options.file.status
              }
              return file
            })
          } else {
            this.uploadFileList = this.uploadFileList.filter(file => {
              return file.uid !== options.file.uid
            })
            this.$notification.error({
              message: '系统消息',
              description: options.file.response.message
            })
          }
        }
      }
    }
  },
  methods: {
    uploadBeforeUpload (file) {
      if (!uploadAccept.includes(file.type)) {
        this.$message.error('仅支持上传word、excel、图片等附件')
        return false
      }
      if (file.size / 1024 / 1024 > 50) {
        this.$message.error('上传附件大小不可超过50M')
        return false
      }
      this.uploadFileList.push(file)
    },
    // uploadChange (options) {
    //   console.log(options, 'options')
    //   if (options.file && ['removed', 'error'].includes(options.file.status)) {
    //     this.uploadFileList = this.uploadFileList.filter(file => {
    //       return file.uid !== options.file.uid
    //     })
    //   }
    //   if (options.file && options.file.status === 'done') {
    //     if (options.file.response && options.file.response.code === '0000') {
    //       this.uploadFileList = this.uploadFileList.map(file => {
    //         if (file.uid === options.file.uid) {
    //           file.url = options.file.response.message
    //           file.uid = options.file.response.result
    //           file.status = options.file.status
    //         }
    //         return file
    //       })
    //       // this.openUploadDrawer(this.infoRecord)
    //     } else {
    //       this.uploadFileList = this.uploadFileList.filter(file => {
    //         return file.uid !== options.file.uid
    //       })
    //       this.$warning({
    //         title: '上传附件失败',
    //         zIndex: 2001,
    //         content: options.file.response.syserror,
    //         onOk: () => {
    //         },
    //         onCancel () {
    //         }
    //       })
    //     }
    //   }
    // },
    uploadPreview (file) {
      // 处理参数
      const fileName = file.uid || ''
      const keyId = this.uploadData.params.split('_')[1] || ''
      const businessName = this.uploadData.params.split('_')[0] || ''
      this.$confirm({
        content: '打开方式',
        cancelText: ['7z', 'rar'].includes(file.type) ? '取消' : '预览',
        zIndex: 2001,
        okText: '下载',
        onOk () {
          window.open(`${BASE_URL}/file/fileDownload?keyId=${keyId}&businessName=${businessName}&fileName=${fileName}`)
        },
        onCancel () {
          if (['7z'].includes(file.type)) {
            return false
          }
          let previewUrl = ''
          const originUrl = `${BASE_PREVIEW_URL}/file/fileDownload?keyId=${keyId}&businessName=${businessName}&fileName=${fileName}`
          if (/^\/.+/.test(originUrl)) {
            previewUrl = `${window.location.protocol}//${window.location.host}${originUrl}&fullfilename=${fileName}`
          } else {
            previewUrl = `${originUrl}&fullfilename=${fileName}`
          }
          window.open(`${VUE_APP_UPLOAD_PREVIEW_URL}/onlinePreview/onlinePreview?url=${encodeURIComponent(Base64.encode(previewUrl))}`)
        }
      })
    },
    uploadRemove (file) {
      if (this.allowRemove) {
        this.$message.warning('禁止删除！')
        return false
      }
      return new Promise((resolve, reject) => {
        this.$confirm({
          title: '确认?',
          zIndex: 2001,
          content: '是否删除该附件',
          onOk: () => {
            // 处理参数
            const fileName = file.uid || ''
            const keyId = this.uploadData.params.split('_')[1] || ''
            const businessName = this.uploadData.params.split('_')[0] || ''
            // 调用接口
            fileApi.deleteFile({ keyId, businessName, fileName, personId: USER_PERSON_ID }).then(res => {
              if (res.code !== '0000') {
                this.$notification.error({
                  message: '系统消息',
                  description: '删除附件失败！'
                })
                reject(new Error())
              }
              this.uploadFileList = this.uploadFileList.filter(item => {
                return item.uid !== file.uid
              })
              resolve()
            })
          },
          onCancel () {
            reject(new Error())
          }
        })
      })
    },
    // 打开附件弹框
    openUploadDrawer (record) {
      if (!this.fileListLoading) {
        this.infoRecord = record
        const { isShowDeleteAndUpload } = this
        this.isShowUpload = isShowDeleteAndUpload(record)
        fileApi
          .listFiles({
            keyId: record[this.keyId],
            businessName: this.businessName
          })
          .then(res => {
            if (res.code !== '0000') {
              this.$notification.error({
                message: '系统消息',
                description: res.message || '获取附件列表失败！'
              })
            }
            for (const item of res.result) {
              const typeList = item.split('.')
              this.uploadFileList.push({
                uid: item,
                url: item.replace(/_[^_]+$/, ''),
                name: item.replace(/_[^_]+$/, ''),
                status: 'done',
                type: typeList[typeList.length - 1]
              })
            }
          })
          .finally(() => {
            this.fileListLoading = false
          })
        this.fileListLoading = true
      }
      this.uploadData = {
        params: this.businessName + '_' + record[this.keyId] + '_' + USER_PERSON_ID + '_' + USER_ORG_ID
      }
      this.uploadFileList = []
      this.uploadVisible = true
    },
    // 关闭附件弹框
    hiddenUploadDrawer () {
      for (const file of this.uploadFileList) {
        if (file && file.status === 'uploading') {
          this.$message.error('有文件正在上传中..., 请稍后再试！')
          return
        }
      }
      this.uploadData = {}
      this.uploadFileList = []
      this.infoRecord = {}
      this.uploadVisible = false
    },
    turnUp (item) {
      const fileName = item.uid || ''
      const keyId = this.uploadData.params.split('_')[1] || ''
      const businessName = this.uploadData.params.split('_')[0] || ''
      fileApi.fileUp({ fileName, businessName, keyId, personId: USER_PERSON_ID, orgId: USER_ORG_ID }).then(res => {
        if (res.code !== '0000') {
          this.$message.error(res.message || '操作失败！')
        } else {
          this.openUploadDrawer(this.infoRecord)
        }
      })
    },
    turnDown (item) {
      const fileName = item.uid || ''
      const keyId = this.uploadData.params.split('_')[1] || ''
      const businessName = this.uploadData.params.split('_')[0] || ''
      fileApi.fileDown({ fileName, businessName, keyId, personId: USER_PERSON_ID, orgId: USER_ORG_ID }).then(res => {
        if (res.code !== '0000') {
          this.$message.error(res.message || '操作失败！')
        } else {
          this.openUploadDrawer(this.infoRecord)
        }
      })
    },
    installAll () {
      window.open(`${BASE_URL}/file/fileDownloadAll?keyId=${this.infoRecord[this.keyId]}&businessName=${this.businessName}`)
      // this.fileListLoading = true
      // fileApi.fileDownloadAll({
      //   keyId: this.infoRecord[this.keyId],
      //   businessName: this.businessName
      // }).then(res => {
      //   console.log(res)
      //   if (res && res.data && res.headers['content-disposition']) {
      //     const link = document.createElement('a')
      //     const url = window.URL.createObjectURL(res.data)
      //     const contentDisposition = res.headers['content-disposition']
      //     let filename = contentDisposition.replace(/(.*;)?filename=([^;]+).*/i, '$2')
      //     try {
      //       filename = decodeURIComponent(escape(filename))
      //     } catch (e) {
      //     }
      //     document.body.appendChild(link)
      //     link.style.display = 'none'
      //     link.download = filename
      //     link.href = url
      //     link.click()
      //     link.remove()
      //     window.URL.revokeObjectURL(url)
      //   } else {
      //     this.$notification.error({
      //       message: '系统消息',
      //       description: '下载失败！'
      //     })
      //   }
      //   this.fileListLoading = false
      // })
    }
  }
}
</script>
<style scoped>
.upload-name-view {
  display: block;
  width: 180px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.upload-icon-view {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #0071fc;
  margin-right: 10px;
}
.after-icon {
  /* line-height: 48px; */
}
.file-list-box {
  height: 30px;
  padding: 0 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  margin-bottom: 5px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
/deep/.ant-upload-list {
  display: none;
}
</style>
