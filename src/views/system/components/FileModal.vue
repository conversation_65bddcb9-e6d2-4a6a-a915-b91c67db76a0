<template>
  <div v-if="visible">
    <a-modal
      :visible="visible"
      title="附件窗口"
      width="1200px"
      :bodyStyle="{ height: '300px', overflow: 'hidden', overflowY: 'scroll' }"
      @cancel="handleCancel"
    >
      <a-descriptions bordered :column="4">
        <a-descriptions-item label="相关附件" :span="4">
          <AppendixNew ref="AppendixNew" text="" :tableDisplay="true" :businessName="businessName"/>
        </a-descriptions-item>
      </a-descriptions>
      <template slot="footer">
        <a-button
          key="back"
          @click="handleCancel"
        >关闭</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
import AppendixNew from '@/views/system/components/AppendixNew'
export default {
  components: {
    AppendixNew
  },

  props: {
    // 模块名
    businessName: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      visible: false
    }
  },
  created () {
    // 初始化选项获取
    this.initOptions()
  },
  methods: {
    initOptions () {
    },
    showModal (record) {
      this.visible = true
      this.record = record
      // 获取跟踪信息
      this.$nextTick(() => {
        this.$refs.AppendixNew.doCreateUpload(this.businessName, record)
      })
    },
    handleCancel (e) {
      const flag = this.$refs.AppendixNew.hiddenUploadDrawer()
      if (flag) {
        this.visible = false
      }
    }
  }
}
</script>

<style lang="less" scoped>

</style>
