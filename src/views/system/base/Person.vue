<template>
  <section>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="20">
            <a-col
              :xl="6"
              :md="4"
              :sm="24"
            >
              <a-form-item label="员工编码">
                <a-input
                  v-model="queryParam.personNum"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <a-col
              :xl="6"
              :md="4"
              :sm="24"
            >
              <a-form-item label="员工姓名">
                <a-input
                  v-model="queryParam.personName"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <a-col
              :xl="6"
              :md="4"
              :sm="24"
            >
              <a-form-item label="可用状态">
                <a-select v-model="queryParam.activity">
                  <a-select-option value>全部</a-select-option>
                  <a-select-option value="Y">可用</a-select-option>
                  <a-select-option value="N">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <a-button
          type="primary"
          icon="search"
          @click="doSearch"
        >查询</a-button>
        <a-button
          v-action:add
          type="primary"
          icon="plus"
          @click="openDrawer('insert')"
        >新增</a-button>
        <a-button
          v-action:edit
          type="primary"
          icon="edit"
          @click="doEdit"
        >修改</a-button>
        <a-button
          v-action:del
          type="danger"
          icon="delete"
          @click="doPersonDel('')"
        >删除</a-button>
      </div>
    </a-card>
    <a-card :bordered="true">
      <a-row :gutter="10">
        <a-col
          :md="6"
          :sm="12"
        >
          <a-card title="行政组织列表">
            <a-button
              shape="circle"
              icon="reload"
              size="small"
              slot="extra"
              @click="initTree"
            />
            <a-tree
              :loadData="onLoadData"
              :loadedKeys="loadedKeys"
              :treeData="treeData"
              @select="onSelect"
            >
              <template
                slot="title"
                slot-scope="{title}"
              >
                <ellipsis
                  :length="24"
                  tooltip
                >{{ title }}</ellipsis>
              </template>
            </a-tree>
          </a-card>
        </a-col>
        <a-col
          :md="18"
          :sm="12"
        >
          <a-card title="明细信息">
            <s-table
              ref="table"
              size="small"
              rowKey="key"
              :columns="columns"
              :data="loadData"
              :alert="options.alert"
              :customRow="rowClick"
              :rowSelection="options.rowSelection"
              :pageSizeOptions="pageSizeOptions"
              :pageSize="defaultPageSize"
              :showPagination="true"
            >
              <!-- 表格表自定义显示转换 -->
              <span
                slot="serial"
                slot-scope="text, record, index"
              >{{ index + 1 }}</span>
              <span
                slot="description"
                slot-scope="text"
              >
                <ellipsis
                  :length="8"
                  tooltip
                >{{ text }}</ellipsis>
              </span>
              <span
                slot="status"
                slot-scope="text"
              >{{ (text==='Y') ? '启用' : '禁用' }}</span>
              <span
                slot="action"
                slot-scope="text, record"
              >
                <a
                  v-action:del
                  @click="doPersonDel(record)"
                >删除</a>
              </span>
            </s-table>
          </a-card>
        </a-col>
      </a-row>
      <!-- 侧边滑动栏 -->
      <a-drawer
        :title="subTitle"
        :width="360"
        :visible="visible"
        :mask="false"
        :bodyStyle="{paddingBottom: '80px'}"
        @close="visible = false"
      >
        <a-spin :spinning="confirmLoading">
          <a-form
            :form="form"
            layout="vertical"
            hideRequiredMark
          >
            <a-form-item
              v-show="false"
              label="personSysId:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-decorator="['personSysId', {
                  rules: [{ required: false }]
                }]"
              />
            </a-form-item>
            <a-form-item
              label="员工编码:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-decorator="['personNum', {
                  rules: [{ required: true, min: 1, message: '请输入至少一个字符' }]
                }]"
              />
            </a-form-item>
            <a-form-item
              label="姓名:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-decorator="['personName', {
                  rules: [{ required: true, min: 1, message: '请输入至少一个字符' }]
                }]"
              />
            </a-form-item>
            <a-form-item
              label="手机号码:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-decorator="['mobilePhone', {
                  rules: [{ required: true, len: 11, max:11, message: '请输入11位手机号码' }]
                }]"
              />
            </a-form-item>
            <a-form-item
              label="身份证:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-decorator="['idCard', {
                  rules: [{ required: true, min:18, len: 18, message: '请输入18位身份证号码' }]
                }]"
              />
            </a-form-item>
            <a-form-item
              label="职位:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-decorator="['postName', {
                  rules: [{required: true, min: 2, message: '请输入至少二个字符'}]
                }]"
              />
            </a-form-item>
            <a-form-item label="所属组织:">
              <a-select
                v-decorator="['orgId', {
                  rules: [{ type: 'string', required: true, message: '请选择组织' }]
                }]"
                allowClear
                @change="handleOrgChange"
              >
                <a-select-option
                  v-for="(item, index) in orgIds"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="所属部门:">
              <a-select
                v-decorator="['departmentSysId', {
                  rules: [{ type: 'string', required: true, message: '请选择部门' }]
                }]"
                allowClear
              >
                <a-select-option
                  v-for="(item, index) in departmentSysIds"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item
              label="可用状态:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-decorator="['activity', {
                  rules: [{ required: true, message: '请选择状态' }]
                }]"
              >
                <a-select-option value="Y">启用</a-select-option>
                <a-select-option value="N">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-form>
        </a-spin>
        <div
          :style="{
            position: 'absolute',
            right: 0,
            bottom: 0,
            width: '100%',
            borderTop: '1px solid #e9e9e9',
            padding: '10px 16px',
            background: '#fff',
            textAlign: 'right',
            zIndex: 1,
          }"
        >
          <a-button
            :style="{marginRight: '8px'}"
            @click="visible = false"
          >取消</a-button>
          <a-button
            @click="doSubmit"
            type="primary"
          >保存</a-button>
        </div>
      </a-drawer>
    </a-card>
  </section>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import { requestBuilder } from '@/utils/util'
import * as baseApi from '@/api/system/base'

export default {
  name: 'Person',
  components: {
    STable,
    Ellipsis
  },
  data () {
    return {
      // 分页参数
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20'],
      // 加载树形根节点
      treeData: [{ title: '组织管理', key: '0', isLeaf: false }],
      mdl: {},
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {
        activity: 'Y',
        orgId: '',
        departmentSysId: ''
      },
      // 树节点key
      treeNodeId: {},
      // 树节点信息
      loadedKeys: [],
      // 组织下拉框
      orgIds: [],
      // 部门下拉框
      departmentSysIds: [],
      // 员工信息表头定义
      columns: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' }
        },
        {
          title: '员工姓名',
          dataIndex: 'personName',
          scopedSlots: { customRender: 'description' }
        },
        {
          title: '手机号',
          dataIndex: 'mobilePhone',
          scopedSlots: { customRender: 'description' }
        },
        {
          title: '职位',
          dataIndex: 'postName'
        },
        {
          title: '可用状态',
          dataIndex: 'activity',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: '150px',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 加载员工信息数据
      loadData: parameter => {
        const param = requestBuilder('', Object.assign(this.queryParam), parameter.pageNo, parameter.pageSize)
        return baseApi.getPersonInfo(param).then(res => {
          if (res.code === '0000') {
            return res.result
          } else {
            this.$message.error(res.message)
          }
        })
      },
      // 行选择参数
      options: {
        alert: {
          show: false,
          clear: () => {
            this.selectedRowKeys = []
          }
        },
        rowSelection: {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        }
      },
      selectedRowKeys: [],
      selectedRows: [],
      // 滑动抽屉参数
      form: this.$form.createForm(this),
      confirmLoading: false, // 加载图标是否出现
      selectedItems: [],
      visible: false,
      subTitle: '员工新建',
      temppass: '',
      actionFlag: '',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      rowClick: record => ({
        on: {
          // 点击事件
          dblclick: () => {
            this.openDrawer('edit', record)
            this.initOptions()
          }
        }
      })
    }
  },
  filters: {},
  created () {
    this.initOptions()
  },
  methods: {
    // 初始化树
    initTree () {
      this.$delete(this.treeData[0], 'children')
    },
    // 初始化下拉框
    initOptions () {
      baseApi.getCommboxById({ id: 'org' }).then(res => {
        this.orgIds = res.result
      })
      baseApi.getCommboxById({ id: 'dept', sqlParams: { isAll: '1' } }).then(res => {
        this.departmentSysIds = res.result
      })
    },
    // 下拉框联动
    handleOrgChange (value) {
      // this.cities = cityData[value]
      // this.secondCity = cityData[value][0]
      this.form.setFieldsValue({
        departmentSysId: ''
      })
      this.departmentSysIds = []
      baseApi.getCommboxById({ id: 'dept', sqlParams: { byOrgId: '1', orgId: value } }).then(res => {
        if (res.code === '0000') {
          this.form.setFieldsValue({
            departmentSysId: res.result.length > 0 ? res.result[0].value : ''
          })
          this.departmentSysIds = res.result
        }
      })
    },
    // 加载组织树节点信息
    onLoadData (treeNode) {
      return new Promise(resolve => {
        if (treeNode.dataRef.children) {
          resolve()
          return
        }
        setTimeout(() => {
          // 获取当前节点的子节点信息
          this.param = {
            orgId: treeNode.dataRef.key
          }
          const param = requestBuilder('', Object.assign(this.param), '', '')
          baseApi.getOrgTreeInfo(param).then(res => {
            if (res.code === '0000') {
              for (const item of res.result) {
                item.scopedSlots = { title: 'title' }
              }
              treeNode.dataRef.children = res.result
              this.treeData = [...this.treeData]
            } else {
              this.$message.error(res.message)
            }
          })
          resolve()
        }, 200)
      })
    },
    // 点击树节点后查询对应的员工信息
    onSelect (selectedKeys, info) {
      // 如果未选中，将参数清零
      if (selectedKeys.length === 0) {
        this.queryParam.orgId = ''
        this.queryParam.departmentSysId = ''
      } else {
        // 判断是组织节点还是部门节点
        if (info.node.isLeaf === false) {
          // 如果是根节点，则显示全部员工信息
          if (selectedKeys[0] === '0') {
            this.queryParam.orgId = ''
            this.queryParam.departmentSysId = ''
          } else {
            this.queryParam.orgId = selectedKeys[0]
            this.queryParam.departmentSysId = ''
          }
        } else if (info.node.isLeaf === true) {
          this.queryParam.orgId = info.node.vcTreeNode.eventKey
          this.queryParam.departmentSysId = selectedKeys[0]
        }
      }
      this.doSearch()
    },
    openDrawer (action, record) {
      this.visible = true
      this.$nextTick(() => {
        if (action === 'insert') {
          this.form.resetFields()
          // this.form.setFieldsValue({
          //   orgName: '宁波港信息通信有限公司',
          //   departmentName: '软件开发部'
          // })
          this.subTitle = '员工新增'
          this.actionFlag = 'insert'
          // 给表单下拉框赋值
          this.form.setFieldsValue({
            orgId: this.queryParam.orgId,
            departmentSysId: this.queryParam.departmentSysId
          })
        } else {
          this.subTitle = '员工修改'
          this.form.resetFields()
          this.form.setFieldsValue(record)
          // this.getRoleListByUserNo(record.userNo)
          // this.temppass = record.password
          this.actionFlag = 'update'
        }
      })
    },
    // 查询列表
    doSearch () {
      this.$refs.table.refresh(true)
      this.$refs.table.updateSelect([], [])
      this.$refs.table.rowSelection.onChange([], [])
    },
    // 修改列表
    doEdit () {
      if (this.selectedRows.length > 0) {
        this.openDrawer('update', this.selectedRows[0])
      } else {
        this.$message.error('未选中任何记录')
      }
    },
    // 提交保存
    doSubmit () {
      const {
        form: { validateFields }
      } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        // 如果输入数据都合规
        if (!errors) {
          this.params = { ...values }
          this.modifyPerson(this.params)
        } else {
          this.confirmLoading = false
        }
      })
    },
    // 保存员工信息
    modifyPerson (params) {
      baseApi
        .modifyPersonInfo(requestBuilder(this.actionFlag, params, '0', '0'))
        .then(res => {
          if (res.code === '0000') {
            this.doSearch()
            this.$message.success(res.message)
          } else if (res.code === '9999') {
            this.$message.error('发生异常' + res.message)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    doPersonDel (record) {
      const personSysIds = []
      if (record !== '') {
        personSysIds.push(record.personSysId)
      } else {
        if (this.selectedRows.length > 0) {
          for (let i = 0; i < this.selectedRows.length; i++) {
            personSysIds.push(this.selectedRows[i].personSysId)
          }
        } else {
          this.$message.error('未选中任何记录')
          return
        }
      }
      this.$confirm({
        title: '确认?',
        content: '是否删除此记录',
        onOk: () => {
          this.handleDel(personSysIds.join(','))
        },
        onCancel () {}
      })
    },
    handleDel (value) {
      this.actionFlag = 'delete'
      const param = {
        personSysId: value
      }
      this.modifyPerson(param)
    },
    handleEdit (record) {},
    handleOk () {},
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    }
  }
}
</script>
<style scoped>
</style>
