<template>
  <section>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="10">
            <a-col
              :xl="6"
              :md="6"
              :sm="24"
            >
              <a-form-item label="模型名称">
                <a-input
                  @pressEnter="doSearch"
                  v-model="queryParam.modelName"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <a-col
              :xl="6"
              :md="6"
              :sm="24"
            >
              <a-form-item label="模型Id">
                <a-input
                  @pressEnter="doSearch"
                  v-model="queryParam.modelId"
                  placeholder
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <a-button
          v-action:query
          type="primary"
          icon="search"
          @click="doSearch"
        >查询</a-button>
        <a-button
          v-action:add
          type="primary"
          icon="plus"
          @click="openDrawer('insert')"
        >新增</a-button>
        <a-button
          v-action:edit
          type="primary"
          icon="edit"
          @click="doEdit"
        >修改</a-button>
        <a-button
          v-action:del
          type="danger"
          icon="delete"
          @click="doBatchDel('')"
        >删除</a-button>
      </div>
    </a-card>

    <a-card :bordered="false">
      <s-table
        ref="table"
        size="default"
        rowKey="key"
        :columns="columns"
        :data="loadData"
        :alert="options.alert"
        :customRow="rowClick"
        :rowSelection="options.rowSelection"
        :pageSizeOptions="pageSizeOptions"
        :pageSize="defaultPageSize"
        :showPagination="true"
      >
        <span
          slot="serial"
          slot-scope="text, record, index"
        >{{ index + 1 }}</span>
        <span
          slot="description"
          slot-scope="text"
        >
          <ellipsis
            :length="8"
            tooltip
          >{{ text }}</ellipsis>
        </span>
        <span
          slot="status"
          slot-scope="text"
        >{{ text==='Y' ? '启用' : '禁用' }}</span>
        <span
          slot="date"
          slot-scope="text"
        >{{ text | moment }}</span>
        <span
          slot="action"
          slot-scope="text, record"
        >
          <a @click="doDeployment(record)">部署</a>
        </span>
      </s-table>
    </a-card>

    <a-card :bordered="false">
      <s-table
        ref="depTable"
        size="default"
        rowKey="key"
        :columns="depColumns"
        :data="loadDepData"
        :alert="options.alert"
        :rowSelection="options.rowSelection"
        :pageSizeOptions="pageSizeOptions"
        :pageSize="defaultPageSize"
        :showPagination="true"
      >
        <span
          slot="serial"
          slot-scope="text, record, index"
        >{{ index + 1 }}</span>
        <span
          slot="description"
          slot-scope="text"
        >
          <ellipsis
            :length="8"
            tooltip
          >{{ text }}</ellipsis>
        </span>
        <span
          slot="status"
          slot-scope="text"
        >{{ text==='Y' ? '启用' : '禁用' }}</span>
        <span
          slot="date"
          slot-scope="text"
        >{{ text | moment }}</span>
      </s-table>
    </a-card>
  </section>
</template>

<script>
import Vue from 'vue'
import { requestBuilder } from '@/utils/util'
import { STable, Ellipsis } from '@/components'
import { ORG_NAME, ORG_ID } from '@/store/mutation-types'
import * as baseApi from '@/api/system/base'
import * as umApi from '@/api/system/um'
import md5 from 'md5'

export default {
  name: 'BpmnManager',
  components: {
    STable,
    Ellipsis
  },
  data () {
    return {
      // 分页参数
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20'],
      mdl: {},
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      // 表头定义
      columns: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' }
        },
        {
          title: '模型名称',
          dataIndex: 'modelName',
          sorter: (a, b) => {
            return a.modelName.localeCompare(b.modelName)
          }
        },
        {
          title: '模型Id',
          dataIndex: 'modelId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'updateTime',
          scopedSlots: { customRender: 'date' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: '80px',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 表头定义
      depColumns: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' }
        },
        {
          title: '流程名称',
          dataIndex: 'processName'
        },
        {
          title: '部署Id',
          dataIndex: 'deploymentId'
        },
        {
          title: '部署时间',
          dataIndex: 'deploymentDate',
          scopedSlots: { customRender: 'date' }
        }
      ],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const param = requestBuilder('', Object.assign(this.queryParam), parameter.pageNo, parameter.pageSize)
        return umApi.getProcessModelInfo(param).then(res => res.result)
      },
      loadDepData: parameter => {
        const param = requestBuilder('', Object.assign(this.queryParam), parameter.pageNo, parameter.pageSize)
        return umApi.getDeploymentProcessInfo(param).then(res => {
          return res.result
        })
      },
      // 行选择参数
      options: {
        alert: {
          show: false,
          clear: () => {
            this.selectedRowKeys = []
          }
        },
        rowSelection: {
          // selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        }
      },
      selectedRowKeys: [],
      selectedRows: [],
      // 滑动抽屉参数
      form: this.$form.createForm(this),
      confirmLoading: false, // 加载图标是否出现
      rolesList: [], // 角色列表
      deptList: [], // 所属部门列表
      selectedItems: [],
      orgName: '',
      visible: false,
      subTitle: '人员新建',
      temppass: '',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      rowClick: record => ({
        on: {
          click: () => {
            // this.$message.error(record.modelId)
            this.$refs.depTable.refresh((this.queryParam = { modelId: record.modelId }))
            this.queryParam = {}
          }
          // 点击事件
          // dblclick: () => {
          //  this.openDrawer('edit', record)
          // }
        }
      })
    }
  },
  filters: {},
  created () {
    // 获取角色列表
    this.getRoleList()
    this.getDeptList()
    this.orgName = Vue.ls.get(ORG_NAME)
  },
  methods: {
    getDisableStatus () {
      if (this.actionFlag === 'insert') {
        return false
      } else {
        return true
      }
    },
    handleChange (selectedItems) {
      this.selectedItems = selectedItems
    },
    doBatchDel (record) {
      const modelIds = []
      if (record !== '') {
        modelIds.push(record.modelId)
      } else {
        if (this.selectedRows.length > 0) {
          for (let i = 0; i < this.selectedRows.length; i++) {
            modelIds.push(this.selectedRows[i].modelId)
          }
        } else {
          this.$message.error('未选中任何记录')
          return
        }
      }
      this.$confirm({
        title: '确认?',
        content: '是否删除此记录',
        onOk: () => {
          this.handleDel(modelIds.join(','))
        },
        onCancel () {}
      })
    },
    handleDel (value) {
      this.actionFlag = 'delete'
      const param = {
        modelId: value
      }
      this.getResult(param)
    },
    doDeployment (record) {
      const modelIds = []
      modelIds.push(record.modelId)
      this.$confirm({
        title: '确认?',
        content: '是否对该模型进行流程部署',
        onOk: () => {
          this.handleDep(modelIds.join(','))
        },
        onCancel () {}
      })
    },
    handleDep (value) {
      this.actionFlag = 'deployment'
      const param = {
        modelId: value
      }
      this.getResult(param)
    },
    doSearch () {
      this.$refs.table.refresh()
      this.$refs.depTable.refresh()
      this.visible = false
    },
    doEdit () {
      if (this.selectedRows.length > 0) {
        if (this.selectedRows.length > 1) {
          this.$message.warning('请勾选一条记录')
        } else {
          var modelId = this.selectedRows[0].modelId
          var str = process.env.VUE_APP_BPMN_VIEW_URL
          window.open(str + '/bpmn/create?modelId=' + modelId, '_blank')
        }
      } else {
        this.$message.error('未选中任何记录')
      }
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    openDrawer (action, record) {
      // var modelId = this.selectedRows[0].modelId
      var str = process.env.VUE_APP_BPMN_VIEW_URL
      window.open(str + '/bpmn/create', '_blank')
    },
    getRoleList () {
      this.confirmLoading = true
      umApi
        .getRoleInfo(requestBuilder('select', { activity: 'Y' }, 0, 0))
        .then(res => {
          if (res.code === '0000') {
            this.rolesList = res.result
          } else {
            this.$message.error(' 获取角色列表失败 ')
          }
        })
        .catch(err => {
          alert(err)
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    getDeptList () {
      this.confirmLoading = true
      baseApi.getCommboxById({ id: 'dept', pars: '0,1,' + Vue.ls.get(ORG_ID) }).then(res => {
        this.deptList = res.result
      })
      this.confirmLoading = false
    },
    getRoleListByUserNo (value) {
      this.confirmLoading = true
      umApi
        .getRoleInfoByUserNo(requestBuilder('', { userNo: value }, 0, 0))
        .then(res => {
          if (res.code === '0000') {
            const arr = []
            for (let i = 0; i < res.result.length; i++) {
              arr.push(res.result[i].roleId)
              this.form.setFieldsValue({
                roleId: arr
              })
            }
          } else {
            this.$message.error('读取角色列表失败 ')
          }
        })
        .catch(err => {
          alert(err)
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    // 保存数据
    doSubmit () {
      const {
        form: { validateFields }
      } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        // 如果输入数据都合规
        if (!errors) {
          this.params = { ...values }
          if (this.actionFlag === 'insert') {
            this.params.password = md5(values.password)
            this.getResult(this.params)
          } else {
            if (this.temppass !== this.params.password) {
              this.params.password = md5(values.password)
            }
            this.getResult(this.params)
          }
        } else {
          this.confirmLoading = false
        }
      })
    },
    getResult (params) {
      umApi
        .modifyProcessModelInfo(requestBuilder(this.actionFlag, params, '0', '0'))
        .then(res => {
          if (res.code === '0000') {
            this.$message.success(res.message)
            this.doSearch()
          } else if (res.code === '9999') {
            this.$message.error(res.message)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    }
  }
}
</script>
<style scoped>
</style>
