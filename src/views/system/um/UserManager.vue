<template>
  <section>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="10">
            <a-col
              :xl="6"
              :md="6"
              :sm="24"
            >
              <a-form-item label="用户名">
                <a-input
                  v-model="queryParam.userNo"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <a-col
              :xl="6"
              :md="6"
              :sm="24"
            >
              <a-form-item label="姓名">
                <a-input
                  v-model="queryParam.personName"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <a-col
              :xl="6"
              :md="6"
              :sm="24"
            >
              <a-form-item label="手机号">
                <a-input v-model="queryParam.mobilePhone" />
              </a-form-item>
            </a-col>
            <a-col
              :xl="6"
              :md="6"
              :sm="24"
            >
              <a-form-item label="状态">
                <a-select
                  v-model="queryParam.activity"
                  default-value="Y"
                  allowClear
                >
                  <a-select-option value="Y">启用</a-select-option>
                  <a-select-option value="N">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <a-button
          v-action:query
          type="primary"
          icon="search"
          @click="doSearch"
        >查询</a-button>
        <a-button
          v-action:add
          type="primary"
          icon="plus"
          @click="openDrawer('insert')"
        >新增</a-button>
        <a-button
          v-action:edit
          type="primary"
          icon="edit"
          @click="doEdit"
        >修改</a-button>
        <a-button
          v-action:del
          type="danger"
          icon="delete"
          @click="doBatchDel('')"
        >删除</a-button>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        size="default"
        rowKey="key"
        :columns="columns"
        :data="loadData"
        :alert="options.alert"
        :customRow="rowClick"
        :rowSelection="options.rowSelection"
        :pageSizeOptions="pageSizeOptions"
        :pageSize="defaultPageSize"
        :showPagination="true"
      >
        <span
          slot="serial"
          slot-scope="text, record, index"
        >{{ index + 1 }}</span>
        <span
          slot="description"
          slot-scope="text"
        >
          <ellipsis
            :length="8"
            tooltip
          >{{ text }}</ellipsis>
        </span>
        <span
          slot="status"
          slot-scope="text"
        >{{ text==='Y' ? '启用' : '禁用' }}</span>
        <span
          slot="date"
          slot-scope="text"
        >{{ text | moment }}</span>
        <span
          slot="action"
          slot-scope="text, record"
        >
          <a
            v-action:del
            @click="doBatchDel(record)"
          >删除</a>
        </span>
      </s-table>

      <!-- 侧边滑动栏 -->
      <a-drawer
        :title="subTitle"
        :width="360"
        :visible="visible"
        :mask="true"
        :bodyStyle="{ paddingBottom: '80px' }"
        @close="visible = false"
      >
        <a-spin :spinning="confirmLoading">
          <a-form
            :form="form"
            layout="vertical"
            hideRequiredMark
          >
            <a-form-item
              label="ID:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-decorator="['id']"
                :disabled="true"
              />
            </a-form-item>
            <a-form-item
              label="用户名:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-decorator="['userNo', {
                  rules: [{ required: true, min: 5, message: '请输入至少五个字符' }]
                }]"
              />
            </a-form-item>
            <a-form-item
              label="密码:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-decorator="['password', {
                  rules: [{ required: true, min: 8, message: '请输入8位以上密码' }]
                }]"
                type="password"
              />
            </a-form-item>
            <a-form-item
              label="手机号码:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                @blur="touchPersonInfo"
                v-decorator="['mobilePhone', {
                  rules: [{ required: true, len: 11, max:11, message: '请输入11位手机号码' }]
                }]"
                :disabled="getDisableStatus()"
              />
            </a-form-item>
            <a-form-item
              label="姓名:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-decorator="['personName', {
                  rules: [{required: true, min: 1, message: '请输入至少一个字符' }]
                }]"
                :disabled="getDisableStatus()"
              />
            </a-form-item>
            <a-form-item
              label="人员编码:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-decorator="['personNum', {
                  rules: [{ required: true, min: 5, message: '请输入至少五个字符' }]
                }]"
                :disabled="getDisableStatus()"
              />
            </a-form-item>
            <a-form-item
              label="职位:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-decorator="['postName']"
                :disabled="getDisableStatus()"
              />
            </a-form-item>
            <a-form-item
              label="身份证:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-decorator="['idCard']"
                :disabled="getDisableStatus()"
              />
            </a-form-item>
            <a-form-item
              v-show="false"
              label="personSysId:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-decorator="['personSysId', {
                  rules: [{ required: false }]
                }]"
                :disabled="getDisableStatus()"
              />
            </a-form-item>
            <a-form-item
              label="所属组织:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-decorator="['orgName', {
                  rules: [{ required: true, min:1, message: '组织不能为空' }]
                }]"
                :disabled="true"
              />
            </a-form-item>
            <a-form-item
              label="所属部门:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-decorator="['departmentSysId', {
                  rules: [{ type: 'string', required: true, message: '部门不能为空' }]
                }]"
                allowClear
                :disabled="getDisableStatus()"
              >
                <a-select-option
                  v-for="(item, index) in deptList"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item
              label="所属角色:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                mode="multiple"
                placeholder="选择角色..."
                :value="selectedItems"
                @change="handleChange"
                style="width: 100%"
                v-decorator="['roleId', {
                  rules: [{ required: true, message: '角色不能为空' }]
                }]"
              >
                <a-select-option
                  v-for="(item, index) in rolesList"
                  :key="index"
                  :value="item.roleId"
                >{{ item.roleName }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item
              label="用户状态:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-decorator="['activity', {
                  rules: [{ required: true, message: '请选择状态' }]
                }]"
              >
                <a-select-option value="Y">启用</a-select-option>
                <a-select-option value="N">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-form>
        </a-spin>
        <div
          :style="{
            position: 'absolute',
            right: 0,
            bottom: 0,
            width: '100%',
            borderTop: '1px solid #e9e9e9',
            padding: '10px 16px',
            background: '#fff',
            textAlign: 'right',
            zIndex: 1,
          }"
        >
          <a-button
            :style="{ marginRight: '8px' }"
            @click="visible = false"
          >取消</a-button>
          <a-button
            @click="doSubmit"
            type="primary"
          >保存</a-button>
        </div>
      </a-drawer>
    </a-card>
  </section>
</template>

<script>
import Vue from 'vue'
import { ORG_NAME, ORG_ID } from '@/store/mutation-types'
import { STable, Ellipsis } from '@/components'
import { requestBuilder } from '@/utils/util'
import * as baseApi from '@/api/system/base'
import * as umApi from '@/api/system/um'
import md5 from 'md5'

export default {
  name: 'UserManager',
  components: {
    STable,
    Ellipsis
  },
  data () {
    return {
      // 分页参数
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20'],
      mdl: {},
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      // 表头定义
      columns: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' }
        },
        {
          title: '用户名',
          dataIndex: 'userNo',
          sorter: (a, b) => {
            return a.userNo.localeCompare(b.userNo)
          }
        },
        {
          title: '姓名',
          dataIndex: 'personName'
        },
        {
          title: '手机号码',
          dataIndex: 'mobilePhone'
        },
        {
          title: '身份证',
          dataIndex: 'idCard',
          scopedSlots: { customRender: 'description' }
        },
        {
          title: '职位',
          dataIndex: 'postName'
        },
        {
          title: '状态',
          dataIndex: 'activity',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '最后修改时间',
          dataIndex: 'modifyDate',
          scopedSlots: { customRender: 'date' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: '80px',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const param = requestBuilder('', Object.assign(this.queryParam), parameter.pageNo, parameter.pageSize)
        return umApi.getUserInfo(param).then(res => res.result)
      },
      // 行选择参数
      options: {
        alert: {
          show: false,
          clear: () => {
            this.selectedRowKeys = []
          }
        },
        rowSelection: {
          // selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        }
      },
      selectedRowKeys: [],
      selectedRows: [],
      // 滑动抽屉参数
      form: this.$form.createForm(this),
      confirmLoading: false, // 加载图标是否出现
      rolesList: [], // 角色列表
      deptList: [], // 所属部门列表
      selectedItems: [],
      orgName: '',
      visible: false,
      subTitle: '人员新建',
      temppass: '',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      rowClick: record => ({
        on: {
          // 点击事件
          dblclick: () => {
            this.openDrawer('edit', record)
          }
        }
      })
    }
  },
  filters: {},
  created () {
    // 获取角色列表
    this.getRoleList()
    this.getDeptList()
    this.orgName = Vue.ls.get(ORG_NAME)
  },
  methods: {
    getDisableStatus () {
      if (this.actionFlag === 'insert') {
        return false
      } else {
        return true
      }
    },
    handleChange (selectedItems) {
      this.selectedItems = selectedItems
    },
    doBatchDel (record) {
      const userNos = []
      if (record !== '') {
        userNos.push(record.userNo)
      } else {
        if (this.selectedRows.length > 0) {
          for (let i = 0; i < this.selectedRows.length; i++) {
            userNos.push(this.selectedRows[i].userNo)
          }
        } else {
          this.$message.error('未选中任何记录')
          return
        }
      }
      this.$confirm({
        title: '确认?',
        content: '是否删除此记录',
        onOk: () => {
          this.handleDel(userNos.join(','))
        },
        onCancel () {}
      })
    },
    handleDel (value) {
      this.actionFlag = 'delete'
      const param = {
        userNo: value
      }
      this.getResult(param)
    },
    doSearch () {
      this.$refs.table.refresh()
      this.visible = false
    },
    doEdit () {
      if (this.selectedRows.length > 0) {
        if (this.selectedRows.length > 1) {
          this.$message.warning('请勾选一条记录')
        } else {
          this.openDrawer('update', this.selectedRows[0])
        }
      } else {
        this.$message.error('未选中任何记录')
      }
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    touchPersonInfo () {
      if (this.form.getFieldValue('mobilePhone').length === 11) {
        this.getPersonInfo(this.form.getFieldValue('mobilePhone'))
      }
    },
    touchUserInfo () {
      if (this.form.getFieldValue('userNo').length >= 5) {
        this.getPersonInfo(this.form.getFieldValue('userNo'))
      }
    },
    openDrawer (action, record) {
      this.visible = true
      this.$nextTick(() => {
        if (action === 'insert') {
          this.form.resetFields()
          this.form.setFieldsValue({
            orgName: this.orgName,
            departmentSysId: ''
          })
          this.subTitle = '用户新增'
          this.actionFlag = 'insert'
        } else {
          this.subTitle = '用户修改'
          this.form.setFieldsValue({
            id: record.id,
            userNo: record.userNo,
            mobilePhone: record.mobilePhone,
            personName: record.personName,
            password: record.password,
            idCard: record.idCard,
            orgName: record.orgName,
            departmentName: record.departmentName,
            activity: record.activity,
            postName: record.postName,
            personNum: record.personNum,
            departmentSysId: record.departmentSysId,
            personSysId: record.personSysId,
            roleId: []
          })
          this.getRoleListByUserNo(record.userNo)
          this.temppass = record.password
          this.actionFlag = 'update'
        }
      })
    },
    getRoleList () {
      this.confirmLoading = true
      umApi
        .getRoleInfo(requestBuilder('select', { activity: 'Y' }, 0, 0))
        .then(res => {
          if (res.code === '0000') {
            this.rolesList = res.result
          } else {
            this.$message.error(' 获取角色列表失败 ')
          }
        })
        .catch(err => {
          alert(err)
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    getDeptList () {
      this.confirmLoading = true
      baseApi.getCommboxById({ id: 'dept', sqlParams: { byOrgId: '1', orgId: Vue.ls.get(ORG_ID) } }).then(res => {
        this.deptList = res.result
      })
      this.confirmLoading = false
    },
    getRoleListByUserNo (value) {
      this.confirmLoading = true
      umApi
        .getRoleInfoByUserNo(requestBuilder('', { userNo: value }, 0, 0))
        .then(res => {
          if (res.code === '0000') {
            const arr = []
            for (let i = 0; i < res.result.length; i++) {
              arr.push(res.result[i].roleId)
              this.form.setFieldsValue({
                roleId: arr
              })
            }
          } else {
            this.$message.error('读取角色列表失败 ')
          }
        })
        .catch(err => {
          alert(err)
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    getUserInfo (userNo) {},
    getPersonInfo (mobilePhone) {
      this.confirmLoading = true
      umApi
        .isPersonExist(requestBuilder('', { mobilePhone: mobilePhone }, 0, 0))
        .then(res => {
          if (res.code === '0000') {
            this.form.setFieldsValue({
              mobilePhone: res.result.mobilePhone,
              personName: res.result.personName,
              idCard: res.result.idCard,
              postName: res.result.postName,
              personNum: res.result.personNum,
              personSysId: res.result.personSysId
            })
          } else if (res.code === '0002') {
            this.form.setFieldsValue({
              personName: '',
              idCard: '',
              postName: '',
              personNum: '',
              personSysId: ''
            })
            this.$message.success(res.message)
          } else if (res.code === '0003') {
            this.$message.error(res.message)
            this.form.setFieldsValue({
              mobilePhone: '',
              personName: '',
              idCard: '',
              departmentName: '',
              postName: '',
              personNum: '',
              departmentSysId: '',
              personSysId: ''
            })
          }
        })
        .catch(err => {
          alert(err)
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    // 保存数据
    doSubmit () {
      const {
        form: { validateFields }
      } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        // 如果输入数据都合规
        if (!errors) {
          this.params = { ...values }
          if (this.actionFlag === 'insert') {
            this.params.password = md5(values.password)
            this.getResult(this.params)
          } else {
            if (this.temppass !== this.params.password) {
              this.params.password = md5(values.password)
            }
            this.getResult(this.params)
          }
        } else {
          this.confirmLoading = false
        }
      })
    },
    getResult (params) {
      umApi
        .modifyUserInfo(requestBuilder(this.actionFlag, params, '0', '0'))
        .then(res => {
          if (res.code === '0000') {
            this.$message.success(res.message)
            this.doSearch()
          } else if (res.code === '9999') {
            this.$message.error(res.message)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    }
  }
}
</script>
<style scoped>
</style>
