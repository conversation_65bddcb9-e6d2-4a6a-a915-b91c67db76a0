<template>
  <section>
    <!-- <span v-if="!error" style="font-size: 18px; color: #303133; padding: 50px;">请等待跳转！请不要使用IE8以下浏览器。如果使用360浏览器，请切换成极速模式。</span> -->
    <span v-if="error" style="font-size: 18px; color: #303133; padding: 50px;">单点登录失败,请联系系统管理员!</span>
    <span v-else style="font-size: 18px; color: #303133; padding: 50px;">请等待跳转</span>
    <ChooseCompanyModal ref="modal" :list="userInfoList"/>
  </section>
</template>

<script>
import NProgress from 'nprogress'
import ChooseCompanyModal from '@/views/system/components/ChooseCompanyModal.vue'
import * as ssoApi from '@/api/system/sso'
import * as baseApi from '@/api/system/base'
import { OPERATOR, ORG_ID, ACCESS_TOKEN, PERSON_ID, CC_TOKEN, TENANT, MOBILE_PHONE } from '@/store/mutation-types'
import Vue from 'vue'
import { requestBuilder } from '@/utils/util'
export default {
  name: 'SSOlogin',
  components: {
    ChooseCompanyModal
    // CreateForm,
  },
  created () {
    NProgress.start()
    const search = window.location.search
    const searchParams = new URLSearchParams(search)
    this.token = searchParams.get('token')
    console.log('this.token', this.token)
    // 调用单点认证接口
    this.ssoLogin()
  },
  data () {
    return {
      token: '',
      error: false,
      userInfoList: []
    }
  },
  methods: {
    ssoLogin () {
      if (!this.token) {
        NProgress.done()
        this.error = true
        return
      }
      const param = {
        token: this.token
      }
      ssoApi.ssoLogin(param).then(res => {
        localStorage.clear()
        console.dir(res) // 以树状结构打印res对象
        if (res.code !== '0000') {
          this.error = true
          return
        }
        const DATA = res.result
        var DATA_ACCESS_TOKEN = DATA.ACCESS_TOKEN
        var DATA_PERSON_ID = DATA.PERSON_ID
        var DATA_OPERATOR = DATA.OPERATOR
        var DATA_ORG_ID = DATA.ORG_ID
        var DATA_CC_TOKEN = DATA.CC_TOKEN
        var DATA_TENANT = DATA.TENANT
        var DATA_MOBILE_PHONE = DATA.MOBILE_PHONE
        const a = { 'value': DATA_ACCESS_TOKEN, 'expire': null }
        const b = { 'value': DATA_PERSON_ID, 'expire': null }
        const c = { 'value': DATA_OPERATOR, 'expire': null }
        const d = { 'value': DATA_ORG_ID, 'expire': null }
        const e = { 'value': DATA_CC_TOKEN, 'expire': null }
        const f = { 'value': DATA_TENANT, 'expire': null }
        const g = { 'value': DATA_MOBILE_PHONE, 'expire': null }
        localStorage.setItem('nbggoods__' + ACCESS_TOKEN, JSON.stringify(a))
        localStorage.setItem('nbggoods__' + PERSON_ID, JSON.stringify(b))
        localStorage.setItem('nbggoods__' + OPERATOR, JSON.stringify(c))
        localStorage.setItem('nbggoods__' + ORG_ID, JSON.stringify(d))
        localStorage.setItem('nbggoods__' + CC_TOKEN, JSON.stringify(e))
        localStorage.setItem('nbggoods__' + TENANT, JSON.stringify(f))
        localStorage.setItem('nbggoods__' + MOBILE_PHONE, JSON.stringify(g))
        if (DATA_TENANT === 'Y') {
          return this.chooseCompany(DATA_MOBILE_PHONE)
        } else {
          this.$router.push({ name: 'dashboard' })
        }
      }).finally(() => {
        NProgress.done()
      })
    },
    // 选择单位登录
    chooseCompany (mobilePhone) {
      // 判断该用户是否存在两个单位，若存在则选择，若只在一个单位则直接登录
      // 调用结果获取多单位信息
      baseApi.queryTenantInfo(requestBuilder('', { mobilePhone: mobilePhone })).then(res => {
        if (res.result && res.result.length > 1) {
          // 让用户选择单位进行登录
          this.userInfoList = res.result
          this.$refs.modal.showModal(false)
        } else {
          // 虽然是多单位但是当前用户只在一个单位中有数据，则默认使用当前信息
          this.$router.push({ name: 'dashboard' })
        }
      })
    }

  }
}

</script>

<style>

</style>
