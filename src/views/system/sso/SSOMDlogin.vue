<template>
  <!-- <span v-if="!error" style="font-size: 18px; color: #303133; padding: 50px;">请等待跳转！请不要使用IE8以下浏览器。如果使用360浏览器，请切换成极速模式。</span> -->
  <span v-if="error" style="font-size: 18px; color: #303133; padding: 50px;">单点登录失败,请联系系统管理员!</span>
  <span v-else style="font-size: 18px; color: #303133; padding: 50px;">请等待跳转</span>

</template>

<script>
import NProgress from 'nprogress'
import * as ssoApi from '@/api/system/sso'
import { OPERATOR, ORG_ID, ACCESS_TOKEN, PERSON_ID } from '@/store/mutation-types'
import Vue from 'vue'
export default {
  name: 'SSOMDlogin',
  created () {
    NProgress.start()
    const search = window.location.search
    const searchParams = new URLSearchParams(search)
    this.token = searchParams.get('randstr')
    console.log('this.randstr', this.token)
    // 调用单点认证接口
    this.ssoLogin()
  },
  data () {
    return {
      token: '',
      error: false
    }
  },
  methods: {
    ssoLogin () {
      if (!this.token) {
        NProgress.done()
        this.error = true
        return
      }
      const param = {
        token: this.token
      }
      ssoApi.ssoMDLogin(param).then(res => {
        localStorage.clear()
        console.dir(res) // 以树状结构打印res对象
        if (res.code !== '0000') {
          this.error = true
          return
        }
        const DATA = res.result
        var DATA_ACCESS_TOKEN = DATA.ACCESS_TOKEN
        var DATA_PERSON_ID = DATA.PERSON_ID
        var DATA_OPERATOR = DATA.OPERATOR
        var DATA_ORG_ID = DATA.ORG_ID
        const a = { 'value': DATA_ACCESS_TOKEN, 'expire': null }
        const b = { 'value': DATA_PERSON_ID, 'expire': null }
        const c = { 'value': DATA_OPERATOR, 'expire': null }
        const d = { 'value': DATA_ORG_ID, 'expire': null }
        localStorage.setItem('nbggoods__' + ACCESS_TOKEN, JSON.stringify(a))
        localStorage.setItem('nbggoods__' + PERSON_ID, JSON.stringify(b))
        localStorage.setItem('nbggoods__' + OPERATOR, JSON.stringify(c))
        localStorage.setItem('nbggoods__' + ORG_ID, JSON.stringify(d))
        this.$router.push({ name: 'dashboard' })
      }).finally(() => {
        NProgress.done()
      })
    }

  }
}

</script>

<style>

</style>
