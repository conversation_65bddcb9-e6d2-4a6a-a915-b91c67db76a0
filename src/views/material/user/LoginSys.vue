<template>
  <section>
    <div class="main">
      <div class="main-container">
        <div class="main-header">
          <div class="title">
            <img
              src="~@/assets/logo/logo.svg"
              class="logo"
              alt="logo"
            >
            <span>ITSM</span>
          </div>
          <div class="description">
            <span>基于 Ant Disign Vue Pro 框架搭建</span>
          </div>
        </div>
        <div class="main-content">
          <a-form
            :form="form"
            ref="formLogin"
            class="user-layout-login"
            @submit="doLogin"
          >
            <a-form-item style="margin-bottom: 20px;">
              <a-input
                size="large"
                type="text"
                placeholder="输入用户名: "
                v-decorator="['username', {
                  rules: [{ required: true, message: '用户名不能为空' }],
                  validateTrigger: 'change'
                }]"
              >
                <a-icon
                  slot="prefix"
                  type="user"
                  :style="{ color: 'rgba(0,0,0,.25)' }"
                />
              </a-input>
            </a-form-item>

            <a-form-item style="margin-bottom: 5px;">
              <a-input-password
                size="large"
                autocomplete="false"
                placeholder="输入密码: "
                v-decorator="['password', {
                  rules: [{ required: true, message: '请输入密码' }],
                  validateTrigger: 'blur'
                }]"
              >
                <a-icon
                  slot="prefix"
                  type="lock"
                  :style="{ color: 'rgba(0,0,0,.25)' }"
                />
              </a-input-password>
              <!-- <a-input
                size="large"
                type="password"
                autocomplete="false"
                placeholder="输入密码: "
                v-decorator="['password', {
                  rules: [{ required: true, message: '请输入密码' }],
                  validateTrigger: 'blur'
                }]"
              >
                <a-icon
                  slot="prefix"
                  type="lock"
                  :style="{ color: 'rgba(0,0,0,.25)' }"
                />
              </a-input> -->
            </a-form-item>

            <a-form-item style="padding: 0 5px; overflow: hidden;">
              <a-checkbox
                v-decorator="['rememberMe']"
              >自动登录</a-checkbox>
              <a
                href="http://www.nbport.com.cn/tam/tamapp/login.jsp"
                style="float: right; font-size: 14px; color: #f34d4d"
              >忘记密码</a>
            </a-form-item>

            <a-form-item style="margin: 30px 0 0">
              <a-button
                size="large"
                type="primary"
                htmlType="submit"
                class="login-button"
                :loading="state.loginBtn"
                :disabled="state.loginBtn"
              >登录</a-button>
            </a-form-item>

            <a-form-item style="padding: 0 5px; overflow: hidden;">
              <router-link
                v-if="false"
                class="register"
                style="float: right; color: #f34d4d;"
                :to="{ name: 'register' }"
              >注册账户</router-link>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>
    <div class="footer">
      <div class="links">
        <a href="_self">帮助</a>
        <a href="_self">隐私</a>
        <a href="_self">条款</a>
      </div>
      <div class="copyright">Copyright &copy; 2020 宁波港信息通信有限公司</div>
    </div>
  </section>
</template>

<script>
import md5 from 'md5'
import { mapActions } from 'vuex'
import { timeFix } from '@/utils/util'

export default {
  data () {
    return {
      state: {
        time: 60,
        loginBtn: false
      },
      loginParams: {},
      form: this.$form.createForm(this),
      isLoginError: false,
      loginBtn: false
    }
  },
  methods: {
    // 登录操作
    doLogin (e) {
      e.preventDefault()
      const { state, LoginSys } = this
      state.loginBtn = true
      this.form.validateFields((err, values) => {
        if (!err) {
          this.loginParams = { ...values }
          this.loginParams.password = md5(values.password)
          LoginSys(this.loginParams)
            .then(res => this.loginSuccess(res))
            .catch(err => this.requestFailed(err))
            .finally(() => {
              state.loginBtn = false
            })
        } else {
          setTimeout(() => {
            state.loginBtn = false
          }, 600)
        }
      })
    },
    // 登录成功
    loginSuccess (res) {
      setTimeout(() => {
        this.$notification.success({
          message: `欢迎`,
          description: `${timeFix()}，欢迎回来`,
          duration: 2
        })
      }, 1000)
      this.$router.push({ path: '/' })
      this.isLoginError = false
    },
    // 登录失败
    requestFailed (err) {
      this.isLoginError = true
      this.$notification['error']({
        message: '错误',
        description: err.message,
        duration: 3
      })
    },
    // 导入 action
    ...mapActions(['LoginSys', 'Logout', 'LoginTX'])
  }
}
</script>

<style lang="less" scoped>
::v-deep {
  .ant-input-lg {
    height: 40px;
    padding: 6px 11px;
    font-size: 16px;
  }
  .ant-form-horizontal,
  .ant-form-vertical,
  .ant-form-inline {
    .ant-form-item {
      width: 100%;
      & > .ant-form-item-control-wrapper {
        flex: 0 0 auto;
        width: calc(100% - 80px);
        height: 40px;
        margin: 0 auto;
      }
    }
  }
}
section {
  display: flex;
  flex-flow: row nowrap;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #f0f2f5 url(~@/assets/login/background.svg);
  & > .main {
    width: 400px;
    height: 100%;
    flex: 0 0 auto;
    position: relative;
    & > .main-container {
      width: 100%;
      height: 450px;
      margin: auto;
      padding-left: 8px;
      overflow: hidden;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 180px;
      & > .main-header {
        width: calc(100% - 80px);
        height: auto;
        margin: 0 auto 48px;
        & > .title {
          & > .logo {
            height: 44px;
            vertical-align: top;
            margin-right: 16px;
            border-style: none;
          }
          font-size: 33px;
          color: rgba(0, 0, 0, 0.85);
          text-align: center;
          font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
          font-weight: 600;
        }
        & > .description {
          font-size: 15px;
          color: rgba(0, 0, 0, 0.45);
          text-align: center;
          margin-top: 10px;
        }
      }
      & > .main-content {
        label {
          font-size: 14px;
        }
        button.login-button {
          padding: 0 15px;
          font-size: 16px;
          height: 40px;
          width: 100%;
          margin: 0;
        }
      }
    }
  }
  & > .footer {
    width: 100%;
    padding: 0 16px;
    margin: 48px 0 24px;
    text-align: center;
    position: fixed;
    bottom: 0;
    .links {
      margin-bottom: 8px;
      font-size: 14px;
      a {
        color: rgba(0, 0, 0, 0.45);
        text-decoration: none;
        transition: all 0.3s;
        &:not(:last-child) {
          margin-right: 40px;
        }
      }
    }
    .copyright {
      color: rgba(0, 0, 0, 0.45);
      font-size: 14px;
    }
  }
}
</style>
