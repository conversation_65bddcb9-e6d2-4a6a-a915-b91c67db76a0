<template>
  <section class="m-art" style="height: 100%;" ref="page" id="page">
    <!-- 公共组件 -->
    <div class="bgc image" style="padding: 80px 10px;">
      <div class="m-btn">
        <a-button v-show="!fullscreenFlag" @click="openDrawer"><a-icon type="user" />编辑模式</a-button>
        <a-button @click="togglePage"><a-icon type="user"/> 全屏切换</a-button>
      </div>
      <div class="m-box">

        <div
          v-for="(info, index) in groupList"
          :key="index"
          :class="[info.className]"
        >
          <div
            class="cardSlick"
            v-for="(item, ins) in info.rightList"
            :key="ins"
          >
            <component :is="item.boxName" :styleName="styleName" />
          </div>
        </div>
      </div>
    </div>
    <CrewEditDrawer
      ref="CrewEditDrawer"
      origin="WZ"
      :currentList="groupList"
      :mainList="checkTopList"
      :mainLargeList="checkmidList"
      @submit="onSubmit"
    />
  </section>
</template>
<script>
import * as components from './modules/MaterialDepartment/CrewComponents.js'
import CrewEditDrawer from './modules/CrewEdit/CrewEditDrawer'
import { SlickList, SlickItem } from 'vue-slicksort'
import { mixin } from '@/utils/mixin'
import { mapGetters } from 'vuex'
import '@/style/baseColor.less'

import Vue from 'vue'
import { ORG_ID, PERSON_NAME } from '@/store/mutation-types'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_MD = USER_ORG_ID === '1.100.104'
export default {
  name: 'MaterialDepartment',
  components: {
    ...components,
    SlickList,
    SlickItem,
    CrewEditDrawer
  },
  mixins: [mixin],
  data () {
    return {
      fullscreenFlag: false,
      styleName: 'themea',
      imgUrl: require('@/assets/screen/head.png'),
      checkTopList: [{
        boxName: 'CrewAcceptancePlan',
        boxNum: 0
      }, {
        boxName: 'CrewIssuePlan',
        boxNum: 1
      }, {
        boxName: 'CrewWarehousing',
        boxNum: 2
      }, {
        boxName: 'CrewGoodsOut',
        boxNum: 3
      }, {
        boxName: 'CrewPriceDeviationM',
        boxNum: 4
      }, {
        boxName: 'CrewFixedAssetsPlanShow',
        boxNum: 5
      },
      ...(IS_MD ? [{
        boxName: 'CrewFixedAssetsPlan',
        boxNum: 5
      }] : [{
        boxName: 'CrewTemporaryPurchaseM',
        boxNum: 5
      }]),
      {
        boxName: 'CrewNonPurchaseM',
        boxNum: 6
      },
      {
        boxName: 'CrewInventoryAmount',
        boxNum: 8
      },
      ...(IS_MD ? [] : [{
        boxName: 'CrewWarningInformationM',
        boxNum: 7
      }])],
      checkmidList: [{
        boxName: 'CrewContractInformation',
        boxNum: 0
      }, {
        boxName: 'CrewTender',
        boxNum: 1
      }, {
        boxName: 'CrewSplitPurchasingM',
        boxNum: 2
      }, {
        boxName: 'CrewSingleSourceM',
        boxNum: 3
      },
      ...(IS_MD ? [{
        boxName: 'CrewTotalConsumption',
        boxNum: 4
      }, {
        boxName: 'CrewImportantMaterialM',
        boxNum: 5
      }, {
        boxName: 'CrewBudgetAnalysis',
        boxNum: 6
      }, {
        boxName: 'CrewBudgetAnalysisShow',
        boxNum: 7
      }] : [{
        boxName: 'CrewAbnormalSettlementM',
        boxNum: 5
      }, {
        boxName: 'CrewInventoryStatistics',
        boxNum: 8
      }])],
      groupList: this.getList(),
      editStatus: '预览模式'
    }
  },
  computed: {
    ...mapGetters(['todoCockpitGroup'])
  },
  mounted () {
  },
  watch: {
    navTheme: {
      immediate: true,
      handler (val) {
        const value = val === 'light' ? 'a' : 'b'
        this.handleMenuClick({ key: value })
      }
    }
  },
  methods: {
    getList () {
      return this.$nextTick(() => {
        const obj = this.todoCockpitGroup.find(item => item.cockpitType === 'WZ')
        if (!obj) {
          return []
        }
        if (IS_MD) {
          this.groupList = obj.outBoxList.map(item => {
            item.rightList = item.rightList.filter(card => !['CrewTemporaryPurchaseM', 'CrewWarningInformationM', 'CrewAbnormalSettlementM'].includes(card.boxName))
            return item
          })
        } else {
          this.groupList = obj.outBoxList
        }

        return obj
      })
    },
    handleMenuClick (command) {
      document.getElementById('app').className = 'theme' + command.key
      this.styleName = 'theme' + command.key
    },
    openDrawer () {
      if (this.fullscreenFlag) {
        this.$message.error('全屏模式不可编辑！')
        return
      }
      this.$refs.CrewEditDrawer.showDrawer()
    },
    onSubmit (list) {
      this.groupList = list
    },
    togglePage () {
      if (this.fullscreenFlag) {
        this.$fullscreen.exit()
        this.fullscreenFlag = false
        return
      }
      const dom = document.getElementById('page')
      this.$fullscreen.enter(dom, {
        wrap: false,
        callback: f => {
          this.fullscreenFlag = f
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
@import "../../../style/baseColor.less";
.bgc {
  background: url(./pic/materialTitle.png);
}
.m-box {
  width: 100%;
  height: 100%;
  .small-area {
    display: flex;
    width: 100%;
    height: auto;
    flex-wrap: wrap;
    .cardSlick {
      width: 24%;
      height: 100%;
      margin: .5%;
    }
  }
  .large-area {
    display: flex;
    width: 100%;
    height: auto;
    flex-wrap: wrap;
    .cardSlick {
      width: 49%;
      display: flex;
      height: 100%;
      margin: .5%;
    }
  }
}
</style>
