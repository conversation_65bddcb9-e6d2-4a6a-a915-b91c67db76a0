<template>
  <CrewCardPlus :title="`${subTitle}`">
    <template slot="title">
      <div class="head" style="cursor: pointer;" @click="showModal">{{ subTitle }}</div>
    </template>
    <template slot="right-icon">
      <div class="selectInp">
        <a-select
          v-model="formData.orgId"
          :disabled="disabled"
          :getPopupContainer="triggerNode => triggerNode.parentNode"
          dropdownClassName="rangePickerIceGai"
          placeholder="公司"
          style="width: 140px"
          @change="onChange"
        >
          <a-select-option :value="item.value" v-for="(item, index) in orgIds" :key="index">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </div>
    </template>
    <template slot="text">
      <div class="content">
        <s-table
          ref="table"
          :columns="columns"
          :data="loadData"
          :scroll="scroll"
          :customRow="rowClick"
          :pageSizeOptions="pageSizeOptions"
          :pageSize="defaultPageSize"
          :showPagination="true"
          rowKey="uuid"
        >
          <span slot="orgId" slot-scope="key">{{ takeSelectLabel(orgIds, key) }}</span>
        </s-table>
      </div>
      <!-- <div class="content-bottom">
        <div class="info-bottom">
          <span class="desc">基层({{ formInfo.companySum || 0 }}/￥{{ formInfo.companyAmount || 0 }})</span>|
          <span class="desc">集团({{ formInfo.groupSum || 0 }}/￥{{ formInfo.groupAmount || 0 }})</span>|
          <span class="desc">地方({{ formInfo.localSum || 0 }}/￥{{ formInfo.localAmount || 0 }})</span>|
          <span class="desc">OA({{ formInfo.oaSum || 0 }}/￥{{ formInfo.oaAmount || 0 }})</span>
        </div>
      </div>
      <div ref="initwarp" :class="{ initwarp: styleName === 'themeb' }">
        <a-modal
          title="查看详情"
          :visible="visible"
          :confirm-loading="confirmLoading"
          :getContainer="() => $refs.initwarp"
          width="1000px"
          :bodyStyle="{ height: '600px' }"
          :footer="null"
          @cancel="handleCancel"
        >
          <s-table
            ref="table"
            :columns="columns"
            :components="components"
            :data="loadData"
            :scroll="scroll"
            :rowClassName="rowClassName"
            :customRow="rowClick"
            :bordered="false"
            :pageSizeOptions="pageSizeOptions"
            :pageSize="defaultPageSize"
            :showPagination="true"
            no-striped
            rowKey="uuid"
          >
            <span
              slot="serial"
              slot-scope="text, record, index"
            >{{ index + 1 }}</span>
          </s-table>
        </a-modal>
      </div> -->
    </template>
  </CrewCardPlus>
</template>
<script>
import CrewCardPlus from '../Crew/CrewCardPlus'
import countTo from 'vue-count-to'
import { Timeline, STable } from '@/components'
import { requestBuilder, takeTreeByKey } from '@/utils/util'
import { ORG_ID } from '@/store/mutation-types'
import Vue from 'vue'
import * as cockpitApi from '@/api/material/cockpit'
import * as baseApi from '@/api/material/base'
import * as workorderApi from '@/api/device/workorder'
import moment from 'moment'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_MS_ENV = USER_ORG_ID === '1.100.104'
export default {
  name: 'CrewEquipmentStatus',
  components: {
    CrewCardPlus,
    countTo,
    Timeline,
    STable
  },
  props: {
    styleName: {
      type: String,
      default: () => {
        return ''
      }
    },
    disabled: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data () {
    this.components = {
      header: {
        cell: (h, props, children) => {
          const { key, ...restProps } = props
          const col = this.columns.find(col => {
            const k = col.dataIndex || col.key
            return k === key
          })

          if (!col || !col.width) {
            return h('th', { ...restProps }, [...children])
          }

          const dragProps = {
            key: col.dataIndex || col.key,
            class: 'table-draggable-handle',
            attrs: {
              w: 10,
              x: col.width,
              z: 1,
              axis: 'x',
              draggable: true,
              resizable: false
            },
            on: {
              dragging: (x, y) => {
                col.width = Math.max(x, 1)
              }
            }
          }
          const drag = h('vue-draggable-resizable', { ...dragProps })
          return h('th', { ...restProps, class: 'resize-table-th' }, [...children, drag])
        }
      }
    }
    return {
      IS_MS_ENV,
      USER_ORG_ID,
      loading: true,
      timeIndex: 0,
      timelength: 0,
      timeLineList: [
        {
          projectType: 322,
          filingDate: 12
        },
        {
          projectType: 322,
          filingDate: 12
        },
        {
          projectType: 322,
          filingDate: 12
        },
        {
          projectType: 322,
          filingDate: 12
        }
      ],
      formData: {
        orgId: USER_ORG_ID,
        projectName: '',
        dates: this.getDate()
      },
      orgIds: [],
      projList: [],
      formInfo: {
        companyAmount: 0,
        companySum: 0,
        groupAmount: 0,
        groupSum: 0,
        localAmount: 0,
        localSum: 0,
        oaAmount: 0,
        oaSum: 0
      },
      visible: false,
      confirmLoading: false,
      // 表格数据
      dataSource: [],
      columns: [
        {
          title: '设备名称',
          dataIndex: 'assetSbNum',
          align: 'center',
          ellipsis: true,
          width: 150
        },
        {
          title: '单位',
          dataIndex: 'orgId',
          scopedSlots: { customRender: 'orgId' },
          align: 'center',
          ellipsis: true,
          width: 100
        },
        {
          title: '设备状态',
          dataIndex: 'deviceStatus',
          align: 'center',
          ellipsis: true,
          width: 150
        }
      ],
      scroll: {
        x: '100%',
        // y: 'calc(60vh)',
        scrollToFirstRowOnChange: false
      },
      selectedRows: [],
      selectedRowKeys: [],
      defaultPageSize: 6,
      pageSizeOptions: ['6', '10', '20', '30', '40'],
      loadData: parameter => {
        const param = requestBuilder(
          '',
          {
            orgId: this.formData.orgId
            // projectName: this.formData.projectName || '',
            // startTime: this.formData.dates.length > 0 ? this.formData.dates[0].format('YYYY-MM-DD') : '',
            // endTime: this.formData.dates.length > 0 ? this.formData.dates[1].format('YYYY-MM-DD') : ''
          },
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder
        )
        return workorderApi.queryWZGAssetSbStatus(param).then(res => {
          if (res.code === '0000') {
            this.dataSource = res.result.data || []
            return res.result
          } else {
            this.$message.error(res.message)
          }
        })
      },
      rowClick: record => ({
        on: {}
      }),
      rowClassName: record => {
        return 'tablerow'
      }
    }
  },
  created () {
    this.init()
    this.doSearch()
  },
  computed: {
    sumNum () {
      return this.formInfo.companySum + this.formInfo.groupSum + this.formInfo.localSum
    },
    subTitle () {
      return '设备状态'
    }
  },
  methods: {
    init () {
      baseApi.getCommboxById({ id: 'wzgOrg' }).then(res => {
        if (res.code === '0000') {
          this.orgIds = res.result
        }
      })
    },
    getDate () {
      const dates = new Date()
      const month = dates.getMonth() + 1
      const month2 = month >= 12 ? 1 : month + 1
      const year = dates.getFullYear()
      const year2 = month >= 12 ? year + 1 : year
      const value = [
        moment(year + '-' + month + '-01', 'YYYY-MM-DD'),
        moment(year2 + '-' + month2 + '-01', 'YYYY-MM-DD')
      ]
      return value
    },
    doSearch () {
      this.$refs.table.refresh()
    },
    changeActive (index) {
      this.timeIndex = index
      this.formData = this.timeLineList[this.timeIndex]
    },
    // 获取下拉框选项文本
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || ''
    },
    notShow () {
      console.log('notShow')
    },
    showModal () {
      // if (this.disabled) {
      //   return
      // }
      // this.visible = true
      // if (this.$refs.table) {
      //   this.$refs.table.refresh()
      // }
    },
    handleCancel (e) {
      this.visible = false
    },
    onChange (value) {
      this.doSearch()
    }
  }
}
</script>
<style lang="less" scoped>
.content-bottom {
  height: 35px;
  .info-bottom {
    height: 35px;
    padding-left: 15px;
    display: flex;
    align-items: center;
    .desc {
      width: 23%;
      margin: 0 5px;
    }
  }
}
.content {
  width: 100%;
  height: 470px;
  padding: 15px;
  overflow-y: scroll;
  .top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
  }
  .topic {
    font-size: 16px;
    font-weight: 700;
  }
  .secTopic {
    width: auto;
    font-size: 10px;
    font-weight: normal;
    opacity: 0.7;
    margin-left: 10px;
  }
  .text {
    display: flex;
    font-size: 10px;
    .label {
      opacity: 0.7;
      margin-right: 5px;
    }
    .value {
      font-weight: 700;
    }
    .divid {
      margin: 0 5px;
      opacity: 0.7;
    }
  }
  .flexBox {
    display: flex;
    justify-content: space-between;
  }
  .bottom {
    margin-top: 15px;
    width: 100%;
    .bottom-title {
      font-size: 14;
      font-weight: 700;
      font-style: italic;
    }
    .bottom-text {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      .checkbox {
        margin-right: 2px;
        position: relative;
        .ant-tag-has-color {
          color: #232323;
        }
        .icongre {
          position: absolute;
          right: 2px;
          top: -3px;
          z-index: 20;
        }
      }
    }
  }
  .step {
    width: 100%;
    margin: 20px 0;
    border-top: 1px solid #d4d4d4;
  }
  .liubiao {
    color: #ffffff;
    background-color: #ef0f0f;
    border-radius: 2px;
    line-height: 22px;
    padding: 0 4px;
  }
  .head {
    cursor: pointer;
  }
  .selectInp {
    display: flex;
  }
}
</style>
