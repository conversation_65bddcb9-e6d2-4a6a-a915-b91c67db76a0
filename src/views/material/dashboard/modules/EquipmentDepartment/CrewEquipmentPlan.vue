<template>
  <CrewCardDeb title="设备计划信息">
    <template slot="text">
      <div class="box">
        <div class="flexBox bordf">
          <div>今日维修计划</div>
          <div class="num" style="color: #FFC54D;">
            <countTo
              :startVal="0"
              :endVal="1000000"
              prefix=""
              suffix=""/>
          </div>
        </div>
        <div class="flexBox bordf">
          <div>明日维修计划</div>
          <div class="num">
            <countTo
              :startVal="0"
              :endVal="1000000"
              prefix=""
              suffix=""/>
          </div>
        </div>
        <div class="flexBox">
          <div>本月维修计划</div>
          <div class="num">
            <countTo
              :startVal="0"
              :endVal="1000000"
              prefix=""
              suffix=""/>
          </div>
        </div>
      </div>
    </template>
  </CrewCardDeb>
</template>
<script>
import CrewCardDeb from '../Crew/CrewCardDeb'
import countTo from 'vue-count-to'
// import * as earlyWarningApi from '@/api/material/earlyWarning'
// import { requestBuilder } from '@/utils/util'
export default {
  name: 'CrewEquipmentPlan',
  components: {
    CrewCardDeb,
    countTo
  },
  data () {
    return {
      loading: false,
      sourceData: []
    }
  },
  // 页面初始化挂载dom
  mounted () {
    this.initApi()
  },
  methods: {
    initApi () {
      // this.loading = true
      // const param = requestBuilder('', { searchYear: new Date().getFullYear() }, 1, 1)
      // earlyWarningApi.earlyWarningHighPrice(param).then(res => {
      //   this.infoData.outOfProportion = res.result.data[0].outOfProportion
      //   this.infoData.proportion = res.result.data[0].proportion
      //   this.sourceData = res.result.data[0].priceCountList || []
      // }).then(() => {
      // }).finally(() => {
      //   this.loading = false
      // })
    }
  }
}
</script>
<style lang="less" scoped>
.box {
  height: 155px;
  display: flex;
  justify-content: space-between;
  .flexBox {
    width: 33.33%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .bordf {
    border-right: 1px solid #666;
  }
  .num {
    font-size: 32px;
    font-weight: 700;
  }
}
</style>
