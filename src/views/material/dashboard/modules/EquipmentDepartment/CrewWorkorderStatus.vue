<template>
  <CrewCard title="工单数量">
    <template slot="right-icon">
      <div class="selectInp">
        <a-select
          v-model="queryParam.orgId"
          :disabled="disabled"
          :getPopupContainer="triggerNode => triggerNode.parentNode"
          dropdownClassName="rangePickerIceGai"
          placeholder="公司"
          style="width: 140px"
          @change="onChange"
        >
          <a-select-option :value="item.value" v-for="(item, index) in orgIds" :key="index">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </div>
    </template>
    <template slot="text">
      <div class="box">
        <div>
          <a-range-picker v-model="dates" :getCalendarContainer="triggerNode => triggerNode.parentNode" dropdownClassName="rangePickerIceGai" @change="onChange" />
          <a-spin :spinning="loading">
            <div class="boxFlex" @click="showModal">
              <div class="num">
                <countTo
                  :startVal="0"
                  :endVal="num"
                  prefix=""
                  suffix=""/>
              </div>
              <div class="text">工单数量</div>
            </div>
          </a-spin>
        </div>
      </div>
      <div ref="initwarp" :class="{ initwarp: styleName === 'themeb' }">
        <a-modal
          title="查看详情"
          :visible="visible"
          :confirm-loading="confirmLoading"
          :getContainer="() => $refs.initwarp"
          width="1000px"
          :bodyStyle="{ height: '600px' }"
          @cancel="handleCancel"
        >
          <s-table
            ref="table"
            :columns="columns"
            :components="components"
            :data="loadData"
            :scroll="scroll"
            :rowClassName="rowClassName"
            :customRow="rowClick"
            :bordered="false"
            :pageSizeOptions="pageSizeOptions"
            :pageSize="defaultPageSize"
            :showPagination="true"
            no-striped
            rowKey="uuid"
          >
            <span
              slot="serial"
              slot-scope="text, record, index"
            >{{ index + 1 }}</span>
            <span
              slot="contractNum"
              slot-scope="text, record"
            >
              <a href="javascript:void(0)" @click="jump(record)">{{ text }} </a>
            </span>
          </s-table>
          <template slot="footer">
            <a-button class="btnExp" :loading="loadingExport" @click="doExport">导出</a-button>
          </template>
        </a-modal>
      </div>
    </template>
  </CrewCard>
</template>
<script>
import CrewCard from '../Crew/CrewCard'
import countTo from 'vue-count-to'
import * as workorderApi from '@/api/device/workorder'
import { requestBuilder } from '@/utils/util'
import * as baseApi from '@/api/material/base'
import { STable } from '@/components'
import moment from 'moment'
import { ORG_ID } from '@/store/mutation-types'
import Vue from 'vue'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
export default {
  name: 'CrewWorkorderStatus',
  components: {
    CrewCard,
    countTo,
    STable
  },
  props: {
    styleName: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data () {
    this.components = {
      header: {
        cell: (h, props, children) => {
          const { key, ...restProps } = props
          const col = this.columns.find(col => {
            const k = col.dataIndex || col.key
            return k === key
          })

          if (!col || !col.width) {
            return h('th', { ...restProps }, [...children])
          }

          const dragProps = {
            key: col.dataIndex || col.key,
            class: 'table-draggable-handle',
            attrs: {
              w: 10,
              x: col.width,
              z: 1,
              axis: 'x',
              draggable: true,
              resizable: false
            },
            on: {
              dragging: (x, y) => {
                col.width = Math.max(x, 1)
              }
            }
          }
          const drag = h('vue-draggable-resizable', { ...dragProps })
          return h('th', { ...restProps, class: 'resize-table-th' }, [...children, drag])
        }
      }
    }
    return {
      USER_ORG_ID,
      num: 0,
      dates: [],
      orgIds: [],
      loading: false,
      loadingExport: false,
      visible: false,
      confirmLoading: false,
      // 表格数据
      dataSource: [],
      columns: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' },
          dataIndex: 'type',
          align: 'center',
          ellipsis: true,
          width: 50
        },
        {
          title: '申报编号',
          dataIndex: 'prlineNum',
          align: 'center',
          ellipsis: true,
          width: 120
        },
        {
          title: '物资代码',
          dataIndex: 'materialNum',
          align: 'center',
          ellipsis: true,
          width: 120
        },
        {
          title: '物资名称',
          dataIndex: 'materialName',
          align: 'center',
          ellipsis: true,
          width: 150
        },
        {
          title: '状态',
          dataIndex: 'statusName',
          align: 'center',
          ellipsis: true,
          width: 100
        },
        {
          title: '计划类型',
          dataIndex: 'lineTypeName',
          align: 'center',
          ellipsis: true,
          width: 100
        },
        {
          title: '申报人',
          dataIndex: 'createByName',
          ellipsis: true,
          align: 'center',
          width: 100
        },
        {
          title: '创建时间',
          dataIndex: 'createDate',
          ellipsis: true,
          sorter: true,
          align: 'center',
          width: 200
        }
      ],
      scroll: {
        x: '100%',
        y: 'calc(50vh)',
        scrollToFirstRowOnChange: false
      },
      selectedRows: [],
      selectedRowKeys: [],
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20', '30', '40'],
      queryParam: {
        year: moment(),
        orgId: USER_ORG_ID
        // proportion: 5
      },
      loadData: parameter => {
        // return workorderApi.earlyWarningTemporaryPurchase(requestBuilder('', {
        //   startTime: this.dates.length > 0 ? this.dates[0].format('YYYY-MM-DD') : '',
        //   endTime: this.dates.length > 0 ? this.dates[1].format('YYYY-MM-DD') : ''
        // }, parameter.pageNo, parameter.pageSize, parameter.sortField, parameter.sortOrder)).then(res => {
        //   if (res.code === '0000') {
        //     this.dataSource = res.result.data
        //     return res.result
        //   } else {
        //     this.$message.error(res.message)
        //   }
        // })
      },
      rowClick: record => ({
        on: {
          click: () => {
            this.jump(record)
          }
        }
      }),
      rowClassName: record => {
        return 'tablerow'
      }
    }
  },
  created () {
    this.initApi()
  },
  methods: {
    onChange () {
      this.initApi()
    },
    initApi () {
      this.loading = true
      const param = requestBuilder('', {
        orgId: this.queryParam.orgId,
        createDateFrom: this.dates.length > 0 ? this.dates[0].format('YYYY-MM-DD') : '',
        createDateTo: this.dates.length > 0 ? this.dates[1].format('YYYY-MM-DD') : ''
      })
      workorderApi.queryWZGWorkorderNumber(param).then(res => {
        console.log(res, 'res')
        this.num = res.result.count || 0
      }).finally(() => {
        this.loading = false
      })
      baseApi.getCommboxById({ id: 'wzgOrg' }).then(res => {
        if (res.code === '0000') {
          this.orgIds = res.result
        }
      })
    },
    showModal () {
      // this.visible = true
      // if (this.$refs.table) {
      //   this.$refs.table.refresh()
      // }
    },
    handleCancel (e) {
      this.visible = false
    },
    jump (record) {
      this.visible = false
      this.$router.push({
        name: 'ProcurePlan',
        params: {
          prlineNum: record.prlineNum,
          materialNum: record.materialNum,
          lineType: record.lineType
        }
      })
    },
    doExport () {
      // this.loadingExport = true
      // workorderApi.exportEarlyWarningTemporaryPurchase(requestBuilder('', {
      //   startTime: this.dates.length > 0 ? this.dates[0].format('YYYY-MM-DD') : '',
      //   endTime: this.dates.length > 0 ? this.dates[1].format('YYYY-MM-DD') : ''
      // },
      // null,
      // null)).then(res => {
      //   if (res && res.data && res.headers['content-disposition']) {
      //     const link = document.createElement('a')
      //     const url = window.URL.createObjectURL(res.data)
      //     const contentDisposition = res.headers['content-disposition']
      //     let filename = contentDisposition.replace(/(.*;)?filename=([^;]+).*/i, '$2')
      //     try {
      //       filename = decodeURIComponent(escape(filename))
      //     } catch (e) {}
      //     document.body.appendChild(link)
      //     link.style.display = 'none'
      //     link.download = filename
      //     link.href = url
      //     link.click()
      //     link.remove()
      //     window.URL.revokeObjectURL(url)
      //   } else {
      //     this.$notification.error({
      //       message: '系统消息',
      //       description: '下载失败！'
      //     })
      //   }
      // }).finally(() => {
      //   this.loadingExport = false
      // })
    }
  }
}
</script>
<style lang="less" scoped>
.box {
  height: 145px;
  display: flex;
  padding: 0 10px;
  justify-content: center;
  .boxFlex {
    text-align: center;
    margin-top: 25px;
  }
  .num {
    font-size: 34px;
    color: #F8AA4B;
  }
  .text {
    font-size: 12px;
  }
}
.btnExp {
  width: 80px;
}
</style>
