<template>
  <CrewCardDeb title="设备能耗维修">
    <template slot="text">
      <div class="box">
        <div class="flexBox bordf">
          <div class="picTitle">
            <div>用水</div>
            <img class="img" src="../../pic//set4.png"/>
          </div>
          <div class="num">
            <countTo
              :startVal="0"
              :endVal="300"
              prefix=""
              suffix=""/>
            <div style="color: #666; font-size: 12px; font-weight: normal;">吨</div>
          </div>
        </div>
        <div class="flexBox bordf">
          <div class="picTitle">
            <div>用油</div>
            <img class="img" src="../../pic//set5.png"/>
          </div>
          <div class="num">
            <countTo
              :startVal="0"
              :endVal="300"
              prefix=""
              suffix=""/>
            <div style="color: #666; font-size: 12px; font-weight: normal;">吨</div>
          </div>
        </div>
        <div class="flexBox">
          <div class="picTitle">
            <div>用电</div>
            <img class="img" src="../../pic//set6.png"/>
          </div>
          <div class="num">
            <countTo
              :startVal="0"
              :endVal="300"
              prefix=""
              suffix=""/>
            <div style="color: #666; font-size: 12px; font-weight: normal;">吨</div>
          </div>
        </div>
      </div>
    </template>
  </CrewCardDeb>
</template>
<script>
import CrewCardDeb from '../Crew/CrewCardDeb'
import countTo from 'vue-count-to'
// import * as earlyWarningApi from '@/api/material/earlyWarning'
// import { requestBuilder } from '@/utils/util'
export default {
  name: 'CrewEquipmentEnergy',
  components: {
    CrewCardDeb,
    countTo
  },
  data () {
    return {
      loading: false,
      sourceData: []
    }
  },
  // 页面初始化挂载dom
  mounted () {
    this.initApi()
  },
  methods: {
    initApi () {
      // this.loading = true
      // const param = requestBuilder('', { searchYear: new Date().getFullYear() }, 1, 1)
      // earlyWarningApi.earlyWarningHighPrice(param).then(res => {
      //   this.infoData.outOfProportion = res.result.data[0].outOfProportion
      //   this.infoData.proportion = res.result.data[0].proportion
      //   this.sourceData = res.result.data[0].priceCountList || []
      // }).then(() => {
      // }).finally(() => {
      //   this.loading = false
      // })
    }
  }
}
</script>
<style lang="less" scoped>
.box {
  height: 155px;
  display: flex;
  justify-content: space-between;
  .flexBox {
    width: 33.33%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .picTitle {
      width: 50%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
  }
  .bordf {
    border-right: 1px solid #666;
  }
  .num {
    width: 50%;
    font-size: 32px;
    font-weight: 700;
    padding-right: 25px;
    text-align: right;
  }
  .img {
    width: 45px;
    height: 45px;
    margin-top: 15px;
  }
}
</style>
