<template>
  <CrewCardPlus :title="`${subTitle}`">
    <template slot="title">
      <div class="head" style="cursor: pointer;" @click="showModal">{{ subTitle }}</div>
    </template>
    <template slot="right-icon">
      <div class="selectInp">
        <a-range-picker v-model="dates" :getCalendarContainer="triggerNode => triggerNode.parentNode" dropdownClassName="rangePickerIceGai" style="width: 200px; margin-right: 5px;" @change="onChange" />
        <a-select
          v-model="formData.status"
          :disabled="disabled"
          placeholder="审批状态"
          :getPopupContainer="triggerNode => triggerNode.parentNode"
          dropdownClassName="rangePickerIceGai"
          allowClear
          style="width: 120px; margin-right: 5px;"
          @change="onChange"
        >
          <a-select-option
            v-for="(item, index) in status"
            :value="item.value"
            :key="index"
          >{{ item.label }}</a-select-option>
        </a-select>
        <a-select
          v-model="formData.orgId"
          :disabled="disabled"
          :getPopupContainer="triggerNode => triggerNode.parentNode"
          dropdownClassName="rangePickerIceGai"
          placeholder="公司"
          style="width: 120px"
          @change="onChange"
        >
          <a-select-option :value="item.value" v-for="(item, index) in orgIds" :key="index">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </div>
    </template>
    <template slot="text">
      <div class="content">
        <s-table
          ref="table"
          :columns="columns"
          :data="loadData"
          :scroll="scroll"
          :customRow="rowClick"
          :pageSizeOptions="pageSizeOptions"
          :pageSize="defaultPageSize"
          :showPagination="true"
          rowKey="uuid"
        >
          <span slot="orgId" slot-scope="key">{{ takeSelectLabel(orgIds, key) }}</span>
          <span
            slot="status"
            slot-scope="key"
          >{{ takeSelectLabel(status, key) }}</span>
        </s-table>
      </div>
      <!-- 信息框 -->
      <!-- <info-drawer
        ref="infoDrawer"
        :readonly="readonly"
        :disabled="drawerDisabled"
        :drawerClosed="drawerClosed"
        :dataChanged="dataChanged"
        :getApproveId="getApproveId"
        :editBusinessMode="editBusinessMode"
        :editApproveMode="editApproveMode"
      /> -->
    </template>
  </CrewCardPlus>
</template>
<script>
import {
  // 填报状态
  APPROVE_STATUS_READY_CODE,
  APPROVE_STATUS_START_CODE,
  // 审批节点
  APPROVE_NODES_BUS_INIT,
  APPROVE_NODES_BUS_FREEZE,
  // 审批下拉框选项
  APPROVE_OPTIONS_HIDES,
  APPROVE_NODES_NOT_APPROVE
} from '@/store/variable-types'
import CrewCardPlus from '../Crew/CrewCardPlus'
import countTo from 'vue-count-to'
import { Timeline, STable } from '@/components'
import { requestBuilder, takeTreeByKey } from '@/utils/util'
import { ORG_ID, PERSON_ID } from '@/store/mutation-types'
import Vue from 'vue'
import * as cockpitApi from '@/api/material/cockpit'
import * as baseApi from '@/api/material/base'
import * as workorderApi from '@/api/device/workorder'
// import InfoDrawer from '../../../../device/check/modules/Workorder/InfoDrawer'
import moment from 'moment'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)
const IS_WZG_ENV = USER_ORG_ID.substr(0, 5) === '1.101'

const IS_MS_ENV = USER_ORG_ID === '1.100.104'
export default {
  name: 'CrewWorkorderBySearch',
  components: {
    CrewCardPlus,
    countTo,
    Timeline,
    STable
    // InfoDrawer
  },
  props: {
    styleName: {
      type: String,
      default: () => {
        return ''
      }
    },
    disabled: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data () {
    this.components = {
      header: {
        cell: (h, props, children) => {
          const { key, ...restProps } = props
          const col = this.columns.find(col => {
            const k = col.dataIndex || col.key
            return k === key
          })

          if (!col || !col.width) {
            return h('th', { ...restProps }, [...children])
          }

          const dragProps = {
            key: col.dataIndex || col.key,
            class: 'table-draggable-handle',
            attrs: {
              w: 10,
              x: col.width,
              z: 1,
              axis: 'x',
              draggable: true,
              resizable: false
            },
            on: {
              dragging: (x, y) => {
                col.width = Math.max(x, 1)
              }
            }
          }
          const drag = h('vue-draggable-resizable', { ...dragProps })
          return h('th', { ...restProps, class: 'resize-table-th' }, [...children, drag])
        }
      }
    }
    return {
      IS_MS_ENV,
      IS_WZG_ENV,
      USER_ORG_ID,
      loading: true,
      // 抽屉框
      readonly: true,
      drawerDisabled: true,
      timeIndex: 0,
      timelength: 0,
      status: [],
      dates: [],
      timeLineList: [
        {
          projectType: 322,
          filingDate: 12
        },
        {
          projectType: 322,
          filingDate: 12
        },
        {
          projectType: 322,
          filingDate: 12
        },
        {
          projectType: 322,
          filingDate: 12
        }
      ],
      formData: {
        orgId: USER_ORG_ID,
        dates: this.getDate()
      },
      orgIds: [],
      projList: [],
      formInfo: {
        companyAmount: 0,
        companySum: 0,
        groupAmount: 0,
        groupSum: 0,
        localAmount: 0,
        localSum: 0,
        oaAmount: 0,
        oaSum: 0
      },
      visible: false,
      confirmLoading: false,
      // 表格数据
      dataSource: [],
      columns: [
        {
          title: '设备编号',
          dataIndex: 'assetSbNum',
          ellipsis: true,
          width: 120
        },
        {
          title: '单位',
          dataIndex: 'orgId',
          scopedSlots: { customRender: 'orgId' },
          align: 'center',
          ellipsis: true,
          width: 100
        },
        {
          title: '工单描述',
          dataIndex: 'description',
          ellipsis: true,
          width: 120
        },
        {
          title: '审批状态',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' },
          ellipsis: true,
          width: 120
        }
      ],
      scroll: {
        x: '100%',
        // y: 'calc(60vh)',
        scrollToFirstRowOnChange: false
      },
      selectedRows: [],
      selectedRowKeys: [],
      defaultPageSize: 6,
      pageSizeOptions: ['6', '10', '20', '30', '40'],
      loadData: parameter => {
        const param = requestBuilder(
          '',
          {
            orgId: this.formData.orgId,
            status: this.formData.status,
            createDateFrom: this.dates.length > 0 ? this.dates[0].format('YYYY-MM-DD') : '',
            createDateTo: this.dates.length > 0 ? this.dates[1].format('YYYY-MM-DD') : ''
            // projectName: this.formData.projectName || '',
            // startTime: this.formData.dates.length > 0 ? this.formData.dates[0].format('YYYY-MM-DD') : '',
            // endTime: this.formData.dates.length > 0 ? this.formData.dates[1].format('YYYY-MM-DD') : ''
          },
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder
        )
        return workorderApi.queryWorkorder(param).then(res => {
          if (res.code === '0000') {
            this.dataSource = res.result.data || []
            return res.result
          } else {
            this.$message.error(res.message)
          }
        })
      },
      rowClick: record => ({
        on: {
          click: () => {
            if (this.disabled) {
              return
            }
            // 双击时消单击事件
            clearTimeout(this.clickTimer)
            // 编辑 / 预览
            if (this.editBusinessMode(record) || this.editApproveMode(record)) {
              this.doInfoDrawerEdit([record])
            } else {
              this.doInfoDrawerLook([record])
            }
          }
        }
      }),
      rowClassName: record => {
        return 'tablerow'
      }
    }
  },
  created () {
    this.init()
  },
  computed: {
    sumNum () {
      return this.formInfo.companySum + this.formInfo.groupSum + this.formInfo.localSum
    },
    subTitle () {
      return '工单查询'
    }
  },
  methods: {
    init () {
      baseApi.getCommboxById({ id: 'wzgOrg' }).then(res => {
        if (res.code === '0000') {
          this.orgIds = res.result
        }
      })
      // 审批状态
      baseApi.getTreeById({ id: 'appro' }).then(res => {
        if (res.code === '0000') {
          for (const item of res.result) {
            if (APPROVE_OPTIONS_HIDES.includes(item.value)) {
              item.disabled = true
            }
            this.status.push(item)
          }
        }
      })
    },
    getDate () {
      const dates = new Date()
      const month = dates.getMonth() + 1
      const month2 = month >= 12 ? 1 : month + 1
      const year = dates.getFullYear()
      const year2 = month >= 12 ? year + 1 : year
      const value = [
        moment(year + '-' + month + '-01', 'YYYY-MM-DD'),
        moment(year2 + '-' + month2 + '-01', 'YYYY-MM-DD')
      ]
      return value
    },
    doSearch () {
      this.$refs.table.refresh(true)
    },
    changeActive (index) {
      this.timeIndex = index
      this.formData = this.timeLineList[this.timeIndex]
    },
    // 获取下拉框选项文本
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || ''
    },
    notShow () {
      console.log('notShow')
    },
    showModal () {
      // if (this.disabled) {
      //   return
      // }
      // this.visible = true
      // if (this.$refs.table) {
      //   this.$refs.table.refresh()
      // }
    },
    handleCancel (e) {
      this.visible = false
    },
    // 编辑事件
    doInfoDrawerEdit (records) {
      this.readonly = true
      this.$refs.infoDrawer.doEdit(records)
    },
    // 预览事件
    doInfoDrawerLook (records) {
      this.readonly = true
      this.$refs.infoDrawer.doEdit(records)
    },
    onChange (value) {
      this.doSearch()
    },
    // 抽屉框关闭事件
    drawerClosed () {},
    dataChanged () {},
    // 获取审批大类ID
    getApproveId (status) {
      for (const item of this.status) {
        if (status === item.value) {
          return item.value
        }
        if (item.children) {
          for (const item2 of item.children) {
            if (status === item2.value) {
              return item.value
            }
          }
        }
      }
    },
    // 业务编辑模式
    editBusinessMode (record) {
      if (APPROVE_STATUS_READY_CODE === this.getApproveId(record.status)) {
        return true
      }
      if (
        !!record.userNext &&
        record.userNext.split(',').includes(USER_PERSON_ID) &&
        APPROVE_STATUS_START_CODE === this.getApproveId(record.status)
      ) {
        return true
      }
      if (IS_WZG_ENV && !APPROVE_NODES_NOT_APPROVE.includes(this.getApproveId(record.status))) {
        return true
      }
      return false
    },
    // 审批编辑模式
    editApproveMode (record) {
      if (
        !!record.userNext &&
        record.userNext.split(',').includes(USER_PERSON_ID) &&
        !APPROVE_NODES_BUS_FREEZE.includes(this.getApproveId(record.status)) &&
        !APPROVE_NODES_BUS_INIT.includes(this.getApproveId(record.status))
      ) {
        return true
      }
      return false
    }
  }
}
</script>
<style lang="less" scoped>
.content-bottom {
  height: 35px;
  .info-bottom {
    height: 35px;
    padding-left: 15px;
    display: flex;
    align-items: center;
    .desc {
      width: 23%;
      margin: 0 5px;
    }
  }
}
.content {
  width: 100%;
  height: 470px;
  padding: 15px;
  overflow-y: scroll;
  .top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
  }
  .topic {
    font-size: 16px;
    font-weight: 700;
  }
  .secTopic {
    width: auto;
    font-size: 10px;
    font-weight: normal;
    opacity: 0.7;
    margin-left: 10px;
  }
  .text {
    display: flex;
    font-size: 10px;
    .label {
      opacity: 0.7;
      margin-right: 5px;
    }
    .value {
      font-weight: 700;
    }
    .divid {
      margin: 0 5px;
      opacity: 0.7;
    }
  }
  .flexBox {
    display: flex;
    justify-content: space-between;
  }
  .bottom {
    margin-top: 15px;
    width: 100%;
    .bottom-title {
      font-size: 14;
      font-weight: 700;
      font-style: italic;
    }
    .bottom-text {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      .checkbox {
        margin-right: 2px;
        position: relative;
        .ant-tag-has-color {
          color: #232323;
        }
        .icongre {
          position: absolute;
          right: 2px;
          top: -3px;
          z-index: 20;
        }
      }
    }
  }
  .step {
    width: 100%;
    margin: 20px 0;
    border-top: 1px solid #d4d4d4;
  }
  .liubiao {
    color: #ffffff;
    background-color: #ef0f0f;
    border-radius: 2px;
    line-height: 22px;
    padding: 0 4px;
  }
  .head {
    cursor: pointer;
  }
  .selectInp {
    display: flex;
  }
}
</style>
