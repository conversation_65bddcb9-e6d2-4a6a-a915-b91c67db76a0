<template>
  <CrewCardDebPlus title="设备台账">
    <template slot="text">
      <div class="box">
        <div class="leftBox">
          <div class="flexBox">
            <div class="left">
              <img class="img" src="../../pic//set1.png"/>
            </div>
            <div class="right">
              <div>今日维修计划</div>
              <div class="num">
                <countTo
                  :startVal="0"
                  :endVal="1000"
                  prefix=""
                  suffix=""/>
              </div>
            </div>
          </div>
          <div class="flexBox">
            <div class="left">
              <img class="img" src="../../pic//set2.png"/>
            </div>
            <div class="right">
              <div>今日维修计划</div>
              <div class="num">
                <countTo
                  :startVal="0"
                  :endVal="1000"
                  prefix=""
                  suffix=""/>
              </div>
            </div>
          </div>
          <div class="flexBox">
            <div class="left">
              <img class="img" src="../../pic//set3.png"/>
            </div>
            <div class="right">
              <div>今日维修计划</div>
              <div class="num">
                <countTo
                  :startVal="0"
                  :endVal="1000"
                  prefix=""
                  suffix=""/>
              </div>
            </div>
          </div>
        </div>
        <div class="tableBox">
          <s-table
            ref="table"
            :columns="columns"
            :data="loadData"
            :scroll="scroll"
            :customRow="rowClick"
            :pageSizeOptions="pageSizeOptions"
            :pageSize="defaultPageSize"
            :showPagination="true"
            rowKey="uuid"
          />
        </div>
      </div>
    </template>
  </CrewCardDebPlus>
</template>
<script>
import CrewCardDebPlus from '../Crew/CrewCardDebPlus'
import countTo from 'vue-count-to'
import { STable } from '@/components'
// import * as earlyWarningApi from '@/api/material/earlyWarning'
// import { requestBuilder } from '@/utils/util'
export default {
  name: 'CrewEquipmentLedger',
  components: {
    CrewCardDebPlus,
    countTo,
    STable
  },
  data () {
    return {
      loading: false,
      sourceData: [],
      // 查询参数
      queryParam: {
      },
      // 表格数据
      dataSource: [],
      columns: [
        {
          title: '设备大类',
          dataIndex: 'jhyf',
          scopedSlots: { customRender: 'jhyf' },
          ellipsis: true
        },
        {
          title: '设备子类',
          dataIndex: 'finStartDate',
          ellipsis: true
        },
        {
          title: '设备编号',
          dataIndex: 'finEndDate',
          ellipsis: true
        },
        {
          title: '所属部门',
          align: 'center',
          dataIndex: 'isClosed',
          ellipsis: true
        },
        {
          title: '设备状态',
          dataIndex: 'createByName',
          ellipsis: true
        }
      ],
      scroll: {
        x: 'max-content',
        scrollToFirstRowOnChange: false
      },
      selectedRows: [],
      selectedRowKeys: [],
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20', '30', '40'],
      loadData: parameter => {
        // return closeAccountApi.queryInvStatistic(requestBuilder('', this.queryParam, parameter.pageNo, parameter.pageSize)).then(res => {
        //   if (res.code === '0000') {
        //     this.dataSource = res.result.data
        //     return res.result
        //   } else {
        //     this.$message.error(res.message)
        //   }
        // })
        return []
      },
      rowClick: record => ({
        on: {}
      })
    }
  },
  // 页面初始化挂载dom
  mounted () {
    this.initApi()
  },
  methods: {
    initApi () {
      // this.loading = true
      // const param = requestBuilder('', { searchYear: new Date().getFullYear() }, 1, 1)
      // earlyWarningApi.earlyWarningHighPrice(param).then(res => {
      //   this.infoData.outOfProportion = res.result.data[0].outOfProportion
      //   this.infoData.proportion = res.result.data[0].proportion
      //   this.sourceData = res.result.data[0].priceCountList || []
      // }).then(() => {
      // }).finally(() => {
      //   this.loading = false
      // })
    }
  }
}
</script>
<style lang="less" scoped>
.box {
  height: 350px;
  width: 100%;
  padding: 25px 15px;
  display: flex;
  justify-content: space-between;
  .leftBox {
    width: 220px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .flexBox {
    width: 100%;
    height: 30%;
    display: flex;
    justify-content: space-between;
    .left {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .right {
      width: 110px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-end;
      padding-right: 15px;
    }
  }
  .tableBox {
    flex: 1;
    margin-left: 15px;
  }
  .num {
    font-size: 24px;
    font-weight: 700;
  }
  .img {
    width: 40px;
    height: 40px;
  }
}
</style>
