<template>
  <div id="echarts_bazaar" ref="echarts" />
</template>
<script>
import * as echarts from 'echarts'

export default {
  props: {
    option: {
      type: Object,
      required: true
    }
  },
  data () {
    return {}
  },
  watch: {
    option (newval) {
      if (newval) {
        this.getEcharts()
      }
    }
  },
  mounted () {
    this.getEcharts()
  },
  methods: {
    getEcharts () {
      const myChart = echarts.init(this.$refs.echarts)
      myChart.setOption(this.option)
    }
  }
}
</script>
<style scoped lang="less">
#echarts_bazaar {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 5;
}
</style>
