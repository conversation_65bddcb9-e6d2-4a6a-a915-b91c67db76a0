<template>
  <CrewCardPlus title="异常结算">
    <template slot="text">
      <div class="content">
        <div class="topBox">
          <CrewToolTip v-for="(item,index) in list" :key="index" :record="item" />
        </div>
        <div class="bottom">
          <a-button class="btn" icon="question-circle" size="large" @click="showModal">全部异常</a-button>
        </div>
      </div>
      <div ref="initwarp" :class="{ initwarp: styleName === 'themeb' }">
        <a-modal
          title="查看详情"
          :visible="visible"
          :confirm-loading="confirmLoading"
          :getContainer="() => $refs.initwarp"
          width="1000px"
          :bodyStyle="{ height: '600px' }"
          @cancel="handleCancel"
        >
          <s-table
            ref="table"
            :components="components"
            :columns="columns"
            :data="loadData"
            :scroll="scroll"
            :rowClassName="rowClassName"
            :customRow="rowClick"
            :bordered="false"
            :pageSizeOptions="pageSizeOptions"
            :pageSize="defaultPageSize"
            :showPagination="true"
            no-striped
            rowKey="uuid"
          >
            <span
              slot="serial"
              slot-scope="text, record, index"
            >{{ index + 1 }}</span>
            <span
              slot="contractNum"
              slot-scope="text, record"
            >
              <a href="javascript:void(0)" @click="jump(record)">{{ text }} </a>
            </span>
          </s-table>
          <template slot="footer">
            <a-button class="btnExp" :loading="loadingExport" @click="doExport">导出</a-button>
          </template>
        </a-modal>
      </div>
    </template>
  </CrewCardPlus>
</template>
<script>
import CrewCardPlus from '../Crew/CrewCardPlus'
import CrewToolTip from '../Crew/CrewToolTip'
import * as earlyWarningApi from '@/api/material/earlyWarning'
import { requestBuilder } from '@/utils/util'
import { STable } from '@/components'
// import * as echarts from 'echarts'
import countTo from 'vue-count-to'
export default {
  name: 'CrewAbnormalSettlement',
  components: {
    CrewCardPlus,
    countTo,
    CrewToolTip,
    STable
  },
  props: {
    styleName: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data () {
    this.components = {
      header: {
        cell: (h, props, children) => {
          const { key, ...restProps } = props
          const col = this.columns.find(col => {
            const k = col.dataIndex || col.key
            return k === key
          })

          if (!col || !col.width) {
            return h('th', { ...restProps }, [...children])
          }

          const dragProps = {
            key: col.dataIndex || col.key,
            class: 'table-draggable-handle',
            attrs: {
              w: 10,
              x: col.width,
              z: 1,
              axis: 'x',
              draggable: true,
              resizable: false
            },
            on: {
              dragging: (x, y) => {
                col.width = Math.max(x, 1)
              }
            }
          }
          const drag = h('vue-draggable-resizable', { ...dragProps })
          return h('th', { ...restProps, class: 'resize-table-th' }, [...children, drag])
        }
      }
    }
    return {
      list: [],
      formData: {},
      visible: false,
      confirmLoading: false,
      loadingExport: false,
      // 表格数据
      dataSource: [],
      columns: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' },
          dataIndex: 'type',
          align: 'center',
          ellipsis: true,
          width: 50
        },
        {
          title: '合同编码',
          dataIndex: 'contractNum',
          scopedSlots: { customRender: 'contractNum' },
          align: 'center',
          ellipsis: true,
          width: 120
        },
        {
          title: '合同名称',
          dataIndex: 'contractName',
          align: 'center',
          ellipsis: true,
          width: 300
        },
        {
          title: '合同金额(元)',
          dataIndex: 'totalBaseCost',
          align: 'center',
          sorter: true,
          ellipsis: true,
          width: 150
        },
        {
          title: '已付金额(元)',
          dataIndex: 'amountPaid',
          align: 'center',
          sorter: true,
          ellipsis: true,
          width: 150
        },
        {
          title: '超额金额(元)',
          dataIndex: 'remainAmount',
          align: 'center',
          sorter: true,
          ellipsis: true,
          width: 150
        },
        {
          title: '超额(%)',
          dataIndex: 'outOfProportion',
          align: 'center',
          sorter: true,
          ellipsis: true,
          width: 100
        }
      ],
      scroll: {
        x: 'max-content',
        y: 'calc(50vh)',
        scrollToFirstRowOnChange: false
      },
      selectedRows: [],
      selectedRowKeys: [],
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20', '30', '40'],
      loadData: parameter => {
        return earlyWarningApi.earlyWarningAbnormalSett(requestBuilder('', this.formData, parameter.pageNo, parameter.pageSize, parameter.sortField, parameter.sortOrder)).then(res => {
          if (res.code === '0000') {
            this.dataSource = res.result.data
            return res.result
          } else {
            this.$message.error(res.message)
          }
        })
      },
      rowClick: record => ({
        on: {}
      }),
      rowClassName: record => {
        return 'tablerow'
      }
    }
  },
  created () {
    this.initApi()
  },
  methods: {
    initApi () {
      const param = requestBuilder('', this.formData, 1, 4)
      earlyWarningApi.earlyWarningAbnormalSett(param).then(res => {
        this.list = res.result.data || []
      })
    },
    showModal () {
      this.visible = true
      if (this.$refs.table) {
        this.$refs.table.refresh()
      }
    },
    handleCancel (e) {
      this.visible = false
    },
    jump (record) {
      const contractNum = record.contractNum
      this.visible = false
      this.$router.push({
        name: 'ContractManagement',
        params: {
          contractNum: contractNum
        }
      })
    },
    doExport () {
      this.loadingExport = true
      earlyWarningApi.exportEarlyWarningAbnormalSett(requestBuilder('', this.formData, null, null)).then(res => {
        if (res && res.data && res.headers['content-disposition']) {
          const link = document.createElement('a')
          const url = window.URL.createObjectURL(res.data)
          const contentDisposition = res.headers['content-disposition']
          let filename = contentDisposition.replace(/(.*;)?filename=([^;]+).*/i, '$2')
          try {
            filename = decodeURIComponent(escape(filename))
          } catch (e) {}
          document.body.appendChild(link)
          link.style.display = 'none'
          link.download = filename
          link.href = url
          link.click()
          link.remove()
          window.URL.revokeObjectURL(url)
        } else {
          this.$notification.error({
            message: '系统消息',
            description: '下载失败！'
          })
        }
      }).finally(() => {
        this.loadingExport = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
  .content {
    width: 100%;
    height: 500px;
    padding: 15px;
    .topBox {
      height: 435px;
    }
    .bottom {
      height: 50px;
      display: flex;
      justify-content: right;
      align-items: center;
      .btn {
        width: 150px;
        height: 35px;
      }
    }
  }
  .btnExp {
  width: 80px;
}
</style>
