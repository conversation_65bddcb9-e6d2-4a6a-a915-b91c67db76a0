<template>
  <section>
    <a-modal
      title="信息栏"
      :visible="visible"
      :confirm-loading="confirmLoading"
      width="800px"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol" @submit="handleOk" @submit.native.prevent>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="申报单号">
              <a-input v-model="form.prlineNum" disabled />
            </a-form-model-item></a-col>
          <a-col :span="8">
            <a-form-model-item label="物资代码">
              <a-input v-model="form.materialNum" disabled />
            </a-form-model-item></a-col>
          <a-col :span="8">
            <a-form-model-item label="物资名称">
              <a-input v-model="form.materialName" disabled />
            </a-form-model-item></a-col>
        </a-row>
        <a-row>
          <!-- <a-col :span="8">
            <a-form-model-item label="品牌">
              <a-input v-model="form.brand" disabled/>
            </a-form-model-item></a-col> -->
          <a-col :span="8">
            <a-form-model-item label="规格型号">
              <a-input v-model="form.jModel" disabled/>
            </a-form-model-item></a-col>
          <a-col :span="8">
            <a-form-model-item label="申请数">
              <a-input v-model="form.orderQty" disabled/>
            </a-form-model-item></a-col>
          <a-col :span="8">
            <a-form-model-item label="审核数">
              <a-input v-model="form.jwspOrderQty" />
            </a-form-model-item></a-col>
        </a-row>
        <a-row>
          <!-- <a-col :span="8">
            <a-form-model-item label="待采购数">
              <a-input v-model="form.jwspOrderQty2" disabled/>
            </a-form-model-item></a-col> -->
          <!-- <a-col :span="8">
            <a-form-model-item label="库存数量">
              <a-input v-model="form.totalCurbal" disabled/>
            </a-form-model-item></a-col> -->
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="预估单价">
              <a-input v-model="form.unitCost" suffix="元"/>
            </a-form-model-item></a-col>
          <a-col :span="8">
            <a-form-model-item label="预估总价">
              <a-input v-model="form.estimatePrice" suffix="元"/>
            </a-form-model-item></a-col>
          <a-col :span="8">
            <a-form-model-item label="使用方向">
              <edit-cell-tree-select
                :value="form.usedFor"
                :options="directions"
                :showSearch="true"
                :status.sync="cellState"
                :expandKeys="null"
                @change="cellChange(form, 'usedFor', $event.selected)"
                @confirm="cellConfirm(form, 'usedFor')"
              />
            </a-form-model-item></a-col>
        </a-row>
        <a-row>
          <a-form-model-item label="备注">
            <a-input v-model="form.remarks" />
          </a-form-model-item>
        </a-row>
        <a-row>
          <!-- <a-col :span="8">
            <a-form-model-item label="采购员">
              <a-select style="width: 100%" v-model="form.agent">
                <a-select-option v-for="(item, index) in agents" :key="index" :value="item.value">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-model-item></a-col> -->
          <!-- <a-col :span="8">
            <a-form-model-item label="申请人">
              <a-select style="width: 100%" v-model="form.requestedBy" disabled>
                <a-select-option v-for="(item, index) in requestedBys" :key="index" :value="item.value">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-model-item></a-col> -->
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="审批状态">
              <span>{{ takeSelectLabel(status, form.status) }}</span>
            </a-form-model-item></a-col>
          <a-col :span="8">
            <a-form-model-item label="计量单位">
              <a-select style="width: 100%" v-model="form.orderUnit" disabled>
                <a-select-option v-for="(item, index) in units" :key="index" :value="item.value">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-model-item></a-col>
          <!-- <a-col :span="8">
            <a-form-model-item label="审批人模式">
              <span>{{ form.autoTag === '1' ? '自动选择' : (form.autoTag ? '手动选择 (模式' + form.autoTag + ')' : '手动选择') }}</span>
            </a-form-model-item></a-col> -->
          <a-col :span="8">
            <a-form-model-item label="创建时间">
              <span>{{ form.createDate }}</span>
            </a-form-model-item></a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="计划描述">
              <a-input v-model="form.description" />
            </a-form-model-item></a-col>
          <a-col :span="12">
            <a-form-model-item label="计划类型">
              <a-select style="width: 100%" v-model="form.lineType" disabled>
                <a-select-option v-for="(item, index) in procurementPlan" :key="index" :value="item.value">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-model-item></a-col>
        </a-row>
        <!-- <a-row>
          <a-col :span="8">
            <a-form-model-item label="申请人">
              <a-input v-model="form.hjRequestedBy" disabled/>
              <a-select style="width: 100%" v-model="form.requestedBy" disabled>
                <a-select-option v-for="(item, index) in requestedBys" :key="index" :value="item.value">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-model-item></a-col>
          <a-col :span="8" v-show="form.departmentName">
            <a-form-model-item label="部门">
              <a-input v-model="form.departmentName" disabled/>
            </a-form-model-item></a-col>
        </a-row> -->
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="项目单体">
              <a-input v-model="form.htdt" disabled/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="合同名称">
              <a-input v-model="form.contName" disabled/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="联系人">
              <a-input v-model="person" disabled/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="联系电话">
              <a-input v-model="form.hjTgRequestPhone" disabled/>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
  </section>
</template>

<script>
import { EditCellTreeSelect } from '@/components'
import { takeTreeByKey, requestBuilder } from '@/utils/util'
import * as prlineApi from '@/api/material/prline'
export default {
  name: 'FormPrline',
  components: {
    EditCellTreeSelect
  },
  props: {
    formInfo: {
      type: Object,
      required: true,
      default: () => {
        return {}
      }
    },
    // 申请人
    requestedBys: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 使用方向
    directions: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 计量单位
    units: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 采购员
    agents: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 审批状态
    status: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 计划类型
    procurementPlan: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      ModalText: 'Content of the modal',
      visible: false,
      confirmLoading: false,
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      form: {
        usedFor: '',
        usedForName: '',
        hjRequestedBy: ''
      },
      person: '',
      cellState: false
    }
  },
  methods: {
    showModal () {
      this.visible = true
      this.form = this.formInfo
      this.person = this.form.lineType === '28' ? this.form.requestedByName : this.form.hjRequestedBy
    },
    handleOk (e) {
      this.confirmLoading = true
      this.cellConfirm(this.form)
      this.visible = false
    },
    handleCancel (e) {
      this.visible = false
      this.$emit('clearModel')
      this.form = {
        usedFor: '',
        usedForName: '',
        hjRequestedBy: ''
      }
      this.person = ''
    },
    // 获取下拉框选项文本
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || ''
    },
    cellConfirm (record, key) {
      this.confirmLoading = true
      prlineApi.modifyProcurePlan(requestBuilder('update', [record])).then(res => {
        if (res.code !== '0000') {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '采购计划修改失败！'
          })
        } else {
          this.$notification.success({
            message: '系统消息',
            description: '采购计划修改成功！'
          })
        }
      }).finally(() => {
        this.confirmLoading = false
      })
    },
    cellChange (record, key, value) {
      this.form.usedFor = value
      this.usedForName = this.takeSelectLabel(this.directions, value)
    }
  }
}
</script>

<style lang="less" scoped>

</style>
