<template>
  <div class="CrewCardDeb">
    <div class="crewCard-head">
      <div class="crewCard-title">
        <slot name="title">
          <div>{{ title }}</div>
        </slot>
      </div>
      <div class="crewCard-icon">
        <slot name="right-icon"/>
      </div>
    </div>
    <div class="crewCard-text">
      <slot name="text"/>
    </div>
  </div>
</template>
<script>
import '@/style/baseColor.less'
export default {
  name: 'CrewCardDeb',
  props: {
    title: {
      type: String,
      default: () => {
        return 'title'
      }
    }
  }
}
</script>

<style lang="less" scoped>

</style>
