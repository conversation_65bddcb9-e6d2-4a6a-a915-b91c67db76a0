<template>
  <div class="CrewEchart">
    <div class="text">
      <div class="title">{{ record.bidVendorsUp }}</div>
      <div class="descript">
        <div class="warp" style="opacity:0.6;">企业组合</div>
        <div class="overTwo" style="font-weight: 700;">{{ record.bidVendorsDown }}</div>
      </div>
      <div class="descript">
        <div class="warp" style="opacity:0.6;">所属物资</div>
        <div class="warp" style="font-weight: 700;">{{ record.materialName }}</div>
      </div>
    </div>
    <div class="content">
      <div style="width: 100%; height: 100%;">
        <div :id="id" :ref="id" style="width: 100%; height: 135px;" />
      </div>
    </div>
  </div>
</template>
<script>
// import * as echarts from 'echarts'
export default {
  name: 'CrewEchart',
  props: {
    id: {
      type: String,
      default: () => {
        return 'echartsId'
      }
    },
    record: {
      type: Object,
      default: () => {
        return {}
      }
    },
    loadData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      options: {
        color: ['#AD4440', 'rgba(240, 248, 255, 0.055)'],
        tooltip: {
          trigger: 'item'
        },
        // legend: {
        //   bottom: '5%',
        //   left: 'center',
        //   icon: 'circle',
        //   textStyle: {
        //     fontSize: 8,
        //     color: 'rgb(144, 144, 144)'
        //   }
        // },
        title: {
          text: '中标占比',
          bottom: '10%',
          left: 'center',
          textStyle: {
            color: '#666',
            fontSize: 10
          }
        },
        series: [
          {
            type: 'pie',
            radius: ['30%', '55%'],
            center: ['50%', '45%'],
            avoidLabelOverlap: false,
            itemStyle: {
            },
            label: {
              show: true,
              position: 'center',
              color: '#4c4a4a',
              formatter: '{total|' + this.curTotal() + '%}',
              rich: {
                total: {
                  fontSize: 12,
                  fontFamily: '微软雅黑',
                  color: 'red'
                }
              }
            },
            lableLine: {
              normal: {
                show: false
              },
              emphasis: {
                show: true
              },
              tooltip: {
                show: false
              }
            },
            data: this.loadData
          }
        ]
      },
      loading: false
    }
  },
  // 页面初始化挂载dom
  mounted () {
    this.init()
  },
  methods: {
    curTotal () {
      if (this.loadData.length > 0) {
        const sum = this.loadData.reduce((pre, cur) => {
          return cur.value + pre
        }, 0)
        const obj = this.loadData.find(item => item.name === '中标供应商')
        const value = (obj.value / sum * 100).toFixed(1)
        return value
      }
      return 0
    },
    init () {
      this.$nextTick(() => {
        setTimeout(() => {
          this.getLoadEcharts()
        }, 100)
      })
    },
    getLoadEcharts () {
      this.loading = true
      this.dom = this.$echarts.init(
        document.getElementById(this.id)
      )
      this.dom.setOption(this.options)
      window.addEventListener('resize', () => {
        // 第六步，执行echarts自带的resize方法，即可做到让echarts图表自适应
        this.dom.resize()
        // 如果有多个echarts，就在这里执行多个echarts实例的resize方法,不过一般要做组件化开发，即一个.vue文件只会放置一个echarts实例
        /*
        this.myChart2.resize();
        this.myChart3.resize();
        ......
        */
      })
    }
  }
}
</script>
<style lang="less" scoped>
  .text {
    padding: 8px;
    .title {
      width: 150px;
      font-weight: 700;
      margin-bottom: 8px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .descript {
      font-size: 12px;
      margin: 5px 0;
      .warp {
        width: 150px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .overTwo {
        width: 150px;
        word-break: break-all;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; /* 这里是超出几行省略 */
        overflow: hidden;
      }
    }
  }
  .content {
    width: 150px;
    height: 100%;
    font-size: 12px;
  }
  .CrewEchart {
    display: flex;
    justify-content: space-between;
    width: 49%;
    height: 135px;
  }
</style>
