<template>
  <div class="CrewToolTip">
    <div class="text">
      <div class="title">{{ record.contractName }}</div>
      <div class="contentText">
        <div class="descript">
          <div class="warp" style="opacity:0.6;">合同编号</div>
          <div class="warp" style="font-weight: 700;">{{ record.contractNum }}</div>
        </div>
        <div class="descript">
          <div class="warp" style="opacity:0.6;">付款金额</div>
          <div class="warp" style="font-weight: 700;">{{ record.amountPaid }}</div>
        </div>
        <div class="descript">
          <div class="warp" style="opacity:0.6;">合同金额</div>
          <div class="warp" style="font-weight: 700;">{{ record.totalBaseCost }}</div>
        </div>
        <div class="descript">
          <div class="warp" style="opacity:0.6;">超额占比</div>
          <div class="warp" style="font-weight: 700;">{{ record.outOfProportion }}%</div>
        </div>
      </div>
    </div>
    <div class="bg">
      <div class="bgtext">合同超额支付</div>
      <span class="num">
        <countTo
          :startVal="0"
          :endVal="record.remainAmount"
          prefix="￥"
          :suffix="`(${record.outOfProportion}%)`"/>
      </span>
    </div>
  </div>
</template>
<script>
import countTo from 'vue-count-to'
export default {
  name: 'CrewToolTip',
  components: {
    countTo
  },
  props: {
    record: {
      type: Object,
      default: () => {
        return {
          contractName: '',
          amountPaid: '',
          contractNum: '',
          outOfProportion: '',
          totalBaseCost: 0,
          remainAmount: 0
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
.text {
  flex: 1;
  padding:  15px 8px;
  .contentText {
    display: flex;
  }
  .title {
    max-width: 350px;
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 5px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .descript {
    font-size: 12px;
    margin: 5px 10px 5px 0;
    max-width: 120px;
    .warp {
      max-width: 120px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
.bg {
  display: flex;
  /* align-items: center; */
  align-items: flex-end;
  flex-direction: column;
  justify-content: center;
  width: 250px;
  height: 100px;
  background: #655;
  background:  linear-gradient(110deg, transparent 80px, rgba(0, 47, 108, 0.372) 0);
  text-align: center;
  padding-right: 15px;
  .bgtext {
    width: 120px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    background: linear-gradient(#EC573B, #5a222760);
    border-radius: 5px;
  }
  .num {
    color: rgb(119, 119, 119);
    font-size: 18px;
    font-weight: 700;
  }
}
.CrewToolTip {
  width: 100%;
  height: 100px;
  margin-bottom: 10px;
  display: flex;
}
</style>
