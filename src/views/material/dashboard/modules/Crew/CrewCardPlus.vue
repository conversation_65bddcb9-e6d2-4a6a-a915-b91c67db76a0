<template>
  <div class="CrewCardPlus">
    <div class="crewCard-head">
      <div class="crewCard-title">
        <slot name="title">
          <div>{{ title }}</div>
        </slot>
      </div>
      <div class="crewCard-icon">
        <slot name="right-icon"/>
      </div>
    </div>
    <div class="crewCard-text">
      <slot name="text"/>
    </div>
  </div>
</template>
<script>
import '@/style/baseColor.less'
export default {
  name: 'CrewCardPlus',
  props: {
    title: {
      type: String,
      default: () => {
        return 'title'
      }
    }
  }
}
</script>

<style lang="less" scoped>
  .CrewCardPlus {
    width: 100%;
    height: 45px;
    .crewCard-head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 45px;
      padding: 3px 15px;
      border: 1px solid black;
      border-image: linear-gradient(to top left, rgba(255, 255, 255, 0) 0%, #0060c1 20%, rgba(255, 255, 255, 0) 99%) 21;
      border-width: 1px 0;
      .crewCard-title {
        display: flex;
        align-items: center;
        height: 45px;
        font-size: 18px;
        font-weight: 700;
      }
      .crewCard-icon {
        font-size: 18px;
        font-weight: 400;
      }
    }
    .crewCard-text {
      height: 100%;
    }
    .content-bottom {
      border: 1px solid black;
      border-image: linear-gradient(to top left, rgba(255, 255, 255, 0) 0%, #0060c1 20%, rgba(255, 255, 255, 0) 99%) 21;
      border-width: 1px 0;
    }
  }
</style>
