<template>
  <div class="CrewToolTip">
    <div class="text">
      <div class="title">
        <div class="head">• {{ record.budgetName || '名称' }} <a-icon v-show="record.isOverBudget" style="color: red;" type="exclamation-circle" theme="filled" /></div>
        <a v-show="!disabled" v-action:YSJEEdit href="javascript:void(0)" @click="$emit('click')">修改</a>
      </div>
      <div class="contentText">
        <div class="descript">
          <div class="warp" style="opacity: 0.7;">已用金额</div>
          <div class="warp" style="color: #F8AA4B; font-weight: 700; font-size: 22px;"><span style="color: #F8AA4B; font-size: 10px;">￥ </span>{{ record.amountUsed || 0 }}</div>
        </div>
        <div class="descript">
          <div class="warp" style="opacity:0.7;">预算金额</div>
          <div class="warp" style="color: #F8AA4B; font-weight: 700; font-size: 22px;"><span style="color: #F8AA4B; font-size: 10px;">￥ </span>{{ record.budgetAmount || 0 }}</div>
        </div>
        <div class="descript">
          <div class="warp" style="opacity:0.7;">占比</div>
          <div class="warp" style="color: #F8AA4B; font-weight: 700; font-size: 22px;">{{ record.usedRate || 0 }}</div>
        </div>
      </div>
    </div>
    <div style="width: 30%; height: 100%;">
      <div :id="dragging ? `CrewBudgetInfoPic${index}${show ? index : ''}` : `CrewBudgetInfoPicEdit${index}${show ? index : ''}`" style="width: 100%; height: 180px;" />
    </div>
    <!-- <div class="bg">
      <div class="bgtext">超额支付</div>
      <span class="num">
        <countTo
          :startVal="0"
          :endVal="record.remainAmount"
          prefix="￥"
          :suffix="`(${record.outOfProportion}%)`"/>
      </span>
    </div> -->
  </div>
</template>
<script>
// import ThroughOutStore from '@/views/material/objectManagement/components/ThroughOutStore.vue'
import countTo from 'vue-count-to'
export default {
  name: 'CrewToolTip',
  components: {
    countTo
  },
  props: {
    dragging: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    show: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    index: {
      type: Number,
      default: () => {
        return 0
      }
    },
    record: {
      type: Object,
      default: () => {
        return {
          contractName: '',
          amountPaid: '',
          contractNum: '',
          outOfProportion: '',
          totalBaseCost: 0,
          remainAmount: 0,
          pies: [],
          isOverBudget: false
        }
      }
    },
    disabled: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  mounted () {
    this.getLoadEcharts()
  },
  methods: {
    getLoadEcharts () {
      const data = this.record.pies.map(item => {
        if (this.record.isOverBudget) {
          if (item.name === '超额') {
            item.itemStyle = { color: '#FF6A6A' }
          }
          if (item.name === '预算') {
            item.itemStyle = { color: '#6AD8FF' }
          }
        } else {
          if (item.name === '已用') {
            item.itemStyle = { color: '#FFC950' }
          }
          if (item.name === '剩余') {
            item.itemStyle = { color: '#6AD8FF' }
          }
        }
        return item
      })
      this.dom = this.$echarts.init(
        document.getElementById(this.dragging ? `CrewBudgetInfoPic${this.index}${this.show ? this.index : ''}` : `CrewBudgetInfoPicEdit${this.index}${this.show ? this.index : ''}`)
      )
      this.dom.clear()
      this.dom.setOption({
        // title: {
        //   text: '库存账龄统计',
        //   left: 'center',
        //   top: 20,
        //   textStyle: {
        //     color: 'rgba(64,158,255)'
        //   }
        // },
        tooltip: {
          trigger: 'item',
          formatter: param => {
            return param.name + '：' + param.value + `(${param.percent}%)`
          }
        },
        // legend: {
        //   orient: 'vertical',
        //   left: 'left',
        //   textStyle: {
        //     color: 'rgba(64,158,255)'
        //   }
        // },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: '50%',
            data: data || [],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              color: 'rgba(64,158,255, 1)'
            },
            labelLine: {
              lineStyle: {
                color: 'rgba(64,158,255, 0.5)'
              },
              smooth: 0.5,
              length: 5,
              length2: 3
            }
          }
        ]
      })
      window.addEventListener('resize', () => {
        this.dom.resize()
      })
      // this.dom.on('click', params => {
      //   const overStockYear = this.overStockYears.find(item => item.label === params.name).value
      //   if (overStockYear) {
      //     this.$router.push({
      //       name: 'OverstockQuery',
      //       params: {
      //         overStockYear: overStockYear
      //       }
      //     })
      //   }
      // })
    }
  }
}
</script>
<style lang="less" scoped>
.text {
  // flex: 1;
  padding:  15px 8px;
  width: 70%;
  .contentText {
    display: flex;
    flex-wrap: wrap;
  }
  .title {
    width: 100%;
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 5px;
    // overflow: hidden;
    // white-space: nowrap;
    // text-overflow: ellipsis;
    display: flex;
    justify-content: space-between;
    a {
      font-size: 12px;
      font-weight: normal;
    }
    .head {
      color: rgba(64,158,255, 1);
      letter-spacing: 3px;
    }
  }
  .descript {
    font-size: 12px;
    margin: 5px 10px 5px 0;
    // max-width: 120px;
    // min-width: 100px;
    width: 45%;
    .warp {
      max-width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
.bg {
  display: flex;
  /* align-items: center; */
  align-items: flex-end;
  flex-direction: column;
  justify-content: center;
  width: 250px;
  height: 100px;
  background: #655;
  background:  linear-gradient(110deg, transparent 80px, rgba(0, 47, 108, 0.372) 0);
  text-align: center;
  padding-right: 15px;
  .bgtext {
    width: 120px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    background: linear-gradient(#EC573B, #5a222760);
    border-radius: 5px;
  }
  .num {
    color: rgb(119, 119, 119);
    font-size: 18px;
    font-weight: 700;
  }
}
.CrewToolTip {
  width: 100%;
  height: 180px;
  margin-bottom: 10px;
  display: flex;
}
</style>
