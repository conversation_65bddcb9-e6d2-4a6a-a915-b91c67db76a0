<template>
  <section>
    <div ref="initwarp" :class="{ initwarp: styleName === 'themeb' }">
      <a-modal
        title="查看详情"
        :visible="visible"
        :confirm-loading="confirmLoading"
        :getContainer="() => $refs.initwarp"
        width="1000px"
        :bodyStyle="{ height: '600px' }"
        :footer="null"
        @cancel="handleCancel"
      >
        <s-table
          ref="table"
          :columns="columns"
          :data="loadData"
          :scroll="scroll"
          :rowClassName="rowClassName"
          :customRow="rowClick"
          :bordered="false"
          :pageSizeOptions="pageSizeOptions"
          :pageSize="defaultPageSize"
          :showPagination="true"
          no-striped
          rowKey="uuid"
        >
          <span
            slot="serial"
            slot-scope="text, record, index"
          >{{ index + 1 }}</span>
        </s-table>
      </a-modal>
    </div>
  </section>
</template>
<script>

import { requestBuilder } from '@/utils/util'
import * as cockpitApi from '@/api/material/cockpit'
import { STable } from '@/components'
export default {
  components: {
    STable
  },
  props: {
    styleName: {
      type: String,
      default: () => {
        return ''
      }
    },
    disabled: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data () {
    return {
      visible: false,
      confirmLoading: false,
      rfqlineNum: '',
      // 表格数据
      dataSource: [],
      columns: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' },
          dataIndex: 'type',
          align: 'center',
          ellipsis: true,
          width: 50
        },
        {
          title: '物资编码',
          dataIndex: 'materialNum',
          scopedSlots: { customRender: 'materialNum' },
          align: 'center',
          ellipsis: true,
          width: 200
        },
        {
          title: '物资名称',
          dataIndex: 'materialName',
          align: 'center',
          ellipsis: true,
          width: 200
        },
        {
          title: '规格型号',
          dataIndex: 'jModel',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 200
        },
        {
          title: '品牌',
          dataIndex: 'brand',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 160
        },
        {
          title: '供应商',
          dataIndex: 'vendorName',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 240
        },
        {
          title: '税后单价',
          dataIndex: 'unitAfterTax',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 150
        },
        {
          title: '税后总价',
          dataIndex: 'totalAfterTax',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 150
        },
        {
          title: '货期',
          dataIndex: 'goodsTime',
          align: 'center',
          ellipsis: true,
          width: 150
        }
      ],
      scroll: {
        x: '100%',
        y: 'calc(60vh)',
        scrollToFirstRowOnChange: false
      },
      selectedRows: [],
      selectedRowKeys: [],
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20', '30', '40'],
      loadData: parameter => {
        const param = requestBuilder(
          '',
          {
            rfqlineNum: this.rfqlineNum || ''
          },
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder
        )
        return cockpitApi.queryMaterialGoodsTime(param).then(res => {
          if (res.code === '0000') {
            this.dataSource = res.result.data
            return res.result
          } else {
            this.$message.error(res.message)
          }
        })
      },
      rowClick: record => ({
        on: {}
      }),
      rowClassName: record => {
        return 'tablerow'
      }
    }
  },
  methods: {
    showModal (rfq) {
      this.visible = true
      this.confirmLoading = false
      this.rfqlineNum = rfq
      this.$nextTick(() => {
        this.$refs.table.refresh(true)
      })
    },
    handleCancel () {
      this.visible = false
      this.confirmLoading = false
      this.rfqlineNum = ''
      this.$nextTick(() => {
        this.$refs.table.clear()
      })
    }
  }
}
</script>
<style lang="less">

</style>
