<template>
  <section>
    <section v-if="IS_HJ_ENV && CockpitVisible">
      <a-row :gutter="24">
        <TodoEchartHJBar/>
        <TodoEchartHJMaterialType/>
        <TodoEchartHJProject/>
      </a-row>
    </section>
  </section>
</template>

<script>
import { PERSON_ID, ORG_ID } from '@/store/mutation-types'
import TodoEchartHJBar from './TodoEchartHJBar'
import TodoEchartHJMaterialType from './TodoEchartHJMaterialType'
import TodoEchartHJProject from './TodoEchartHJProject'
import { mapGetters } from 'vuex'
import Vue from 'vue'

export default {
  components: {
    TodoEchartHJBar,
    TodoEchartHJMaterialType,
    TodoEchartHJProject
  },
  name: 'TodoEchartGroup',
  data () {
    return {
      // 操作人 userPersonId
      USER_PERSON_ID: Vue.ls.get(PERSON_ID),
      IS_HJ_ENV: Vue.ls.get(ORG_ID) === '1.100.131',
      CockpitVisible: false
    }
  },
  created () {
    this.JudgingCockpit()
  },
  computed: {
    ...mapGetters(['todoCardGroup'])
  },
  methods: {
    JudgingCockpit () {
      this.CockpitVisible = this.todoCardGroup.some(item => item.name === 'TodoCardEchartGroup')
    }
  }
}
</script>

<style lang="less" scoped>

</style>
