<template>
  <section>
    <div class="list-container">
      <div class="list-header">
        <div class="title">{{ !IS_JYS_ENV ? '采购申报单号' : '计划单号' }} </div>
        <span
          v-if="isAllSelected() === false"
          class="button"
          @click="doAllSelected"
        >全选</span>
        <span
          v-if="isAllSelected() === true"
          class="button"
          @click="doAllNotSelect"
        >全不选</span>
      </div>
      <a-spin :spinning="loading.query">
        <div class="list-content">
          <a-row type="flex" justify="space-around">
            <a-col :span="11">
              <a-select
                placeholder="选择计划类型"
                v-model="queryPrlineMode"
                style="width: 100%"
                @change="doChangeMode()"
                allowClear>
                <a-select-option
                  v-for="(item, index) in lineTypeAll"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="11">
              <a-input
                v-model="prlineNum"
                placeholder="申报单号"
                @pressEnter="doChangeMode()"
              />
            </a-col>
          </a-row>
          <div
            v-for="item of options"
            :key="item.value"
            class="list-item"
            @click="doItemReverse(item.value)"
          >
            <div class="left">
              <span
                class="ant-checkbox"
                :class="{ 'ant-checkbox-checked': isItemSelected(item.value) }"
              >
                <span class="ant-checkbox-inner" />
              </span>
            </div>
            <div class="right">
              <div class="num">{{ item.label }}</div>
              <div class="num" v-if="IS_NJMZ_ENV">{{ item.createBy }}</div>
              <div class="num" v-if="IS_WZG">{{ item.departmentName }}</div>
              <div class="description" v-if="!IS_JYS_ENV">预估总金额: {{ item.actualEstimatePriceSum || 0 }}/{{ item.estimatePriceSum || 0 }}元</div>
              <div class="description" v-else>预估总金额: {{ item.estimatePriceSum || 0 }}元</div>
              <div
                v-if="item.description"
                class="description"
              >{{ item.description }}</div>
            </div>
          </div>
        </div>
      </a-spin>
    </div>
    <div class="table-container">
      <AddPrlineDrawer ref="AddPrlineDrawer" @search="doQueryAll()"/>
      <EditPrlineDrawer ref="EditPrlineDrawer" @search="doQueryAll()"/>
      <a-drawer
        title="筛选"
        placement="right"
        :closable="true"
        :visible="insideTableVisible"
        :mask="false"
        :get-container="false"
        :wrap-style="{ position: 'absolute' }"
        @close="insideTableClose"
      >
        <a-form-model :model="searchForm">
          <a-form-model-item label="物资名称">
            <a-input v-model="searchForm.materialName" />
          </a-form-model-item>
          <a-form-model-item label="申请人">
            <a-select v-model="searchForm.requestedBy" allowClear optionFilterProp="label" showSearch>
              <a-select-option v-for="(item, index) in requestedBys" :value="item.value" :key="index" :label="item.label">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="采购人">
            <a-select v-model="searchForm.agent" allowClear optionFilterProp="label" showSearch>
              <a-select-option v-for="(item, index) in agents" :value="item.value" :key="index" :label="item.label">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-form-model>
        <div
          :style="{
            position: 'absolute',
            bottom: 0,
            width: '100%',
            borderTop: '1px solid #e8e8e8',
            padding: '10px 16px',
            textAlign: 'right',
            left: 0,
            background: '#fff',
            borderRadius: '0 0 4px 4px',
          }"
        >
          <a-button type="primary" @click="doQuerySource">
            查询
          </a-button>
          <a-button type="info" @click="reSetDrawer">
            重置
          </a-button>
        </div>
      </a-drawer>
      <s-table
        ref="table"
        :data="loadData"
        :scroll="scroll"
        :columns="computedColumns"
        :customRow="rowClick"
        :rowClassName="rowClassName"
        :rowSelection="rowSelection"
        :getPopupContainer="getPopupContainer"
        :showPagination="showPagination"
        :pageSizeOptions="pageSizeOptions"
        :pageSize="defaultPageSize"
        :immediate="immediate"
        :bordered="bordered"
        rowKey="prlineSysId"
        no-striped
      >
        <template slot="title">
          <div class="tableTitle">
            <a-popover v-model="popVisible" trigger="click">
              <template slot="content">
                <DropdownChecked :showTitle="false" title="自定义表格排序" tableId="TodoApprovePrlineTable" v-model="columns" />
              </template>
              <span class="title-icons" >
                <a-icon type="setting" theme="filled" />
              </span>
            </a-popover>
            <span class="title-icons" v-if="IS_DMY_ENV">
              <a-tooltip>
                <template slot="title">
                  新增
                </template>
                <a-icon type="plus" @click="addPrline"/>
              </a-tooltip>
            </span>
            <span class="title-icons" v-if="IS_SH_ENV">
              <a-tooltip>
                <template slot="title">
                  修改
                </template>
                <a-icon type="edit" @click="editPrline"/>
              </a-tooltip>
            </span>
            <span class="title-icons">
              <a-tooltip>
                <template slot="title">
                  查询
                </template>
                <a-icon type="search" @click="insideTableOpen"/>
              </a-tooltip>
            </span>
            <span class="title-icons" style="border: none;">
              <a-tooltip>
                <template slot="title">
                  刷新
                </template>
                <a-icon type="sync" :spin="popLoading" @click="reColumns"/>
              </a-tooltip>
            </span>
          </div>
        </template>
        <span
          slot="lineType"
          slot-scope="value, record"
        >
          <edit-cell-tree-select
            :value="value"
            :options="lineTypeAll"
            :cellStyle="cellStyle"
            :status.sync="cellState"
            :disabled="!IS_MD_ENV || record.lineType ==='10'|| record.lineType ==='11'"
            @change="cellChange(record, 'lineType', $event.selected)"
            @confirm="cellConfirm(record, 'lineType')"
          />
        </span>
        <span
          slot="usedCo"
          slot-scope="value, record"
        >
          <edit-cell-select
            :value="value"
            :options="usedCo"
            :cellStyle="cellStyle"
            :status.sync="cellState"
            :disabled="!editApproveMode(record)"
            @change="cellChange(record, 'usedCo', $event.selected)"
            @confirm="cellConfirm(record, 'usedCo')"
          />
        </span>
        <span
          slot="functionDept"
          slot-scope="text, record"
        >
          <edit-cell-tree-select
            :value="text"
            :showSearch="true"
            :options="orgIds"
            :cellStyle="cellStyle"
            :status.sync="cellState"
            :disabled="true"
            @change="cellChange(record, 'functionDept', $event.selected)"
            @confirm="cellConfirm(record, 'functionDept')"
          />
        </span>
        <span
          slot="attachmentExists"
          slot-scope="key"
        >{{ key ? (key === 'Y' ? '有': '无'): '' }}</span>
        <span
          slot="status"
          slot-scope="key"
        >{{ takeSelectLabel(status, key) }}</span>
        <span
          slot="autoTag"
          slot-scope="key"
        >{{ key === '1' ? '自动选择' : (key ? '手动选择 (模式' + key + ')' : '手动选择') }}</span>
        <span
          slot="orderUnit"
          slot-scope="key"
        >{{ takeSelectLabel(units, key) }}</span>
        <span
          slot="fylxId"
          slot-scope="value, record"
        >
          <edit-cell-tree-select
            :value="value"
            :options="skFylxs"
            :cellStyle="cellStyle"
            :status.sync="cellState"
            @change="cellChange(record, 'fylxId', $event.selected)"
            @confirm="cellConfirm(record, 'fylxId')"
          />
        </span>
        <!-- <span
          slot="orderQty"
          slot-scope="text"
        >
          <edit-cell-input
            :text="text"
            :disabled="true"
          />
        </span> -->
        <span
          slot="orderQty"
          slot-scope="text, record"
        >
          <edit-cell-input
            :text="text"
            :status.sync="cellState"
            :disabled="true"
            @blur="cellBlur(record, 'orderQty', $event)"
            @change="cellChange(record, 'orderQty', $event)"
            @confirm="cellConfirm(record, 'orderQty')"
          />
        </span>
        <span
          slot="jwspOrderQty"
          slot-scope="text, record"
        >
          <edit-cell-input
            :text="text"
            :status.sync="cellState"
            :disabled="!editApproveMode(record)"
            @blur="cellBlur(record, 'jwspOrderQty', $event)"
            @change="cellChange(record, 'jwspOrderQty', $event)"
            @confirm="cellConfirm(record, 'jwspOrderQty')"
          />
        </span>
        <span
          slot="jModel"
          slot-scope="text, record"
        >
          <edit-cell-input
            :text="text"
            :status.sync="cellState"
            :disabled="!(editApproveMode(record) && IS_BYJ_ENV)"
            @change="cellChange(record, 'jModel', $event)"
            @confirm="cellConfirm(record, 'jModel')"
          />
        </span>
        <span
          slot="unitCost"
          slot-scope="text, record"
        >
          <edit-cell-input
            :text="text"
            :status.sync="cellState"
            :disabled="!editApproveMode(record)"
            @change="cellChange(record, 'unitCost', $event)"
            @confirm="cellConfirm(record, 'unitCost')"
          />
        </span>
        <span
          slot="estimatePrice"
          slot-scope="text, record"
        >
          <edit-cell-input
            :text="text"
            :status.sync="cellState"
            :disabled="!editApproveMode(record)"
            @change="cellChange(record, 'estimatePrice', $event)"
            @confirm="cellConfirm(record, 'estimatePrice')"
          />
        </span>
        <span slot="jwspOrderQty2" slot-scope="key">
          {{ key }}
        </span>
        <span slot="maxReceiveNum" slot-scope="key">
          {{ key || 0 }}
        </span>
        <span
          slot="usedFor"
          slot-scope="text, record"
        >
          <edit-cell-tree-select
            :value="text"
            :options="directions"
            :showSearch="true"
            :status.sync="cellState"
            :disabled="!editApproveMode(record)"
            :expandKeys="null"
            @change="cellChange(record, 'usedFor', $event.selected)"
            @confirm="cellConfirm(record, 'usedFor')"
          />
        </span>
        <span
          slot="cellTextarea"
          slot-scope="text, record, index, column"
        >
          <edit-cell-textarea
            :text="text"
            :status.sync="cellState"
            :disabled="(!editApproveMode(record))"
            @change="cellChange(record, column.key, $event)"
            @confirm="cellConfirm(record, column.key)"
          />
        </span>
        <span
          slot="agent"
          slot-scope="value, record"
        >
          <edit-cell-select
            :value="value"
            :options="agents"
            :cellStyle="cellStyle"
            :status.sync="cellState"
            :disabled="!editApproveMode(record)"
            @change="cellChange(record, 'agent', $event.selected)"
            @confirm="cellConfirm(record, 'agent')"
          />
        </span>
        <span
          slot="handler"
          slot-scope="value, record"
        >
          <edit-cell-select
            :value="value"
            :options="handler"
            :cellStyle="cellStyle"
            :status.sync="cellState"
            :disabled="!editApproveMode(record)"
            @change="cellChange(record, 'hjHandler', $event.selected)"
            @confirm="cellConfirm(record, 'hjHandler')"
          />
        </span>
        <span
          slot="departmentSysId"
          slot-scope="value, record, index, column"
        >
          <edit-cell-select
            :value="value"
            :options="deptno"
            :cellStyle="cellStyle"
            :status.sync="cellState"
            @change="cellChange(record, column.key, $event.selected)"
            @confirm="cellConfirm(record, column.key)"
          />
        </span>
        <span
          slot="security"
          slot-scope="value, record, index, column"
        >
          <edit-cell-select
            :value="value"
            :options="securitys"
            :cellStyle="cellStyle"
            :status.sync="cellState"
            @change="cellChange(record, column.key, $event.selected)"
            @confirm="cellConfirm(record, column.key)"
          />
        </span>
        <span
          slot="isSpecialMaterial"
          slot-scope="value, record"
        >
          <edit-cell-select
            :value="value"
            :options="isSpecialMaterials"
            :cellStyle="cellStyle"
            :status.sync="cellState"
            :disabled="!editApproveMode(record)"
            @change="cellChange(record, 'isSpecialMaterial', $event.selected)"
            @confirm="cellConfirm(record, 'isSpecialMaterial')"
          />
        </span>
        <span
          slot="requestedBy"
          slot-scope="value"
        >
          <edit-cell-select
            :value="value"
            :options="requestedBys"
            :disabled="true"
          />
        </span>
        <span
          slot="totalcostAftertax"
          slot-scope="value, record"
        >
          {{ accMul(record.unitcostAftertax, record.orderQty) || 0 }}
        </span>
        <div
          slot="action"
          slot-scope="text, record"
        >
          <a
            href="javascript:void(0)"
            style="margin-right: 5px;"
            @click="openRecordDrawer(record)"
          >审批历史</a>
          <a
            href="javascript:void(0)"
            style="margin: 0 3px; color: #40a9ff;"
            @click.stop="openUpload(record)"
          >附件</a>
        </div>
      </s-table>
    </div>
    <div class="drawer-container">
      <a-drawer
        width="360"
        title="采购审批"
        :getContainer="false"
        :visible="visible"
        @close="doDrawerClose"
      >
        <a-spin :spinning="approvelLoading">
          <a-form
            layout="vertical"
            style="position: relative; z-index: 0"
          >
            <a-form-item label="审批模式:">
              <a-select v-model="approveParam.selectMode">
                <a-select-option
                  v-for="(item, index) in approveModeList"
                  :disabled="item.disabled"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="审批操作:">
              <a-select v-model="approveParam.action">
                <a-select-option value="1">同意</a-select-option>
                <a-select-option
                  value="0"
                  :disabled="approveReturnDisabled"
                >退回</a-select-option>
                <a-select-option
                  value="9"
                  :disabled="approveCancelDisabled"
                >取消</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="审批意见:">
              <a-textarea
                placeholder="最多可输入200个文本"
                v-model="approveParam.approveSuggest"
                :autoSizdoApprovee="{ minRows: 3 }"
                :maxLength="200"
              />
            </a-form-item>
            <a-form-item
              v-if="approveParam.showApprove && approveParam.action === '1'"
              :label="approveLabel"
            >
              <a-select
                v-model="approveParam.selectApprove"
                allowClear
              >
                <a-select-option
                  v-for="(item, index) in approveList"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="合同名称:" v-if="IS_HJ_ENV && contractList.length > 0">
              <a-select v-model="approveParam.contractSysId" optionFilterProp="label" showSearch>
                <a-select-option v-for="item in contractList" :key="item.contractSysId" :value="item.contractSysId" :label="item.description">{{ item.description }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-form>
          <div v-if="recordPrlineSysId">
            <a-divider style="width: calc(100% + 20px); margin: 20px -10px 15px;" />
            <div class="approve-records-select">
              <a-select
                class="border-only-bottom"
                v-model="recordPrlineSysId"
                style="width: 100%"
              >
                <a-select-option
                  v-for="(item, index) in prlineSysIdOptions"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </div>
            <div class="approve-records-line">
              <div
                v-if="!prlineSysIdRecord"
                style="padding: 8px 14px; font-size: 14px; color: #cccccc"
              >正在查询中...</div>
              <div
                v-else-if="prlineSysIdRecord.length === 0"
                style="padding: 8px 14px; font-size: 14px; color: #909399"
              >暂无审批记录</div>
              <a-timeline
                v-else
                style="padding: 12px"
              >
                <a-timeline-item
                  v-for="(item, index) in prlineSysIdRecord"
                  :key="index"
                  color="green"
                >
                  <div>{{ item.msg }}</div>
                  <div>{{ item.flag }}</div>
                  <div>{{ item.createBy }}</div>
                  <div>{{ item.createDate }}</div>
                </a-timeline-item>
                <a-timeline-item>
                  <span slot="dot" />
                </a-timeline-item>
              </a-timeline>
            </div>
          </div>
        </a-spin>
        <div class="drawer-footer">
          <div class="footer-fixed">
            <a-button @click="doDrawerClose">返回</a-button>
            <a-button
              type="primary"
              :loading="approvelLoading"
              @click="doApprove"
            >提交</a-button>
          </div>
        </div>
      </a-drawer>
    </div>
    <!-- 引入模式对话框组件 -->
    <!-- 侧边滑动栏 物资代码新增、修改 -->
    <template>
      <a-drawer
        :title="subTitle"
        :width="600"
        :visible="materialVisible"
        :mask="false"
        :bodyStyle="{paddingBottom: '80px'}"
        @close="materialVisible = false"
      >
        <a-spin :spinning="confirmLoading">
          <a-form :form="form">
            <a-row :gutter="8">
              <!-- 前台不显示唯一编码 -->
              <a-form-item
                v-show="false"
                label="唯一编码:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input v-decorator="['materialSysId', {rules: [{required: false}]}]" />
              </a-form-item>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="物资代码:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input
                    :disabled="IsProhibit !== '1'"
                    v-decorator="['materialNum', {rules: [{required: true, validator: checkMaterialNum}]}]"
                    @blur="handlematerialNumBlur"
                  />
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="物资名称:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input
                    :disabled="IsProhibit !== '1'"
                    v-decorator="['materialName', {rules: [{required: true, message: '请输入至少一个字符！'}]}]"
                  />
                </a-form-item>
              </a-col>
              <a-col
                v-if="IS_JX_ENV"
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="嘉兴物资类型:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-tree-select
                    v-decorator="['jxMaterialClass', { rules: [{ type: 'string', required: false, message: '请选择物资类别' }] }]"
                    treeNodeFilterProp="label"
                    :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                    :treeData=" jxMaterialClass "
                    :disabled="IsEDIT === '1'"
                    showSearch
                    allowClear
                    @change="jxMateNum"
                  />
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="物资类别:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-tree-select
                    v-decorator="['materialClass', { rules: [{ type: 'string', required: false, message: '请选择物资类别' }] }]"
                    treeNodeFilterProp="label"
                    :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                    :treeData="materialClass"
                    :disabled="IsEDIT === '1'"
                    showSearch
                    allowClear
                    @change="wzgMateNum"
                  />
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="26大类类型:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    :disabled="IsEDIT === '1'"
                    v-decorator="['materialType', { rules: [{ type: 'string', required: false, message: '请选择26大类类型' }] }]"
                    allowClear
                    showSearch
                    :filterOption="filterOption"
                  >
                    <a-select-option
                      v-for="(item, index) in materialType"
                      :value="item.value"
                      :key="index"
                    >{{ item.label }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="规格型号:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input
                    :disabled="IsEDIT === '1'"
                    v-decorator="['jmodel', {rules: [{required: false, message: '请输入至少一个字符！'}]}]"
                  />
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="采购员:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    :disabled="IsEDIT === '1'"
                    v-decorator="['buyer', { rules: [{ type: 'string', required: true, message: '请选择采购员' }] }]"
                    allowClear
                    showSearch
                    :filterOption="filterOption"
                  >
                    <a-select-option
                      v-for="(item, index) in buyer"
                      :value="item.value"
                      :key="index"
                    >{{ item.label }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="仓库管理员:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    :disabled="IsEDIT === '1'"
                    v-decorator="['storeman', { rules: [{ type: 'string', required: true, message: '请选择仓库管理员' }] }]"
                    allowClear
                    showSearch
                    :filterOption="filterOption"
                  >
                    <a-select-option
                      v-for="(item, index) in storeman"
                      :value="item.value"
                      :key="index"
                    >{{ item.label }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="职能部门:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    :disabled="IsEDIT === '1'"
                    v-decorator="['deptno', { rules: [{ type: 'string', required: false, message: '请选择责任部门' }] }]"
                    allowClear
                    showSearch
                    :filterOption="filterOption"
                  >
                    <a-select-option
                      v-for="(item, index) in deptno"
                      :value="item.value"
                      :key="index"
                    >{{ item.label }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="是否统购物资:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  style="width: 200"
                >
                  <a-select
                    :disabled="IsEDIT === '1'"
                    style="width: 100"
                    v-decorator="['isUniOrder', {rules: [{required: true, message: '请选择是或否'}]}]"
                  >
                    <a-select-option value="Y">是</a-select-option>
                    <a-select-option value="N">否</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="是否补库物资:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    :disabled="IsEDIT === '1'"
                    v-decorator="['isResupply', {rules: [{required: true, message: '请选择是或否'}]}]"
                  >
                    <a-select-option value="Y">是</a-select-option>
                    <a-select-option value="N">否</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                v-if="IS_JX_ENV"
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="是否固定资产:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    :disabled="IsEDIT === '1'"
                    v-decorator="['isFixedAssets', {rules: [{required: true, message: '请选择是或否'}]}]"
                  >
                    <a-select-option value="Y">是</a-select-option>
                    <a-select-option value="N">否</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="计量单位:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    :disabled="IsEDIT === '1'"
                    v-decorator="['issueUnit', { rules: [{ required: true, message: '请选择是或否',type: 'string' }] }]"
                    allowClear
                    showSearch
                    :filterOption="filterOption"
                  >
                    <a-select-option
                      v-for="(item, index) in invoiceUnits"
                      :value="item.value"
                      :key="index"
                    >{{ item.label }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="物资编码账本号:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input
                    :disabled="IsEDIT === '1'"
                    v-decorator="['materialnum5', {rules: [{required: false, message: '物资编码账本号不能为空！'}]}]"
                  />
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="批次类型:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    :disabled="IsEDIT === '1'"
                    v-decorator="['lotType', { rules: [{ type: 'string', required: false, message: '请选择是否批次' }] }]"
                  >
                    <a-select-option
                      v-for="(item, index) in lottype"
                      :value="item.value"
                      :key="index"
                    >{{ item.label }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="类型细目:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    :disabled="IsEDIT === '1'"
                    allowClear
                  >
                    <a-select-option
                      v-for="(item, index) in typedetail"
                      :value="item.value"
                      :key="index"
                    >{{ item.label }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>

              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="最大库存:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input
                    :disabled="IsEDIT === '1'"
                    v-decorator="['maxAmount', {rules: [{required: false,type:'number', transform(value) { if(value){ return Number(value); } },message: '最大库存不能为空且值为数字！'}]}]"
                  />
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="最小库存:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input
                    :disabled="IsEDIT === '1'"
                    v-decorator="['minAmount', {rules: [{required: false, type:'number', transform(value) { if(value){ return Number(value); } },message: '最小库存不能为空且值为数字！'}]}]"
                  />
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="参考单价:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input
                    :disabled="IsEDIT === '1'"
                    @blur="handlerefCostBlur"
                    v-decorator="['refCost', {rules: [{pattern: /^[+-]?(0|([1-9]\d*))(\.\d+)?$/,required: true,message:'参考单价不能为空且值为数字！'}]}]"
                  />
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="税率:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input
                    :disabled="IsEDIT === '1'"
                    @blur="handletaxrateBlur('taxrate')"
                    v-decorator="['taxrate', {rules: [{required: true, type:'number',transform(value) { if(value){ return Number(value); } },message: '参考单价不能为空且值为数字！'}]}]"
                  />
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="税后单价:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input
                    :disabled="IsEDIT === '1'"
                    @blur="handleunitcostAfterTaxBlur"
                    v-decorator="['unitcostAfterTax', {rules: [{required: true, type:'number',transform(value) { if(value){ return Number(value); } },message: '税后单价不能为空且值为数字！'}]}]"
                  />
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="是否二次利用:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    :disabled="IsEDIT === '1'"
                    v-decorator="['isReuse', {rules: [{required: true, message: '请选择是或否'}]}]"
                  >
                    <a-select-option value="Y">是</a-select-option>
                    <a-select-option value="N">否</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="是否劳保用品:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    :disabled="IsEDIT === '1'"
                    v-decorator="['isLabourSupply', {rules: [{required: true, message: '请选择是或否'}]}]"
                  >
                    <a-select-option value="Y">是</a-select-option>
                    <a-select-option value="N">否</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                v-if="IS_JYS_ENV"
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="所在单位"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    v-decorator="['belongToSite', { rules: [{ type: 'string', required: false, message: '请选择所在单位信息' }] }]"
                    showSearch
                    allowClear
                  >
                    <a-select-option
                      v-for="(item, index) in belongToSites"
                      :value="item.value"
                      :key="index"
                    >{{ item.label }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="库:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    :disabled="IsEDIT === '1'"
                    v-decorator="['yardId', { rules: [{ type: 'string', required: false, message: '请选择库信息' }] }]"
                    allowClear
                    showSearch
                    @change="handleStorehouseChange"
                  >
                    <a-select-option
                      v-for="(item, index) in storehouses"
                      :value="item.value"
                      :key="index"
                    >{{ item.label }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="区:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    :disabled="IsEDIT === '1'"
                    v-decorator="['zone', { rules: [{ type: 'string', required: false, message: '请选择库信息' }] }]"
                    allowClear
                    showSearch
                    @change="handleZoneChange"
                  >
                    <a-select-option
                      v-for="(item, index) in zones"
                      :value="item.value"
                      :key="index"
                    >{{ item.label }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="架:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    :disabled="IsEDIT === '1'"
                    v-decorator="['frame', { rules: [{ type: 'string', required: false, message: '请选择库信息' }] }]"
                    allowClear
                    showSearch
                    @change="handleFrameChange"
                  >
                    <a-select-option
                      v-for="(item, index) in frames"
                      :value="item.value"
                      :key="index"
                    >{{ item.label }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="层:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    :disabled="IsEDIT === '1'"
                    v-decorator="['floor', { rules: [{ type: 'string', required: false, message: '请选择库信息' }] }]"
                    allowClear
                    showSearch
                    @change="handleFloorChange"
                  >
                    <a-select-option
                      v-for="(item, index) in floors"
                      :value="item.value"
                      :key="index"
                    >{{ item.label }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="位:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    :disabled="IsEDIT === '1'"
                    v-decorator="['bit', { rules: [{ type: 'string', required: false, message: '请选择库信息' }] }]"
                    allowClear
                    showSearch
                  >
                    <a-select-option
                      v-for="(item, index) in bits"
                      :value="item.value"
                      :key="index"
                    >{{ item.label }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="是否激活:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    :disabled="IsEDIT === '1'"
                    v-decorator="['activity', {rules: [{required: true, message: '请选择是或否'}]}]"
                  >
                    <a-select-option value="Y">是</a-select-option>
                    <a-select-option value="N">否</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :md="12"
                :sm="24"
              >
                <a-form-item
                  label="审批状态:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-select
                    v-decorator="['status', { rules: [{ type: 'string' }] }]"
                    allowClear
                    :disabled="true"
                  >
                    <a-select-option
                      v-for="(item, index) in statusDetal"
                      :value="item.value"
                      :key="index"
                    >{{ item.label }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :md="24"
                :sm="24"
              >
                <a-form-item
                  label="二维码:"
                  v-show="IsShowQr=== '1'"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <div class="material-qrCode">
                    <img
                      v-if="materialQrCodeUrl"
                      :src="materialQrCodeUrl"
                      alt="qrCode"
                    >
                  </div>
                  <a-button
                    v-show="IsShowQr=== '1'"
                    :style="{marginRight: '8px'}"
                    @click="downloadMaterialQrCode()"
                  >下载二维码</a-button>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-spin>
        <div
          :style="{
            position: 'absolute',
            right: 0,
            bottom: 0,
            width: '100%',
            borderTop: '1px solid #e9e9e9',
            padding: '10px 16px',
            background: '#fff',
            textAlign: 'right',
            zIndex: 1,
          }"
        >
          <a-button
            :style="{marginRight: '8px'}"
            @click="hiddenMaterialDrawer"
          >取消</a-button>
          <a-button
            @click="doSubmit"
            type="primary"
            :disabled="IsEDIT === '1'"
          >保存</a-button>
        </div>
      </a-drawer>
    </template>
    <a-drawer
      title="审核数修改记录"
      placement="right"
      :closable="false"
      :visible="modifyJwspVisible"
      :after-visible-change="afterVisibleChange"
      @close="onClose"
    >
      <a-timeline>
        <a-timeline-item
          v-for="(item, index) in modifyJwspRecord"
          :key="index"
          color="blue"
        >
          <div>第{{ index+1 }}次修改</div>
          <div>{{ item.createBy }}</div>
          <div>修改前审核数:  {{ item.oldJwspOrderQty }}</div>
          <div>修改后审核数:  {{ item.jwspOrderQty }}</div>
          <div>{{ item.modifyJwspDate }}</div>
        </a-timeline-item>
        <a-timeline-item>
          <span slot="dot" />
        </a-timeline-item>
      </a-timeline>
    </a-drawer>
    <!-- 海建表格信息 -->
    <FormPrline
      ref="FormPrline"
      :formInfo="formInfo"
      :requestedBys="requestedBys"
      :directions="directions"
      :units="units"
      :status="status"
      :agents="agents"
      :procurementPlan="lineTypeAll"
      @clearModel="clearModel"/>
    <appendix
      ref="appendix"
      businessName="prline"
      keyId="prlineSysId"
      :isShowDeleteAndUpload="isShowDeleteAndUpload"
    />
    <!-- 查看审批历史——弹出层 -->
    <ApproveRecordsDrawer ref="ApproveRecordsDrawer" :recordSysId="prlineSysId" origin="prline"/>
  </section>
</template>

<script>
import {
  // 填报状态
  APPROVE_STATUS_READY_CODE,
  APPROVE_STATUS_START_CODE,
  // 价格单位
  PRICE_UNIT,
  // 审批状态
  APPROVE_STATUS_CONFIRM_CODE,
  // 审批节点
  APPROVE_NODES_NOT_RETURN,
  APPROVE_NODES_ALLOW_CANCEL,
  // 审批下拉框选项
  APPROVE_NODES_BUS_FREEZE,
  APPROVE_NODES_TABLE_EDIT,
  APPROVE_NODES_BUS_INIT
} from '@/store/variable-types'

import { OPERATOR, ORG_ID, ROLE_NAME, PERSON_ID } from '@/store/mutation-types'
import { STable, EditCellInput, EditCellSelect, EditCellTreeSelect, EditCellTextarea, DropdownChecked } from '@/components'
import { requestBuilder, deepUpdate, takeTreeByKey } from '@/utils/util'
import * as prlineApi from '@/api/material/prline'
import * as materialApi from '@/api/material/material'
import * as baseApi from '@/api/material/base'
import ApproveRecordsDrawer from '@/views/system/components/ApproveRecordsDrawer'
import AddPrlineDrawer from '../modules/Components/TodoApprovePrline/addPrlineDrawer.vue'
import EditPrlineDrawer from '../modules/Components/TodoApprovePrline/editPrlineDrawer.vue'
import * as flowApi from '@/api/system/flow'
import { tableMixin } from '@/utils/tableMixin'
import FormPrline from '../modules/FormPrline.vue'
import Vue from 'vue'
import Appendix from '@/views/system/components/Appendix'

// 操作人 userNo
const USER_OPERATOR = Vue.ls.get(OPERATOR)
// 组织机构 orgId
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const PERSON_SYS_ID = Vue.ls.get(PERSON_ID)
// 运行环境 ENV
const IS_JYS_ENV = USER_ORG_ID === '1.100.117'
// 运行环境-北一
const IS_BYJ_ENV = USER_ORG_ID === '1.100.101'
const IS_MD_ENV = USER_ORG_ID === '1.100.104'
// 运行环境-嘉兴
const IS_JX_ENV = USER_ORG_ID === '1.102'
// 温州港
const IS_WZG = USER_ORG_ID.substr(0, 5) === '1.101'
// 南京明州
const IS_NJMZ_ENV = USER_ORG_ID === '1.100.109'
// 铁司
const IS_TS_ENV = USER_ORG_ID === '1.100.122'
const IS_TCWF_ENV = USER_ORG_ID === '1.100.114'
// 铁司
const IS_SLH_ENV = USER_ORG_ID === '1.100.111'
const IS_SZXD_ENV = USER_ORG_ID === '1.100.106'
const USER_ROLE_NAME = Vue.ls.get(ROLE_NAME)
// 海建
const IS_HJ_ENV = USER_ORG_ID === '1.100.131'
const IS_DMY_ENV = USER_ORG_ID === '1.122.801'
const IS_SH_ENV = USER_ORG_ID === '1.100.115.111'
const IS_XT_ENV = USER_ORG_ID === '1.100.118'

export default {
  name: 'TodoApprovePrline',
  components: {
    STable,
    EditCellInput,
    EditCellSelect,
    DropdownChecked,
    EditCellTextarea,
    EditCellTreeSelect,
    FormPrline,
    Appendix,
    ApproveRecordsDrawer,
    AddPrlineDrawer,
    EditPrlineDrawer
  },
  inject: {
    doQueryAll: {
      default: function () {
        return () => {
          this.doQuerySource()
        }
      }
    }
  },
  props: {
    origin: {
      type: String,
      default: ''
    },
    loading: {
      type: Object,
      default: function () {
        return {
          query: false,
          approve: false
        }
      }
    },
    queryApi: {
      type: String,
      default: ''
    },
    useApprove: {
      type: Boolean,
      default: false
    },
    queryParam: {
      type: Object,
      default: function () {
        return {}
      }
    },
    companyArr: {
      type: Array,
      default: function () {
        return []
      }
    },
    status: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  mixins: [tableMixin],
  data () {
    return {
      // 测试
      // RUN_ENV
      IS_DMY_ENV,
      IS_BYJ_ENV,
      IS_WZG,
      IS_JYS_ENV,
      IS_JX_ENV,
      IS_NJMZ_ENV,
      IS_SLH_ENV,
      IS_SZXD_ENV,
      IS_TS_ENV,
      IS_MD_ENV,
      IS_TCWF_ENV,
      IS_HJ_ENV,
      IS_SH_ENV,
      uploadRecord: {
        status: ''
      },
      IS_XT_ENV,
      // 选项配置
      insideTableVisible: false,
      popVisible: false,
      popLoading: false,
      searchForm: {
        requestedBy: '',
        agent: '',
        materialName: ''
      },
      prlineSysId: '',
      options: [],
      selected: [],
      alert: '',
      securitys: [{
        value: 'Y',
        label: '安全'
      },
      {
        value: 'N',
        label: '非安全'
      }],
      // 受控计划费用类型
      skFylxs: [{
        label: '安全生产费',
        value: 'SHG5399'
      }, {
        label: '口岸费',
        value: 'SHG3601'
      }, {
        label: '劳动保护费',
        value: 'SHG1402'
      }],
      forced: true,
      // 有无领用计划
      existRequis: false,
      // 当前配置项表格数据
      formInfo: {
        name: 1
      },
      // 表格配置
      columns: [
        {
          title: '申报单号',
          dataIndex: 'prlineNum',
          ellipsis: true,
          sorter: true,
          check: true,
          edit: false,
          fixed: true,
          width: 80
        },
        ...(IS_BYJ_ENV || IS_MD_ENV ? [
          {
            title: '计划类型',
            dataIndex: 'lineType',
            scopedSlots: { customRender: 'lineType' },
            fixed: true,
            ellipsis: true,
            sorter: false,
            check: true,
            edit: false,
            align: 'center',
            width: 100
          },
          {
            title: '使用单位',
            dataIndex: 'usedCo',
            scopedSlots: { customRender: 'usedCo' },
            ellipsis: true,
            fixed: true,
            sorter: false,
            check: true,
            edit: false,
            align: 'center',
            width: 100
          } ]
          : []),
        {
          title: '附件有无',
          dataIndex: 'attachmentExists',
          scopedSlots: { customRender: 'attachmentExists' },
          ellipsis: true,
          fixed: true,
          sorter: false,
          check: true,
          edit: false,
          align: 'center',
          width: 100
        },
        {
          title: '物资代码',
          dataIndex: 'materialNum',
          ellipsis: true,
          sorter: true,
          check: true,
          edit: false,
          fixed: true,
          width: 100
        },
        {
          title: '物资名称',
          dataIndex: 'materialName',
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: true,
          width: 120
        },
        ...(IS_MD_ENV ? [
          {
            title: '年度计划单号',
            dataIndex: 'yearPrlineNum',
            align: 'center',
            ellipsis: true,
            sorter: false,
            check: true,
            edit: false,
            fixed: false,
            width: 100
          },
          {
            title: '年度计划',
            dataIndex: 'yearDescription',
            ellipsis: true,
            sorter: false,
            check: true,
            edit: false,
            fixed: false,
            align: 'center',
            width: 220
          }
        ]
          : []),
        ...(IS_JX_ENV
          ? [
            {
              title: '是否特定物资',
              scopedSlots: { customRender: 'isSpecialMaterial' },
              dataIndex: 'isSpecialMaterial',
              ellipsis: true,
              sorter: false,
              check: true,
              edit: false,
              fixed: false,
              width: 130
            }
          ]
          : []),
        {
          title: '品牌',
          dataIndex: 'brand',
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: false,
          width: 110
        },
        {
          title: '职能部门',
          dataIndex: 'functionDept',
          scopedSlots: { customRender: 'functionDept' },
          ellipsis: true,
          width: 180
        },
        {
          title: '规格型号',
          dataIndex: 'jModel',
          scopedSlots: { customRender: 'jModel' },
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: false,
          width: 110
        },
        ...(IS_SZXD_ENV ? [
          {
            title: '工单号',
            dataIndex: 'workorderNo',
            ellipsis: true,
            sorter: false,
            check: true,
            edit: false,
            fixed: false,
            width: 100
          },
          {
            title: '设备编号',
            dataIndex: 'assetSbNum',
            ellipsis: true,
            sorter: false,
            check: true,
            edit: false,
            fixed: false,
            width: 100
          } ]
          : []),
        ...(IS_JX_ENV ? [
          {
            title: '计量单位',
            dataIndex: 'orderUnit',
            scopedSlots: { customRender: 'orderUnit' },
            ellipsis: true,
            sorter: false,
            check: true,
            edit: false,
            fixed: false,
            width: 85
          } ]
          : []),
        {
          title: '申请数',
          dataIndex: 'orderQty',
          scopedSlots: { customRender: 'orderQty' },
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: false,
          width: 90
        },
        {
          title: '审核数',
          dataIndex: 'jwspOrderQty',
          scopedSlots: { customRender: 'jwspOrderQty' },
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: false,
          width: 90
        },
        {
          title: IS_BYJ_ENV ? '未采购计划数' : '待采购数',
          dataIndex: 'jwspOrderQty2',
          scopedSlots: { customRender: 'jwspOrderQty2' },
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: false,
          width: 110
        },
        {
          title: '库存数量',
          dataIndex: 'totalCurbal',
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: false,
          width: 90
        },
        ...(IS_WZG
          ? [
            {
              title: '剩余定额',
              dataIndex: 'remainQuota',
              ellipsis: true,
              sorter: false,
              check: true,
              edit: false,
              fixed: false,
              width: 110
            }
          ]
          : []),
        ...(!IS_JYS_ENV
          ? [
            {
              title: '预估单价/' + PRICE_UNIT,
              dataIndex: 'unitCost',
              scopedSlots: { customRender: 'unitCost' },
              ellipsis: true,
              sorter: false,
              check: true,
              edit: false,
              fixed: false,
              width: 90
            },
            {
              title: '预估总价/' + PRICE_UNIT,
              dataIndex: 'estimatePrice',
              scopedSlots: { customRender: 'estimatePrice' },
              ellipsis: true,
              sorter: false,
              check: true,
              edit: false,
              fixed: false,
              width: 90
            }
          ]
          : []),
        {
          title: '使用方向',
          dataIndex: 'usedFor',
          scopedSlots: { customRender: 'usedFor' },
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: false,
          width: 200
        },
        {
          title: '备注',
          dataIndex: 'remarks',
          scopedSlots: { customRender: 'cellTextarea' },
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: false,
          width: 130
        },
        {
          title: '采购员',
          dataIndex: 'agent',
          scopedSlots: { customRender: 'agent' },
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: false,
          width: 110
        },
        {
          title: '当前审批人',
          dataIndex: 'userNextNames',
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: false,
          width: 110
        },
        ...(IS_SLH_ENV ? [{
          title: '上报日期',
          dataIndex: 'approveDate',
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: false,
          width: 100
        }] : []),
        {
          title: '申请人',
          dataIndex: 'requestedBy',
          scopedSlots: { customRender: 'requestedBy' },
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: false,
          width: 110
        },
        {
          title: '审批状态',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' },
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: false,
          width: 120
        },
        (IS_HJ_ENV ? {} : {
          title: '审批人模式',
          dataIndex: 'autoTag',
          scopedSlots: { customRender: 'autoTag' },
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: false,
          width: 110
        }),
        {
          title: '计划年月',
          dataIndex: 'planMonth',
          ellipsis: true,
          check: true,
          edit: false,
          fixed: false,
          sorter: true,
          width: 100
        },
        ...(IS_JX_ENV ? [
          {
            title: '申请时间',
            dataIndex: 'createDate',
            scopedSlots: { customRender: 'createDate' },
            ellipsis: true,
            sorter: false,
            check: true,
            edit: false,
            fixed: false,
            width: 120
          } ]
          : []),
        {
          title: '计划描述',
          scopedSlots: { customRender: 'cellTextarea' },
          dataIndex: 'description',
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: false,
          width: 140
        },
        ...(!(IS_BYJ_ENV || IS_MD_ENV) ? [
          {
            title: '计划类型',
            dataIndex: 'lineType',
            scopedSlots: { customRender: 'lineType' },
            ellipsis: true,
            sorter: false,
            check: true,
            edit: false,
            fixed: false,
            width: 120
          } ]
          : []),
        ...(IS_JYS_ENV
          ? [
            {
              title: '预估税后单价/' + PRICE_UNIT,
              dataIndex: 'unitcostAftertax',
              ellipsis: true,
              sorter: false,
              check: true,
              edit: false,
              fixed: false,
              width: 90
            },
            {
              title: '预估税后总价/' + PRICE_UNIT,
              dataIndex: 'totalcostAftertax',
              scopedSlots: { customRender: 'totalcostAftertax' },
              ellipsis: true,
              sorter: false,
              check: true,
              edit: false,
              fixed: false,
              width: 90
            }
          ]
          : []),
        ...(!IS_JX_ENV ? [
          {
            title: '计量单位',
            dataIndex: 'orderUnit',
            scopedSlots: { customRender: 'orderUnit' },
            ellipsis: true,
            sorter: false,
            check: true,
            edit: false,
            fixed: false,
            width: 85
          } ]
          : []),
        ...(IS_MD_ENV ? [
          {
            title: '受控费用',
            dataIndex: 'fylxId',
            scopedSlots: { customRender: 'fylxId' },
            ellipsis: true,
            sorter: false,
            check: true,
            edit: false,
            fixed: false,
            width: 100
          } ]
          : []),
        {
          title: '创建人',
          dataIndex: 'createByName',
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: false,
          width: 95
        },
        {
          title: '部门',
          dataIndex: 'departmentName',
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: false,
          width: 95
        },
        ...(IS_SZXD_ENV
          ? [
            {
              title: '领用部门',
              dataIndex: 'departmentSysId',
              scopedSlots: { customRender: 'departmentSysId' },
              ellipsis: true,
              sorter: false,
              check: true,
              edit: false,
              fixed: false,
              width: 100
            }
          ]
          : []),
        ...(IS_TCWF_ENV ? [
          {
            title: '物资属性',
            dataIndex: 'isSpecialMaterial',
            scopedSlots: { customRender: 'security' },
            ellipsis: true,
            sorter: false,
            check: true,
            edit: false,
            fixed: 'right',
            width: 120
          } ]
          : []),
        {
          title: '可领数',
          dataIndex: 'maxReceiveNum',
          scopedSlots: { customRender: 'maxReceiveNum' },
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          fixed: false,
          width: 95
        },
        ...(IS_MD_ENV ? [
          {
            title: '预算名称',
            dataIndex: 'budgetName',
            ellipsis: true,
            sorter: false,
            check: true,
            edit: false,
            fixed: false,
            width: 120
          } ]
          : []),
        {
          title: '操作',
          key: 'action',
          scopedSlots: { customRender: 'action' },
          ellipsis: true,
          sorter: false,
          check: true,
          edit: false,
          disabled: true,
          align: 'center',
          fixed: 'right',
          width: 100
        }
      ],
      approvelLoading: false,
      scroll: {
        x: '1500px',
        y: '275px',
        scrollToFirstRowOnChange: false
      },
      rowSelection: {
        type: 'checkbox',
        onChange: (selectedRowKeys, selectedRows) => {
          this.selectedRows = selectedRows
          this.selectedRowKeys = selectedRowKeys
        },
        hideDefaultSelections: true,
        selections: [
          {
            key: 'selectAll',
            text: '选择所有',
            onSelect: () => {
              const selected = this.selected
              const dataSource = this.dataSource
              let selectedRows = this.selectedRows
              let selectedRowKeys = this.selectedRowKeys
              selectedRows = dataSource.filter(item => selected.includes(item.prlineNum))
              selectedRowKeys = selectedRows.map(item => item.prlineSysId)
              this.$refs.table.triggerSelect(selectedRowKeys, selectedRows)
            }
          },
          {
            key: 'cancelAll',
            text: '取消所有',
            onSelect: () => {
              this.$refs.table.triggerSelect([], [])
            }
          }
        ]
      },
      dataSource: [],
      selectedRows: [],
      selectedRowKeys: [],
      // 存储所有被修改的数据，用于批量修改
      cellChangeSource: [],
      pageSizeOptions: ['10', '15', '20', '25', '30'],
      defaultPageSize: 20,
      showPagination: 'imitate',
      immediate: false,
      bordered: false,
      cellState: false,
      cellStyle: {
        edit: {
          right: 'auto',
          left: '55px'
        }
      },
      loadData: parameter => {
        // loading
        this.loading.query = true
        // 接口调参数
        const param = requestBuilder(
          '',
          deepUpdate({
            pageTag: !this.useApprove,
            ...this.queryParam,
            ...this.searchForm
          }),
          undefined,
          undefined,
          parameter.sortField,
          parameter.sortOrder
        )
        // 接口调用
        const promise = this.useApprove
          ? flowApi.findTodoByOrigin(this.queryApi, param)
          : flowApi.findDoneByOrigin(this.queryApi, param)

        // 返回处理
        return promise
          .then(res => {
            if (res.code !== '0000') {
              this.$notification.error({
                message: '系统消息',
                description: res.message || '采购计划获取失败！'
              })
              return Promise.reject(res.result)
            }
            const data = res.result
            this.dataSource = data
            this.doHandleSource()
            return { data: data.filter(item => this.selected.includes(item.prlineNum)) }
          })
          .finally(() => {
            this.loading.query = false
          })
      },
      rowClick: record => ({
        on: {
          dblclick: () => {
            // 双击时消单击事件
            clearTimeout(this.todoApproPrClickTimer)
            // 双击修改启用查看审核数修改记录(鼠浪湖)
            if (IS_SLH_ENV || IS_BYJ_ENV) {
              prlineApi.queryModifyJwspHis(requestBuilder('', { prlineSysId: record.prlineSysId })).then(res => {
                if (res.code === '0000' && res.result.length > 0) {
                  this.modifyJwspRecord = res.result
                  this.modifyJwspVisible = true
                } else {
                  this.$message.info('该条计划审核数没有修改记录')
                }
              })
            }
            if (this.roleName) {
              if (record.isAbsenceData) {
              // 查询参数
                const query = {}
                query.materialSysId = record.materialSysId
                const param = requestBuilder(
                  '',
                  Object.assign(query)
                )
                materialApi.getMaterialInfo(param).then(res => {
                  this.openDrawer(res.result.data[0])
                })
                this.selectPrlineInfo = record
                this.oldMaterialSysId = record.materialSysId
                this.oldMaterialNum = record.materialNum
              }
            }
            if (IS_HJ_ENV) {
              this.formInfo = record
              this.$nextTick(() => {
                this.$refs.FormPrline.showModal()
              })
            }
          },
          click: () => {
            // 限制频繁触发单击事件
            clearTimeout(this.todoApproPrClickTimer)
            this.todoApproPrClickTimer = setTimeout(() => {
              const dataSource = this.dataSource
              const selectedRows = this.selectedRows
              const selectedRowKeys = this.selectedRowKeys
              const currentIndex = selectedRowKeys.indexOf(record.prlineSysId)
              if (currentIndex > -1) {
                selectedRowKeys.splice(currentIndex, 1)
                selectedRows.splice(currentIndex, 1)
              } else {
                selectedRowKeys.push(record.prlineSysId)
                selectedRows.push(record)
              }
              const newDataSource = dataSource.filter(item => {
                return selectedRowKeys.includes(item.prlineSysId)
              })
              const newSelectedRows = newDataSource.map(item => item)
              const newSelectedRowKeys = newDataSource.map(item => item.prlineSysId)
              this.$refs.table.triggerSelect(newSelectedRowKeys, newSelectedRows)
            }, 200)
          }
        }
      }),
      rowClassName: record => {
        if (IS_BYJ_ENV) {
          // 补报物资
          if (record.lineType === '9') {
            return 'linkRowStyle2'
          }
          // 应急物资
          if (record.lineType === '5') {
            return 'emergencyStyle'
          }
          // 即将到达需求日期(前一周)物资
          if (record.modifyDate) {
            if (record.demandDate) {
              // 审批完成后开始计算时间
              const deadline = this.addDate(record.modifyDate, Number(record.demandDate))
              const day = this.getNumberOfDays(new Date(), deadline)
              if (day <= 7) {
                return 'deadLineStyle'
              }
            }
          }
        } else if (IS_SLH_ENV) {
          if (record.jwspFlag === 'Y') {
            return 'modifyJwspStyle'
          }
        } else if (IS_HJ_ENV) {
          if (record.attachmentExists === 'Y') {
            return 'attachmentExists'
          }
        }
        // 退回物资颜色提示
        if (record.flag && record.flag === '0') {
          return 'returnStyle'
        }
      },
      getPopupContainer: () => {
        return this.$el
      },
      cellChange: (record, key, value) => {
        if (key === 'jwspOrderQty' || key === 'orderQty') {
          const event = value
          const regex = /^\d+(\.\d*)?$/
          if (regex.test(event.value)) {
            event.value = +event.value
            value = event.value || 0
          } else if (!event.value.trim()) {
            event.value = ''
            value = 0
          } else {
            value = event.value = record[key] || 0
          }
          if (key === 'jwspOrderQty') {
            value = event.value = value > record.orderQty ? record.orderQty : value
          }
          record.estimatePrice = value * record.unitCost
        } else if (key === 'remarks' || key === 'description' || key === 'jModel') {
          value = value.value
        } else if (key === 'usedFor') {
          this.$set(record, 'usedforName', this.takeSelectLabel(this.directions, value))
        } else if (key === 'estimatePrice') {
          value = value.value
          record.unitCost = value / record.jwspOrderQty
        } else if (key === 'unitCost') {
          value = value.value
          record.estimatePrice = value * record.jwspOrderQty
        }
        const arr = ['agent', 'usedCo', 'lineType', 'hjHandler']
        if (arr.includes(key) && this.selectedRows.includes(record)) {
          for (const values of this.selectedRows) {
            values[key] = value
            this.$set(values, key, value)
            if (!this.cellChangeSource.includes(values)) {
              this.cellChangeSource.push(values)
            }
          }
        }
        this.$set(record, key, value)
        if (key === 'unitCost' || key === 'estimatePrice') {
          this.updateEstimatePrice(record.prlineNum)
        }
        if (!this.cellChangeSource.includes(record)) {
          this.cellChangeSource.push(record)
        }
      },
      cellConfirm: (record, key) => {
        record = this.cellChangeSource
        prlineApi.modifyProcurePlan(requestBuilder('update', record)).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '采购计划修改失败！'
            })
          } else {
            this.$notification.success({
              message: '系统消息',
              description: '采购计划修改成功！'
            })
            this.cellChangeSource = []
            this.doQueryAll()
          }
        })
      },
      cellBlur (record, key, value) {
        if (key === 'jwspOrderQty') {
          const event = value
          event.value = event.value || 0
        }
      },
      editApproveMode: (record, key = '') => {
        if (IS_SLH_ENV && key === 'orderQty') {
          return true
        }
        if (
          APPROVE_NODES_TABLE_EDIT.includes(this.getApproveId(record.status)) &&
          record.userNext &&
          record.userNext.split(',').includes(PERSON_SYS_ID)
        ) {
          return true
        }
        return false
      },

      // 审批弹框配置
      visible: false,
      approveList: [],
      approveLabel: '下个审批人',
      approveModeList: [
        {
          label: '审批申报单号中指定的采购计划',
          value: '1'
        }
      ],
      approveParam: {
        showApprove: false,
        selectMode: '',
        selectPrlines: [],
        selectApprove: '',
        approveSuggest: '',
        action: '1',
        contractSysId: ''
      },
      // 合同名称 -- 海建
      contractList: [],

      // 审批记录缓存
      prlineSysIdRecords: {},
      recordPrlineSysId: '',

      // 下拉框选项 - 使用方向
      units: [],
      agents: [],
      requestedBys: [],
      isSpecialMaterials: [],
      directions: [],

      // 滑动抽屉参数
      form: this.$form.createForm(this),
      confirmLoading: false, // 加载图标是否出现
      rolesList: [], // 角色列表
      selectedItems: [],
      buyer: [], // 采购员
      handler: [],
      storeman: [], // 仓库保管员
      status1: [], // 审批状态
      statusDetal: [], // 审批详细状态
      materialType: [], // 26大类类型
      materialClass: [], // 物资类别
      jxMaterialClass: [], // 嘉兴港物资类别
      deptno: [], // 责任部门
      typedetail: [], // 类型细目
      storehouses: [], // 库
      zones: [], // 区
      frames: [], // 架
      floors: [], // 层
      bits: [], // 位
      lottype: [], // 批次类型
      invoiceUnits: [], // 计量单位
      belongToSites: [], // 下拉框选项 - 所在单位
      materialQrCodeUrl: false, // 二维码url
      materialVisible: false,
      visibleApprove: false,
      subTitle: '修改物资代码',
      IsProhibit: '', // 判断物资代码弹窗的字段是否为只读
      IsEDIT: '',
      IsShowQr: '', // 判断二维码是否显示
      // 甲供详情弹出层
      popForm: {},

      // 物资代码弹窗的样式
      labelCol: {
        xs: { span: 28 },
        sm: { span: 9, offset: 0 }
      },
      wrapperCol: {
        xs: { span: 20 },
        sm: { span: 14 }
      },

      // 审批弹窗的样式
      labelColApprove: {
        xs: { span: 24 },
        sm: { span: 9, offset: 0 }
      },
      wrapperColApprove: {
        xs: { span: 24 },
        sm: { span: 14 }
      },

      // 选中的缺位数据id
      selectPrlineSysId: '',

      // 当前角色名(计划员)
      roleName: USER_ROLE_NAME.some(item => {
        return ['物资计划员', '计划员'].includes(item) === true
      }),
      lineTypeAll: [],
      // 采购计划类型
      queryPrlineMode: '',
      prlineNum: '',
      // 是否是退回物资
      isReturnMaterial: [],
      // 缺位数据物资标识
      oldMaterialSysId: '',
      // 禁用物资编码集合
      disabledMaterial: [],
      modifyJwspVisible: false,
      modifyJwspRecord: [],
      orgIds: []

    }
  },
  computed: {
    computedColumns () {
      const arr = this.columns.filter(item => {
        return item.check
      })
      return arr
    },
    // 新增可领数
    // computedColumns () {
    //   if (this.existRequis) {
    //     const computedColumns = []
    //     for (const item of this.columns) {
    //       computedColumns.push(item)
    //       if (item.dataIndex === 'jwspOrderQty') {
    //         computedColumns.push({
    //           title: '可领数',
    //           dataIndex: 'maxReceiveNum',
    //           scopedSlots: { customRender: 'maxReceiveNum' },
    //           width: 95
    //         })
    //       }
    //     }
    //     return computedColumns
    //   }
    //   return this.columns
    // },
    // 采购审批记录下拉框
    prlineSysIdOptions () {
      const approveParam = this.approveParam
      return approveParam.selectPrlines.map(item => {
        return {
          label: item.materialName + ' (申报单号:' + item.prlineNum + ')',
          value: item.prlineSysId
        }
      })
    },
    // 采购审批记录查询
    prlineSysIdRecord () {
      const recordPrlineSysId = this.recordPrlineSysId
      const prlineSysIdRecords = this.prlineSysIdRecords
      if (!prlineSysIdRecords.hasOwnProperty(recordPrlineSysId)) {
        this.queryApproveRecords(recordPrlineSysId)
        return false
      }
      // console.log(prlineSysIdRecords[recordPrlineSysId])
      return prlineSysIdRecords[recordPrlineSysId] || []
    },
    // 采购审批退回禁用与否
    approveReturnDisabled () {
      const dataSource = this.dataSource
      const approveParam = this.approveParam
      if (approveParam.selectMode !== '1') {
        return true
      }
      for (const select of approveParam.selectPrlines) {
        const item = dataSource.find(item => item.prlineSysId === select.prlineSysId)
        if (APPROVE_NODES_NOT_RETURN.includes(this.getApproveId(item.status))) {
          return true
        }
      }
      return false
    },
    // 采购审批取消禁用与否
    approveCancelDisabled () {
      const dataSource = this.dataSource
      const approveParam = this.approveParam
      if (approveParam.selectMode !== '1') {
        return true
      }
      for (const select of approveParam.selectPrlines) {
        const item = dataSource.find(item => item.prlineSysId === select.prlineSysId)
        if (!(APPROVE_NODES_ALLOW_CANCEL.includes(this.getApproveId(item.status)) ||
        (IS_TS_ENV && item.status === 'approval1032') || (IS_NJMZ_ENV && item.status === 'approval1025') ||
        (IS_HJ_ENV && item.status === 'approval1010'))) {
          return true
        }
      }
      return false
    }
  },
  watch: {
    // 采购审批退回禁用时
    'approveReturnDisabled': {
      handler (disabled) {
        const approveParam = this.approveParam
        if (disabled && approveParam.action === '0') {
          approveParam.action = '1'
        }
      }
    },
    // 采购审批取消禁用时
    'approveCancelDisabled': {
      handler (disabled) {
        const approveParam = this.approveParam
        if (disabled && approveParam.action === '9') {
          approveParam.action = '1'
        }
      }
    },
    // 采购审批操作更改时
    'approveParam.action': {
      handler () {
        this.approveParam.selectApprove = ''
      }
    }
  },
  created () {
    // 初始化选项获取
    this.initOptions()
  },
  mounted () {
    this.initTable('columns', 'TodoApprovePrlineTable')
  },
  methods: {
    reColumns () {
      this.popLoading = true
      this.$store.dispatch('SetTodoTableGroup', { USER_ORG_ID, PERSON_SYS_ID })
      setTimeout(() => {
        this.initTable('columns', 'TodoApprovePrlineTable')
        this.popLoading = false
      }, 500)
    },
    // 初始化选项获取
    initOptions () {
      this.isSpecialMaterials = [
        { value: 'Y', label: '是' },
        { value: 'N', label: '否' }
      ]
      // 职能部门
      baseApi.getTreeById({ id: 'dept', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.orgIds = res.result
        }
      })
      baseApi.getCommboxById({ id: 'unit' }).then(res => {
        if (res.code === '0000') {
          this.units = res.result
        }
      })
      baseApi.getCommboxById({ id: IS_BYJ_ENV ? 'laborPerson' : 'applyPerson' }).then(res => {
        if (res.code === '0000') {
          this.requestedBys = res.result
        }
      })
      // 使用方向
      baseApi.getTreeById({ id: 'usedfor' }).then(res => {
        if (res.code === '0000') {
          this.disableSelectable(res.result)
          if (IS_JX_ENV) {
            this.formatTreeData(res.result)
          }
          this.directions = res.result
        }
      })
      baseApi.getTreeById({ id: 'basCodeByClassIdAndOrgId', sqlParams: { codeClassId: 'sk_fylx' } }).then(res => {
        if (res.code === '0000') {
          this.skFylxs = res.result
        }
      })
      baseApi
        .getCommboxById({ id: 'personByRoleName', sqlParams: { byRoleName: '1', roleName: '采购员' } })
        .then(res => {
          if (res.code === '0000') {
            this.buyer = [...res.result]
            this.agents = [...res.result]
          }
        })
      if (IS_HJ_ENV) {
        baseApi
          .getCommboxById({ id: 'personByRoleId', sqlParams: { roleId: '27733271563513367' } })
          .then(res => {
            this.handler = res.result
          })
      }
      baseApi.getTreeById({ id: 'appro' }).then(res => {
        if (res.code === '0000') {
          this.status1 = [...res.result]
          this.statusDetal = [...res.result]
        }
      })
      baseApi.getCommboxById({ id: 'matrtype' }).then(res => {
        if (res.code === '0000') {
          this.materialType = res.result
        }
      })
      if (IS_WZG) {
        baseApi.getTreeById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'wg_material_class' } }).then(res => {
          if (res.code === '0000') {
            this.disableSelectable(res.result)
            this.materialClass = res.result
          }
        })
      } else if (IS_MD_ENV) {
        baseApi.getTreeById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'md_material_class' } }).then(res => {
          if (res.code === '0000') {
            this.disableSelectable(res.result)
            this.materialClass = res.result
          }
        })
      } else if (IS_SZXD_ENV) {
        baseApi.getTreeById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'sz_material_class' } }).then(res => {
          if (res.code === '0000') {
            this.disableSelectable(res.result)
            this.materialClass = res.result
          }
        })
      } else {
        baseApi.getCommboxById({ id: 'materialClass' }).then(res => {
          if (res.code === '0000') {
            this.materialClass = res.result
          }
        })
      }
      baseApi.getTreeById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'jx_material_class' } }).then(res => {
        if (res.code === '0000') {
          this.disableSelectable(res.result)
          this.jxMaterialClass = res.result
        }
      })
      baseApi
        .getCommboxById({ id: 'personByRoleName', sqlParams: { byRoleName: '1', roleName: '仓库管理员' } })
        .then(res => {
          if (res.code === '0000') {
            this.storeman = res.result
          }
        })
      baseApi.getCommboxById({ id: 'typedetail' }).then(res => {
        if (res.code === '0000') {
          this.typedetail = res.result
        }
      })
      baseApi.getCommboxById({ id: 'lottype' }).then(res => {
        if (res.code === '0000') {
          this.lottype = res.result
        }
      })
      baseApi.getCommboxById({ id: 'dept', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.deptno = res.result
        }
      })
      baseApi.getCommboxById({ id: 'unit' }).then(res => {
        if (res.code === '0000') {
          this.invoiceUnits = res.result
        }
      })
      baseApi.getCommboxById({ id: 'storehouse' }).then(res => {
        if (res.code === '0000') {
          this.storehouses = res.result
        }
      })
      baseApi.getCommboxById({ id: 'zone', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.zones = res.result
        }
      })
      baseApi.getCommboxById({ id: 'frame', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.frames = res.result
        }
      })
      baseApi.getCommboxById({ id: 'floor', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.floors = res.result
        }
      })
      baseApi.getCommboxById({ id: 'bit', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.bits = res.result
        }
      })
      baseApi.getCommboxById({ id: 'personByDept', sqlParams: { userNo: USER_OPERATOR } }).then(res => {
        if (res.code === '0000') {
          this.approveList = res.result
        }
      })
      // 所在单位
      if (IS_JYS_ENV) {
        baseApi.getCommboxById({ id: 'belongToSite' }).then(res => {
          if (res.code === '0000') {
            this.belongToSites = res.result
          }
        })
      }
      // 计划类型
      baseApi.getCommboxById({ id: 'prlineLineType' }).then(res => {
        if (res.code === '0000') {
          this.lineTypeAll = res.result
          this.existRequis = res.result.some(item => item.value === '10')
          for (const item of this.lineTypeAll) {
            item.selectable = !['10', '7', '11'].includes(item.value)
          }
        }
      })

      // 获取该单位下所有禁用物资,acyivity = N
      materialApi.getDisabledMaterial(requestBuilder()).then(res => {
        if (res.code === '0000') {
          if (res.result) {
            this.disabledMaterial = res.result
          }
        }
      })
      // 使用单位 NBCT
      if (IS_BYJ_ENV || IS_MD_ENV) {
        baseApi.getCommboxById({ id: 'usedCo' }).then(res => {
          if (res.code === '0000') {
            this.usedCo = res.result
          }
        })
      }
    },
    // 修改预估金额后同步变更 左边预估总金额
    updateEstimatePrice (PrlineNum) {
      const num = this.dataSource.filter(item => item.prlineNum === PrlineNum)
        .reduce((sum, item) => sum + Number(item.estimatePrice), 0)
      for (const arr of this.dataSource) {
        if (arr.prlineNum === PrlineNum) {
          arr.estimatePriceSum = arr.estimatePriceSum + num - arr.actualEstimatePriceSum
          arr.actualEstimatePriceSum = num
        }
      }
      for (const arr of this.options) {
        if (arr.value === PrlineNum) {
          arr.estimatePriceSum = arr.estimatePriceSum + num - arr.actualEstimatePriceSum
          arr.actualEstimatePriceSum = num
          break
        }
      }
    },
    // 查询下个审批人
    queryApproveList (param) {
      this.approveList = []
      return prlineApi.queryPrlineAssigee(requestBuilder('', param)).then(res => {
        if (res.code !== '0000') {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '审批人列表获取失败！'
          })
          return Promise.reject(res.result)
        }
        this.approveList = res.result || []
        return Promise.resolve(this.approveList)
      })
    },
    // 查询审批历史记录
    queryApproveRecords (sysId) {
      const prlineSysId = sysId
      prlineApi.getApproveRecordByNum({ prlineSysId }).then(res => {
        if (res.code === '0000') {
          this.prlineSysIdRecords = {
            ...this.prlineSysIdRecords,
            [sysId]: res.result || []
          }
        }
      })
      this.prlineSysIdRecords[sysId] = false
    },
    // 获取下拉框选项文本
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || ''
    },
    // 获取审批大类ID
    getApproveId (status) {
      for (const item of this.status) {
        if (status === item.value) {
          return item.value
        }
        if (item.children) {
          for (const item2 of item.children) {
            if (status === item2.value) {
              return item.value
            }
          }
        }
      }
    },
    // 判断当前是否已全选
    isAllSelected () {
      return this.options.length > 0 ? this.options.every(item => this.selected.includes(item.value)) : null
    },
    // 判断当前是否已选
    isItemSelected (key) {
      return this.selected.includes(key)
    },
    // 处理左侧选项事件
    doHandleSelect () {
      const selected = this.selected
      let dataSource = this.dataSource
      let selectedRows = this.selectedRows
      let selectedRowKeys = this.selectedRowKeys
      selectedRows = selectedRows.filter(item => selected.includes(item.prlineNum))
      selectedRowKeys = selectedRows.map(item => item.prlineSysId)
      dataSource = dataSource.filter(item => selected.includes(item.prlineNum))
      this.$refs.table.triggerSelect(selectedRowKeys, selectedRows)
      this.$refs.table.select(dataSource)
      this.onSelect()
    },
    // 默认全选
    onSelect () {
      const selected = this.selected
      const dataSource = this.dataSource
      let selectedRows = this.selectedRows
      let selectedRowKeys = this.selectedRowKeys
      selectedRows = dataSource.filter(item => selected.includes(item.prlineNum))
      selectedRowKeys = selectedRows.map(item => item.prlineSysId)
      this.$refs.table.triggerSelect(selectedRowKeys, selectedRows)
    },
    // 处理待办数据
    doHandleSource () {
      const options = []
      const selected = []
      const descripts = {}
      const estimatePriceSums = {}
      // 实际显示的计划的预估总价
      const actualEstimatePriceSums = {}
      for (const record of this.dataSource) {
        // 领用计划
        const flag = (record.lineType === '10' || record.lineType === '11')
        let planType = (flag ? '领用计划' : '采购计划')
        if (IS_XT_ENV && record.lineType === '11') {
          planType = '二级库领用'
        }
        // 预估总金额
        if (!estimatePriceSums[record.prlineNum]) {
          if (record.estimatePriceSum) {
            estimatePriceSums[record.prlineNum] = record.estimatePriceSum
          }
        }
        if (record.estimatePrice) {
          if (!actualEstimatePriceSums[record.prlineNum]) {
            actualEstimatePriceSums[record.prlineNum] = 0
          }
          actualEstimatePriceSums[record.prlineNum] += record.estimatePrice || 0
          actualEstimatePriceSums[record.prlineNum] = parseFloat(Math.round(actualEstimatePriceSums[record.prlineNum] * 100000000) / 100000000)
        }
        // 计划描述
        if (!descripts[record.prlineNum]) {
          if (record.description) {
            descripts[record.prlineNum] = record.description
          }
        }
        if (!selected.includes(record.prlineNum)) {
          options.push({
            createBy: record.createByName + '-' + planType,
            departmentName: record.departmentName,
            value: record.prlineNum,
            label: !IS_NJMZ_ENV
              ? '申报单号【' + record.prlineNum + '】' + (IS_DMY_ENV || IS_MD_ENV ? record.lineTypeName : planType)
              : '申报单号【' + record.prlineNum + '】'
          })
          selected.push(record.prlineNum)
        }
      }
      for (const item of options) {
        item.description = descripts[item.value] || ''
        item.estimatePriceSum = estimatePriceSums[item.value] || 0
        item.actualEstimatePriceSum = actualEstimatePriceSums[item.value] || 0
      }
      for (let i = 0; i < this.selected.length; i++) {
        if (!selected.includes(this.selected[i])) {
          this.selected.splice(i--, 1)
        }
      }
      this.forced = false
      this.options = options
    },
    // 查询待办数据
    doQuerySource (force) {
      this.cellState = false
      this.forced = force === true
      this.insideTableClose()
      this.$refs.AddPrlineDrawer.hiddenProcureDrawer()
      this.$refs.AddPrlineDrawer.hiddenMaterialDrawer()
      this.$refs.EditPrlineDrawer.hiddenProcureDrawer()
      this.$refs.EditPrlineDrawer.hiddenMaterialDrawer()
      this.$refs.table.triggerSelect([], [])
      this.$refs.table.refresh(true)
      this.doDrawerClose()
      this.cellChangeSource = []
      this.insideTableVisible = false
    },
    // 清理待办缓存
    doCleanAll () {
      this.insideTableVisible = false
      this.options = []
      this.selected = []
      this.dataSource = []
      this.selectedRows = []
      this.selectedRowKeys = []
      this.$refs.table.select([])
      this.approveParam = {
        selectPrlines: [],
        approveSuggest: '',
        selectApprove: '',
        selectMode: '',
        action: '1',
        contractSysId: ''
      }
      this.approveList = []
      this.prlineSysIdRecords = {}
      this.recordPrlineSysId = ''
      this.loading.approve = false
      this.loading.query = false
      this.visible = false
      this.forced = true
    },
    // 左侧栏选项全选
    doAllSelected () {
      this.selected = this.options.map(item => item.value)
      this.doHandleSelect()
    },
    // 左侧栏选项全不选
    doAllNotSelect () {
      this.selected = []
      this.doHandleSelect()
    },
    // 左侧栏单个选项反选
    doItemReverse (key) {
      const index = this.selected.indexOf(key)
      index === -1 ? this.selected.push(key) : this.selected.splice(index, 1)
      this.doHandleSelect()
    },
    // 打开审批弹框
    doDrawerOpen () {
      if (this.dataSource.length === 0) {
        this.$message.error('当前页没有需要审批的采购计划！')
        return
      }
      if (this.selectedRows.length === 0) {
        this.$message.error('请选择需要审批的采购计划！')
        return
      }
      if (IS_SZXD_ENV) {
        const set = new Set(this.selectedRows.map(item => item.lineType))
        if (set.has('10') && set.size > 1) {
          this.$message.error('领用与非领用不可一起审批！')
          return
        }
      }
      let autoTag = null
      const origin = 'prline'
      for (const item of this.selectedRows) {
        if (autoTag === null || autoTag === item.autoTag) {
          autoTag = item.autoTag
        } else {
          this.$message.error('当前页所选采购计划的审批人模式不一致！')
          return
        }
      }

      if (autoTag !== '1') {
        this.approveParam.showApprove = true
        this.queryApproveList({ autoTag, origin, uuids: [this.selectedRows[0].prlineSysId] })
      }

      const selectPrlines = [...this.selectedRows]
      this.approveParam.selectPrlines = selectPrlines
      this.recordPrlineSysId = selectPrlines[0].prlineSysId
      this.approveParam.selectMode = '1'
      if (IS_HJ_ENV) {
        const row = this.selectedRows.find(item => item.status === 'approval1031')
        if (row) {
          this.approveLabel = '选择经办人'
          const set = new Set(this.selectedRows.map(item => item.hjHandler))
          console.log(set)
          if (set.size > 1) {
            setTimeout(() => {
              this.$info({
                title: '提醒',
                content: '经办人存在多个'
              })
            }, 100)
          } else {
            for (const val of set.values()) { this.approveParam.selectApprove = val }
          }
        } else {
          this.approveLabel = '下个审批人'
        }
        // 先判断当前当前是否为经办人审批流程
        const filterSelectedRows1 = this.selectedRows.filter(item => item.status === 'approval1109')
        const filterSelectedRows2 = this.selectedRows.filter(item => item.status !== 'approval1109')
        if (filterSelectedRows1.length > 0 && filterSelectedRows2 > 0) {
          return this.$message.error(`单号为[${filterSelectedRows2[0].prlineNum}]的计划不能与经办人流程一起审批,请重新选择`)
        }
        if (filterSelectedRows1.length > 0) {
          prlineApi.findContractByPrline(requestBuilder('', this.selectedRows)).then(res => {
            if (res.code !== '0000') {
              this.$notification.error({
                message: '系统消息',
                description: res.message || '系统繁忙'
              })
              return Promise.reject(false)
            }
            this.visible = true
            return res
          }).then(res => {
            this.contractList = res.result || []
            if (this.contractList.length > 0) {
              this.approveParam.contractSysId = this.contractList[0].contractSysId
            }
            this.visible = true
          }).catch(err => {
            console.log(err)
          })
        } else {
          this.visible = true
        }
      } else {
        this.visible = true
      }
    },
    // 关闭审批弹框
    doDrawerClose () {
      this.visible = false
      this.approveList = []
      this.prlineSysIdRecords = {}
      this.recordPrlineSysId = ''
      this.approveParam = {
        showApprove: false,
        selectPrlines: [],
        approveSuggest: '',
        selectApprove: '',
        selectMode: '',
        action: '1',
        contractSysId: ''
      }
      this.contractList = []
      this.approvelLoading = false
    },
    // 审批待办事项
    doApprove () {
      const approveParam = this.approveParam
      // 手动模式须前端指定审批人
      if (approveParam.showApprove && !approveParam.selectApprove) {
        if (
          !['0', '9'].includes(approveParam.action) &&
          !approveParam.selectPrlines.every(item =>
            [APPROVE_STATUS_CONFIRM_CODE].includes(this.getApproveId(item.status))
          )
        ) {
          this.$message.error('请指定下个审批人！')
          return
        }
      }
      if (IS_BYJ_ENV && approveParam.action === '1') {
        if (approveParam.selectPrlines.find(v => v.status === 'approval1025' &&
        v.materialNum.length === 4)) {
          this.$message.error('选择的计划存在物资代码缺位情况，请完善缺位')
          return
        }
      }
      this.approvelLoading = true
      const param = {
        op: USER_OPERATOR,
        pattern: approveParam.selectMode === '1' ? 'approByNum' : 'approAll',
        userId: approveParam.showApprove && approveParam.action !== '9' ? approveParam.selectApprove : '',
        list: approveParam.selectPrlines.map(item => item.prlineSysId),
        msg: approveParam.approveSuggest,
        flag: approveParam.action,
        origin: 'prline',
        todoAppro: true,
        contractSysId: IS_HJ_ENV ? approveParam.contractSysId : ''
      }
      prlineApi.approveProcurePlan(requestBuilder('', param)).then(res => {
        if (res.code !== '0000') {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '审批失败！'
          })
        } else {
          this.$notification.success({
            message: '系统消息',
            description: res.message || '审批成功！'
          })
        }
        this.doQueryAll()
      })
        .then(() => {
          this.doDrawerClose()
        }).finally(() => {
          this.approvelLoading = false
        })
    },
    // 禁用下拉框父类选项
    disableSelectable (array) {
      for (const item of array) {
        if (item.children && item.children.length > 0) {
          item.selectable = false
          this.disableSelectable(item.children)
        }
      }
    },
    formatTreeData (arr, name) {
      for (const item of arr) {
        item.title = item.label
        item.label = (name ? `${name}-` : '') + item.title
        if (item.children) {
          this.formatTreeData(item.children, (name ? `${name}-` : '') + item.title)
        }
      }
    },
    filterOption (input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    // 下拉框物资细目-物资类别联动
    handletypeDetailChange (value) {
      // console.log('typeDetail===========' + value)
      if (value === 'type_detail_1') {
        this.form.setFieldsValue({
          materialClass: 'material_class_1'
        })
      } else if (value === 'type_detail_2') {
        this.form.setFieldsValue({
          materialClass: 'material_class_4'
        })
      } else if (value === 'type_detail_3') {
        this.form.setFieldsValue({
          materialClass: 'material_class_3'
        })
      } else if (value === 'type_detail_4') {
        this.form.setFieldsValue({
          materialClass: 'material_class_2'
        })
      }
    },
    // 下拉框库-区联动
    handleStorehouseChange (value) {
      // this.cities = cityData[value]
      // this.secondCity = cityData[value][0]
      this.form.setFieldsValue({
        zone: '',
        frame: '',
        floor: '',
        bit: ''
      })
      this.zones = []
      baseApi
        .getCommboxById({ id: 'zone', sqlParams: { byStorehouseSysId: '1', storehouseSysId: value } })
        .then(res => {
          if (res.code === '0000') {
            // this.form.setFieldsValue({
            //   zone: res.result.length > 0 ? res.result[0].value : ''
            // })
            this.zones = res.result
          }
        })
    },
    // 下拉框区-架联动
    handleZoneChange (value) {
      // this.cities = cityData[value]
      // this.secondCity = cityData[value][0]
      this.form.setFieldsValue({
        frame: '',
        floor: '',
        bit: ''
      })
      this.frames = []
      baseApi.getCommboxById({ id: 'frame', sqlParams: { byZoneSysId: '1', zoneSysId: value } }).then(res => {
        if (res.code === '0000') {
          // this.form.setFieldsValue({
          //   zone: res.result.length > 0 ? res.result[0].value : ''
          // })
          this.frames = res.result
        }
      })
    },
    // 下拉框架-层联动
    handleFrameChange (value) {
      // this.cities = cityData[value]
      // this.secondCity = cityData[value][0]
      this.form.setFieldsValue({
        floor: '',
        bit: ''
      })
      this.floors = []
      baseApi.getCommboxById({ id: 'floor', sqlParams: { byFrameSysId: '1', frameSysId: value } }).then(res => {
        if (res.code === '0000') {
          // this.form.setFieldsValue({
          //   zone: res.result.length > 0 ? res.result[0].value : ''
          // })
          this.floors = res.result
        }
      })
    },
    // 下拉框层-位联动
    handleFloorChange (value) {
      // this.cities = cityData[value]
      // this.secondCity = cityData[value][0]
      this.form.setFieldsValue({
        bit: ''
      })
      this.bits = []
      baseApi.getCommboxById({ id: 'bit', sqlParams: { byFloorSysId: '1', floorSysId: value } }).then(res => {
        if (res.code === '0000') {
          // this.form.setFieldsValue({
          //   zone: res.result.length > 0 ? res.result[0].value : ''
          // })
          this.bits = res.result
        }
      })
    },
    handleEdit (record) {
      this.$refs.createModal.edit(record)
    },
    // 物资税率改变
    handletaxrateBlur (value) {
      // 根据税后单价和税率计算税前单价
      if (
        this.form.getFieldValue('taxrate') &&
        this.form.getFieldValue('taxrate') &&
        this.form.getFieldValue('unitcostAfterTax') &&
        this.form.getFieldValue('unitcostAfterTax')
      ) {
        this.form.setFieldsValue({
          refCost: this.form.getFieldValue('unitcostAfterTax') / ((+this.form.getFieldValue('taxrate') || 0) + 1)
        })
      }
    },
    // 物资参考单价（税前单价）变化
    handlerefCostBlur () {
      // 根据税前单价和税率计算税后单价
      if (
        this.form.getFieldValue('refCost') === Number(0) &&
        this.form.getFieldValue('refCost') === Number(0)
      ) {
        this.form.setFieldsValue({
          unitcostAfterTax: Number(0)
        })
      } else if (
        this.form.getFieldValue('refCost') &&
        this.form.getFieldValue('refCost') &&
        this.form.getFieldValue('taxrate') &&
        this.form.getFieldValue('taxrate')
      ) {
        if (this.form.getFieldValue('refCost') === '0') {
          this.form.setFieldsValue({
            unitcostAfterTax: '0'
          })
        } else {
          this.form.setFieldsValue({
            unitcostAfterTax: this.form.getFieldValue('refCost') * ((+this.form.getFieldValue('taxrate') || 0) + 1)
          })
        }
      }
    },
    // 物资税后单价改变
    handleunitcostAfterTaxBlur () {
      // 根据税后单价和税率计算税前单价
      if (
        this.form.getFieldValue('unitcostAfterTax') &&
        this.form.getFieldValue('unitcostAfterTax') &&
        this.form.getFieldValue('taxrate') &&
        this.form.getFieldValue('taxrate')
      ) {
        this.form.setFieldsValue({
          refCost: this.form.getFieldValue('unitcostAfterTax') / ((+this.form.getFieldValue('taxrate') || 0) + 1)
        })
      }
    },
    handlematerialNumBlur () {
      // 物资编码账本号自动设置为物资代码前5位数
      this.form.setFieldsValue({
        materialnum5: this.form.getFieldValue('materialNum').substring(0, 5)
      })
      // 判断补全的物资编码是否已存在，若存在则自动补全信息，不存在则新增物资编码
      materialApi.queryMaterialByNum(requestBuilder('', Object.assign({ materialNum: this.form.getFieldValue('materialNum') })))
        .then(res => {
          this.isActivity = ''
          if (res.result && res.result.activity === 'Y') {
            this.isActivity = 'Y'
            const data = res.result
            this.form.setFieldsValue({
              materialSysId: data.materialSysId,
              materialNum: data.materialNum,
              materialName: data.materialName,
              materialClass: data.materialClass,
              jmodel: data.jmodel,
              buyer: data.buyer,
              storeman: data.storeman,
              deptno: data.deptno,
              isUniOrder: data.isUniOrder,
              isResupply: data.isResupply,
              isFixedAssets: data.isFixedAssets,
              issueUnit: data.issueUnit,
              materialnum5: data.materialnum5,
              materialType: data.materialType,
              jxMaterialType: data.jxMaterialType,
              maxAmount: data.maxAmount,
              minAmount: data.minAmount,
              lotType: data.lotType,
              typeDetail: data.typeDetail,
              orderUnit: data.orderUnit,
              refCost: data.refCost,
              isReuse: data.isReuse,
              isLabourSupply: data.isLabourSupply,
              isStop: data.isStop,
              activity: data.activity,
              yardId: data.yardId,
              zone: data.zone,
              frame: data.frame,
              floor: data.floor,
              bit: data.bit,
              status: data.status,
              belongToSite: data.belongToSite,
              taxrate: data.taxrate,
              unitcostAfterTax: data.unitcostAfterTax
            })
          } if (res.result && res.result.activity === 'N') {
            this.isActivity = 'N'
          }
        })
    },
    // 打开物资详细页弹窗
    openDrawer (record) {
      this.materialVisible = true
      this.materialQrCodeUrl = ''
      this.$nextTick(() => {
        // 生产二维码
        this.queryMaterialQrCode(record)
        // 显示二维码
        this.IsShowQr = '1'
        this.IsProhibit = '1'
        this.form.setFieldsValue({
          materialSysId: record.materialSysId,
          materialNum: record.materialNum,
          materialName: record.materialName,
          materialClass: record.materialClass,
          jmodel: record.jmodel,
          buyer: record.buyer,
          storeman: record.storeman,
          deptno: record.deptno,
          isUniOrder: record.isUniOrder,
          isResupply: record.isResupply,
          isFixedAssets: record.isFixedAssets,
          issueUnit: record.issueUnit,
          materialnum5: record.materialnum5,
          materialType: record.materialType,
          jxMaterialType: record.jxMaterialType,
          maxAmount: record.maxAmount,
          minAmount: record.minAmount,
          lotType: record.lotType,
          typeDetail: record.typeDetail,
          orderUnit: record.orderUnit,
          refCost: record.refCost,
          isReuse: record.isReuse,
          isLabourSupply: record.isLabourSupply,
          isStop: record.isStop,
          activity: 'Y', // 缺位物资编码activity是N，补全编码保存将其设为Y
          yardId: record.yardId,
          zone: record.zone,
          frame: record.frame,
          floor: record.floor,
          bit: record.bit,
          status: record.status,
          belongToSite: record.belongToSite,
          taxrate: record.taxrate,
          unitcostAfterTax: record.unitcostAfterTax
        })
        this.form.setFieldsValue({
          materialnum5: record.materialNum ? record.materialNum.substring(0, 5) : ''
        })
        // this.getRoleListByUserNo(record.userNo)
        // this.temppass = record.password
        this.actionFlag = 'update'
      })
    },
    // 关闭物资代码弹框
    hiddenMaterialDrawer () {
      this.materialVisible = false
      this.materialQrCodeUrl = ''
      this.IsEDIT = ''
    },
    // 生成二维码
    queryMaterialQrCode (record) {
      const param = requestBuilder('', {
        materialNum: record.materialNum,
        orgId: record.orgId
      })
      materialApi.getMaterialQrCode(param).then(res => {
        if (res && res.data) {
          this.materialQrCodeUrl = window.URL.createObjectURL(res.data)
        }
      })
    },
    downloadMaterialQrCode () {
      if (!this.materialQrCodeUrl) {
        this.$message.error('暂无物资二维码！')
        return
      }
      const link = document.createElement('a')
      const filename = '物资二维码.jpg'
      document.body.appendChild(link)
      link.style.display = 'none'
      link.download = filename
      link.href = this.materialQrCodeUrl
      link.click()
      link.remove()
    },
    // 保存数据
    doSubmit () {
      const {
        form: { validateFields }
      } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        // 如果输入数据都合规
        if (!errors) {
          this.params = { ...values, issueUnit: values.issueUnit, orderUnit: values.issueUnit }
          this.getResult(this.params)
        } else {
          this.confirmLoading = false
        }
      })
    },
    getResult (params) {
      materialApi
        .modifyMaterialInfo(requestBuilder(this.actionFlag, this.params, '0', '0'))
        .then(res => {
          // 将material中相关更新后的数据反写到prline中
          this.selectPrlineInfo.materialNum = params.materialNum
          this.selectPrlineInfo.unitCost = params.refCost
          this.selectPrlineInfo.unitcostAftertax = params.unitcostAfterTax
          this.selectPrlineInfo.agent = params.buyer
          this.selectPrlineInfo.orderUnit = params.issueUnit
          this.selectPrlineInfo.materialSysId = params.materialSysId
          if (res.code === '0000') {
            // 修改prline表中的materialNum,materialSysId
            prlineApi.modifyProcurePlan(requestBuilder('update', [this.selectPrlineInfo])).then(res => {
              if (res.code !== '0000') {
                this.$notification.error({
                  message: '系统消息',
                  description: '物资信息修改失败！'
                })
              } else {
                this.$notification.success({
                  message: '系统消息',
                  description: '物资信息修改成功！'
                })
              }
              // 若使用的是已经存在的物资编码，则需要删除弃用的缺位物资编码(条件为4位)
              if (this.oldMaterialSysId !== this.selectPrlineInfo.materialSysId && this.oldMaterialNum.length === 4) {
                materialApi
                  .deleteAbsenceMaterial({ materialSysId: this.oldMaterialSysId, materialNum: this.oldMaterialNum })
                  .then(res => {
                    if (res.code !== '0000') {
                      this.$message.error('发生异常 ' + res.message)
                    }
                  })
              }
            })
          } else {
            this.$message.error('发生异常 ' + res.message)
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.confirmLoading = false
          this.materialVisible = false
          this.$refs.table.refresh()
        })
    },
    wzgMateNum (num) {
      if (IS_WZG) {
        baseApi.selectMateNum(requestBuilder('', { materialClass: num })).then(res => {
          this.form.setFieldsValue({
            materialNum: res.result,
            materialnum5: res.result ? res.result.substring(0, 5) : ''
          })
        })
      }
    },
    jxMateNum (num) {
      if (IS_JX_ENV) {
        baseApi.selectJxMateNum(requestBuilder('', { jxMaterialClass: num })).then(res => {
          this.form.setFieldsValue({
            materialNum: res.result
          })
        })
      }
    },
    isShowDeleteAndUpload (record) {
      return [APPROVE_STATUS_READY_CODE, APPROVE_STATUS_START_CODE].includes(this.getApproveId(this.uploadRecord.status))
    },
    // 校验修改的物资编码是否重复
    checkMaterialNum (rule, value, callback) {
      if (value.length <= 0) {
        callback(new Error('物资编码不能为空'))
        return
      }
      if (value.length < 8) {
        callback(new Error('编码编码至少为8位'))
        return
      }
      if (this.disabledMaterial.indexOf(value) !== -1) {
        callback(new Error('编码为为未激活状态，请重输'))
        return
      }
      callback()
    },
    // 日期相加
    addDate (date, days) {
      if (days === undefined || days === '') {
        days = 1
      }
      var newDate = new Date(date)
      newDate.setDate(newDate.getDate() + days)
      // 月份从0开始所以需要+1
      var month = newDate.getMonth() + 1
      var day = newDate.getDate()
      return newDate.getFullYear() + '-' + month + '-' + day
    },
    // 日期相减获得天数
    getNumberOfDays (date1, date2) {
    // date1：开始日期，date2结束日期
      var a1 = Date.parse(new Date(date1))
      var a2 = Date.parse(new Date(date2))
      // 时间戳相减，然后除以天数
      var day = parseInt((a2 - a1) / (1000 * 60 * 60 * 24))
      return day
    },
    doChangeMode () {
      this.queryParam.lineType = this.queryPrlineMode
      this.queryParam.prlineNum = this.prlineNum
      this.doQuerySource()
    },
    onClose () {
      this.modifyJwspVisible = false
    },
    afterVisibleChange (val) {
      console.log('modifyJwspVisible', val)
    },
    clearModel () {
      this.formInfo = {}
    },
    async clickJG (record) {
      const res = await prlineApi.queryHJPMCInfoDetail(requestBuilder('', { pMCMatapplyId: record.pMCMatapplyId }))
      if (res.code !== '0000') {
        this.$notification.error({
          message: '系统消息',
          description: res.message || '系统繁忙！'
        })
      } else {
        this.$nextTick(() => {
          this.popForm = res.result[0]
        })
      }
    },
    accMul (arg1, arg2) {
      let m = 0
      const s1 = arg1.toString()
      const s2 = arg2.toString()
      try { m += s1.split('.')[1].length } catch (e) { }
      try { m += s2.split('.')[1].length } catch (e) { }
      return Number(s1.replace('.', '')) * Number(s2.replace('.', '')) / Math.pow(10, m)
    },
    insideTableOpen () {
      this.insideTableVisible = true
    },
    insideTableClose () {
      this.insideTableVisible = false
    },
    reSetDrawer () {
      this.searchForm = {
        requestedBy: '',
        agent: '',
        materialName: ''
      }
    },
    openRecordDrawer (record) {
      this.prlineSysId = record.prlineSysId
      this.$refs.ApproveRecordsDrawer.doOpen()
    },
    addPrline () {
      // 判断是否勾选了记录
      if (this.selected.length === 0) {
        return this.$message.error('请选择申报单号!')
      }
      if (this.selected.length > 1) {
        return this.$message.error('请勿多选申报单号!')
      }
      this.$refs.AddPrlineDrawer.openProcureDrawer({}, 'insert', this.selected[0])
    },
    editPrline () {
      if (this.selectedRows.length !== 1) {
        this.$message.error('请选择一条数据进行修改')
        return
      }
      this.$refs.EditPrlineDrawer.openProcureDrawer(this.selectedRows[0], 'update', null)
    },
    openUpload (record) {
      this.uploadRecord = record
      this.$refs.appendix.openUploadDrawer(record)
    }
  }
}
</script>

<style lang="less" scoped>
@import './TodoApproveStyle.less';
::v-deep {
  .ant-table-tbody > tr.linkRowStyle2 {
    & > td,
    &.ant-table-row-selected > td {
      background-color: #e7e081;
    }
  }
  .ant-table-tbody > tr.emergencyStyle {
    & > td,
    &.ant-table-row-selected > td {
      background-color: #9acf85;
    }
  }
  .ant-table-tbody > tr.deadLineStyle {
    & > td,
    &.ant-table-row-selected > td {
      background-color: #ddb5dfec;
    }
  }
  .ant-table-tbody > tr.returnStyle {
    & > td,
    &.ant-table-row-selected > td {
      background-color: #a5e7e1;
    }
  }
  .ant-table-tbody > tr.modifyJwspStyle {
    & > td,
    &.ant-table-row-selected > td {
      background-color: #c5dbec;
    }
  }
    .ant-table-tbody > tr.attachmentExists {
    & > td,
    &.ant-table-row-selected > td {
      background-color: #9f9f9f;
    }
  }
}
a.disabled {
    pointer-events: none;
    color: #999;
    -moz-opacity: 0.5;
    opacity: 0.5;
}
.popStyle {
  width: 500px;
}
</style>
