<template>
  <CrewCard title="拆分采购">
    <template slot="text">
      <div class="form-box">
        <!-- <a-month-picker
          v-model="paramData.planMonth"
          :disabled="disabled"
          :getCalendarContainer="triggerNode => triggerNode.parentNode"
          dropdownClassName="rangePickerIceGai"
          style="width: 100%;"
          placeholder="选择月份"
          @change="onChange"
        /> -->
        <a-range-picker v-model="dates" :getCalendarContainer="triggerNode => triggerNode.parentNode" dropdownClassName="rangePickerIceGai" style="width: 100%;" @change="doSearch" />
      </div>
      <a-spin :spinning="loading">
        <div class="flexBox" @click="showModal">
          <div class="left">
            <img class="img" src="../../pic//set8.png">
          </div>
          <div class="right">
            <div class="num">
              <countTo
                :startVal="0"
                class="topNum"
                :endVal="record.count"
                prefix=""
                suffix=""/>
              <span class="descript">份</span>
              <!-- <div>
                <countTo
                  :startVal="0"
                  class="subNum"
                  style="font-size: 12px;"
                  :endVal="record.lineCostSum"
                  prefix="￥"
                  suffix=""/>
              </div> -->
            </div>
          </div>
        </div>
      </a-spin>
      <div ref="initwarp" :class="{ initwarp: styleName === 'themeb' }">
        <a-modal
          title="查看详情"
          :visible="visible"
          :confirm-loading="confirmLoading"
          :getContainer="() => $refs.initwarp"
          width="1000px"
          :bodyStyle="{ height: '600px' }"
          :footer="null"
          @cancel="handleCancel"
        >
          <s-table
            ref="table"
            :columns="columns"
            :components="components"
            :data="loadData"
            :scroll="scroll"
            :rowClassName="rowClassName"
            :customRow="rowClick"
            :bordered="false"
            :pageSizeOptions="pageSizeOptions"
            :pageSize="defaultPageSize"
            :showPagination="true"
            no-striped
            rowKey="uuid"
          >
            <span
              slot="serial"
              slot-scope="text, record, index"
            >{{ index + 1 }}</span>
          </s-table>
        </a-modal>
      </div>
    </template>
  </CrewCard>
</template>
<script>
import CrewCard from '../Crew/CrewCard'
import countTo from 'vue-count-to'
import moment from 'moment'
import { requestBuilder } from '@/utils/util'
import { STable } from '@/components'
import * as earlyWarningApi from '@/api/material/earlyWarning'
import * as cockpitApi from '@/api/material/cockpit'
export default {
  name: 'CrewIssuePlan',
  components: {
    CrewCard,
    countTo,
    STable
  },
  props: {
    styleName: {
      type: String,
      default: () => {
        return ''
      }
    },
    disabled: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data () {
    this.components = {
      header: {
        cell: (h, props, children) => {
          const { key, ...restProps } = props
          const col = this.columns.find(col => {
            const k = col.dataIndex || col.key
            return k === key
          })

          if (!col || !col.width) {
            return h('th', { ...restProps }, [...children])
          }

          const dragProps = {
            key: col.dataIndex || col.key,
            class: 'table-draggable-handle',
            attrs: {
              w: 10,
              x: col.width,
              z: 1,
              axis: 'x',
              draggable: true,
              resizable: false
            },
            on: {
              dragging: (x, y) => {
                col.width = Math.max(x, 1)
              }
            }
          }
          const drag = h('vue-draggable-resizable', { ...dragProps })
          return h('th', { ...restProps, class: 'resize-table-th' }, [...children, drag])
        }
      }
    }
    return {
      paramData: {
        planMonth: moment(new Date())
      },
      loading: false,
      record: {
        count: 0
        // lineCostSum: 0
      },
      dates: [],
      visible: false,
      confirmLoading: false,
      // 表格数据
      dataSource: [],
      columns: [
        // {
        //   title: '序号',
        //   scopedSlots: { customRender: 'serial' },
        //   dataIndex: 'type',
        //   align: 'center',
        //   ellipsis: true,
        //   width: 50
        // },
        // {
        //   title: '计划号',
        //   dataIndex: 'prlineNum',
        //   align: 'center',
        //   ellipsis: true,
        //   width: 150
        // },
        // {
        //   title: '物资编码',
        //   dataIndex: 'materialNum',
        //   align: 'center',
        //   ellipsis: true,
        //   width: 150
        // },
        // {
        //   title: '物资名称',
        //   dataIndex: 'materialName',
        //   align: 'center',
        //   ellipsis: true,
        //   width: 150
        // },
        // {
        //   title: '型号',
        //   dataIndex: 'jmodel',
        //   align: 'center',
        //   ellipsis: true,
        //   width: 150
        // },
        // {
        //   title: '计划类型',
        //   dataIndex: 'lineTypeName',
        //   align: 'center',
        //   ellipsis: true,
        //   width: 150
        // },
        // {
        //   title: '申报人',
        //   dataIndex: 'requestedByName',
        //   align: 'center',
        //   ellipsis: true,
        //   width: 150
        // },
        // {
        //   title: '创建日期',
        //   dataIndex: 'createDate',
        //   align: 'center',
        //   ellipsis: true,
        //   sorter: true,
        //   width: 150
        // }
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' },
          dataIndex: 'type',
          align: 'center',
          ellipsis: true,
          width: 50
        },
        {
          title: '物资编码',
          dataIndex: 'materialNum',
          scopedSlots: { customRender: 'materialNum' },
          align: 'center',
          ellipsis: true,
          width: 150
        },
        {
          title: '物资名称',
          dataIndex: 'materialName',
          align: 'center',
          ellipsis: true,
          width: 150
        },
        // {
        //   title: '物资类型',
        //   dataIndex: 'materialTypeName',
        //   align: 'center',
        //   ellipsis: true,
        //   width: '15%'
        // },
        {
          title: '询价单号',
          dataIndex: 'rfqlineNum',
          scopedSlots: { customRender: 'ellipsis2' },
          align: 'center',
          sorter: true,
          width: 600
        },
        {
          title: '日期',
          dataIndex: 'createDate',
          scopedSlots: { customRender: 'createDate' },
          align: 'center',
          ellipsis: true,
          width: 200
        }
      ],
      scroll: {
        x: '100%',
        y: 'calc(60vh)',
        scrollToFirstRowOnChange: false
      },
      selectedRows: [],
      selectedRowKeys: [],
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20', '30', '40'],
      loadData: parameter => {
        const param = requestBuilder(
          'tender',
          {
            startTime: this.dates.length > 0 ? this.dates[0].format('YYYY-MM-DD') : '',
            endTime: this.dates.length > 0 ? this.dates[1].format('YYYY-MM-DD') : ''
          },
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder
        )
        return earlyWarningApi.earlyWarningSplitPurchaseDetail(param).then(res => {
          if (res.code === '0000') {
            this.dataSource = res.result.data
            return res.result
          } else {
            this.$message.error(res.message)
          }
        })
      },
      rowClick: record => ({
        on: {}
      }),
      rowClassName: record => {
        return 'tablerow'
      }
    }
  },
  created () {
    this.doSearch()
  },
  methods: {
    showModal () {
      if (this.disabled) {
        return
      }
      this.visible = true
      if (this.$refs.table) {
        this.$refs.table.refresh()
      }
    },
    handleCancel (e) {
      this.visible = false
    },
    onChange () {
      this.doSearch()
    },
    doSearch () {
      this.loading = true
      const param = requestBuilder(
        '',
        {
          startTime: this.dates.length > 0 ? this.dates[0].format('YYYY-MM-DD') : '',
          endTime: this.dates.length > 0 ? this.dates[1].format('YYYY-MM-DD') : ''
        }
      )
      earlyWarningApi.earlyWarningSplitPurchase(param).then(res => {
        this.record.count = res.result.length || 0
        // this.record.lineCostSum = res.result.lineCostSum || 0
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
  .form-box {
    width: 100%;
    padding: 0 10px;
  }
  .flexBox {
    width: 100%;
    height: 100px;
    display: flex;
    padding: 0 10px;
    justify-content: space-between;
    align-items: center;
    /* background-color: rgb(235, 235, 235); */
    .left {
      flex: 1;
      width: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .right {
      width: 50%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
    .img {
      width: 60px;
      height: 60px;
    }
    .num {
      text-align: right;
      .topNum {
        font-size: 30px;
        font-weight: 700;
        margin: 0;
        padding: 0;
      }
      .descript {
        font-size: 10px;
        opacity: 0.8;
      }
      .subNum {
        font-size: 10px;
        opacity: 0.8;
      }
    }
  }
</style>
