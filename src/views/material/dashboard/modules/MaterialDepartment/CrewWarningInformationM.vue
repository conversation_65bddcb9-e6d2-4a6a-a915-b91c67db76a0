<template>
  <CrewCard title="预警信息">
    <template slot="text">
      <div class="box">
        <div style="width: 100%;">
          <div ref="selectwarp" class="searchBox" @click.stop>
            <a-date-picker
              mode="year"
              placeholder="选择年份"
              :open="yearShowOne"
              :allowClear="true"
              format="YYYY"
              :getCalendarContainer="triggerNode => triggerNode.parentNode"
              dropdownClassName="rangePickerIceGai"
              @change="panelChangeOne"
              @openChange="openChangeOne"
              @panelChange="panelChangeOne"
              v-model="queryParam.year"
              style="width: 45%;"
            />
            <a-select
              @change="queryCount"
              :getPopupContainer="triggerNode => triggerNode.parentNode"
              dropdownClassName="rangePickerIceGai"
              v-model.trim="queryParam.scene"
              style="width: 45%;">
              <a-select-option key="1" :value="EARLY_WARNING_JTOA_SCENE">{{ EARLY_WARNING_JTOA_SCENE }}</a-select-option>
              <!-- <a-select-option key="2" :value="EARLY_WARNING_TENDER_SCENE">{{ EARLY_WARNING_TENDER_SCENE }}</a-select-option> -->
              <a-select-option key="3" :value="EARLY_WARNING_RFQLINE_SCENE">{{ EARLY_WARNING_RFQLINE_SCENE }}</a-select-option>
              <a-select-option key="4" :value="EARLY_WARNING_CONTRACT_SCENE">{{ EARLY_WARNING_CONTRACT_SCENE }}</a-select-option>
            </a-select>
          </div>
          <!-- <a-range-picker v-model="dates" dropdownClassName="rangePickerIceGai" @change="onChange" /> -->
          <a-spin :spinning="loading">
            <div class="boxFlex" @click="showModal">
              <div class="num">
                <countTo
                  :startVal="0"
                  :endVal="num"
                  prefix=""
                  suffix=""/>
              </div>
              <div class="text">预警信息数</div>
            </div>
          </a-spin>
        </div>
      </div>
      <div ref="initwarp" :class="{ initwarp: styleName === 'themeb' }">
        <a-modal
          title="预警信息详情"
          :visible="visible"
          :confirm-loading="confirmLoading"
          :getContainer="() => $refs.initwarp"
          width="1000px"
          :bodyStyle="{ height: '600px' }"
          @cancel="handleCancel"
        >
          <s-table
            ref="table"
            :columns="computedColumns"
            :components="components"
            :data="loadData"
            :scroll="scroll"
            :rowClassName="rowClassName"
            :customRow="rowClick"
            :bordered="false"
            :pageSizeOptions="pageSizeOptions"
            :pageSize="defaultPageSize"
            :showPagination="true"
            no-striped
            rowKey="uuid"
          >
            <span
              slot="serial"
              slot-scope="text, record, index"
            >{{ index + 1 }}</span>
            <!-- <span
              slot="materialNum"
              slot-scope="text, record"
            >
              <a href="javascript:void(0)" @click="jump(record)">{{ text }} </a>
            </span> -->
          </s-table>
          <template slot="footer">
            <a-button class="btnExp" :loading="loadingExport" @click="doExport">导出</a-button>
          </template>
        </a-modal>
      </div>
    </template>
  </CrewCard>
</template>
<script>
import CrewCard from '../Crew/CrewCard'
import countTo from 'vue-count-to'
import * as earlyWarningApi from '@/api/material/earlyWarning'
import { requestBuilder } from '@/utils/util'
import { STable } from '@/components'
import moment from 'moment'
import '@/style/baseColor.less'
import {
  EARLY_WARNING_JTOA_SCENE,
  // EARLY_WARNING_TENDER_SCENE,
  EARLY_WARNING_RFQLINE_SCENE,
  EARLY_WARNING_CONTRACT_SCENE
} from '@/store/variable-types'
export default {
  name: 'CrewWarningInformationM',
  components: {
    CrewCard,
    countTo,
    STable
  },
  props: {
    styleName: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data () {
    this.components = {
      header: {
        cell: (h, props, children) => {
          const { key, ...restProps } = props
          const col = this.columns.find(col => {
            const k = col.dataIndex || col.key
            return k === key
          })

          if (!col || !col.width) {
            return h('th', { ...restProps }, [...children])
          }

          const dragProps = {
            key: col.dataIndex || col.key,
            class: 'table-draggable-handle',
            attrs: {
              w: 10,
              x: col.width,
              z: 1,
              axis: 'x',
              draggable: true,
              resizable: false
            },
            on: {
              dragging: (x, y) => {
                col.width = Math.max(x, 1)
              }
            }
          }
          const drag = h('vue-draggable-resizable', { ...dragProps })
          return h('th', { ...restProps, class: 'resize-table-th' }, [...children, drag])
        }
      }
    }
    return {
      EARLY_WARNING_JTOA_SCENE,
      // EARLY_WARNING_TENDER_SCENE,
      EARLY_WARNING_RFQLINE_SCENE,
      EARLY_WARNING_CONTRACT_SCENE,
      num: 0,
      yearShowOne: false,
      loading: false,
      visible: false,
      loadingExport: false,
      confirmLoading: false,
      // 表格数据
      dataSource: [],
      columns: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' },
          dataIndex: 'type',
          align: 'center',
          ellipsis: true,
          width: 50
        },
        {
          title: '预警场景',
          dataIndex: 'scene',
          align: 'center',
          ellipsis: true,
          width: 100
        },
        {
          title: '预警原因',
          dataIndex: 'warningReason',
          align: 'center',
          ellipsis: true,
          width: 300
        },
        {
          title: '操作人',
          dataIndex: 'createByName',
          align: 'center',
          ellipsis: true,
          width: 100
        },
        {
          title: '操作时间',
          dataIndex: 'createDate',
          ellipsis: true,
          align: 'center',
          sorter: true,
          width: 200
        }
      ],
      scroll: {
        x: '100%',
        y: 'calc(50vh)',
        scrollToFirstRowOnChange: false
      },
      selectedRows: [],
      selectedRowKeys: [],
      defaultPageSize: 20,
      pageSizeOptions: ['10', '20', '30', '50'],
      queryParam: {
        year: moment(),
        scene: EARLY_WARNING_JTOA_SCENE,
        proportion: 30
      },
      loadData: parameter => {
        const param = requestBuilder(
          '',
          {
            ...this.queryParam
          },
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder
        )
        return earlyWarningApi.earlyWarningPurchaseMethod(param).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '查询失败！'
            })
            return Promise.reject(res)
          }
          return res.result
        })
      },
      rowClick: record => ({
        on: {
          click: () => {
            this.jump(record)
          }
        }
      }),
      rowClassName: record => {
        return 'tablerow'
      }
    }
  },
  created () {
    this.queryCount()
  },
  computed: {
    // 新增可领数
    computedColumns () {
      const computedColumns = []
      for (const item of this.columns) {
        computedColumns.push(item)
        if (this.queryParam.scene === EARLY_WARNING_CONTRACT_SCENE && item.dataIndex === 'scene') {
          computedColumns.push({
            title: '合同编号',
            dataIndex: 'contractNum',
            align: 'center',
            ellipsis: true,
            width: 100
          })
          computedColumns.push({
            title: '付款单号',
            dataIndex: 'paymentNum',
            align: 'center',
            ellipsis: true,
            width: 100
          })
        } else if (item.dataIndex === 'scene') {
          computedColumns.push({
            title: '询价单号',
            dataIndex: 'rfqlineNum',
            align: 'center',
            ellipsis: true,
            width: 100
          })
        }
      }
      return computedColumns
    }
  },
  methods: {
    // 弹出日历和关闭日历的回调
    openChangeOne (status) {
      if (status) {
        this.yearShowOne = true
      } else {
        this.yearShowOne = false
      }
    },
    // 得到年份选择器的值
    panelChangeOne (value) {
      this.queryParam.year = value
      this.yearShowOne = false
      this.queryCount()
    },
    onChange () {
      this.queryCount()
    },
    queryCount () {
      this.loading = true
      this.queryParam = {
        ...this.queryParam,
        searchYear: this.moment(this.queryParam.year, 'YYYY')
      }
      const param = requestBuilder('', this.queryParam, 0, 1)
      earlyWarningApi.earlyWarningPurchaseMethod(param).then(res => {
        if (res.result.data) {
          this.num = res.result.totalCount
        } else {
          this.num = 0
        }
      }).finally(() => {
        this.loading = false
      })
    },
    initApi () {
      // this.loading = true
      // const param = requestBuilder('', {
      //   startTime: this.dates.length > 0 ? this.dates[0].format('YYYY-MM-DD') : '',
      //   endTime: this.dates.length > 0 ? this.dates[1].format('YYYY-MM-DD') : ''
      // })
      // earlyWarningApi.earlyWarningNonUnified(param).then(res => {
      //   this.num = res.result.totalCount || 0
      // }).finally(() => {
      //   this.loading = false
      // })
    },
    showModal () {
      this.visible = true
      if (this.$refs.table) {
        this.$refs.table.refresh()
      }
    },
    handleCancel (e) {
      this.visible = false
    },
    moment (text, pattern = 'YYYY-MM') {
      return text ? this.$moment(text).format(pattern) : ''
    },
    doExport () {
      this.loadingExport = true
      const param = requestBuilder(
        '',
        {
          ...this.queryParam
        },
        null,
        null
      )
      earlyWarningApi.exportEarlyWarningPurchaseMethod(param).then(res => {
        if (res && res.data && res.headers['content-disposition']) {
          const link = document.createElement('a')
          const url = window.URL.createObjectURL(res.data)
          const contentDisposition = res.headers['content-disposition']
          let filename = contentDisposition.replace(/(.*;)?filename=([^;]+).*/i, '$2')
          try {
            filename = decodeURIComponent(escape(filename))
          } catch (e) {}
          document.body.appendChild(link)
          link.style.display = 'none'
          link.download = filename
          link.href = url
          link.click()
          link.remove()
          window.URL.revokeObjectURL(url)
        } else {
          this.$notification.error({
            message: '系统消息',
            description: '下载失败！'
          })
        }
      }).finally(() => {
        this.loadingExport = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
.box {
  height: 145px;
  display: flex;
  padding: 0 10px;
  width: 100%;
  justify-content: center;
  .searchBox {
    display: flex;
    justify-content: space-between;
  }
  .boxFlex {
    text-align: center;
    margin-top: 25px;
  }
  .num {
    font-size: 34px;
    color: #F8AA4B;
  }
  .text {
    font-size: 12px;
  }
}
.btnExp {
  width: 80px;
}
</style>
