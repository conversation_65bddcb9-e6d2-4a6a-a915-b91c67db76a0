<template>
  <CrewCardPlus title="拆分采购">
    <template slot="right-icon">
      <a-range-picker v-model="dates" :getCalendarContainer="triggerNode => triggerNode.parentNode" dropdownClassName="rangePickerIceGai" style="width: 270px;" @change="onChange" />
    </template>
    <template slot="text">
      <div class="top">
        <a-radio-group default-value="1" v-model="mode" @change="callback">
          <a-radio-button value="1">
            按简易询价
          </a-radio-button>
          <!-- <a-radio-button value="2">
            按海建招标申请
          </a-radio-button> -->
        </a-radio-group>
        <div>
          <a-badge color="#2db7f5" text="简易询价" />
          <br>
          <!-- <a-badge color="#f50" text="海建招标申请" /> -->
        </div>
      </div>
      <div class="content">
        <a-spin :spinning="loading">
          <div style="width: 100%; height: 100%; background: #89898918;">
            <div id="echartCategory" style="width: 100%; height: 385px;" />
          </div>
        </a-spin>
      </div>
      <div class="bottom">
        <a-button class="btn" icon="retweet" size="large" @click="showModal">查看详情</a-button>
      </div>
      <div ref="initwarp" :class="{ initwarp: styleName === 'themeb' }">
        <a-modal
          title="拆分采购详情"
          :visible="visible"
          :confirm-loading="confirmLoading"
          :getContainer="() => $refs.initwarp"
          width="1000px"
          :bodyStyle="{ height: '600px' }"
          @cancel="handleCancel"
        >
          <a-form-model
            ref="ruleForm"
            :model="editInfo"
          >
            <a-row>
              <a-col :span="6">
                <a-form-model-item
                  label="物资代码">
                  <a-input
                    v-model="editInfo.materialNum"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item
                  label="物资名称">
                  <a-input
                    v-model="editInfo.materialName"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="6"><a-button class="btnExp" @click="searchModal">查询</a-button></a-col>
            </a-row>
          </a-form-model>
          <s-table
            ref="table"
            :columns="columns"
            :components="components"
            :data="loadData"
            :scroll="scroll"
            :rowClassName="rowClassName"
            :customRow="rowClick"
            :bordered="false"
            :pageSizeOptions="pageSizeOptions"
            :pageSize="defaultPageSize"
            no-striped
            :showPagination="true"
            rowKey="uuid"
          >
            <span
              slot="serial"
              slot-scope="text, record, index"
            >{{ index + 1 }}</span>
            <!-- <span slot="createDate" slot-scope="text">{{ moment(text) }}</span> -->
            <span
              slot="ellipsis2"
              slot-scope="text"
            >
              <a href="javascript:void(0)" v-for="item in text.split(',')" :key="item" @click="jump(item)">{{ item }} </a>
            </span>
          </s-table>
          <template slot="footer">
            <a-button class="btnExp" :loading="loadingExport" @click="doExport">导出</a-button>
          </template>
        </a-modal>
      </div>
    </template>
  </CrewCardPlus>
</template>
<script>
import CrewCardPlus from '../Crew/CrewCardPlus'
import * as echarts from 'echarts'
import countTo from 'vue-count-to'
import * as earlyWarningApi from '@/api/material/earlyWarning'
import { requestBuilder } from '@/utils/util'
import { STable } from '@/components'
import moment from 'moment'

import Vue from 'vue'
import { ORG_ID, PERSON_NAME } from '@/store/mutation-types'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_MD = USER_ORG_ID === '1.100.104'
export default {
  name: 'CrewSplitPurchasingM',
  components: {
    CrewCardPlus,
    countTo,
    STable
  },
  props: {
    styleName: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data () {
    this.components = {
      header: {
        cell: (h, props, children) => {
          const { key, ...restProps } = props
          const col = this.columns.find(col => {
            const k = col.dataIndex || col.key
            return k === key
          })

          if (!col || !col.width) {
            return h('th', { ...restProps }, [...children])
          }

          const dragProps = {
            key: col.dataIndex || col.key,
            class: 'table-draggable-handle',
            attrs: {
              w: 10,
              x: col.width,
              z: 1,
              axis: 'x',
              draggable: true,
              resizable: false
            },
            on: {
              dragging: (x, y) => {
                col.width = Math.max(x, 1)
              }
            }
          }
          const drag = h('vue-draggable-resizable', { ...dragProps })
          return h('th', { ...restProps, class: 'resize-table-th' }, [...children, drag])
        }
      }
    }
    return {
      mode: '1',
      loading: false,
      loadingExport: false,
      dates: this.getDate(),
      sortList: [],
      sortTTList: [],
      sortTenderInList: [],
      sortTenderList: [],
      sortListTitle: [],
      sortTenderListTitle: [],
      visible: false,
      editInfo: {},
      confirmLoading: false,
      // 表格数据
      dataSource: [],
      columns: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' },
          dataIndex: 'type',
          align: 'center',
          ellipsis: true,
          width: 50
        },
        {
          title: '物资编码',
          dataIndex: 'materialNum',
          scopedSlots: { customRender: 'materialNum' },
          align: 'center',
          ellipsis: true,
          width: 150
        },
        {
          title: '物资名称',
          dataIndex: 'materialName',
          align: 'center',
          ellipsis: true,
          width: 150
        },
        // {
        //   title: '物资类型',
        //   dataIndex: 'materialTypeName',
        //   align: 'center',
        //   ellipsis: true,
        //   width: '15%'
        // },
        {
          title: '询价单号',
          dataIndex: 'rfqlineNum',
          scopedSlots: { customRender: 'ellipsis2' },
          align: 'center',
          sorter: true,
          width: 600
        },
        {
          title: '次数',
          dataIndex: 'count',
          align: 'center',
          ellipsis: true,
          width: 200
        }
      ],
      scroll: {
        x: '100%',
        y: 'calc(50vh)',
        scrollToFirstRowOnChange: false
      },
      selectedRows: [],
      selectedRowKeys: [],
      defaultPageSize: 20,
      pageSizeOptions: ['10', '20', '30', '50'],
      queryParam: {
        year: moment()
        // proportion: 5
      },
      loadData: parameter => {
        return earlyWarningApi.earlyWarningSplitPurchaseDetail(requestBuilder(
          this.mode === '1' ? 'tender' : 'jtoa', {
            ...this.editInfo,
            startTime: this.dates.length > 0 ? this.dates[0].format('YYYY-MM-DD') : '',
            endTime: this.dates.length > 0 ? this.dates[1].format('YYYY-MM-DD') : ''
          }, parameter.pageNo, parameter.pageSize, parameter.sortField, parameter.sortOrder)).then(res => {
          if (res.code === '0000') {
            // this.dataSource = res.result.data
            return res.result
          } else {
            this.$message.error(res.message)
          }
        })
      },
      rowClick: record => ({
        on: {
          click: () => {
            // this.jump(record)
          }
        }
      }),
      rowClassName: record => {
      }
    }
  },
  // 页面初始化挂载dom
  mounted () {
    this.initApi()
  },
  methods: {
    onChange () {
      this.initApi()
    },
    getDate () {
      const dates = new Date()
      let month = dates.getMonth() + 1
      let year = dates.getFullYear()
      const month2 = month >= 12 ? 1 : month + 1
      const year2 = month >= 12 ? year + 1 : year
      if (IS_MD) {
        year = month < 5 ? year - 1 : year
        month = month < 5 ? month + 5 : month - 5
      }
      console.log(year + '-' + month + '-01')
      const value = [moment(year + '-' + month + '-01', 'YYYY-MM-DD'), moment(year2 + '-' + month2 + '-01', 'YYYY-MM-DD')]
      return value
    },
    showModal () {
      this.visible = true
      if (this.$refs.table) {
        this.$refs.table.refresh()
      }
    },
    handleCancel (e) {
      this.visible = false
      this.editInfo = {}
    },
    callback () {
      this.getLoadEcharts()
    },
    initApi () {
      this.loading = true
      const param = requestBuilder('', {
        startTime: this.dates.length > 0 ? this.dates[0].format('YYYY-MM-DD') : '',
        endTime: this.dates.length > 0 ? this.dates[1].format('YYYY-MM-DD') : ''
      })
      earlyWarningApi.earlyWarningSplitPurchase(param).then(res => {
        const arr = []
        res.result.forEach(item => {
          if (item.materialName) {
            arr.push({
              materialName: item.materialName,
              instCount: item.instCount,
              tenderCount: item.tenderCount
            })
          }
        })
        this.dataSource = arr || []
        // 按照hj招标
        const arr1 = arr.sort((a, b) => { return b.instCount - a.instCount })
        this.sortList = arr1.map(item => { return item.instCount })
        this.sortTTList = arr1.map(item => { return item.tenderCount })
        this.sortListTitle = arr1.map(item => { return item.materialName })
        // 按照简单询价
        const arr2 = arr.sort((a, b) => { return b.tenderCount - a.tenderCount })
        this.sortTenderList = arr2.map(item => { return item.tenderCount })
        this.sortTenderInList = arr2.map(item => { return item.instCount })
        this.sortTenderListTitle = arr2.map(item => { return item.materialName })
      }).then(() => {
        this.getLoadEcharts()
      }).finally(() => {
        this.loading = false
      })
    },
    jump (rfqlineNum) {
      rfqlineNum = rfqlineNum.replace('、', '')
      this.visible = false
      this.$router.push({
        name: 'RfqManagement',
        params: {
          rfqlineNum: rfqlineNum
        }
      })
    },
    getLoadEcharts () {
      this.dom = this.$echarts.init(
        document.getElementById('echartCategory')
      )
      this.dom.clear()
      this.dom.setOption({
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          top: '10%',
          left: '5%',
          right: '5%',
          bottom: '10%',
          containLabel: true
        },
        toolbox: {
          show: true
        },
        xAxis: [
          {
            type: 'category',
            // prettier-ignore
            data: this.mode === '1' ? this.sortTenderListTitle : this.sortListTitle,
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: 12,
                color: '#2789CB'
              }
            }
            // textStyle: {
            //   fontSize: 10,
            //   color: '#fff'
            // }
          }
        ],
        yAxis: [
          {
            type: 'value',
            show: true,
            splitLine: {
              show: true
            },
            axisLabel: {
              // show: true,
              textStyle: {
                fontSize: 12,
                color: '#2789CB'
              }
            }
          }
        ],
        series: [
          // tenderCount
          {
            name: '简易询价',
            type: 'bar',
            data: this.mode === '1' ? this.sortTenderList : this.sortTTList,
            barWidth: 7,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#04F9F7' },
                { offset: 1, color: 'rgba(225, 215, 215, 0.26)' }
              ])
            }
          }
          // instCount
          // {
          //   name: '海建招标申请',
          //   type: 'bar',
          //   data: this.mode === '1' ? this.sortTenderInList : this.sortList,
          //   barWidth: 7,
          //   itemStyle: {
          //     color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //       { offset: 0, color: '#F1704F' },
          //       { offset: 1, color: 'rgba(225, 215, 215, 0.26)' }
          //     ])
          //   }
          // }
        ]
      })
      window.addEventListener('resize', () => {
        this.dom.resize()
      })
    },
    doExport () {
      this.loadingExport = true
      earlyWarningApi.exportEarlyWarningSplitPurchaseDetail(requestBuilder(
        this.mode === '1' ? 'tender' : 'jtoa', {
          startTime: this.dates.length > 0 ? this.dates[0].format('YYYY-MM-DD') : '',
          endTime: this.dates.length > 0 ? this.dates[1].format('YYYY-MM-DD') : ''
        }, null, null)).then(res => {
        if (res && res.data && res.headers['content-disposition']) {
          const link = document.createElement('a')
          const url = window.URL.createObjectURL(res.data)
          const contentDisposition = res.headers['content-disposition']
          let filename = contentDisposition.replace(/(.*;)?filename=([^;]+).*/i, '$2')
          try {
            filename = decodeURIComponent(escape(filename))
          } catch (e) {}
          document.body.appendChild(link)
          link.style.display = 'none'
          link.download = filename
          link.href = url
          link.click()
          link.remove()
          window.URL.revokeObjectURL(url)
        } else {
          this.$notification.error({
            message: '系统消息',
            description: '下载失败！'
          })
        }
      }).finally(() => {
        this.loadingExport = false
      })
    },
    searchModal () {
      this.$refs.table.refresh(true)
    }
  },
  beforeDestroy () {
    /* 页面组件销毁的时候，别忘了移除绑定的监听resize事件，否则的话，多渲染几次
    容易导致内存泄漏和额外CPU或GPU占用哦 */
    window.removeEventListener('resize', () => {
      this.myChart.resize()
    })
  }
}
</script>
<style lang="less" scoped>
  .top {
    height: 45px;
    margin: 10px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .content {
    width: 100%;
    height: 385px;
    padding: 0 15px;
  }
  .bottom {
    height: 50px;
    display: flex;
    justify-content: right;
    align-items: center;
    padding-right: 25px;
    .btn {
      width: 150px;
      height: 35px;
    }
  }
  .ant-radio-group {
    /* background-color: #ffffff00; */
    .ant-radio-button-wrapper {
      background-color: #ffffff00;
      border: none;
    }
  }
  .btnExp {
  width: 80px;
}
</style>
