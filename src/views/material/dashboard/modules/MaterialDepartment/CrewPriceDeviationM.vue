<template>
  <CrewCard title="价格偏离度">
    <template slot="text">
      <div class="box">
        <div class="form-box">
          <div @click.stop>
            <a-date-picker
              mode="year"
              placeholder="选择年份"
              :open="yearShowOne"
              format="YYYY"
              :getCalendarContainer="triggerNode => triggerNode.parentNode"
              dropdownClassName="rangePickerIceGai"
              @change="panelChangeOne"
              @openChange="openChangeOne"
              @panelChange="panelChangeOne"
              v-model="queryParam.year"
              style="width: 100%;"
            />
          </div>
        </div>
        <div class="bottom-box">
          <div class="leftNum" @click="showModal">
            <div class="boxFlex">
              <div class="num">
                <countTo
                  :startVal="0"
                  :endVal="infoData.num"
                  prefix=""
                  suffix=""/>
              </div>
              <div class="text">总计</div>
            </div>
          </div>
          <div class="rightIcon">
            <a-icon v-show="!loading && (infoData.num < infoData.lastNum)" style="color: rgb(222, 16, 16);" class="iconStyle" type="fall" />
            <a-icon v-show="!loading && (infoData.num >= infoData.lastNum)" style="color: rgb(21, 181, 0);" class="iconStyle" type="rise" />
            <a-icon v-show="loading" style="color: rgb(28, 162, 220);" class="iconStyle" type="loading" />
            <div class="iconDesc">相较于去年</div>
          </div>
          <!-- <div class="left-text">
            <div>
              <countTo
                :startVal="0"
                :endVal="infoData.outOfProportion"
                prefix=""
                suffix=""/>
              <span style="font-size: 12px; margin-left: 5px;">高于平均数</span>
              <div style="font-size: 12px; text-align: left;">
                占比
                <span style="color: red;">
                  <countTo
                    :startVal="0"
                    :endVal="infoData.proportion"
                    prefix=""
                    suffix="%"/>
                </span>
              </div>
            </div>
          </div>
          <div class="left-btn">
            <a-button size="large" class="left-btn-init" icon="search" @click="showModal">
              查看详情
            </a-button>
          </div> -->
        </div>
      </div>
      <div ref="initwarp" :class="{ initwarp: styleName === 'themeb' }">
        <a-modal
          title="查看详情"
          :visible="visible"
          :confirm-loading="confirmLoading"
          :getContainer="() => $refs.initwarp"
          width="1000px"
          :bodyStyle="{ height: '600px' }"
          @cancel="handleCancel"
        >
          <s-table
            ref="table"
            :columns="columns"
            :components="components"
            :data="loadData"
            :scroll="scroll"
            :rowClassName="rowClassName"
            :customRow="rowClick"
            :bordered="false"
            :pageSizeOptions="pageSizeOptions"
            :pageSize="defaultPageSize"
            :showPagination="true"
            no-striped
            rowKey="uuid"
          >
            <span
              slot="serial"
              slot-scope="text, record, index"
            >{{ index + 1 }}</span>
            <span
              slot="materialNum"
              slot-scope="text, record"
            >
              <a href="javascript:void(0)" @click="jump(record)">{{ text }} </a>
            </span>
          </s-table>
          <template slot="footer">
            <a-button class="btnExp" :loading="loadingExport" @click="doExport">导出</a-button>
          </template>
        </a-modal>
      </div>
    </template>
  </CrewCard>
</template>
<script>
import '@/style/baseColor.less'
import CrewCard from '../Crew/CrewCard'
import countTo from 'vue-count-to'
import EchartPicInit from './EchartPicInitM'
import * as earlyWarningApi from '@/api/material/earlyWarning'
import { STable } from '@/components'
import { requestBuilder } from '@/utils/util'
import moment from 'moment'
export default {
  name: 'CrewPriceDeviationM',
  components: {
    CrewCard,
    countTo,
    EchartPicInit,
    STable
  },
  props: {
    styleName: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data () {
    this.components = {
      header: {
        cell: (h, props, children) => {
          const { key, ...restProps } = props
          const col = this.columns.find(col => {
            const k = col.dataIndex || col.key
            return k === key
          })

          if (!col || !col.width) {
            return h('th', { ...restProps }, [...children])
          }

          const dragProps = {
            key: col.dataIndex || col.key,
            class: 'table-draggable-handle',
            attrs: {
              w: 10,
              x: col.width,
              z: 1,
              axis: 'x',
              draggable: true,
              resizable: false
            },
            on: {
              dragging: (x, y) => {
                col.width = Math.max(x, 1)
              }
            }
          }
          const drag = h('vue-draggable-resizable', { ...dragProps })
          return h('th', { ...restProps, class: 'resize-table-th' }, [...children, drag])
        }
      }
    }
    return {
      loading: false,
      loadingExport: false,
      yearShowOne: false,
      sourceData: [],
      infoData: {
        num: 0,
        lastNum: 0
      },
      queryParam: {
        year: moment()
        // proportion: 5
      },
      visible: false,
      confirmLoading: false,
      // 表格数据
      dataSource: [],
      columns: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' },
          dataIndex: 'type',
          align: 'center',
          ellipsis: true,
          width: 50
        },
        {
          title: '物资编码',
          dataIndex: 'materialNum',
          scopedSlots: { customRender: 'materialNum' },
          align: 'center',
          ellipsis: true,
          width: 100
        },
        {
          title: '物资名称',
          dataIndex: 'materialName',
          align: 'center',
          ellipsis: true,
          width: 100
        },
        {
          title: '规格型号',
          dataIndex: 'jmodel',
          align: 'center',
          ellipsis: true,
          width: 100
        },
        // {
        //   title: '合同名称',
        //   dataIndex: 'contractName',
        //   align: 'center',
        //   ellipsis: true,
        //   width: 300
        // },
        {
          title: '平均价',
          dataIndex: 'averagePrice',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 150
        },
        {
          title: '订单采购价',
          dataIndex: 'unitcostAfterTax',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 150
        },
        {
          title: '超额(%)',
          dataIndex: 'outOfProportion',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 100
        }
      ],
      scroll: {
        x: '100%',
        y: 'calc(50vh)',
        scrollToFirstRowOnChange: false
      },
      selectedRows: [],
      selectedRowKeys: [],
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20', '30', '40'],
      loadData: parameter => {
        return earlyWarningApi.earlyWarningHighPrice(requestBuilder(
          '',
          {
            searchYear: moment(this.queryParam.year).format('YYYY')
          },
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder
        )).then(res => {
          if (res.code === '0000') {
            this.dataSource = res.result.data
            return res.result
          } else {
            this.$message.error(res.message)
          }
        })
      },
      rowClick: record => ({
        on: {}
      }),
      rowClassName: record => {
        return 'tablerow'
      }
    }
  },
  // 页面初始化挂载dom
  mounted () {
    // this.infoData = {
    //   outOfProportion: 0,
    //   proportion: 0
    // }
    this.initApi()
  },
  methods: {
    openChangeOne (status) {
      if (status) {
        this.yearShowOne = true
      } else {
        this.yearShowOne = false
      }
    },
    // 得到年份选择器的值
    panelChangeOne (value) {
      this.queryParam.year = value
      this.yearShowOne = false
      this.initApi()
    },
    showModal () {
      this.visible = true
      if (this.$refs.table) {
        this.$refs.table.refresh()
      }
    },
    handleCancel (e) {
      this.visible = false
    },
    moment (text, pattern = 'YYYY-MM') {
      return text ? this.$moment(text).format(pattern) : ''
    },
    initApi () {
      this.loading = true
      const param = requestBuilder('', { searchYear: moment(this.queryParam.year).format('YYYY') }, 1, 1)
      earlyWarningApi.earlyWarningHighPriceScale(param).then(res => {
        console.log(res)
        this.infoData.num = res.result.yearCount
        this.infoData.lastNum = res.result.lastYearCount
      }).then(() => {
        // this.getLoadEcharts()
      }).finally(() => {
        this.loading = false
      })
    },
    getLoadEcharts () {
      let num = 0
      if (this.sourceData.length > 0) {
        const sum = this.sourceData.reduce((pre, cur) => {
          return cur.value + pre
        }, 0)
        const obj = this.sourceData.find(item => item.name === '高于平均数')
        num = (obj.value / sum * 100).toFixed(1)
      }
      this.dom = this.$echarts.init(
        this.$refs.echartPrice
      )
      this.dom.setOption({
        color: ['#AD4440', 'rgba(240, 248, 255, 0.055)'],
        tooltip: {
          trigger: 'item'
        },
        legend: {
          bottom: '5%',
          left: 'center',
          icon: 'circle',
          textStyle: {
            fontSize: 8,
            color: 'rgb(144, 144, 144)'
          }
        },
        series: [
          {
            type: 'pie',
            radius: ['30%', '55%'],
            center: ['50%', '35%'],
            avoidLabelOverlap: false,
            itemStyle: {
            },
            label: {
              show: true,
              position: 'center',
              color: '#4c4a4a',
              formatter: '{total|' + num + '%}',
              rich: {
                total: {
                  fontSize: 12,
                  fontFamily: '微软雅黑',
                  color: 'red'
                }
              }
              // emphasis: {
              //   show: true
              // }
            },
            lableLine: {
              normal: {
                show: false
              },
              emphasis: {
                show: true
              },
              tooltip: {
                show: false
              }
            },
            data: this.sourceData || []
          }
        ]
      })
      window.addEventListener('resize', () => {
        // 第六步，执行echarts自带的resize方法，即可做到让echarts图表自适应
        this.dom.resize()
        // 如果有多个echarts，就在这里执行多个echarts实例的resize方法,不过一般要做组件化开发，即一个.vue文件只会放置一个echarts实例
        /*
        this.myChart2.resize();
        this.myChart3.resize();
        ......
        */
      })
    },
    jump (record) {
      const materialNum = record.materialNum
      // const contractNum = record.contractNum
      this.visible = false
      this.$router.push({
        name: 'PolineManagement',
        params: {
          materialNum: materialNum,
          status: 'finished'
        }
      })
    },
    doExport () {
      this.loadingExport = true
      earlyWarningApi.exportEarlyWarningHighPrice(requestBuilder('',
        {
          searchYear: moment(this.queryParam.year).format('YYYY')
        },
        null,
        null)).then(res => {
        if (res && res.data && res.headers['content-disposition']) {
          const link = document.createElement('a')
          const url = window.URL.createObjectURL(res.data)
          const contentDisposition = res.headers['content-disposition']
          let filename = contentDisposition.replace(/(.*;)?filename=([^;]+).*/i, '$2')
          try {
            filename = decodeURIComponent(escape(filename))
          } catch (e) {}
          document.body.appendChild(link)
          link.style.display = 'none'
          link.download = filename
          link.href = url
          link.click()
          link.remove()
          window.URL.revokeObjectURL(url)
        } else {
          this.$notification.error({
            message: '系统消息',
            description: '下载失败！'
          })
        }
      }).finally(() => {
        this.loadingExport = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
.box {
  height: 145px;
  padding: 0 10px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  .bottom-box {
    display: flex;
    margin-top: 10px;
    width: 100%;
    justify-content: space-between;
    height: 100px;
    .leftNum {
      width: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    .rightIcon {
      width: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .iconDesc {
        font-size: 12px;
        opacity: 0.7;
      }
      .iconStyle {
        font-size: 70px;
        color: rgb(222, 16, 16);
      }
    }
  }
  .form-box {
    width: 100%;
  }
  .num {
    font-size: 34px;
    color: #F8AA4B;
  }
  .boxFlex {
    text-align: center;
    /* margin-top: 25px; */
  }
}
.btnExp {
  width: 80px;
}
/* .initwarp {
  /deep/.ant-modal-content {
    background-color: #03203dbd !important;
  }
  /deep/.ant-modal-header {
    background-color: #03203dbd !important;
  }
} */
</style>
