<template>
  <CrewCard title="物资出库">
    <template slot="right-icon">
      <div class="selectInp">
        <a-select
          show-search
          allowClear
          placeholder="仓库类型"
          option-filter-prop="children"
          :getPopupContainer="triggerNode => triggerNode.parentNode"
          dropdownClassName="rangePickerIceGai"
          v-model="paramData.storehouseType"
          style="width: 140px"
          @change="onChange"
        >
          <a-select-option :label="item.label" :value="item.value" v-for="item in storeTypes" :key="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </div>
    </template>
    <template slot="text">
      <div class="flexBox">
        <div class="left">
          <div class="left-flex">
            <a-date-picker
              mode="year"
              placeholder="选择年份"
              :open="yearShowOne"
              format="YYYY"
              :getCalendarContainer="triggerNode => triggerNode.parentNode"
              dropdownClassName="rangePickerIceGai"
              @change="panelChangeOne"
              @openChange="openChangeOne"
              @panelChange="panelChangeOne"
              v-model="paramData.statYear"
              style="width: 48%;"
            />
            <a-month-picker
              v-model="paramData.statMonth"
              :disabled="disabled"
              :getCalendarContainer="triggerNode => triggerNode.parentNode"
              dropdownClassName="rangePickerIceGai"
              style="width: 48%;"
              placeholder="选择月份"
              @change="onChange"
            />
          </div>
          <div class="sub">
            <img class="imgs" src="../../pic//set9.png">
            <div class="des">
              <div>物资出库 > {{ record.count }}件</div>
              <div>
                <countTo
                  :startVal="0"
                  class="subNum"
                  style="font-size: 16px;"
                  :endVal="record.amount"
                  prefix="￥"
                  suffix=""
                />
              </div>
            </div>
          </div>
        </div>
        <div class="right ant-btn" @click="showModal">
          <a-icon style="font-size: 24px;" type="right-circle" />
          <div style="margin-top: 15px;">出库详情</div>
        </div>
      </div>
      <div ref="initwarp" :class="{ initwarp: styleName === 'themeb' }">
        <a-modal
          title="查看详情"
          :visible="visible"
          :confirm-loading="confirmLoading"
          :getContainer="() => $refs.initwarp"
          width="1000px"
          :bodyStyle="{ height: '600px' }"
          :footer="null"
          @cancel="handleCancel"
        >
          <s-table
            ref="table"
            :columns="columns"
            :components="components"
            :data="loadData"
            :scroll="scroll"
            :rowClassName="rowClassName"
            :customRow="rowClick"
            :bordered="false"
            :pageSizeOptions="pageSizeOptions"
            :pageSize="defaultPageSize"
            :showPagination="true"
            no-striped
            rowKey="uuid"
          >
            <span
              slot="serial"
              slot-scope="text, record, index"
            >{{ index + 1 }}</span>
            <!-- <span
              slot="materialNum"
              slot-scope="text, record"
            >
              <a href="javascript:void(0)" @click="jump(record)">{{ text }} </a>
            </span> -->
          </s-table>
        </a-modal>
      </div>
    </template>
  </CrewCard>
</template>
<script>
import CrewCard from '../Crew/CrewCard'
import countTo from 'vue-count-to'
import { requestBuilder } from '@/utils/util'
import { STable } from '@/components'
import * as cockpitApi from '@/api/material/cockpit'
import moment from 'moment'
export default {
  name: 'CrewWarehousing',
  components: {
    CrewCard,
    countTo,
    STable
  },
  props: {
    styleName: {
      type: String,
      default: () => {
        return ''
      }
    },
    disabled: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data () {
    this.components = {
      header: {
        cell: (h, props, children) => {
          const { key, ...restProps } = props
          const col = this.columns.find(col => {
            const k = col.dataIndex || col.key
            return k === key
          })

          if (!col || !col.width) {
            return h('th', { ...restProps }, [...children])
          }

          const dragProps = {
            key: col.dataIndex || col.key,
            class: 'table-draggable-handle',
            attrs: {
              w: 10,
              x: col.width,
              z: 1,
              axis: 'x',
              draggable: true,
              resizable: false
            },
            on: {
              dragging: (x, y) => {
                col.width = Math.max(x, 1)
              }
            }
          }
          const drag = h('vue-draggable-resizable', { ...dragProps })
          return h('th', { ...restProps, class: 'resize-table-th' }, [...children, drag])
        }
      }
    }
    return {
      storeTypes: [
        { label: '一级库', value: '一级库' },
        { label: '现场库', value: '二级库' }
      ],
      yearShowOne: false,
      paramData: {
        storehouseType: '一级库',
        statMonth: moment(),
        statYear: null
      },
      loading: false,
      record: {
        count: 0,
        amount: 0
      },
      queryParam: {
        year: moment()
        // proportion: 5
      },
      visible: false,
      confirmLoading: false,
      // 表格数据
      dataSource: [],
      columns: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' },
          dataIndex: 'type',
          align: 'center',
          ellipsis: true,
          width: 50
        },
        {
          title: '物资编码',
          dataIndex: 'materialNum',
          scopedSlots: { customRender: 'materialNum' },
          align: 'center',
          ellipsis: true,
          width: 100
        },
        {
          title: '物资名称',
          dataIndex: 'materialName',
          align: 'center',
          ellipsis: true,
          width: 100
        },
        {
          title: '数量',
          dataIndex: 'quantity',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 100
        },
        {
          title: '税前金额',
          dataIndex: 'lineCost',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 150
        },
        {
          title: '出库时间',
          dataIndex: 'createDateString',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 150
        },
        {
          title: '计划年月',
          dataIndex: 'statMonth',
          align: 'center',
          ellipsis: true,
          width: 150
        }
      ],
      scroll: {
        x: '100%',
        y: 'calc(60vh)',
        scrollToFirstRowOnChange: false
      },
      selectedRows: [],
      selectedRowKeys: [],
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20', '30', '40'],
      loadData: parameter => {
        const param = requestBuilder(
          '',
          {
            ...this.paramData,
            statYear: this.paramData.statYear ? this.paramData.statYear.format('YYYY') : '',
            statMonth: this.paramData.statMonth ? this.paramData.statMonth.format('YYYYMM') : ''
          },
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder
        )
        return cockpitApi.queryMatuDetail(param).then(res => {
          if (res.code === '0000') {
            this.dataSource = res.result.data
            return res.result
          } else {
            this.$message.error(res.message)
          }
        })
      },
      rowClick: record => ({
        on: {}
      }),
      rowClassName: record => {
        return 'tablerow'
      }
    }
  },
  created () {
    this.doSearch()
  },
  methods: {
    showModal () {
      if (this.disabled) {
        return
      }
      this.visible = true
      if (this.$refs.table) {
        this.$refs.table.refresh()
      }
    },
    handleCancel (e) {
      this.visible = false
    },
    moment (text, pattern = 'YYYY-MM') {
      return text ? this.$moment(text).format(pattern) : ''
    },
    // 弹出日历和关闭日历的回调
    openChangeOne (status) {
      if (status) {
        this.yearShowOne = true
      } else {
        this.yearShowOne = false
      }
    },
    // 得到年份选择器的值
    panelChangeOne (value) {
      this.paramData.statYear = value
      this.paramData.statMonth = null
      this.yearShowOne = false
      this.doSearch()
    },
    // 得到年份选择器的值
    onChange (value) {
      this.paramData.statYear = null
      this.yearShowOne = false
      this.doSearch()
    },
    doSearch () {
      this.loading = true
      const param = requestBuilder(
        '',
        {
          ...this.paramData,
          statYear: this.paramData.statYear ? this.paramData.statYear.format('YYYY') : '',
          statMonth: this.paramData.statMonth ? this.paramData.statMonth.format('YYYYMM') : ''
        }
      )
      cockpitApi.queryMatu(param).then(res => {
        this.record.count = res.result.count || 0
        this.record.amount = res.result.amount || 0
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
  .flexBox {
    width: 100%;
    height: 140px;
    display: flex;
    justify-content: space-between;
    padding: 15px;
    align-items: center;
    .right {
      width: 100px;
      height: 100%;
      border-radius: 5px;
      margin-left: 15px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
    .left {
      flex: 1;
      height: 100%;
      width: auto;
      .left-flex {
        display: flex;
        justify-content: space-between;
      }
      .sub {
        display: flex;
        height: 60px;
        width: 100%;
        flex: 1;
        margin-top: 15px;
        justify-content: space-around;
        align-items: center;
        .des {
          width: 100%;
          text-align: right;
        }
      }
    }
    .imgs {
      width: 40px;
      height: 40px;
      margin: 0 5px;
    }
  }
</style>
