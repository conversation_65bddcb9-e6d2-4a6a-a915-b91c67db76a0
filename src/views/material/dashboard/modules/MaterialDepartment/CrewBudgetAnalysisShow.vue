<template>
  <CrewCardPlus title="预算金额分析展示">
    <template slot="right-icon">
      <div class="selectInp">
        <!-- <a style="font-size: 12px;" href="javascript:void(0)" @click="showModal">详情</a> -->
        <a-date-picker
          mode="year"
          placeholder="选择年份"
          :open="yearShowOne"
          :allowClear="false"
          format="YYYY"
          :getCalendarContainer="triggerNode => triggerNode.parentNode"
          dropdownClassName="rangePickerIceGai"
          @change="panelChangeOne"
          @openChange="openChangeOne"
          @panelChange="panelChangeOne"
          v-model="formData.year"
          style="width: 140px; margin-right: 10px;"
        />
        <!-- <a-select
          show-search
          placeholder="请选择费用类型"
          option-filter-prop="children"
          :getPopupContainer="triggerNode => triggerNode.parentNode"
          dropdownClassName="rangePickerIceGai"
          v-model="formData.budgetName"
          style="width: 140px"
          @change="onChange"
        >
          <a-select-option value="N">
            按照物资项
          </a-select-option>
          <a-select-option value="Y">
            按照物资大类
          </a-select-option>
        </a-select> -->
      </div>
    </template>
    <template slot="text">
      <a-spin :spinning="loading">
        <div class="content">
          <div class="topBox">
            <CrewBudgetInfo
              v-for="(item,index) in list"
              :disabled="true"
              :index="index"
              :show="true"
              :dragging="dragging"
              :key="index"
              :record="item"
              @click="showModal(item)"
            />
          </div>
          <div class="bottom">
            <!-- <a-button class="btn" icon="question-circle" size="large" @click="showModal">全部异常</a-button> -->
          </div>
        </div>
      </a-spin>
      <div ref="initwarp" :class="{ initwarp: styleName === 'themeb' }">
        <a-modal
          title="预算金额修改"
          :visible="visible"
          :confirm-loading="confirmLoading"
          :getContainer="() => $refs.initwarp"
          width="500px"
          @cancel="handleCancel"
        >
          <a-form-model
            ref="ruleForm"
            :model="editInfo"
          >
            <a-form-model-item
              label="已用金额"
              prop="amountUsed"
              :rules="{ required: true, message: '请输入'}">
              <a-input
                v-model="editInfo.amountUsed"
                type="number"
              />
            </a-form-model-item>
            <a-form-model-item
              label="预算金额"
              prop="budgetAmount"
              :rules="{ required: true, message: '请输入'}">
              <a-input
                v-model="editInfo.budgetAmount"
                type="number"
              />
            </a-form-model-item>
          </a-form-model>
          <template slot="footer">
            <!-- <a-button class="btnExp" :loading="confirmLoading" @click="doSave">取消</a-button> -->
            <a-button class="btnExp" :loading="confirmLoading" @click="doSave">保存</a-button>
          </template>
        </a-modal>
      </div>
    </template>
  </CrewCardPlus>
</template>
<script>
import CrewCardPlus from '../Crew/CrewCardPlus'
import * as echarts from 'echarts'
import countTo from 'vue-count-to'
import * as budgetNewApi from '@/api/device/budgetNew'
import * as itemApi from '@/api/device/item'
import CrewBudgetInfo from '../Crew/CrewBudgetInfo'
import { requestBuilder } from '@/utils/util'
import moment from 'moment'
import { STable } from '@/components'
// const echarts = require("echarts")
export default {
  name: 'CrewBudgetAnalysisShow',
  components: {
    CrewCardPlus,
    countTo,
    STable,
    CrewBudgetInfo
  },
  props: {
    styleName: {
      type: String,
      default: () => {
        return ''
      }
    },
    dragging: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data () {
    return {
      yearShowOne: false,
      mode: '',
      formData: {
        year: moment()
      },
      list: [],
      loading: false,
      visible: false,
      confirmLoading: false,
      queryParam: {
        year: moment()
      },
      editInfo: {}
    }
  },
  // 页面初始化挂载dom
  mounted () {
    this.initApi()
  },
  created () {
  },
  methods: {
    showModal (record) {
      this.visible = true
      this.editInfo = record
    },
    doSave () {
      this.confirmLoading = true
      const param = requestBuilder('update', [{
        ...this.editInfo,
        statYear: this.formData.year ? moment(this.formData.year).format('YYYY') : ''
      }])
      budgetNewApi.modifyBudgetCockpit(param).then(res => {
        if (res.code !== '0000') {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '提交失败！'
          })
        } else {
          this.$notification.success({
            message: '系统消息',
            description: res.message || '提交成功！'
          })
          this.handleCancel()
          this.initApi()
        }
      }).then(() => {
      }).finally(() => {
        this.confirmLoading = false
      })
    },
    handleCancel (e) {
      this.visible = false
      this.editInfo = {}
    },
    openChangeOne (status) {
      if (status) {
        this.yearShowOne = true
      } else {
        this.yearShowOne = false
      }
    },
    // 得到年份选择器的值
    panelChangeOne (value) {
      this.formData.year = value
      this.yearShowOne = false
      this.initApi()
    },
    initApi () {
      this.loading = true
      const param = requestBuilder('noEdit', {
        ...this.formData,
        statYear: moment(this.formData.year).format('YYYY')
      })
      budgetNewApi.queryBudgetCockpit(param).then(res => {
        this.list = res.result || []
        console.log(res.result, 'bud')
        // this.vendorNames = res.result.vendorNames || []
        // this.winnerBidderCounts = res.result.proportions || []
      }).then(() => {
        // this.getLoadEcharts()
      }).finally(() => {
        this.loading = false
      })
    },
    onChange () {
      this.initApi()
    }
  },
  beforeDestroy () {
    window.removeEventListener('resize', () => {
      this.myChart.resize()
    })
  }
}
</script>
<style lang="less" scoped>
  .selectInp {
    display: flex;
  }
  .content {
    width: 100%;
    height: 500px;
    padding: 15px;
    .topBox {
      height: 435px;
      overflow-y: scroll;
    }
    .bottom {
      height: 50px;
      display: flex;
      justify-content: right;
      align-items: center;
      .btn {
        width: 150px;
        height: 35px;
      }
    }
  }
  .btnExp {
  width: 80px;
}
</style>
