<template>
  <CrewCardPlus title="当年消耗总计">
    <template slot="right-icon">
      <div class="selectInp">
        <a-select
          show-search
          allowClear
          placeholder="请选择部门"
          option-filter-prop="children"
          :getPopupContainer="triggerNode => triggerNode.parentNode"
          dropdownClassName="rangePickerIceGai"
          v-model="formData.departmentSysId"
          style="width: 140px"
          @change="onChange"
        >
          <a-select-option :label="item.label" :value="item.value" v-for="item in depts" :key="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </div>
    </template>
    <template slot="text">
      <a-spin :spinning="loading">
        <div class="content">
          <div class="topBox">
            <CrewTotalInfo
              v-for="(item,index) in list"
              :dragging="dragging"
              :key="index"
              :index="index"
              :record="item"/>
          </div>
          <div class="bottom">
            <a-button class="btn" icon="question-circle" size="large" @click="showModal">查看详情</a-button>
          </div>
        </div>
      </a-spin>
      <div ref="initwarp" :class="{ initwarp: styleName === 'themeb' }">
        <a-modal
          title="当年消耗总计"
          :visible="visible"
          :confirm-loading="confirmLoading"
          :getContainer="() => $refs.initwarp"
          width="700px"
          :bodyStyle="{ height: '600px' }"
          @cancel="handleCancel"
        >
          <div class="selectInp" style="margin-bottom: 15px;">
            <a-select
              show-search
              allowClear
              placeholder="请选择部门"
              option-filter-prop="children"
              :getPopupContainer="triggerNode => triggerNode.parentNode"
              dropdownClassName="rangePickerIceGai"
              v-model="modalFrom.departmentSysId"
              style="width: 140px"
              @change="getInfoModal"
            >
              <a-select-option :label="item.label" :value="item.value" v-for="item in depts" :key="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
          <div class="topBox2">
            <CrewTotalInfo
              v-for="(item,index) in modalList"
              :inModal="true"
              :dragging="dragging"
              :index="index"
              :key="index"
              :record="item"/>
          </div>
          <template slot="footer">
            <a-button class="btnExp" :loading="confirmLoading" @click="handleCancel">取消</a-button>
            <!-- <a-button class="btnExp" :loading="confirmLoading" @click="doSave">保存</a-button> -->
          </template>
        </a-modal>
      </div>
    </template>
  </CrewCardPlus>
</template>
<script>
import CrewCardPlus from '../Crew/CrewCardPlus'
import countTo from 'vue-count-to'
import * as matuApi from '@/api/material/matu'
import * as baseApi from '@/api/material/base'
import CrewTotalInfo from '../Crew/CrewTotalInfo'
import { requestBuilder } from '@/utils/util'
import moment from 'moment'
import { STable } from '@/components'
import { ORG_ID, PERSON_ID, ROLE_NAME } from '@/store/mutation-types'
import Vue from 'vue'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_MS_ENV = USER_ORG_ID === '1.100.104'
// const echarts = require("echarts")
export default {
  name: 'CrewTotalConsumption',
  components: {
    CrewCardPlus,
    countTo,
    STable,
    CrewTotalInfo
  },
  props: {
    styleName: {
      type: String,
      default: () => {
        return ''
      }
    },
    dragging: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data () {
    return {
      yearShowOne: false,
      mode: '',
      formData: {
        // year: moment()
      },
      modalFrom: {},
      list: [],
      modalList: [],
      loading: false,
      visible: false,
      confirmLoading: false,
      queryParam: {
        year: moment()
      },
      editInfo: {},
      depts: []
    }
  },
  // 页面初始化挂载dom
  mounted () {
    this.initApi()
    this.init()
  },
  created () {
  },
  methods: {
    init () {
      // 职能部门
      if (IS_MS_ENV) {
        baseApi.getCommboxById({ id: 'deptMdGcbFirst', sqlParams: { byOrgId: '1' } }).then(res => {
          if (res.code === '0000') {
            this.depts = res.result
          }
        })
      } else {
        baseApi.getCommboxById({ id: 'dept', sqlParams: { byOrgId: '1' } }).then(res => {
          if (res.code === '0000') {
            this.depts = res.result
          }
        })
      }
    },
    showModal (record) {
      this.visible = true
      this.editInfo = record
      this.modalFrom.departmentSysId = this.formData.departmentSysId
      this.getInfoModal()
    },
    getInfoModal () {
      this.confirmLoading = true
      const param = requestBuilder('', {
        ...this.modalFrom
      },
      null,
      null)
      matuApi.getThisYearXh(param).then(res => {
        this.modalList = res.result.data || []
        // this.vendorNames = res.result.vendorNames || []
        // this.winnerBidderCounts = res.result.proportions || []
      }).then(() => {
        // this.getLoadEcharts()
      }).finally(() => {
        this.confirmLoading = false
      })
    },
    doSave () {
      // this.confirmLoading = true
      // const param = requestBuilder('update', [{
      //   ...this.editInfo,
      //   statYear: this.formData.year ? moment(this.formData.year).format('YYYY') : ''
      // }])
      // matuApi.modifyBudgetCockpit(param).then(res => {
      //   if (res.code !== '0000') {
      //     this.$notification.error({
      //       message: '系统消息',
      //       description: res.message || '提交失败！'
      //     })
      //   } else {
      //     this.$notification.success({
      //       message: '系统消息',
      //       description: res.message || '提交成功！'
      //     })
      //     this.handleCancel()
      //     this.initApi()
      //   }
      // }).then(() => {
      // }).finally(() => {
      //   this.confirmLoading = false
      // })
    },
    handleCancel (e) {
      this.visible = false
      this.editInfo = {}
    },
    openChangeOne (status) {
      if (status) {
        this.yearShowOne = true
      } else {
        this.yearShowOne = false
      }
    },
    // 得到年份选择器的值
    panelChangeOne (value) {
      this.formData.year = value
      this.yearShowOne = false
      this.initApi()
    },
    initApi () {
      this.loading = true
      const param = requestBuilder('out', {
        ...this.formData
        // statYear: moment(this.formData.year).format('YYYY')
      })
      matuApi.getThisYearXh(param).then(res => {
        this.list = res.result || []
        // this.vendorNames = res.result.vendorNames || []
        // this.winnerBidderCounts = res.result.proportions || []
      }).then(() => {
        // this.getLoadEcharts()
      }).finally(() => {
        this.loading = false
      })
    },
    onChange () {
      this.initApi()
    }
  },
  beforeDestroy () {
    window.removeEventListener('resize', () => {
      this.myChart.resize()
    })
  }
}
</script>
<style lang="less" scoped>
  .selectInp {
    display: flex;
  }
  .content {
    width: 100%;
    height: 500px;
    padding: 15px;
    .topBox {
      height: 435px;
      overflow-y: scroll;
    }
    .bottom {
      height: 50px;
      display: flex;
      justify-content: right;
      align-items: center;
      .btn {
        width: 150px;
        height: 35px;
      }
    }
  }
  .btnExp {
  width: 80px;
}
    .topBox2 {
      height: 500px;
      overflow-y: scroll;
    }
</style>
