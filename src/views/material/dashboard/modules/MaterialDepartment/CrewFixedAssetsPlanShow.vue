<template>
  <CrewCard :title="`固定资产完成率(${infoData.allFinishedRatio}%)展示`">
    <template slot="text">
      <div class="box">
        <div class="form-box">
          <div @click.stop>
            <a-date-picker
              mode="year"
              placeholder="选择年份"
              :open="yearShowOne"
              :allowClear="false"
              format="YYYY"
              :getCalendarContainer="triggerNode => triggerNode.parentNode"
              dropdownClassName="rangePickerIceGai"
              @change="panelChangeOne"
              @openChange="openChangeOne"
              @panelChange="panelChangeOne"
              v-model="queryParam.year"
              style="width: 100%;"
            />
          </div>
        </div>
        <div class="bottom-box">
          <div class="leftNum"">
            <div class="boxFlex">
              <div class="num">
                <countTo
                  :startVal="0"
                  :endVal="infoData.allFinishedAmount"
                  prefix=""
                  suffix=""/>
              </div>
              <div class="text">完成金额</div>
            </div>
          </div>
          <div class="rightIcon">
            <div class="boxFlex">
              <div class="num">
                <countTo
                  :startVal="0"
                  :endVal="infoData.contractAmount"
                  prefix=""
                  suffix=""/>
              </div>
              <div class="text">合同总金额</div>
            </div>
          </div>
        </div>
      </div>
      <div ref="initwarp" :class="{ initwarp: styleName === 'themeb' }">
        <a-modal
          title="合同总金额修改"
          :visible="visible"
          :confirm-loading="confirmLoading"
          :getContainer="() => $refs.initwarp"
          width="500px"
          @cancel="handleCancel"
        >
          <a-form-model
            ref="ruleForm"
            :model="editInfo"
          >
            <a-form-model-item
              label="完成金额"
              prop="allFinishedAmount"
              :rules="{ required: true, message: '请输入'}">
              <a-input
                v-model="editInfo.allFinishedAmount"
                type="number"
              />
            </a-form-model-item>
          </a-form-model>
          <template slot="footer">
            <!-- <a-button class="btnExp" :loading="confirmLoading" @click="doSave">取消</a-button> -->
            <a-button class="btnExp" :loading="confirmLoading" @click="doSave">保存</a-button>
          </template>
        </a-modal>
      </div>
    </template>
  </CrewCard>
</template>
<script>
import '@/style/baseColor.less'
import CrewCard from '../Crew/CrewCard'
import countTo from 'vue-count-to'
import EchartPicInit from './EchartPicInitM'
import * as fixedAssetApi from '@/api/material/fixedAsset'
import { STable } from '@/components'
import { requestBuilder } from '@/utils/util'
import moment from 'moment'
export default {
  name: 'CrewFixedAssetsPlanShow',
  components: {
    CrewCard,
    countTo,
    EchartPicInit,
    STable
  },
  props: {
    styleName: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data () {
    return {
      loading: false,
      yearShowOne: false,
      sourceData: [],
      infoData: {},
      queryParam: {
        year: moment()
        // proportion: 5
      },
      visible: false,
      confirmLoading: false,
      // 表格数据
      dataSource: [],
      editInfo: {}
    }
  },
  // 页面初始化挂载dom
  mounted () {
    this.initApi()
  },
  methods: {
    openChangeOne (status) {
      if (status) {
        this.yearShowOne = true
      } else {
        this.yearShowOne = false
      }
    },
    // 得到年份选择器的值
    panelChangeOne (value) {
      this.queryParam.year = value
      this.yearShowOne = false
      this.initApi()
    },
    doSave () {
      this.confirmLoading = true
      const param = requestBuilder('update', {
        ...this.editInfo
      })
      fixedAssetApi.modifyAllFinishedAmount(param).then(res => {
        if (res.code !== '0000') {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '提交失败！'
          })
        } else {
          this.$notification.success({
            message: '系统消息',
            description: res.message || '提交成功！'
          })
          this.handleCancel()
          this.initApi()
        }
      }).then(() => {
      }).finally(() => {
        this.confirmLoading = false
      })
    },
    showModal () {
      this.visible = true
      this.editInfo = {
        allFinishedAmount: this.infoData.allFinishedAmount,
        year: moment(this.queryParam.year).format('YYYY')
      }
    },
    handleCancel (e) {
      this.visible = false
      this.editInfo = {}
    },
    moment (text, pattern = 'YYYY-MM') {
      return text ? this.$moment(text).format(pattern) : ''
    },
    initApi () {
      this.loading = true
      const param = requestBuilder('', { year: moment(this.queryParam.year).format('YYYY') }, 1, 1)
      fixedAssetApi.queryAllFinishedAmount(param).then(res => {
        console.log(res, 'finis')
        this.infoData = res.result
      }).then(() => {
        // this.getLoadEcharts()
      }).finally(() => {
        this.loading = false
      })
    }
    // jump (record) {
    //   const materialNum = record.materialNum
    //   const contractNum = record.contractNum
    //   this.visible = false
    //   this.$router.push({
    //     name: 'ContractManagement',
    //     params: {
    //       materialNum: materialNum,
    //       contractNum: contractNum
    //     }
    //   })
    // }
  }
}
</script>
<style lang="less" scoped>
.box {
  height: 145px;
  padding: 0 10px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  .bottom-box {
    display: flex;
    margin-top: 10px;
    width: 100%;
    justify-content: space-between;
    height: 100px;
    .leftNum {
      width: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    .rightIcon {
      width: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .iconDesc {
        font-size: 12px;
        opacity: 0.7;
      }
      .iconStyle {
        font-size: 70px;
        color: rgb(222, 16, 16);
      }
    }
  }
  .form-box {
    width: 100%;
  }
  .num {
    font-size: 34px;
    color: #F8AA4B;
  }
  .boxFlex {
    text-align: center;
    /* margin-top: 25px; */
  }
}
.btnExp {
  width: 80px;
}
/* .initwarp {
  /deep/.ant-modal-content {
    background-color: #03203dbd !important;
  }
  /deep/.ant-modal-header {
    background-color: #03203dbd !important;
  }
} */
</style>
