<template>
  <CrewCardPlus title="供应商中标预警">
    <template slot="right-icon">
      <div class="selectInp">
        <!-- <a style="font-size: 12px;" href="javascript:void(0)" @click="showModal">详情</a> -->
        <a-date-picker
          mode="year"
          placeholder="选择年份"
          :open="yearShowOne"
          format="YYYY"
          :getCalendarContainer="triggerNode => triggerNode.parentNode"
          dropdownClassName="rangePickerIceGai"
          @change="panelChangeOne"
          @openChange="openChangeOne"
          @panelChange="panelChangeOne"
          v-model="formData.year"
          style="width: 140px; margin-right: 10px;"
        />
        <a-select
          v-if="!IS_MD"
          show-search
          placeholder="请选择"
          option-filter-prop="children"
          :getPopupContainer="triggerNode => triggerNode.parentNode"
          dropdownClassName="rangePickerIceGai"
          v-model="formData.byMaterialType"
          style="width: 140px"
          @change="onChange"
        >
          <a-select-option value="N">
            按照物资项
          </a-select-option>
          <a-select-option value="Y">
            按照物资大类
          </a-select-option>
        </a-select>
      </div>
    </template>
    <template slot="text">
      <div class="content">
        <a-spin :spinning="loading">
          <div style="width: 100%; height: 100%; background: #89898918;">
            <div id="echartSS" style="width: 100%; height: 435px; min-width: 400px;" />
          </div>
        </a-spin>
      </div>
      <div class="bottom">
        <a-button class="btn" icon="retweet" size="large" @click="showModal">查看详情</a-button>
      </div>
      <div ref="initwarp" :class="{ initwarp: styleName === 'themeb' }">
        <a-modal
          title="供应商中标预警详情"
          :visible="visible"
          :confirm-loading="confirmLoading"
          :getContainer="() => $refs.initwarp"
          width="1000px"
          :bodyStyle="{ height: '600px' }"
          @cancel="handleCancel"
        >
          <s-table
            ref="table"
            :columns="columns"
            :components="components"
            :data="loadData"
            :scroll="scroll"
            :rowClassName="rowClassName"
            :customRow="rowClick"
            :bordered="false"
            :pageSizeOptions="pageSizeOptions"
            :pageSize="defaultPageSize"
            no-striped
            :showPagination="true"
            rowKey="uuid"
          >
            <span
              slot="serial"
              slot-scope="text, record, index"
            >{{ index + 1 }}</span>

            <span
              v-if="IS_MD"
              slot="itemNum"
              slot-scope="text"
            >
              <a v-for="num in (text||'').split('、')" :key="num" href="javascript:void(0)" @click="goProject(num)">
                {{ num }}
              </a>
            </span>

            <!-- <span slot="createDate" slot-scope="text">{{ moment(text) }}</span> -->
            <!-- <span
              slot="ellipsis2"
              slot-scope="text"
            >
              <a href="javascript:void(0)" v-for="item in text.split(/(?=、)/g)" :key="item" @click="jump(item)">{{ item }} </a>
            </span> -->
          </s-table>
          <template slot="footer">
            <a-button class="btnExp" :loading="loadingExport" @click="doExport">导出</a-button>
          </template>
        </a-modal>
      </div>
    </template>
  </CrewCardPlus>
</template>
<script>
import CrewCardPlus from '../Crew/CrewCardPlus'
import * as echarts from 'echarts'
import countTo from 'vue-count-to'
import * as earlyWarningApi from '@/api/material/earlyWarning'
import { requestBuilder } from '@/utils/util'
import moment from 'moment'
import { STable } from '@/components'
// const echarts = require("echarts")

import Vue from 'vue'
import { ORG_ID, PERSON_NAME } from '@/store/mutation-types'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_MD = USER_ORG_ID === '1.100.104'
export default {
  name: 'CrewSingleSourceM',
  components: {
    CrewCardPlus,
    countTo,
    STable
  },
  props: {
    styleName: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data () {
    this.components = {
      header: {
        cell: (h, props, children) => {
          const { key, ...restProps } = props
          const col = this.columns.find(col => {
            const k = col.dataIndex || col.key
            return k === key
          })

          if (!col || !col.width) {
            return h('th', { ...restProps }, [...children])
          }

          const dragProps = {
            key: col.dataIndex || col.key,
            class: 'table-draggable-handle',
            attrs: {
              w: 10,
              x: col.width,
              z: 1,
              axis: 'x',
              draggable: true,
              resizable: false
            },
            on: {
              dragging: (x, y) => {
                col.width = Math.max(x, 1)
              }
            }
          }
          const drag = h('vue-draggable-resizable', { ...dragProps })
          return h('th', { ...restProps, class: 'resize-table-th' }, [...children, drag])
        }
      }
    }
    return {
      IS_MD,
      yearShowOne: false,
      loadingExport: false,
      mode: '',
      formData: {
        byMaterialType: 'Y',
        year: moment()
      },
      dataSource: [],
      vendorNames: [],
      winnerBidderCounts: [],
      listDate: {
        xList: [],
        yList: []
      },
      loading: false,
      visible: false,
      confirmLoading: false,
      // 表格数据
      columns: IS_MD ? [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' },
          align: 'center',
          width: '10%'
        },
        {
          title: '供应商',
          dataIndex: 'vendorName',
          align: 'center',
          width: '15%'
        },
        {
          title: '中标次数',
          dataIndex: 'actualNum',
          align: 'center',
          width: '15%'
        },
        {
          title: '项目号',
          dataIndex: 'itemNum',
          align: 'center',
          scopedSlots: { customRender: 'itemNum' },
          width: '10%'
        },
        {
          title: '项目名称',
          dataIndex: 'itemName',
          ellipsis: true,
          align: 'center',
          scopedSlots: { customRender: 'itemName' },
          width: '30%'
        }
      ] : [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' },
          dataIndex: 'type',
          align: 'center',
          ellipsis: true,
          width: '10%'
        },
        {
          title: '物资名称',
          dataIndex: 'materialName',
          align: 'center',
          ellipsis: true,
          width: '15%'
        },
        {
          title: '物资大类',
          dataIndex: 'materialTypeName',
          align: 'center',
          ellipsis: true,
          width: '15%'
        },
        {
          title: '总次数',
          dataIndex: 'totalBidderCount',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: '10%'
        },
        {
          title: '中标供应商',
          dataIndex: 'vendorName',
          ellipsis: true,
          align: 'center',
          width: '30%'
        },
        {
          title: '中标次数',
          dataIndex: 'winnerBidderCount',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: '10%'
        },
        {
          title: '中标比例(%)',
          dataIndex: 'proportion',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: '10%'
        }
      ],
      scroll: {
        x: '100%',
        y: 'calc(50vh)',
        scrollToFirstRowOnChange: false
      },
      selectedRows: [],
      selectedRowKeys: [],
      defaultPageSize: 20,
      pageSizeOptions: ['10', '20', '30', '50'],
      queryParam: {
        year: moment()
        // proportion: 5
      },
      loadData: parameter => {
        let method = 'earlyWarningSinglePurchaseDetail'
        const param = {
          ...this.formData
        }
        if (IS_MD) {
          method = 'earlyWarningSinglePurchaseMsDetail'
          param.byMaterialType = ''
        }
        return earlyWarningApi[method](requestBuilder(
          '', {
            ...param,
            searchYear: moment(this.formData.year).format('YYYY')
          }, parameter.pageNo, parameter.pageSize, parameter.sortField, parameter.sortOrder)).then(res => {
          if (res.code === '0000') {
            this.dataSource = res.result.data
            return res.result
          } else {
            this.$message.error(res.message)
          }
        })
      },
      rowClick: record => ({
        on: {
          click: () => {
            // this.jump(record)
          }
        }
      }),
      rowClassName: record => {
      }
    }
  },
  // 页面初始化挂载dom
  mounted () {
    this.initApi()
  },
  created () {
  },
  methods: {
    goProject (num) {
      this.$router.push({
        name: 'ProjectApplication',
        params: {
          itemNum: num
        }
      })
    },
    showModal () {
      this.visible = true
      if (this.$refs.table) {
        this.$refs.table.refresh()
      }
    },
    handleCancel (e) {
      this.visible = false
    },
    openChangeOne (status) {
      if (status) {
        this.yearShowOne = true
      } else {
        this.yearShowOne = false
      }
    },
    // 得到年份选择器的值
    panelChangeOne (value) {
      this.formData.year = value
      this.yearShowOne = false
      this.initApi()
    },
    initApi () {
      this.loading = true
      const param = requestBuilder('', {
        ...this.formData,
        searchYear: moment(this.formData.year).format('YYYY')
      }, 1, 9)
      let method = 'earlyWarningSinglePurchase'

      if (IS_MD) {
        method = 'earlyWarningSinglePurchaseMs'
        param.byMaterialType = ''
      }
      earlyWarningApi[method](param).then(res => {
        this.dataSource = res.result || []
        this.vendorNames = res.result.vendorNames || []
        if (IS_MD) {
          this.winnerBidderCounts = res.result.winnerBidderCounts || []
        } else {
          this.winnerBidderCounts = res.result.proportions || []
        }
      }).then(() => {
        this.getLoadEcharts()
      }).finally(() => {
        this.loading = false
      })
    },
    onChange () {
      this.initApi()
    },
    getLoadEcharts () {
      this.dom = this.$echarts.init(
        document.getElementById('echartSS')
      )
      this.dom.clear()
      this.dom.setOption({
        tooltip: {
          trigger: 'axis',
          formatter: IS_MD ? '{b}<br />{a0}: {c0}次' : '{b}<br />{a0}: {c0}%'
        },
        grid: {
          top: '5%',
          left: '5%',
          right: '10%',
          bottom: '10%',
          containLabel: true
        },
        toolbox: {
          show: true
        },
        yAxis: [
          {
            type: 'category',
            show: true,
            // prettier-ignore
            data: this.vendorNames || [],
            axisLabel: {
              // show: true,
              textStyle: {
                fontSize: 12,
                color: '#2789CB'
              }
            }
          }
        ],
        xAxis: [
          {
            name: IS_MD ? '次' : '%',
            type: 'value',
            show: true,
            splitLine: {
              show: false
            },
            axisLine: {
              show: true
            },
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: 12,
                color: '#2789CB'
              }
            }
          }
        ],
        series: [
          {
            name: '简易询价',
            type: 'bar',
            data: this.winnerBidderCounts || [],
            barWidth: 14,
            // label: {
            //   normal: {
            //     show: true,
            //     position: 'insideRight',
            //     formatter: '{c}%'
            //   }
            // },
            itemStyle: {
              normal: {
                color: function (params) {
                  var colorList = [
                    ['#40F750', '#40f74f77'],
                    ['#41FD71', '#41fd7072'],
                    ['#C8FA42', '#c9fa426f'],

                    ['#C9FB42', '#cafb4276'],
                    ['#C8FA41', '#c9fa4170'],
                    ['#FBE042', '#fbdf4271'],

                    ['#FDBF42', '#fdbf426d'],
                    ['#FDA341', '#fda24167'],
                    ['#FC7242', '#fc704269'],

                    ['#FD4141', '#fd414164']
                  ]
                  var index = params.dataIndex
                  if (params.dataIndex >= colorList.length) {
                    index = params.dataIndex % colorList.length
                  }
                  return new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                    { offset: 0, color: colorList[index][1] },
                    { offset: 1, color: colorList[index][0] }
                  ])
                }
              }
            }
          }
        ]
      })
      window.addEventListener('resize', () => {
        this.dom.resize()
        this.dom.resize()
      })
    },
    doExport () {
      this.loadingExport = true
      let method = 'exportEarlyWarningSinglePurchaseDetail'
      const param = {
        ...this.formData
      }
      if (IS_MD) {
        param.byMaterialType = ''
        method = 'exportEarlyWarningSinglePurchaseMs'
      }
      earlyWarningApi[method](requestBuilder(
        '', {
          ...param,
          searchYear: moment(this.formData.year).format('YYYY')
        }, null, null)).then(res => {
        if (res && res.data && res.headers['content-disposition']) {
          const link = document.createElement('a')
          const url = window.URL.createObjectURL(res.data)
          const contentDisposition = res.headers['content-disposition']
          let filename = contentDisposition.replace(/(.*;)?filename=([^;]+).*/i, '$2')
          try {
            filename = decodeURIComponent(escape(filename))
          } catch (e) {}
          document.body.appendChild(link)
          link.style.display = 'none'
          link.download = filename
          link.href = url
          link.click()
          link.remove()
          window.URL.revokeObjectURL(url)
        } else {
          this.$notification.error({
            message: '系统消息',
            description: '下载失败！'
          })
        }
      }).finally(() => {
        this.loadingExport = false
      })
    }
  },
  beforeDestroy () {
    window.removeEventListener('resize', () => {
      this.myChart.resize()
    })
  }
}
</script>
<style lang="less" scoped>
  .selectInp {
    display: flex;
  }
  .content {
    width: 100%;
    height: 450px;
    padding: 15px;
    color: #FD4141;
  }
  .bottom {
    height: 50px;
    display: flex;
    justify-content: right;
    align-items: center;
    padding-right: 25px;
    .btn {
      width: 150px;
      height: 35px;
    }
  }
  .btnExp {
  width: 80px;
}
</style>
