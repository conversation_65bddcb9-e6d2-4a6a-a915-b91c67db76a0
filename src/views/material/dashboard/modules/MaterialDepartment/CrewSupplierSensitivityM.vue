<template>
  <CrewCardPlus title="供应商敏感性">
    <template slot="right-icon">
      <div class="selectInp">
        <!-- <a style="font-size: 12px;" href="javascript:void(0)" @click="showModal">详情</a> -->
        <a-date-picker
          mode="year"
          placeholder="选择年份"
          :open="yearShowOne"
          format="YYYY"
          :getCalendarContainer="triggerNode => triggerNode.parentNode"
          dropdownClassName="rangePickerIceGai"
          @change="panelChangeOne"
          @openChange="openChangeOne"
          @panelChange="panelChangeOne"
          v-model="formData.searchYear"
          style="width: 140px; margin-right: 10px;"
        />
      </div>
    </template>
    <template slot="text">
      <div class="content">
        <div class="topBox">
          <CrewEchart
            v-for="(item, index) in rendList"
            :key="index"
            :id="`CrewEchart${index}`"
            :record="item"
            :loadData="item.pieList"
          />
        </div>
        <div class="bottom">
          <a-button class="btn" icon="question-circle" size="large" @click="showModal">全部敏感企业</a-button>
        </div>
      </div>
      <div ref="initwarp" :class="{ initwarp: styleName === 'themeb' }">
        <a-modal
          title="查看详情"
          :visible="visible"
          :confirm-loading="confirmLoading"
          :getContainer="() => $refs.initwarp"
          width="1000px"
          :bodyStyle="{ height: '600px' }"
          @cancel="handleCancel"
        >
          <s-table
            ref="table"
            :columns="columns"
            :components="components"
            :data="loadData"
            :scroll="scroll"
            :rowClassName="rowClassName"
            :customRow="rowClick"
            :bordered="false"
            :pageSizeOptions="pageSizeOptions"
            :pageSize="defaultPageSize"
            :showPagination="true"
            no-striped
            rowKey="uuid"
          >
            <span
              slot="serial"
              slot-scope="text, record, index"
            >{{ index + 1 }}</span>
            <!-- <span
              slot="vendorGroup"
              slot-scope="text, record"
            >
              <a href="javascript:void(0)" @click="bidWinningRate(record)" :loading="queryLoading">详情</a>
            </span> -->
          </s-table>
          <template slot="footer">
            <a-button class="btnExp" :loading="loadingExport" @click="doExport">导出</a-button>
          </template>
        </a-modal>
      </div>
    </template>
  </CrewCardPlus>
</template>
<script>
import CrewCardPlus from '../Crew/CrewCardPlus'
import CrewEchart from '../Crew/CrewEchart'
import moment from 'moment'
import * as earlyWarningApi from '@/api/material/earlyWarning'
import { requestBuilder } from '@/utils/util'
import { STable } from '@/components'
// import * as echarts from 'echarts'
import countTo from 'vue-count-to'
export default {
  name: 'CrewSupplierSensitivityM',
  components: {
    CrewCardPlus,
    countTo,
    CrewEchart,
    STable
  },
  props: {
    styleName: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data () {
    this.components = {
      header: {
        cell: (h, props, children) => {
          const { key, ...restProps } = props
          const col = this.columns.find(col => {
            const k = col.dataIndex || col.key
            return k === key
          })

          if (!col || !col.width) {
            return h('th', { ...restProps }, [...children])
          }

          const dragProps = {
            key: col.dataIndex || col.key,
            class: 'table-draggable-handle',
            attrs: {
              w: 10,
              x: col.width,
              z: 1,
              axis: 'x',
              draggable: true,
              resizable: false
            },
            on: {
              dragging: (x, y) => {
                col.width = Math.max(x, 1)
              }
            }
          }
          const drag = h('vue-draggable-resizable', { ...dragProps })
          return h('th', { ...restProps, class: 'resize-table-th' }, [...children, drag])
        }
      }
    }
    return {
      yearShowOne: false,
      loadingExport: false,
      rendList: [],
      visible: false,
      confirmLoading: false,
      // 表格数据
      dataSource: [],
      columns: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' },
          dataIndex: 'type',
          align: 'center',
          ellipsis: true,
          width: 50
        },
        {
          title: '询价单号',
          dataIndex: 'rfqlineNum',
          align: 'center',
          ellipsis: true,
          width: 100
        },
        {
          title: '物资编码',
          dataIndex: 'materialNum',
          scopedSlots: { customRender: 'materialNum' },
          align: 'center',
          ellipsis: true,
          width: 100
        },
        {
          title: '物资名称',
          dataIndex: 'materialName',
          align: 'center',
          ellipsis: true,
          width: 200
        },
        {
          title: '供应商组合',
          dataIndex: 'bidVendorsDown',
          scopedSlots: { customRender: 'vendorGroup' },
          ellipsis: true,
          align: 'center',
          width: 800
        },
        {
          title: '组合出现次数',
          dataIndex: 'vendorCount',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 100
        },
        {
          title: '组合总次数',
          dataIndex: 'vendorAllCount',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 100
        },
        {
          title: '组合出现比例',
          dataIndex: 'vendorProportion',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 100
        }
      ],
      scroll: {
        x: '100%',
        y: 'calc(50vh)',
        scrollToFirstRowOnChange: false
      },
      selectedRows: [],
      selectedRowKeys: [],
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20', '30', '40'],
      loadData: parameter => {
        return earlyWarningApi.earlyWarningVendorSensitive(requestBuilder('', { searchYear: this.formData.searchYear.format('YYYY') }, parameter.pageNo, parameter.pageSize, parameter.sortField, parameter.sortOrder)).then(res => {
          if (res.code === '0000') {
            this.dataSource = res.result.data
            return res.result
          } else {
            this.$message.error(res.message)
          }
        })
      },
      rowClick: record => ({
        on: {}
      }),
      rowClassName: record => {
        return 'tablerow'
      },
      formData: {
        searchYear: moment()
      }
    }
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      const param = requestBuilder('', { searchYear: this.formData.searchYear.format('YYYY') }, 1, 6)
      earlyWarningApi.earlyWarningVendorSensitive(param).then(res => {
        this.rendList = res.result.data || []
      })
    },
    showModal () {
      this.visible = true
      if (this.$refs.table) {
        this.$refs.table.refresh()
      }
    },
    openChangeOne (status) {
      if (status) {
        this.yearShowOne = true
      } else {
        this.yearShowOne = false
      }
    },
    // 得到年份选择器的值
    panelChangeOne (value) {
      this.formData.searchYear = value
      this.yearShowOne = false
      this.init()
    },
    handleCancel (e) {
      this.visible = false
    },
    doExport () {
      this.loadingExport = true
      earlyWarningApi.exportEarlyWarningVendorSensitive(requestBuilder('', { searchYear: this.formData.searchYear.format('YYYY') }, null, null)).then(res => {
        if (res && res.data && res.headers['content-disposition']) {
          const link = document.createElement('a')
          const url = window.URL.createObjectURL(res.data)
          const contentDisposition = res.headers['content-disposition']
          let filename = contentDisposition.replace(/(.*;)?filename=([^;]+).*/i, '$2')
          try {
            filename = decodeURIComponent(escape(filename))
          } catch (e) {}
          document.body.appendChild(link)
          link.style.display = 'none'
          link.download = filename
          link.href = url
          link.click()
          link.remove()
          window.URL.revokeObjectURL(url)
        } else {
          this.$notification.error({
            message: '系统消息',
            description: '下载失败！'
          })
        }
      }).finally(() => {
        this.loadingExport = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
  .selectInp {
    display: flex;
  }
  .content {
    width: 100%;
    height: 500px;
    padding: 15px;
    .topBox {
      height: 435px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }
    .bottom {
      height: 50px;
      display: flex;
      justify-content: right;
      align-items: center;
      .btn {
        width: 150px;
        height: 35px;
      }
    }
  }
  .btnExp {
  width: 80px;
}
</style>
