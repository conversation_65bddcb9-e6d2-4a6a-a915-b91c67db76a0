<template>
  <CrewCardPlus :title="`${subTitle}(${sumNum})`">
    <template slot="title">
      <div class="head" style="cursor: pointer;" @click="showModal">{{ subTitle }}({{ sumNum }})</div>
    </template>
    <template slot="right-icon">
      <div class="selectInp">
        <!-- <a-month-picker
          v-model="formData.statMonth"
          :getCalendarContainer="triggerNode => triggerNode.parentNode"
          dropdownClassName="rangePickerIceGai"
          placeholder="选择月份"
          @change="onChange"
          style="width: 140px; margin-right: 10px;"
        /> -->
        <a-range-picker
          v-model="formData.dates"
          :disabled="disabled"
          style="width: 200px; margin-right: 10px;"
          :getCalendarContainer="triggerNode => triggerNode.parentNode"
          dropdownClassName="rangePickerIceGai"
          @change="onChange"
        />
        <a-input
          v-model="formData.projectNum"
          :disabled="disabled"
          placeholder="招采编号"
          style="width: 100px; margin-right: 10px;"
          @pressEnter="doSearch()"
        />
        <a-input
          v-model="formData.projectName"
          :disabled="disabled"
          placeholder="项目名称"
          style="width: 100px"
          @pressEnter="doSearch()"
        />
      </div>
    </template>
    <template slot="text">
      <div class="content">
        <a-spin :spinning="loading">
          <a-collapse style="margin-bottom: 10px;" v-for="(item, index) in projList" :key="index" :bordered="false" expandIconPosition="right">
            <a-collapse-panel :key="item.uuid">
              <template slot="header">
                <div style="width: 100%;">
                  <div class="top">
                    <div class="topic">{{ item.projectName }}<span class="secTopic">{{ item.projectNum }}</span></div>
                    <div style="white-space: nowrap; margin-left: 4px;">
                      <!-- <a-tag color="#f50">{{ item.tradingPlatform }}</a-tag> -->
                      <a-tag color="#f50">{{ item.projectKind }}</a-tag>
                      <a-tag color="#f50">{{ item.lastStatusName }}</a-tag>
                    </div>
                  </div>
                  <div class="text">
                    <!-- <div class="label">业主单位</div><div class="value">{{ item.orgName }}</div><div class="divid">|</div> -->
                    <div class="label">预计金额</div><div class="value">￥{{ item.planMoney }}</div><div class="divid">|</div>
                    <div class="label">采购员</div><div class="value">{{ item.businessMan }}</div>
                  </div>
                  <div v-show="item.bidders.length > 0" class="bottom" @click.stop="notShow">
                    <div class="bottom-title">投标</div>
                    <div class="bottom-text">
                      <div v-for="(info, ins) in item.bidders" :key="ins" class="checkbox">
                        <a-icon v-show="info.isWin" class="icongre" type="check-circle" theme="twoTone" two-tone-color="#0ce32c"/>
                        <a-tag :color="info.isWin ? '#108ee9' : '#d4d4d4'">{{ info.vendorName }}</a-tag>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
              <div class="step">
                <Timeline
                  :disabled="true"
                  v-model="item.current"
                  :list="item.timeLineList"
                  @click="openWindow($event,item)"
                />
              </div>
            </a-collapse-panel>
          </a-collapse>
        </a-spin>
      </div>
      <!-- <div class="content-bottom">
        <div class="info-bottom">
          <span class="desc">基层({{ formInfo.companySum || 0 }}/￥{{ formInfo.companyAmount || 0 }})</span>|
          <span class="desc">集团({{ formInfo.groupSum || 0 }}/￥{{ formInfo.groupAmount || 0 }})</span>|
          <span class="desc">地方({{ formInfo.localSum || 0 }}/￥{{ formInfo.localAmount || 0 }})</span>|
          <span class="desc">OA({{ formInfo.oaSum || 0 }}/￥{{ formInfo.oaAmount || 0 }})</span>
        </div>
      </div> -->
      <div ref="initwarp" :class="{ initwarp: styleName === 'themeb' }">
        <a-modal
          title="查看详情"
          :visible="visible"
          :confirm-loading="confirmLoading"
          :getContainer="() => $refs.initwarp"
          width="1000px"
          :bodyStyle="{ height: '600px' }"
          :footer="null"
          @cancel="handleCancel"
        >
          <s-table
            ref="table"
            :columns="columns"
            :components="components"
            :data="loadData"
            :scroll="scroll"
            :rowClassName="rowClassName"
            :customRow="rowClick"
            :bordered="false"
            :pageSizeOptions="pageSizeOptions"
            :pageSize="defaultPageSize"
            :showPagination="true"
            no-striped
            rowKey="uuid"
          >
            <span
              slot="serial"
              slot-scope="text, record, index"
            >{{ index + 1 }}</span>
          </s-table>
        </a-modal>
        <CrewTenderGoodsModal ref="CrewTenderGoodsModal" :styleName="styleName"/>
      </div>
    </template>
  </CrewCardPlus>
</template>
<script>
import CrewCardPlus from '../Crew/CrewCardPlus'
import countTo from 'vue-count-to'
import { Timeline, STable } from '@/components'
import { requestBuilder } from '@/utils/util'
import { ORG_ID } from '@/store/mutation-types'
import Vue from 'vue'
import * as cockpitApi from '@/api/material/cockpit'
import moment from 'moment'
import CrewTenderGoodsModal from '@/views/material/dashboard/modules/Crew/CrewTenderGoodsModal.vue'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_MS_ENV = USER_ORG_ID === '1.100.104'
export default {
  name: 'CrewTender',
  components: {
    CrewCardPlus,
    countTo,
    Timeline,
    STable,
    CrewTenderGoodsModal
    // ACollapse,
    // ACollapsePanel
  },
  props: {
    styleName: {
      type: String,
      default: () => {
        return ''
      }
    },
    disabled: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data () {
    this.components = {
      header: {
        cell: (h, props, children) => {
          const { key, ...restProps } = props
          const col = this.columns.find(col => {
            const k = col.dataIndex || col.key
            return k === key
          })

          if (!col || !col.width) {
            return h('th', { ...restProps }, [...children])
          }

          const dragProps = {
            key: col.dataIndex || col.key,
            class: 'table-draggable-handle',
            attrs: {
              w: 10,
              x: col.width,
              z: 1,
              axis: 'x',
              draggable: true,
              resizable: false
            },
            on: {
              dragging: (x, y) => {
                col.width = Math.max(x, 1)
              }
            }
          }
          const drag = h('vue-draggable-resizable', { ...dragProps })
          return h('th', { ...restProps, class: 'resize-table-th' }, [...children, drag])
        }
      }
    }
    return {
      IS_MS_ENV,
      loading: true,
      timeIndex: 0,
      timelength: 0,
      timeLineList: [
        {
          projectType: 322,
          filingDate: 12
        },
        {
          projectType: 322,
          filingDate: 12
        },
        {
          projectType: 322,
          filingDate: 12
        },
        {
          projectType: 322,
          filingDate: 12
        }
      ],
      formData: {
        projectName: '',
        dates: this.getDate()
      },
      projList: [],
      formInfo: {
        companyAmount: 0,
        companySum: 0,
        groupAmount: 0,
        groupSum: 0,
        localAmount: 0,
        localSum: 0,
        oaAmount: 0,
        oaSum: 0
      },
      visible: false,
      confirmLoading: false,
      // 表格数据
      dataSource: [],
      columns: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' },
          dataIndex: 'type',
          align: 'center',
          ellipsis: true,
          width: 50
        },
        {
          title: '物资编码',
          dataIndex: 'materialNum',
          scopedSlots: { customRender: 'materialNum' },
          align: 'center',
          ellipsis: true,
          width: 100
        },
        {
          title: '物资名称',
          dataIndex: 'materialName',
          align: 'center',
          ellipsis: true,
          width: 100
        },
        {
          title: '数量',
          dataIndex: 'quantity',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 100
        },
        {
          title: '税前金额',
          dataIndex: 'lineCost',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 150
        },
        {
          title: '出库时间',
          dataIndex: 'createDate',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 150
        },
        {
          title: '计划年月',
          dataIndex: 'statMonth',
          align: 'center',
          ellipsis: true,
          width: 150
        }
      ],
      scroll: {
        x: '100%',
        y: 'calc(60vh)',
        scrollToFirstRowOnChange: false
      },
      selectedRows: [],
      selectedRowKeys: [],
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20', '30', '40'],
      loadData: parameter => {
        const param = requestBuilder(
          '',
          {
            projectName: this.formData.projectName || '',
            projectNum: this.formData.projectNum || '',
            startTime: this.formData.dates.length > 0 ? this.formData.dates[0].format('YYYY-MM-DD') : '',
            endTime: this.formData.dates.length > 0 ? this.formData.dates[1].format('YYYY-MM-DD') : ''
          },
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder
        )
        return cockpitApi.queryTender(param).then(res => {
          if (res.code === '0000') {
            this.dataSource = res.result.data
            return res.result
          } else {
            this.$message.error(res.message)
          }
        })
      },
      rowClick: record => ({
        on: {}
      }),
      rowClassName: record => {
        return 'tablerow'
      }
    }
  },
  created () {
    this.doSearch()
  },
  mounted () {
    console.log(this.$refs, '采购结果提交')
  },
  computed: {
    sumNum () {
      return this.formInfo.companySum + this.formInfo.groupSum + this.formInfo.localSum
    },
    subTitle () {
      return IS_MS_ENV ? '非招相关' : '招投标相关'
    }
  },
  methods: {
    openWindow (index, item) {
      if (IS_MS_ENV && index === 3 && item.timeLineList[index].title === '采购结果提交') {
        console.log(item.rfqlineNum, '采购结果提交')
        console.log(this.$refs.CrewTenderGoodsModal, '采购结果提交')
        this.$refs.CrewTenderGoodsModal.showModal(item.rfqlineNum)
      }
      if (IS_MS_ENV && index === 4) {
        const win = item.winnerBidderList || []
        if (win.length) {
          window.open(win[0].bidWinningDoc, '', 'toolbar=no,status=no,menubar=no,location=no,height=500,width=500,scrollbars=yes')
        }
      }
    },
    getDate () {
      const dates = new Date()
      const month = dates.getMonth() + 1
      const month2 = month >= 12 ? 1 : month + 1
      const year = dates.getFullYear()
      const year2 = month >= 12 ? year + 1 : year
      const value = [moment(year + '-' + month + '-01', 'YYYY-MM-DD'), moment(year2 + '-' + month2 + '-01', 'YYYY-MM-DD')]
      return value
    },
    doSearch () {
      this.loading = true
      const param = requestBuilder('', {
        projectNum: this.formData.projectNum || '',
        projectName: this.formData.projectName || '',
        startTime: this.formData.dates.length > 0 ? this.formData.dates[0].format('YYYY-MM-DD') : '',
        endTime: this.formData.dates.length > 0 ? this.formData.dates[1].format('YYYY-MM-DD') : ''
      },
      null,
      null)
      cockpitApi.queryTender(param).then(res => {
        res.result.list = res.result.list.map(item => {
          item.timeLineList = item.timeLineList.map((titem, index) => {
            titem.index = index
            return titem
          })
          return item
        })
        this.projList = res.result.list || []
        this.formInfo.companyAmount = res.result.companyAmount || 0
        this.formInfo.companySum = res.result.companySum || 0
        this.formInfo.groupAmount = res.result.groupAmount || 0
        this.formInfo.groupSum = res.result.groupSum || 0
        this.formInfo.localAmount = res.result.localAmount || 0
        this.formInfo.localSum = res.result.localSum || 0
        this.formInfo.oaAmount = res.result.oaAmount || 0
        this.formInfo.oaSum = res.result.oaSum || 0
      }).finally(() => {
        this.loading = false
      })
    },
    changeActive (index) {
      this.timeIndex = index
      this.formData = this.timeLineList[this.timeIndex]
    },
    notShow () {
      console.log('notShow')
    },
    showModal () {
      if (this.disabled) {
        return
      }
      this.visible = true
      if (this.$refs.table) {
        this.$refs.table.refresh()
      }
    },
    handleCancel (e) {
      this.visible = false
    },
    onChange (value) {
      this.doSearch()
    }
  }
}
</script>
<style lang="less" scoped>
  .content-bottom {
    height: 35px;
    .info-bottom {
      height: 35px;
      padding-left: 15px;
      display: flex;
      align-items: center;
      .desc {
        width: 23%;
        margin: 0 5px;
      }
    }
  }
  .content {
    width: 100%;
    height: 470px;
    padding: 15px;
    overflow-y: scroll;
    .top {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }
    .topic {
      font-size: 16px;
      font-weight: 700;
    }
    .secTopic {
      width: auto;
      font-size: 10px;
      font-weight: normal;
      opacity: 0.7;
      margin-left: 10px;
    }
    .text {
      display: flex;
      font-size: 10px;
      .label {
        opacity: 0.7;
        margin-right: 5px;
      }
      .value {
        font-weight: 700;
      }
      .divid {
        margin: 0 5px;
        opacity: 0.7;
      }
    }
    .flexBox {
      display: flex;
      justify-content: space-between;
    }
    .bottom {
      margin-top: 15px;
      width: 100%;
      .bottom-title {
        font-size: 14;
        font-weight: 700;
        font-style: italic;
      }
      .bottom-text {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        .checkbox {
          margin-right: 2px;
          position: relative;
          .ant-tag-has-color {
            color: #232323;
          }
          .icongre {
            position: absolute;
            right: 2px;
            top: -3px;
            z-index: 20;
          }
        }
      }
    }
    .step {
      width: 100%;
      margin: 20px 0;
      border-top: 1px solid #d4d4d4;
    }
    .liubiao {
      color: #ffffff;
      background-color: #ef0f0f;
      border-radius: 2px;
      line-height: 22px;
      padding: 0 4px;
    }
    .head {
      cursor: pointer;
    }
    .selectInp {
      display: flex;
    }
}
</style>
