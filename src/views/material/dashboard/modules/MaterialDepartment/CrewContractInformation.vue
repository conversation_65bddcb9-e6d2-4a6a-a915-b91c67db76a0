<template>
  <CrewCardPlus title="合同信息">
    <template slot="text">
      <div class="content">
        <div class="left">
          <div class="form">
            <a-range-picker
              v-model="dates"
              :disabled="disabled"
              style="width: 100%;"
              :getCalendarContainer="triggerNode => triggerNode.parentNode"
              dropdownClassName="rangePickerIceGai"
              @change="onChange"
            />
          </div>
          <div class="qt">
            <div class="qt-ys">
              <div class="qt-title">
                <div class="topic">签订</div>
                <countTo
                  :startVal="0"
                  class="topNum"
                  :endVal="record.signCount"
                  prefix=""
                  suffix=""
                />
                <span class="descript">份</span>
                <div>
                  <countTo
                    :startVal="0"
                    class="subNum"
                    style="font-size: 12px;"
                    :endVal="record.signAmount"
                    prefix="￥"
                    suffix=""/>
                </div>
              </div>
              <div class="qt-title">
                <div class="topic">验收</div>
                <countTo
                  :startVal="0"
                  class="topNum"
                  :endVal="record.polineCount"
                  prefix=""
                  suffix=""
                />
                <span class="descript">份</span>
                <div>
                  <countTo
                    :startVal="0"
                    class="subNum"
                    style="font-size: 12px;"
                    :endVal="record.polineAmount"
                    prefix="￥"
                    suffix=""
                  />
                </div>
              </div>
            </div>
            <a-spin :spinning="loading">
              <div class="qt-list">
                <div v-for="(item, index) in protionList" :key="index">
                  <div class="qt-list-head">
                    <span class="qt-list-headleft">{{ item.vendorName }}</span>
                    <countTo
                      :startVal="0"
                      style="font-size: 10px;"
                      :endVal="item.totalBaseCostSum"
                      prefix="￥"
                      suffix=""
                    />
                  </div>
                  <a-tooltip :title="`￥${item.paidAmountSum}`">
                    <a-progress
                      :stroke-color="{
                        '0%': '#F4F768',
                        '100%': '#47FEAA',
                      }"
                      :percent="item.paidRatio"
                      :show-info="false"
                    />
                  </a-tooltip>
                </div>
              </div>
            </a-spin>
          </div>
          <div class="btnjd">
            <a-button class="btn" icon="question-circle" size="large" :disabled="disabled" @click="showModal('1')">全部进度</a-button>
          </div>
        </div>
        <div class="right">
          <div class="form-2">
            <div class="form-title">供应商付款金额排序</div>
            <a v-show="sortStatus === 'desc'" href="javascript:void(0)" class="form-btn" :disabled="disabled" @click="changeSort('asc')"><a-icon type="sort-ascending" />从高到低</a>
            <a v-show="sortStatus === 'asc'" href="javascript:void(0)" class="form-btn" :disabled="disabled" @click="changeSort('desc')"><a-icon type="sort-descending" />从低到高</a>
          </div>
          <a-spin :spinning="sortLoading">
            <div class="qt">
              <div v-for="(item, index) in sortList" :key="index" class="qt-list2">
                <i style="width: 20px;">{{ index + 1 }}</i>
                <div class="company">
                  <div class="company">{{ item.vendorName }}</div>
                  <div class="company">{{ item.vendorNum }}</div>
                </div>
                <div>
                  <countTo
                    :startVal="0"
                    style="font-size: 10px;"
                    :endVal="item.paidAmountSum"
                    prefix="￥"
                    suffix=""
                  />
                </div>
              </div>
            </div>
          </a-spin>
          <div class="btnjd">
            <a-button class="btn" icon="question-circle" size="large" :disabled="disabled" @click="showModal('2')">全部排序</a-button>
          </div>
        </div>
      </div>
      <div ref="initwarp" :class="{ initwarp: styleName === 'themeb' }">
        <a-modal
          title="查看详情"
          :visible="visible"
          :confirm-loading="confirmLoading"
          :getContainer="() => $refs.initwarp"
          width="1000px"
          :bodyStyle="{ height: '600px' }"
          :footer="null"
          @cancel="handleCancel"
        >
          <s-table
            ref="table"
            :columns="columns"
            :components="components"
            :data="loadData"
            :scroll="scroll"
            :rowClassName="rowClassName"
            :customRow="rowClick"
            :bordered="false"
            :pageSizeOptions="pageSizeOptions"
            :pageSize="defaultPageSize"
            :showPagination="true"
            no-striped
            rowKey="uuid"
          >
            <span
              slot="serial"
              slot-scope="text, record, index"
            >{{ index + 1 }}</span>
            <span
              slot="contractNum"
              slot-scope="text, record"
            >
              <a href="javascript:void(0)" @click="jump(record)">{{ text }} </a>
            </span>
          </s-table>
        </a-modal>
      </div>
    </template>
  </CrewCardPlus>
</template>
<script>
import CrewCardPlus from '../Crew/CrewCardPlus'
import countTo from 'vue-count-to'
import { requestBuilder } from '@/utils/util'
import * as cockpitApi from '@/api/material/cockpit'
import { STable } from '@/components'
import moment from 'moment'
import { ORG_ID } from '@/store/mutation-types'
import Vue from 'vue'

const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_MD_ENV = USER_ORG_ID === '1.100.104'

export default {
  name: 'CrewContractInformation',
  components: {
    CrewCardPlus,
    countTo,
    STable
  },
  props: {
    styleName: {
      type: String,
      default: () => {
        return ''
      }
    },
    disabled: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data () {
    this.components = {
      header: {
        cell: (h, props, children) => {
          const { key, ...restProps } = props
          const col = this.columns.find(col => {
            const k = col.dataIndex || col.key
            return k === key
          })

          if (!col || !col.width) {
            return h('th', { ...restProps }, [...children])
          }

          const dragProps = {
            key: col.dataIndex || col.key,
            class: 'table-draggable-handle',
            attrs: {
              w: 10,
              x: col.width,
              z: 1,
              axis: 'x',
              draggable: true,
              resizable: false
            },
            on: {
              dragging: (x, y) => {
                col.width = Math.max(x, 1)
              }
            }
          }
          const drag = h('vue-draggable-resizable', { ...dragProps })
          return h('th', { ...restProps, class: 'resize-table-th' }, [...children, drag])
        }
      }
    }
    return {
      IS_MD_ENV,
      loading: false,
      sortLoading: false,
      // contract 合同; pay 付款
      action: 'contract',
      origin: '',
      dates: this.getDate(),
      record: {
        polineAmount: 0,
        polineCount: 0,
        signCount: 0,
        signAmount: 0
      },
      protionList: [],
      sortList: [],
      sortStatus: 'desc',
      visible: false,
      confirmLoading: false,
      // 表格数据
      dataSource: [],
      columns: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' },
          dataIndex: 'type',
          align: 'center',
          ellipsis: true,
          width: 50
        },
        ...(IS_MD_ENV ? [] : [{
          title: '合同编号',
          dataIndex: 'contractNum',
          scopedSlots: { customRender: 'contractNum' },
          align: 'center',
          ellipsis: true,
          width: 200
        },
        {
          title: '供应商代码',
          dataIndex: 'paymentNum',
          scopedSlots: { customRender: 'paymentNum' },
          align: 'center',
          ellipsis: true,
          width: 200
        }]),
        {
          title: '供应商名称',
          dataIndex: 'vendorName',
          align: 'center',
          ellipsis: true,
          width: 200
        },
        ...(IS_MD_ENV ? [] : [{
          title: '已付比例（%）',
          dataIndex: 'paidRatio',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 120
        },
        {
          title: '已付金额（￥）',
          dataIndex: 'paidAmountSum',
          align: 'center',
          ellipsis: true,
          sorter: true,
          width: 150
        }]),
        {
          title: '总金额（￥）',
          dataIndex: 'totalBaseCostSum',
          align: 'center',
          sorter: true,
          ellipsis: true,
          width: 150
        }
      ],
      scroll: {
        x: '100%',
        y: 'calc(60vh)',
        scrollToFirstRowOnChange: false
      },
      selectedRows: [],
      selectedRowKeys: [],
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20', '30', '40'],
      loadData: parameter => {
        const param = requestBuilder(this.action,
          {
            contractCreateDateStart: this.dates.length > 0 ? this.dates[0].format('YYYY-MM-DD') : '',
            contractCreateDateEnd: this.dates.length > 0 ? this.dates[1].format('YYYY-MM-DD') : ''
          },
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder
          // this.origin === '1' ? parameter.sortField : 'paidAmountSum',
          // this.origin === '1' ? parameter.sortOrder : this.sortStatus
        )
        return cockpitApi.queryContractVendor(param).then(res => {
          if (res.code === '0000') {
            this.dataSource = res.result.data
            return res.result
          } else {
            this.$message.error(res.message)
          }
        })
      },
      rowClick: record => ({
        on: {}
      }),
      rowClassName: record => {
        return 'tablerow'
      }
    }
  },
  created () {
    this.initApi()
  },
  methods: {
    getDate () {
      const dates = new Date()
      const month = dates.getMonth() + 1
      const month2 = month >= 12 ? 1 : month + 1
      const year = dates.getFullYear()
      const year2 = month >= 12 ? year + 1 : year
      const value = [moment(year + '-' + month + '-01', 'YYYY-MM-DD'), moment(year2 + '-' + month2 + '-01', 'YYYY-MM-DD')]
      return value
    },
    showModal (num) {
      this.origin = num
      this.visible = true
      if (num === '1') {
        this.action = 'contract'
      } else if (num === '2') {
        this.action = IS_MD_ENV ? 'poline' : 'pay'
      }
      if (this.$refs.table) {
        this.$refs.table.refresh()
      }
    },
    handleCancel (e) {
      this.visible = false
      this.origin = ''
    },
    onChange () {
      this.initApi()
    },
    initApi () {
      this.getRecord()
      this.getContractVendor()
      this.getSortList()
    },
    getRecord () {
      const param = requestBuilder('', {
        contractCreateDateStart: this.dates.length > 0 ? this.dates[0].format('YYYY-MM-DD') : '',
        contractCreateDateEnd: this.dates.length > 0 ? this.dates[1].format('YYYY-MM-DD') : ''
      })
      cockpitApi.queryContract(param).then(res => {
        this.record.polineAmount = res.result.polineAmount || 0
        this.record.polineCount = res.result.polineCount || 0
        this.record.signCount = res.result.signCount || 0
        this.record.signAmount = res.result.signAmount || 0
      }).finally(() => {
      })
    },
    getContractVendor () {
      this.loading = true
      this.action = 'contract'
      const param = requestBuilder('contract', {
        contractCreateDateStart: this.dates.length > 0 ? this.dates[0].format('YYYY-MM-DD') : '',
        contractCreateDateEnd: this.dates.length > 0 ? this.dates[1].format('YYYY-MM-DD') : ''
      })
      cockpitApi.queryContractVendor(param).then(res => {
        this.protionList = res.result.data || []
        this.protionList.forEach(item => {
          item.paidRatio = Number(item.paidRatio)
        })
      }).finally(() => {
        this.loading = false
      })
    },
    getSortList () {
      this.sortLoading = true
      this.action = IS_MD_ENV ? 'poline' : 'pay'
      const param = requestBuilder(this.action,
        {
          contractCreateDateStart: this.dates.length > 0 ? this.dates[0].format('YYYY-MM-DD') : '',
          contractCreateDateEnd: this.dates.length > 0 ? this.dates[1].format('YYYY-MM-DD') : ''
        },
        1,
        15,
        'paidAmountSum',
        this.sortStatus
      )
      cockpitApi.queryContractVendor(param).then(res => {
        this.sortList = res.result.data || []
      }).finally(() => {
        this.sortLoading = false
      })
    },
    changeSort (value) {
      this.sortStatus = value
      this.getSortList()
    },
    jump (record) {
      // const materialNum = record.materialNum
      const contractNum = record.contractNum
      this.visible = false
      this.$router.push({
        name: 'ContractManagement',
        params: {
          // materialNum: materialNum,
          contractNum: contractNum
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
  .content {
    width: 100%;
    height: 500px;
    display: flex;
    justify-content: space-between;
    .form {
      height: 50px;
    }
    .form-2 {
      height: 50px;
      display: flex;
      justify-content: space-between;
      .form-title {
        font-size: 16px;
        font-weight: 700;
        font-style: italic;
      }
    }
    .right {
      width: 50%;
      padding: 15px;

    }
    .left {
      width: 50%;
      padding: 15px;
      border-right: 1px solid #666;
    }
    .qt {
      overflow-y: scroll;
      height: 370px;
      .qt-ys {
        height: 90px;
        padding: 0 5px;
        display: flex;
        justify-content: space-between;
      }
      .qt-list {
        padding: 0 5px;
        height: 280px;
        .qt-list-head {
          display: flex;
          justify-content: space-between;
          .qt-list-headleft {
            /* max-width: 160px; */
            width: auto;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        /* overflow: scroll; */
      }
      .qt-list2 {
        padding: 5px 25px;
        font-size: 12px;
        display: flex;
        opacity: .7;
        justify-content: space-between;
        align-items: center;
        .company {
          width: auto;
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .qt-title {
        width: 50%;
        .topic {
          font-size: 16px;
          font-weight: 700;
          opacity: 0.8;
        }
        .topNum {
          font-size: 26px;
          font-weight: 700;
        }
        .descript {
          font-size: 12px;
          opacity: 0.8;
        }
        .subNum {
          font-size: 12px;
          opacity: 0.8;
        }
      }
    }
    .btnjd {
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      .btn {
        width: 150px;
        height: 35px;
      }
    }
  }
</style>
