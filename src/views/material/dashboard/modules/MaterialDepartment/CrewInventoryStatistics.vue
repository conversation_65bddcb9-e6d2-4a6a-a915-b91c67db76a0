<template>
  <CrewCardPlus title="库存账龄统计">
    <!-- <template slot="right-icon">
      <div class="selectInp">
        <a-date-picker
          mode="year"
          placeholder="选择年份"
          :open="yearShowOne"
          format="YYYY"
          :getCalendarContainer="triggerNode => triggerNode.parentNode"
          dropdownClassName="rangePickerIceGai"
          @change="panelChangeOne"
          @openChange="openChangeOne"
          @panelChange="panelChangeOne"
          v-model="formData.year"
          style="width: 140px; margin-right: 10px;"
        />
      </div>
    </template> -->
    <template slot="text">
      <div class="content">
        <a-spin :spinning="loading">
          <div style="width: 100%; height: 100%; background: #89898918;">
            <div id="echartIS" style="width: 100%; height: 435px; min-width: 400px;" />
          </div>
        </a-spin>
      </div>
      <div class="bottom">
        <!-- <a-button class="btn" icon="retweet" size="large" @click="showModal">查看详情</a-button> -->
      </div>
      <div ref="initwarp" :class="{ initwarp: styleName === 'themeb' }">
        <a-modal
          title="重点物资统计详情"
          :visible="visible"
          :confirm-loading="confirmLoading"
          :getContainer="() => $refs.initwarp"
          width="1000px"
          :bodyStyle="{ height: '600px' }"
          @cancel="handleCancel"
        >
          <s-table
            ref="table"
            :columns="columns"
            :components="components"
            :data="loadData"
            :scroll="scroll"
            :rowClassName="rowClassName"
            :customRow="rowClick"
            :bordered="false"
            no-striped
            :showPagination="false"
            :rowKey="(record,index)=>{return index}"
          >
            <span
              slot="serial"
              slot-scope="text, record, index"
            >{{ index + 1 }}</span>

          </s-table>
        </a-modal>
      </div>
    </template>
  </CrewCardPlus>
</template>
<script>
import CrewCardPlus from '../Crew/CrewCardPlus'
import * as echarts from 'echarts'
import countTo from 'vue-count-to'
import * as earlyWarningApi from '@/api/material/earlyWarning'
import { requestBuilder, takeTreeByKey } from '@/utils/util'
import moment from 'moment'
import { STable } from '@/components'
import * as baseApi from '@/api/material/base'
// const echarts = require("echarts")

import Vue from 'vue'
import { ORG_ID, PERSON_NAME } from '@/store/mutation-types'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_MD = USER_ORG_ID === '1.100.104'
export default {
  name: 'CrewInventoryStatistics',
  components: {
    CrewCardPlus,
    countTo,
    STable
  },
  props: {
    styleName: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data () {
    this.components = {
      header: {
        cell: (h, props, children) => {
          const { key, ...restProps } = props
          const col = this.columns.find(col => {
            const k = col.dataIndex || col.key
            return k === key
          })

          if (!col || !col.width) {
            return h('th', { ...restProps }, [...children])
          }

          const dragProps = {
            key: col.dataIndex || col.key,
            class: 'table-draggable-handle',
            attrs: {
              w: 10,
              x: col.width,
              z: 1,
              axis: 'x',
              draggable: true,
              resizable: false
            },
            on: {
              dragging: (x, y) => {
                col.width = Math.max(x, 1)
              }
            }
          }
          const drag = h('vue-draggable-resizable', { ...dragProps })
          return h('th', { ...restProps, class: 'resize-table-th' }, [...children, drag])
        }
      }
    }
    return {
      IS_MD,
      yearShowOne: false,
      loadingExport: false,
      mode: '',
      formData: {
        year: moment()
      },
      dataSource: [],
      vendorNames: [],
      winnerBidderOutCounts: [], // 出库
      winnerBidderCounts: [],
      listDate: {
        xList: [],
        yList: []
      },
      overStockYears: [],
      loading: false,
      visible: false,
      confirmLoading: false,
      // 表格数据

      columns: [
        {
          title: '年份',
          dataIndex: 'year',
          align: 'center',
          width: 120
        },
        {
          title: '26大类',
          dataIndex: 'className',
          align: 'center',
          width: 120
        },
        {
          title: '期初金额',
          dataIndex: 'qckc',
          align: 'center',
          width: 120
        },
        {
          title: '收入金额',
          dataIndex: 'srhj',
          align: 'center',
          width: 120
        },
        {
          title: '发出金额',
          dataIndex: 'fchj',
          align: 'center',
          width: 120
        },
        {
          title: '期末金额',
          dataIndex: 'qmkc',
          align: 'center',
          width: 120
        }

      ],
      scroll: {
        x: '100%',
        y: 'calc(50vh)',
        scrollToFirstRowOnChange: false
      },
      selectedRows: [],
      selectedRowKeys: [],
      defaultPageSize: 20,
      pageSizeOptions: ['10', '20', '30', '50'],
      loadData: parameter => {
        const param = {
          ...this.formData
        }
        return earlyWarningApi.earlyWarningImportantMaterialDetail(requestBuilder(
          '', {
            ...param,
            searchYear: moment(this.formData.year).format('YYYY')
          }, parameter.pageNo, parameter.pageSize, parameter.sortField, parameter.sortOrder)).then(res => {
          if (res.code === '0000') {
            this.dataSource = res.result.data
            return res.result
          } else {
            this.$message.error(res.message)
          }
        })
      },
      rowClick: record => ({
        on: {
          click: () => {
            // this.jump(record)
          }
        }
      }),
      rowClassName: record => {
      }
    }
  },
  // 页面初始化挂载dom
  mounted () {
    this.initApi()
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      baseApi.getTreeById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'over_stock_year' } }).then(res => {
        if (res.code === '0000') {
          this.overStockYears = res.result
        }
      })
    },
    goProject (num) {
      this.$router.push({
        name: 'ProjectApplication',
        params: {
          itemNum: num
        }
      })
    },
    showModal () {
      this.visible = true
      if (this.$refs.table) {
        this.$refs.table.refresh()
      }
    },
    handleCancel (e) {
      this.visible = false
    },
    openChangeOne (status) {
      if (status) {
        this.yearShowOne = true
      } else {
        this.yearShowOne = false
      }
    },
    // 得到年份选择器的值
    panelChangeOne (value) {
      this.formData.year = value
      this.yearShowOne = false
      this.initApi()
    },
    // 获取下拉框选项文本
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || ''
    },
    initApi () {
      this.loading = true
      const param = requestBuilder('', {
        ejk: false,
        skFylxId: 'nothing'
      })
      earlyWarningApi.earlyWarningOverstockInfo(param).then(res => {
        this.dataSource = []
        const obj = res.result || {}
        for (var key in obj) {
          this.dataSource.push({
            value: obj[key],
            name: this.takeSelectLabel(this.overStockYears, key)
          })
        }
        // this.dataSource.push()
      }).then(() => {
        this.getLoadEcharts()
      }).finally(() => {
        this.loading = false
      })
    },
    onChange () {
      this.initApi()
    },
    getLoadEcharts () {
      this.dom = this.$echarts.init(
        document.getElementById('echartIS')
      )
      this.dom.clear()
      this.dom.setOption({
        title: {
          text: '库存账龄统计',
          left: 'center',
          top: 20,
          textStyle: {
            color: 'rgba(64,158,255)'
          }
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          textStyle: {
            color: 'rgba(64,158,255)'
          }
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: '50%',
            data: this.dataSource || [],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              color: 'rgba(64,158,255, 0.6)'
            },
            labelLine: {
              lineStyle: {
                color: 'rgba(64,158,255, 0.5)'
              },
              smooth: 0.2,
              length: 10,
              length2: 20
            }
          }
        ]
      })
      window.addEventListener('resize', () => {
        this.dom.resize()
      })
      this.dom.on('click', params => {
        const overStockYear = this.overStockYears.find(item => item.label === params.name).value
        if (overStockYear) {
          this.$router.push({
            name: 'OverstockQuery',
            params: {
              overStockYear: overStockYear
            }
          })
        }
      })
    },
    doExport () {
      this.loadingExport = true
      let method = 'exportEarlyWarningSinglePurchaseDetail'
      const param = {
        ...this.formData
      }
      if (IS_MD) {
        param.byMaterialType = ''
        method = 'exportEarlyWarningSinglePurchaseMs'
      }
      earlyWarningApi[method](requestBuilder(
        '', {
          ...param,
          searchYear: moment(this.formData.year).format('YYYY')
        }, null, null)).then(res => {
        if (res && res.data && res.headers['content-disposition']) {
          const link = document.createElement('a')
          const url = window.URL.createObjectURL(res.data)
          const contentDisposition = res.headers['content-disposition']
          let filename = contentDisposition.replace(/(.*;)?filename=([^;]+).*/i, '$2')
          try {
            filename = decodeURIComponent(escape(filename))
          } catch (e) {}
          document.body.appendChild(link)
          link.style.display = 'none'
          link.download = filename
          link.href = url
          link.click()
          link.remove()
          window.URL.revokeObjectURL(url)
        } else {
          this.$notification.error({
            message: '系统消息',
            description: '下载失败！'
          })
        }
      }).finally(() => {
        this.loadingExport = false
      })
    }
  },
  beforeDestroy () {
    window.removeEventListener('resize', () => {
      this.myChart.resize()
    })
  }
}
</script>
<style lang="less" scoped>
  .selectInp {
    display: flex;
  }
  .content {
    width: 100%;
    height: 450px;
    padding: 15px;
    color: #FD4141;
  }
  .bottom {
    height: 50px;
    display: flex;
    justify-content: right;
    align-items: center;
    padding-right: 25px;
    .btn {
      width: 150px;
      height: 35px;
    }
  }
  .btnExp {
  width: 80px;
}
</style>
