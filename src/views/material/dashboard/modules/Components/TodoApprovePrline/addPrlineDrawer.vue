<template>
  <!-- 新增/修改 - 弹框 -->
  <a-drawer
    width="380"
    :mask="true"
    :maskClosable="false"
    :getContainer="false"
    :title="procureTitle"
    :visible="procureVisible"
    @close="hiddenProcureDrawer()"
  >
    <a-spin :spinning="procureLoading">
      <a-form
        :form="procureForm"
        layout="vertical"
        labelAlign="right"
        class="customize-label"
        style="position: relative; z-index: 0"
      >
        <a-form-item
          v-show="[procureFormEdit].includes(procureEditMode)"
          label="计划类型:"
        >
          <a-select
            v-decorator="['lineType', {
              rules: [{ type: 'string', required: true, message: '请选择计划类型' }]
            }]"
            @change="onLineTypeChange"
            @focus="onLineTypeFocus"
          >
            <a-select-option
              v-if="!item.disabled"
              v-for="(item, index) in lineTypefilter"
              :value="item.value"
              :key="index"
            >{{ item.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          v-if="[procureFormEdit].includes(procureEditMode)"
          label="计划描述:"
        >
          <a-textarea
            v-decorator="['description',{
              rules: IS_HJ_ENV || procureLineType === '4' ? [{ type: 'string', required: true, message: '请输入计划描述' }] : []
            }]"
            :autoSize="{ minRows: 3 }"
          />
        </a-form-item>
        <a-form-item
          v-show="[procureFormEdit].includes(procureEditMode) && !procureAdd"
          label="计划年月:"
        >
          <a-monthPicker
            v-decorator="[ 'planMonth', {
              rules: [procureFormEdit].includes(procureEditMode)
                ? [{ type: 'object', required: [procureFormEdit].includes(procureEditMode) && !procureAdd, message: '请选择计划年月' }]
                : []
            }]"
          />
        </a-form-item>
        <a-form-item
          v-show="[procureFormEdit].includes(procureEditMode)"
          label="选择物资:"
        >
          <a-select
            v-decorator="[materialKey, {
              rules: [{ type: procureAdd ? 'array' : 'string', required: true, message: '请选择物资' }]
            }]"
            :mode="procureAdd ? 'multiple' : 'default'"
            :showArrow="false"
            :open="false"
            class="not-select-search"
            @focus="openMaterialDrawer"
          >
            <a-select-option
              v-for="(item, index) in materials"
              :value="item[materialKey]"
              :key="index"
            >{{ item.materialName + (materialKey === 'prlineSysIdRel' ? ' (申报单号:' + item.prlineNumRel + ', 代码:' + item.materialNum + ')' : ' (代码:' + item.materialNum + ', 规格:' + item.jModel + ')') }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          v-show="[procureFormEdit].includes(procureEditMode) && this.materialKey !== 'prlineSysIdRel'"
          label="申请数:"
        >
          <a-input
            v-decorator="['orderQty', {
              rules: [procureFormEdit].includes(procureEditMode)
                ? [{ required: this.materialKey !== 'prlineSysIdRel', type: 'number', validator: this.materialKey !== 'prlineSysIdRel' ? formCheckOrderQty : '' }]
                : []
            }]"
          />
        </a-form-item>
        <a-form-item
          v-show="[procureFormApprove].includes(procureEditMode)"
          label="审核数:"
        >
          <a-input
            v-decorator="['jwspOrderQty', {
              rules: [procureFormApprove].includes(procureEditMode)
                ? [{ required: true, validator: formCheckJwspOrderQty }]
                : []
            }]"
          />
        </a-form-item>
        <a-form-item
          v-if="!IS_MD_ENV && ([procureFormEdit].includes(procureEditMode) && ((IS_SZXD_ENV ? this.materialKey === 'prlineSysIdRel':this.materialKey !== 'prlineSysIdRel') || this.jishou || IS_ZYA_ENV))"
          :label="() => {return IS_SZXD_ENV ? '成本代码' : '使用方向'}"
        >
          <a-tree-select
            v-decorator="['usedFor', {
              rules: [procureFormEdit].includes(procureEditMode)
                ? [{ type: 'string', required:((IS_SZXD_ENV ? this.materialKey === 'prlineSysIdRel':this.materialKey !== 'prlineSysIdRel') || this.jishou ), message: '请选择' }]
                : []
            }]"
            treeNodeFilterProp="label"
            :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
            :treeData="directions"
            @change="onUsedForChange"
            showSearch
            allowClear
          />
        </a-form-item>
        <a-form-item
          v-if="IS_SZXD_ENV && ([procureFormEdit].includes(procureEditMode) && ((this.materialKey === 'prlineSysIdRel')))"
          label="领用部门"
        >
          <a-select
            v-decorator="['departmentSysId', {
              rules: [procureFormEdit].includes(procureEditMode)
                ? [{ type: 'string', required: true, message: '请选择' }]
                : []
            }]"
          >
            <a-select-option
              v-if="!item.disabled"
              v-for="(item, index) in orgIds"
              :value="item.value"
              :key="index"
            >{{ item.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          v-if="IS_JX_ENV"
          v-show="[procureFormEdit].includes(procureEditMode)"
          label="仓库"
        >
          <a-select
            v-decorator="['storehouseSysId', {
              rules: [procureFormEdit].includes(procureEditMode)
                ? [{ type: 'string', required: true, message: '请选择仓库' }]
                : []
            }]"
          >
            <a-select-option
              v-if="!item.disabled"
              v-for="(item, index) in storehouse"
              :value="item.value"
              :key="index"
            >{{ item.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          v-if="IS_SLH_ENV && procureLineType === '20'"
          v-show="[procureFormEdit].includes(procureEditMode)"
          label="仓库"
        >
          <a-select
            v-decorator="['storehouseSysId', {
              rules: [procureFormEdit].includes(procureEditMode)
                ? [{ type: 'string', required: true, message: '请选择仓库' }]
                : []
            }]"
          >
            <a-select-option
              v-if="!item.disabled"
              v-for="(item, index) in slhStorehouse"
              :value="item.value"
              :key="index"
            >{{ item.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          v-show="false"
          v-if="[procureFormEdit].includes(procureEditMode) && this.materialKey !== 'prlineSysIdRel' && IS_HJ_ENV"
          label="申请单号:"
        >
          <a-input
            v-decorator="['hjPrlineNumber', {}]"
          />
        </a-form-item>
        <a-form-item
          v-if="IS_JX_ENV"
          v-show="[procureFormEdit].includes(procureEditMode)"
          label="业务类型"
        >
          <a-select
            v-decorator="['useType', {
              rules: [procureFormEdit].includes(procureEditMode)
                ? [{ type: 'string', required: true, message: '请选择业务类型' }]
                : []
            }]"
          >
            <a-select-option
              v-if="!item.disabled"
              v-for="(item, index) in useType"
              :value="item.value"
              :key="index"
            >{{ item.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          v-show="[procureFormEdit].includes(procureEditMode)"
          label="申请人"
        >
          <a-select
            :disabled="true"
            v-decorator="['requestedBy', {
              rules: [procureFormEdit].includes(procureEditMode)
                ? [{ type: 'string', required: true, message: '请选择申请人' }]
                : []
            }]"
            showSearch
            @change="value => requestedByChange(value,1)"
            optionFilterProp="label"
          >
            <a-select-option
              v-if="!item.disabled"
              v-for="(item, index) in requestedBys"
              :value="item.value"
              :label="item.label"
              :key="index"
            >{{ item.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          v-show="(IS_BYJ_ENV|| IS_MD_ENV) && this.materialKey !== 'prlineSysIdRel'"
          label="使用单位"
        >
          <a-select
            :disabled="this.usedCoFlag"
            v-decorator="['usedCo', {
              rules: [{ required: (IS_BYJ_ENV|| IS_MD_ENV) && this.materialKey !== 'prlineSysIdRel', message: '请选择使用单位' }]
            }]"
          >
            <a-select-option
              v-for="(item, index) in usedCo"
              :value="item.value"
              :key="index"
            >{{ item.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          v-if="IS_BYJ_ENV"
          label="需求日期"
        >
          <a-input
            v-decorator="['demandDate',{rules:[{ required: false, validator: checkDemandDate }]}]"
          />
        </a-form-item>
        <a-form-item
          v-if="IS_SZXD_ENV"
          v-show="!this.$route.params.emergencyRepairUuid"
          label="工单号:">
          <a-select
            style="width: 100%"
            v-decorator="['workorderNo']"
            allowClear
            showSearch
            :filter-option="false"
            placeholder="按工单号搜索更多..."
            @change="value => changeNum(value)"
            @search="doSearchWorkOrder"
          >
            <a-select-option
              v-for="(item, index) in workorder"
              :value="item.label"
              :key="index"
            >{{ item.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          v-if="IS_SZXD_ENV"
          label="设备编号"
        >
          <a-select
            v-decorator="['assetSbNum', {
              rules: [{ required:
                procureForm.getFieldValue('workorderNo'), message: '请选择' }]
            }]"
          >
            <a-select-option
              v-for="(item, index) in assetList"
              :value="item.value"
              :key="index"
            >{{ item.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <span slot="label">备注说明:</span>
          <a-textarea
            v-decorator="['remarks']"
            :autoSize="{ minRows: 3 }"
          />
        </a-form-item>
        <a-form-item
          v-if="procureLineType === '27'"
          label="货币:"
        >
          <a-input
            v-decorator="['currency']"
          />
        </a-form-item>
        <a-form-item
          v-if="procureLineType === '27'"
          label="计划金额:"
        >
          <a-input
            v-decorator="['planAmount']"
          />
        </a-form-item>
        <a-form-item
          v-if="procureLineType === '27'"
          label="到货时间:"
        >
          <a-input
            v-decorator="['arrivedTime']"
          />
        </a-form-item>
        <a-form-item
          v-if="procureLineType === '4'"
          label="补充规格型号:"
        >
          <a-input
            v-decorator="['modelSupply']"
          />
        </a-form-item>
        <a-form-item
          v-if="procureLineType === '4'"
          label="主要技术参数:"
        >
          <a-input
            v-decorator="['maintech']"
          />
        </a-form-item>
        <a-form-item
          v-if="procureLineType === '4'"
          label="质量要求:"
        >
          <a-input
            v-decorator="['quarequired']"
          />
        </a-form-item>
        <a-form-item
          v-if="procureLineType === '4' || IS_MD_ENV"
          :label="() => {return IS_MD_ENV ? '要求工作日期':'要求供货日期'}"
        >
          <a-date-picker
            v-decorator="['reqDeliveryDate']"
          />
        </a-form-item>
      </a-form>
    </a-spin>
    <div class="drawer-footer">
      <div class="footer-fixed">
        <a-button @click="hiddenProcureDrawer()">取消</a-button>
        <a-button
          type="primary"
          :loading="procureLoading"
          @click="doSaveProcurePlan"
        >提交</a-button>
      </div>
    </div>
    <!-- 选择物资列表 -->
    <a-drawer
      :title="materialTitle"
      :visible="materialVisible"
      :maskClosable="false"
      :bodyStyle="{ padding: '80px 20px 10px'}"
      :destroyOnClose="true"
      :width=" IS_BYJ_ENV && materialTitle === '领料列表' ? '850' : '700'"
      @close="hiddenMaterialDrawer"
    >
      <div
        class="drawer-body-header"
        :style="{ height: IS_JYS_ENV ? '165px' : (['10','15'].includes(procureLineType) && !IS_BYJ_ENV)? IS_HJ_ENV ? '40px' : 'auto' : 'auto'}"
      >
        <div
          class="body-header-fixed"
          :style="{ height: IS_JYS_ENV ? '165px' : (['10','15'].includes(procureLineType)&& !IS_BYJ_ENV)? '130px' : 'auto' }"
        >
          <a-form
            layout="inline"
            class="subForm"
          >
            <div style="width: 100%; display: flex;">
              <a-row
                :gutter="materialGrid.gutter"
                style="width: calc(100% - 60px);"
              >
                <a-col
                  v-if="IS_MD_ENV && procureLineType === '1'"
                  :xl="24"
                  :md="24"
                  :sm="materialGrid.sm"
                >
                  <a-form-item label="年度计划">
                    <a-select
                      v-model="materialParam.yearPlan"
                      optionFilterProp="label"
                      showSearch
                      allowClear
                      @change="doMaterialSearch(true)"
                    >
                      <a-select-option
                        v-for="(item, index) in yearPlans"
                        :value="item.yearPrlineNum"
                        :label="item.description"
                        :key="index"
                      >{{ item.description }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="materialGrid.xl"
                  :md="materialGrid.md"
                  :sm="materialGrid.sm"
                >
                  <a-form-item label="物资代码">
                    <a-input
                      v-model="materialParam.materialNum"
                      @pressEnter="doMaterialSearch"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="materialGrid.xl"
                  :md="materialGrid.md"
                  :sm="materialGrid.sm"
                >
                  <a-form-item label="物资名称">
                    <a-input
                      v-model="materialParam.materialName"
                      @pressEnter="doMaterialSearch"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  v-if="['10','15'].includes(procureLineType)"
                  :xl="materialGrid.xl"
                  :md="materialGrid.md"
                  :sm="materialGrid.sm"
                >
                  <a-form-item label="申请单号">
                    <a-input
                      v-model="materialParam.prlineNum"
                      @pressEnter="doMaterialSearch"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="materialGrid.xl"
                  :md="materialGrid.md"
                  :sm="materialGrid.sm"
                >
                  <a-form-item label="规格型号">
                    <a-input
                      v-model="materialParam.jModel"
                      @pressEnter="doMaterialSearch"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="materialGrid.xl"
                  :md="materialGrid.md"
                  :sm="materialGrid.sm"
                  v-if="!(IS_HJ_ENV && procureLineType === '27')"
                >
                  <a-form-item :label="() => {return IS_SZXD_ENV ? '财务科目' : '物资类别'}">
                    <a-select
                      v-model="materialParam.materialClass"
                      optionFilterProp="label"
                      showSearch
                      allowClear
                      @change="doMaterialSearch(true)"
                    >
                      <a-select-option
                        v-for="(item, index) in materialClass"
                        :value="item.value"
                        :label="item.label"
                        :key="index"
                      >{{ item.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  v-if="!(IS_HJ_ENV && procureLineType === '27')"
                  :xl="materialGrid.xl"
                  :md="materialGrid.md"
                  :sm="materialGrid.sm"
                >
                  <a-form-item label="选择仓库">
                    <a-select
                      v-model="materialParam.storehouseSysId"
                      optionFilterProp="label"
                      showSearch
                      :allowClear="!(IS_JX_ENV)"
                      @change="doMaterialSearch(true)"
                    >
                      <a-select-option
                        v-for="(item, index) in storehouse"
                        :value="item.value"
                        :label="item.label"
                        :key="index"
                      >{{ item.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="materialGrid.xl"
                  :md="materialGrid.md"
                  :sm="materialGrid.sm"
                  v-show="IS_WZG_ENV"
                >
                  <a-form-item label="办公用品">
                    <a-select
                      v-model="materialParam.officeSupplies"
                      allowClear
                      @change="doMaterialSearch(true)"
                    >
                      <a-select-option value="Y">是</a-select-option>
                      <a-select-option value="N">否</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  v-if="IS_JYS_ENV"
                  :xl="materialGrid.xl"
                  :md="materialGrid.md"
                  :sm="materialGrid.sm"
                >
                  <a-form-item label="所在单位">
                    <a-select
                      v-model="materialParam.belongToSite"
                      optionFilterProp="label"
                      showSearch
                      allowClear
                      @change="doMaterialSearch"
                    >
                      <a-select-option
                        v-for="(item, index) in belongToSites"
                        :value="item.value"
                        :label="item.label"
                        :key="index"
                      >{{ item.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  v-if="!IS_HJ_ENV && this.materialKey === 'prlineSysIdRel'"
                  :xl="materialGrid.xl"
                  :md="materialGrid.md"
                  :sm="materialGrid.sm"
                >
                  <a-form-item label="是否补库">
                    <a-select
                      v-model="materialParam.isResupply"
                      @change="doMaterialSearch(true)"
                    >
                      <a-select-option
                        v-for="(item, index) in resupply"
                        :value="item.value"
                        :label="item.label"
                        :key="index"
                      >{{ item.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="24"
                  :md="24"
                  :sm="materialGrid.sm"
                  v-if="IS_HJ_ENV && procureLineType === '27'"
                >
                  <a-form-item label="工程名称">
                    <a-select
                      show-search
                      placeholder="查询工程名称"
                      option-filter-prop="children"
                      :filter-option="filterOption"
                      @change="contractChange"
                    >
                      <a-select-option v-for="item in contractOptions" :key="item.purapplyId" :value="item.purapplyId">
                        {{ item.projName }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="materialGrid.xl"
                  :md="materialGrid.md"
                  :sm="materialGrid.sm"
                >
                  <a-form-item label="申报人">
                    <a-select
                      v-model="materialParam.requestedBy"
                      optionFilterProp="label"
                      showSearch
                      allowClear
                      @change="doMaterialSearch(true)"
                    >
                      <a-select-option
                        v-for="(item, index) in requestedBys"
                        :value="item.value"
                        :label="item.label"
                        :key="index"
                      >{{ item.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="materialGrid.xl"
                  :md="materialGrid.md"
                  :sm="materialGrid.sm"
                  v-show="IS_JYS_ENV"
                >
                  <a-form-item label="是否修旧">
                    <a-select
                      v-model="materialParam.isRm"
                      allowClear
                      @change="doMaterialSearch(true)"
                    >
                      <a-select-option value="Y">是</a-select-option>
                      <a-select-option value="N">否</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <div
                style="display: flex; align-items: center; margin-left: 10px; width: 50px; border-left: dashed 1px #cfcfcf"
              >
                <a-button
                  type="primary"
                  style="margin-left: 8px; margin-bottom: 8px"
                  @click="doMaterialSearch"
                >查询</a-button>
              </div>
            </div>
          </a-form>
        </div>
      </div>
      <div class="drawer-body-content" style="{padding: 120px}">
        <s-table
          ref="materialTable"
          :data="materialLoadData"
          :columns="materialColumns"
          :customRow="materialRowClick"
          :scroll="materialScroll"
          :rowSelection="materialRowSelection"
          :clearSelection="materialClearSelection"
          :showPagination="materialShowPagination"
          :pageSizeOptions="materialPageSizeOptions"
          :pageSize="materialDefaultPageSize"
          :rowClassName="materialRowClassName"
          :immediate="materialImmediate"
          :bordered="materialBordered"
          :rowKey="materialKey"
        >
          <span
            slot="curbal"
            slot-scope="text, record"
          >{{ (text || 0) + takeSelectLabel(units, record.orderUnit) }}</span>
          <span
            slot="usedCoName"
            slot-scope="key"
          >{{ takeSelectLabel(usedCo, key) }}</span>
          <span
            slot="action"
            slot-scope="text, record"
          >
            <template>
              <a
                style="margin: 3px;"
                href="javascript: void(0)"
                @click.stop="() => $refs.appendix.openUploadDrawer(record)"
              >附件</a>
            </template>
          </span>
        </s-table>
        <a-spin :spinning="curbalLoading">
          <div class="selected-material">
            <div class="header">
              <span class="title">已选择的物资</span>
              <a
                class="action"
                href="javascript:void(0)"
                style="float: right; padding-right: 12px; font-size: 12px; color: #f34d4d;"
                @click="doMaterialRemove(cacheMaterials)"
              >全部移除</a>
            </div>
            <s-table
              ref="selectedMaterialTable"
              :data="selectedMaterialLoadData"
              :columns="selectedMaterialColumns"
              :scroll="materialScroll"
              :customRow="selectedMaterialRowClick"
              :clearSelection="selectedMaterialClearSelection"
              :showPagination="selectedMaterialShowPagination"
              :pageSizeOptions="selectedMaterialPageSizeOptions"
              :pageSize="selectedMaterialDefaultPageSize"
              :rowClassName="selectedMaterialRowClassName"
              :immediate="selectedMaterialImmediate"
              :bordered="selectedMaterialBordered"
              :rowKey="materialKey"
            >
              <span
                slot="serial"
                slot-scope="text, record, index"
              >{{ index + 1 }}</span>
              <span
                slot="functionDept"
                slot-scope="text, record"
              >
                <edit-cell-tree-select
                  :value="text"
                  :showSearch="true"
                  :options="orgIds"
                  :status.sync="materialState"
                  @change="materialChange(record, 'functionDept', $event.selected)"
                  @confirm="materialConfirm(record, 'functionDept', $event.selected)"
                />
              </span>
              <span
                slot="budgetUuid"
                slot-scope="text, record"
              >
                <edit-cell-tree-select
                  :value="text"
                  :showSearch="true"
                  :options="budgetMaterials"
                  :status.sync="materialState"
                  @change="materialChange(record, 'budgetUuid', $event.selected)"
                  @confirm="materialConfirm(record, 'budgetUuid', $event.selected)"
                />
              </span>
              <span
                slot="curbal"
                slot-scope="text, record"
              >{{ (text || 0) + takeSelectLabel(units, record.orderUnit) }}</span>
              <span
                slot="usedCoName"
                slot-scope="key"
              >{{ takeSelectLabel(usedCo, key) }}</span>
              <div
                slot="action"
                slot-scope="text, record"
                style="color: #f34d4d; cursor: pointer;"
                @click="doMaterialRemove([record])"
              >移除</div>
            </s-table>
          </div>
        </a-spin>
      </div>
      <div class="drawer-footer">
        <div class="footer-fixed">
          <a-button @click="hiddenMaterialDrawer">取消</a-button>
          <a-button
            type="primary"
            @click="doSelectMaterial"
          >确认</a-button>
        </div>
      </div>
    </a-drawer>
  </a-drawer>
</template>

<script>
import {
  // 价格单位
  PRICE_UNIT,
  // 审批状态
  APPROVE_STATUS_READY_CODE,
  APPROVE_STATUS_START_CODE,
  APPROVE_STATUS_FLOWING_CODE,
  APPROVE_STATUS_CONFIRM_CODE,
  // 审批节点
  APPROVE_NODES_BUS_INIT,
  APPROVE_NODES_BUS_FREEZE,
  APPROVE_NODES_NOT_RETURN,
  APPROVE_NODES_ALLOW_APPROVE,
  APPROVE_NODES_ALLOW_CANCEL,
  // 审批下拉框选项
  APPROVE_OPTIONS_HIDES,
  APPROVE_OPTIONS_FLOWINGS
} from '@/store/variable-types'
import { OPERATOR, PERSON_ID, ORG_ID, ACCESS_TOKEN, ROLE_NAME } from '@/store/mutation-types'
import { requestBuilder, deepUpdate, takeTreeByKey, getPermission, getNowDate } from '@/utils/util'
import { STable, EditCellInput, EditCellSelect, EditCellTreeSelect, EditCellTextarea } from '@/components'
import * as materialApi from '@/api/material/material'
import * as prlineApi from '@/api/material/prline'
import * as uploadApi from '@/api/material/upload'
import * as baseApi from '@/api/material/base'
import * as quotaApi from '@/api/material/quota'
import * as contractApi from '@/api/material/contract'
import moment from 'moment'
import Vue from 'vue'
// 默认值 lineType
const DEFAULT_LINE_TYPE = '1'
// 南京明州 25号及以后修改lineType默认值为'5'急件计划
const DEFAULT_LINE_TYPE_NJMZ = '5'
// 操作人 userPersonId
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)
// 操作人 userNo
const USER_OPERATOR = Vue.ls.get(OPERATOR)
// 操作人 userNo orgId
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const USER_ROLE_NAME = Vue.ls.get(ROLE_NAME)
// 运行环境 ENV
const IS_JYS_ENV = USER_ORG_ID === '1.100.117'
const IS_SLH_ENV = USER_ORG_ID === '1.100.111'
const IS_HJ_ENV = USER_ORG_ID === '1.100.131'
const IS_JX_ENV = USER_ORG_ID === '1.102'
const IS_MD_ENV = USER_ORG_ID === '1.100.104'
const IS_SZXD_ENV = USER_ORG_ID === '1.100.106'
const IS_BYJ_ENV = USER_ORG_ID === '1.100.101'
const IS_NJMZ_ENV = USER_ORG_ID === '1.100.109'
const IS_TS_ENV = USER_ORG_ID === '1.100.122'
const IS_XT_ENV = USER_ORG_ID === '1.100.118'
const IS_WZG_ENV = USER_ORG_ID.substr(0, 5) === '1.101'
const IS_DMY_ENV = USER_ORG_ID === '1.122.801'
const IS_ZYA_ENV = USER_ORG_ID === '1.101.104'
// 领料计划ID
const PICK_TYPE_ID = '10'
const RETURN_VENDOR_ID = '15'
export default {
  components: {
    STable,
    EditCellInput,
    EditCellSelect,
    EditCellTreeSelect,
    EditCellTextarea
  },
  data () {
    return {
      USER_ORG_ID,
      USER_PERSON_ID,
      yearPlans: [],
      // 合同下拉框
      contractOptions: [],
      contracts: [],
      assetList: [],
      // RUN_ENV
      IS_ZYA_ENV,
      IS_SZXD_ENV,
      IS_JYS_ENV,
      IS_SLH_ENV,
      IS_HJ_ENV,
      IS_JX_ENV,
      IS_XT_ENV,
      IS_BYJ_ENV,
      IS_NJMZ_ENV,
      IS_TS_ENV,
      IS_WZG_ENV,
      IS_MD_ENV,
      IS_DMY_ENV,
      // 物资选项 - 排版
      materialGrid: {
        xl: IS_JYS_ENV ? 12 : 8,
        md: IS_JYS_ENV ? 12 : 8,
        sm: IS_JYS_ENV ? 12 : 8,
        gutter: 10
      },
      addPrlineVisible: false,
      lineTypes: [],
      searchForm: {
        requestedBy: '',
        agent: '',
        materialName: ''
      },
      // 表格配置 - 物资列表
      materialRowSelection: {
        type: 'checkbox',
        onChange: this.materialSelectChange
      },
      // 物资选项 - 参数
      materialParam: {
        materialNum: '',
        requestedBy: '',
        materialName: '',
        officeSupplies: '',
        jmodel: '',
        prlineNum: '',
        storehouseSysId: IS_JX_ENV ? 'JX001' : '',
        materialClass: '',
        belongToSite: IS_JYS_ENV ? 'site2' : '',
        jModel: '',
        isResupply: '',
        contractSysId: '',
        yearPlan: '',
        isRm: ''
      },
      materialDataSource: [],
      materialSelectedRows: [],
      materialSelectedRowKeys: [],
      materialPageSizeOptions: ['3', '15', '20', '25', '30', '50'],
      materialDefaultPageSize: 15,
      materialClearSelection: false,
      materialShowPagination: true,
      materialImmediate: true,
      materialBordered: false,
      materialLoadData: parameter => {
        const materialParam = {
          ...this.materialParam,
          procureLineType: this.procureLineType
        }
        if (this.materialKey === 'prlineSysIdRel') {
          if (this.materialParam.isResupply === '') {
            if (IS_SZXD_ENV) {
              this.materialParam.isResupply = 'Y'
            } else if (IS_JYS_ENV) {
              this.materialParam.isResupply = this.procureLineType === '15' ? 'Y' : 'N'
            } else {
              this.materialParam.isResupply = 'N'
            }
          }
        }
        const param = requestBuilder(
          '',
          deepUpdate(
            {
              materialNum: '',
              materialName: '',
              jmodel: '',
              officeSupplies: '',
              storehouseSysId: '',
              materialClass: '',
              belongToSite: '',
              activity: 'Y',
              jModel: '',
              requestedBy: '',
              prlineNum: '',
              procureLineType: '',
              isResupply: '',
              contractSysId: '',
              purapplyId: '',
              isRm: ''
            },
            materialParam
          ),
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder
        )
        if (IS_MD_ENV && this.materialParam.yearPlan) {
          param.param.yearPrlineNum = this.materialParam.yearPlan
        }
        if (this.materialKey === 'materialNum') {
          param.param.jmodel = param.param.jModel
          return materialApi.getMaterialForm(param).then(res => {
            if (res.code !== '0000') {
              this.$notification.error({
                message: '系统消息',
                description: res.message || '物资列表获取失败！'
              })
              return Promise.reject(res)
            }
            this.materialDataSource = res.result.data || []
            this.materialSelectedRows = this.materialDataSource.filter(item =>
              this.cacheMaterials.some(item2 => item2[this.materialKey] === item[this.materialKey])
            )
            this.materialSelectedRowKeys = this.materialSelectedRows.map(item => item[this.materialKey])
            this.$refs.materialTable.triggerSelect(this.materialSelectedRowKeys, this.materialSelectedRows)
            return res.result
          })
        }
        if (this.materialKey === 'prlineSysIdRel') {
          // param.param.jModel = param.param.jmodel
          if (IS_JYS_ENV && this.firstTime === 'y') {
            // 修改集运司默认排序
            if (typeof (param.sortField) === 'undefined' && typeof (param.sortOrder) === 'undefined') {
              param.sortField = 'prline_num_rel'
              param.sortOrder = 'desc'
            }
            this.firstTime = 'n'
          }
          return prlineApi.queryPrlineRequest(param).then(res => {
            if (res.code !== '0000') {
              this.$notification.error({
                message: '系统消息',
                description: res.message || '领料计划获取失败！'
              })
              return Promise.reject(res)
            }
            this.materialDataSource = res.result.data || []
            this.materialDataSource.forEach(item => {
              item.prlineNumRel = item.prlineNum || ''
              item.prlineSysIdRel = item.prlineSysId || ''
              item.curbal = parseFloat((item.jwspOrderQty - item.requestQty).toFixed(8)) || 0
              item.jmodel = item.jModel
            })
            this.materialSelectedRows = this.materialDataSource.filter(item =>
              this.cacheMaterials.some(item2 => item2[this.materialKey] === item[this.materialKey])
            )
            this.materialSelectedRowKeys = this.materialSelectedRows.map(item => item[this.materialKey])
            this.$refs.materialTable.triggerSelect(this.materialSelectedRowKeys, this.materialSelectedRows)
            return res.result
          })
        }
      },
      materialRowClick: record => ({
        on: {
          click: () => {
            if (this.materialRowSelection.type === 'radio') {
              this.$refs.materialTable.triggerSelect([record[this.materialKey]], [record])
            }
            if (this.materialRowSelection.type === 'checkbox') {
              if (!this.materialSelectedRowKeys.includes(record[this.materialKey])) {
                this.materialSelectedRowKeys.push(record[this.materialKey])
                this.materialSelectedRows.push(record)
              } else {
                this.materialSelectedRows = this.materialSelectedRows.filter(
                  item => item[this.materialKey] !== record[this.materialKey]
                )
                this.materialSelectedRowKeys = this.materialSelectedRowKeys.filter(
                  item => item !== record[this.materialKey]
                )
              }
              this.$refs.materialTable.triggerSelect(this.materialSelectedRowKeys, this.materialSelectedRows)
            }
          }
        }
      }),
      materialScroll: {
        x: '100%',
        y: 'calc(50vh)',
        scrollToFirstRowOnChange: false
      },
      materialRowClassName: () => {
        return 'cursor-pointer'
      },
      // 已选择的物资
      selectedMaterialRowSelection: {
        type: 'checkbox'
      },
      selectedMaterialSelectedRows: [],
      selectedMaterialSelectedRowKeys: [],
      selectedMaterialPageSizeOptions: ['10', '15', '20', '25', '30', '50'],
      selectedMaterialDefaultPageSize: 15,
      selectedMaterialClearSelection: false,
      selectedMaterialShowPagination: false,
      selectedMaterialImmediate: false,
      selectedMaterialBordered: false,
      selectedMaterialLoadData: parameter => {
        return Promise.resolve(this.cacheMaterials)
      },
      selectedMaterialRowClick: record => ({
        on: {}
      }),
      selectedMaterialRowClassName: () => {
        return ''
      },
      // 采购弹框配置
      procureTitle: '',
      procureAction: '',
      procureEditMode: '',
      procureLineType: '1',
      procureFormEdit: 'form-edit',
      procureFormApprove: 'approve-edit',
      procureForm: this.$form.createForm(this, {
        onValuesChange: (props, values) => {
          if (values[this.materialKey]) {
            // 取消选项，则从 options 移除
            if (!this.procureAdd) {
              this.materials = this.materials.filter(item =>
                values[this.materialKey].includes(item[this.materialKey])
              )
            } else {
              this.materials = this.materials.filter(item => values[this.materialKey] === item[this.materialKey])
            }
          }
        }
      }),
      procureVisible: false,
      procureLoading: false,
      procureAdd: false,
      // 物资弹框配置
      materialTitle: '',
      materialVisible: false,
      curbalLoading: false,
      // 使用单位是否锁定
      usedCoFlag: false,
      // 当前用户是否是计划员or管理员
      roleName: USER_ROLE_NAME.some(item => {
        return ['物资计划员', '计划员', '管理员'].includes(item) === true
      }),
      // 领用计划中选择的是否是寄售物资
      jishou: false,
      // 温州港办公用品定额
      wzgQuotaRecord: [],
      resupply: [{
        label: '是',
        value: 'Y'
      }, {
        label: '否',
        value: 'N'
      }],
      personDept: [],
      status: [],
      budgetMaterials: [],
      usedCo: [],
      cacheMaterials: [],
      materialClass: [],
      storehouse: [],
      // 业务种类
      useType: [],
      materials: [],
      requestedBys: [],
      prlineNum: ''
    }
  },
  created () {
    this.initOptions()
  },
  computed: {
    // 物资列表 KEY
    materialKey () {
      if (this.procureLineType === PICK_TYPE_ID || this.procureLineType === RETURN_VENDOR_ID) {
        return 'prlineSysIdRel'
      }
      return 'materialNum'
    },
    // 新增计划
    lineTypefilter () {
      const arr = this.lineTypes.filter(item => {
        return !['27'].includes(item.value)
      })
      return IS_HJ_ENV ? arr : this.lineTypes
    },
    // 物资列表 列名
    materialColumns () {
      if (this.materialKey === 'prlineSysIdRel') {
        return [
          ...(IS_BYJ_ENV || IS_MD_ENV ? [
            {
              title: '使用单位',
              dataIndex: 'usedCo',
              scopedSlots: { customRender: 'usedCoName' },
              sorter: true,
              width: 90
            }] : []),
          {
            title: '申报单号',
            dataIndex: 'prlineNumRel',
            ellipsis: true,
            sorter: true,
            width: 75
          },
          {
            title: '物资代码',
            dataIndex: 'materialNum',
            ellipsis: true,
            sorter: true,
            width: 100
          },
          ...(IS_SZXD_ENV
            ? [
              {
                title: '物资名称',
                dataIndex: 'materialName',
                ellipsis: true,
                width: 400
              }
            ]
            : [
              {
                title: '物资名称',
                dataIndex: 'materialName',
                ellipsis: true,
                width: 130
              }
            ]),
          {
            title: '规格型号',
            dataIndex: 'jModel',
            ellipsis: true,
            width: 90
          },
          ...(IS_BYJ_ENV ? [
            {
              title: '使用方向',
              dataIndex: 'usedforName',
              ellipsis: true,
              width: 120
            }] : []),
          ...(IS_BYJ_ENV ? [
            {
              title: '申报人',
              dataIndex: 'requestedByName',
              ellipsis: true,
              width: 120
            }] : []),
          {
            title: '可领数',
            dataIndex: 'curbal',
            fixed: 'right',
            scopedSlots: { customRender: 'curbal' },
            align: 'center',
            width: 70
          },
          {
            title: '库存数',
            dataIndex: 'totalCurbal',
            fixed: 'right',
            scopedSlots: { customRender: 'curbal' },
            align: 'center',
            width: 80
          }
        ]
      }
      return [
        {
          title: '物资代码',
          dataIndex: 'materialNum',
          ellipsis: true,
          sorter: true,
          width: 90
        },
        ...((IS_SZXD_ENV)
          ? [
            {
              title: '物资名称',
              dataIndex: 'materialName',
              ellipsis: true,
              width: 400
            }
          ]
          : [
            {
              title: '物资名称',
              dataIndex: 'materialName',
              ellipsis: true,
              width: 130
            }
          ]),
        ...((IS_JX_ENV || IS_MD_ENV)
          ? [
            {
              title: '所属仓库',
              dataIndex: 'storehouseName',
              ellipsis: true,
              width: 90
            }
          ]
          : []),
        {
          title: '品牌',
          dataIndex: 'brand',
          ellipsis: true,
          width: 110
        },
        {
          title: '规格型号',
          dataIndex: 'jmodel',
          ellipsis: true,
          width: 120
        },
        {
          title: (IS_HJ_ENV && this.procureLineType === '27') ? '数量' : '余量',
          dataIndex: 'curbal',
          scopedSlots: { customRender: 'curbal' },
          align: 'center',
          fixed: 'right',
          width: 80
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 50,
          fixed: 'right',
          scopedSlots: { customRender: 'action' }
        }
      ]
    },
    // 已选择物资 列名
    selectedMaterialColumns () {
      if (this.materialKey === 'prlineSysIdRel') {
        if (IS_HJ_ENV) {
          return [
            {
              title: '序号',
              scopedSlots: { customRender: 'serial' },
              ellipsis: true,
              align: 'center',
              width: 50
            },
            {
              title: '合同编码',
              dataIndex: 'contractNum',
              ellipsis: true,
              width: 70
            },
            {
              title: '物资代码',
              dataIndex: 'materialNum',
              ellipsis: true,
              width: 80
            },
            {
              title: '物资名称',
              dataIndex: 'materialName',
              ellipsis: true,
              width: 110
            },
            {
              title: '规格型号',
              dataIndex: 'jModel',
              ellipsis: true,
              width: 100
            },
            {
              title: '余量',
              dataIndex: 'curbal',
              scopedSlots: { customRender: 'curbal' },
              align: 'center',
              width: 60
            },
            {
              title: '操作',
              key: 'action',
              scopedSlots: { customRender: 'action' },
              align: 'center',
              fixed: 'right',
              width: 50
            }
          ]
        } else {
          return [
            {
              title: '序号',
              scopedSlots: { customRender: 'serial' },
              ellipsis: true,
              align: 'center',
              width: 50
            },
            ...(IS_BYJ_ENV || IS_MD_ENV ? [
              {
                title: '使用单位',
                dataIndex: 'usedCo',
                scopedSlots: { customRender: 'usedCoName' },
                width: 70
              }] : []),
            {
              title: '申报单号',
              dataIndex: 'prlineNumRel',
              ellipsis: true,
              width: 70
            },
            {
              title: '物资代码',
              dataIndex: 'materialNum',
              ellipsis: true,
              width: 80
            },
            {
              title: '物资名称',
              dataIndex: 'materialName',
              ellipsis: true,
              width: 110
            },
            {
              title: '规格型号',
              dataIndex: 'jModel',
              ellipsis: true,
              width: 100
            },
            ...(IS_MD_ENV && this.procureLineType === '10' ? [{
              title: '预算名称',
              dataIndex: 'budgetUuid',
              scopedSlots: { customRender: 'budgetUuid' },
              ellipsis: true,
              width: 180
            }] : []),
            {
              title: '余量',
              dataIndex: 'curbal',
              scopedSlots: { customRender: 'curbal' },
              align: 'center',
              width: 60
            },
            {
              title: '操作',
              key: 'action',
              scopedSlots: { customRender: 'action' },
              align: 'center',
              fixed: 'right',
              width: 50
            }
          ]
        }
      }
      return [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' },
          ellipsis: true,
          align: 'center',
          width: 50
        },
        {
          title: '物资代码',
          dataIndex: 'materialNum',
          ellipsis: true,
          width: 90
        },
        {
          title: '物资名称',
          dataIndex: 'materialName',
          ellipsis: true,
          width: 130
        },
        ...(IS_MD_ENV && this.procureLineType === '16' ? [{
          title: '职能部门',
          dataIndex: 'functionDept',
          scopedSlots: { customRender: 'functionDept' },
          ellipsis: true,
          width: 180
        }] : []),
        {
          title: '品牌',
          dataIndex: 'brand',
          ellipsis: true,
          width: 110
        },
        {
          title: '规格型号',
          dataIndex: 'jModel',
          ellipsis: true,
          width: 120
        },
        {
          title: '余量',
          dataIndex: 'curbal',
          scopedSlots: { customRender: 'curbal' },
          align: 'center',
          width: 80
        },
        {
          title: '操作',
          key: 'action',
          scopedSlots: { customRender: 'action' },
          align: 'center',
          fixed: 'right',
          width: 50
        }
      ]
    }
  },
  methods: {
    initOptions () {
      // 预算
      baseApi.getTreeById({ id: 'budgetMaterial' }).then(res => {
        console.log('budgetMaterial', res.result)
        this.budgetMaterials = res.result
      })
      this.isSpecialMaterials = [
        { value: 'Y', label: '是' },
        { value: 'N', label: '否' }
      ]
      contractApi.selectContractWithoutPage(requestBuilder('', { 'activity': 'Y', 'status': 'approved' })).then(res => {
        this.contracts = res.result.map(item => {
          return {
            value: item.contractSysId,
            label: item.contractNum + '-' + item.description
          }
        })
      })
      prlineApi.getSelectYearPrline(requestBuilder('', {})).then(res => {
        this.yearPlans = res.result || []
      })
      // 物资类别
      baseApi.getCommboxById({ id: 'materialClass' }).then(res => {
        this.materialClass = res.result
      })
      // 物资类别
      baseApi.getCommboxById({ id: 'matrtype' }).then(res => {
        if (res.code === '0000') {
          this.categorys = res.result
        }
      })
      // 职能部门
      baseApi.getTreeById({ id: 'dept', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.orgIds = res.result
        }
      })
      // 采购人员
      baseApi
        .getCommboxById({ id: 'personByRoleName', sqlParams: { byRoleName: '1', roleName: '采购员' } })
        .then(res => {
          if (res.code === '0000') {
            this.agents = res.result
          }
        })
      // 申请人员,labor包含劳保人员
      baseApi.getCommboxById({ id: IS_BYJ_ENV ? 'laborPerson' : 'applyPerson' }).then(res => {
        if (res.code === '0000') {
          res.result.sort((a, b) => a.label.localeCompare(b.label))
          this.requestedBys = res.result
        }
      })
      // 计量单位
      baseApi.getCommboxById({ id: 'unit' }).then(res => {
        if (res.code === '0000') {
          this.units = res.result
        }
      })
      // 业务类型
      if (IS_JX_ENV) {
        baseApi.getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'business_typeJX' } }).then(res => {
          if (res.code === '0000') {
            this.useType = res.result
          }
        })
      }
      // 计划类型
      baseApi.getCommboxById({ id: 'prlineLineType' }).then(res => {
        if (res.code === '0000') {
          res.result = res.result.filter(item => item.label !== '二级库计划')
          this.existRequis = res.result.some(item => item.value === '10')
          if (IS_TS_ENV) {
            // 铁司计划类型只保留月度计划 项目计划 工具劳保计划 零星 这四个
            const arr = USER_ROLE_NAME.includes('劳保申报员') ? ['1', '3', '6', '25'] : ['1', '3', '25']
            res.result = res.result.filter(item => arr.includes(item.value))
          }
          if (IS_JX_ENV) {
            res.result = res.result.filter(item => (item.label === '月度计划' ||
            item.label === '受控计划' || item.label === '统购计划' || item.label === '补库计划'))
            res.result.forEach(item => {
              if (item.label === '月度计划') {
                item.label = '领料计划'
              }
            })
          }
          if (IS_SLH_ENV) {
            this.lineTypes = res.result.filter(item => (item.label === '月度计划' || item.label === '领用计划'))
            const list1 = res.result.filter(item => (item.label !== '领用计划' && item.label !== '月度计划'))
            for (const i of list1) {
              this.lineTypes.push(i)
            }
          }
          if (this.dateNow) { // 南京明州，25号以后计划类型只显示领用和急件
            res.result = res.result.filter(item => (item.label === '领用计划' || item.label === '急件计划'))
          }
          this.lineTypes = res.result
        }
      })
      // 审批状态
      baseApi.getTreeById({ id: 'appro' }).then(res => {
        if (res.code === '0000') {
          for (const item of res.result) {
            if (APPROVE_OPTIONS_HIDES.includes(item.value)) {
              item.disabled = true
            }
            this.status.push(item)
          }
        }
      })
      // 使用方向
      baseApi.getTreeById({ id: 'usedfor' }).then(res => {
        if (res.code === '0000') {
          this.disableSelectable(res.result)
          this.directions = res.result
        }
      })
      // 选择仓库
      baseApi.getCommboxById({ id: 'storehouse' }).then(res => {
        this.storehouse = res.result
      })
      // 所在单位
      if (IS_JYS_ENV) {
        baseApi.getCommboxById({ id: 'belongToSite' }).then(res => {
          if (res.code === '0000') {
            this.belongToSites = res.result
          }
        })
      }
      // 使用单位
      if (IS_BYJ_ENV || IS_MD_ENV) {
        baseApi.getCommboxById({ id: 'usedCo' }).then(res => {
          if (res.code === '0000') {
            this.usedCo = res.result
          }
        })
      }
      // 温州港/梅东获取办公用品定额
      if (IS_WZG_ENV || IS_MD_ENV) {
        this.queryWZGQuota()
        // 获取人员与对应部门关系
        baseApi.getCommboxById({ id: 'personDept' }).then(res => {
          if (res.code === '0000') {
            res.result.forEach(item => {
              this.personDept[item.label] = item.value
            })
          }
        })
      }
    },
    // 禁用下拉框父类选项
    disableSelectable (array) {
      for (const item of array) {
        if (item.children && item.children.length > 0) {
          item.selectable = false
          this.disableSelectable(item.children)
        }
      }
    },
    // 打开采购弹框
    openProcureDrawer (obj, action, prlineNum) {
      this.prlineNum = prlineNum
      const record = {
        ...obj
      }
      // 根据 action 判断
      switch (action) {
        case 'insert': {
          this.procureAdd = true
          this.procureTitle = '新增'
          this.procureAction = 'insert'
          this.procureEditMode = this.procureFormEdit
          this.materials = []
          break
        }
        case 'update': {
          this.procureAdd = false
          this.procureTitle = '修改'
          this.materials = [
            {
              prlineSysIdRel: record.prlineSysIdRel,
              yearPrlineNum: record.yearPrlineNum,
              yearPrlineSysId: record.yearPrlineSysId,
              prlineNumRel: record.prlineNumRel,
              materialNum: record.materialNum,
              materialName: record.materialName,
              orderUnit: record.orderUnit,
              unitCost: record.refCost,
              jModel: record.jModel,
              curbal: record.curbal,
              agent: record.agent,
              budgetUuid: record.budgetUuid,
              functionDept: record.functionDept
            }
          ]
          this.procureAction = 'update'
          this.procureLineType = record.lineType
          this.procureEditMode = !record.prlineNum
            ? this.procureFormEdit
            : APPROVE_STATUS_START_CODE !== this.getApproveId(record.status)
              ? this.procureFormApprove
              : this.procureFormEdit
          break
        }
      }
      // 重置 form表单
      this.procureForm.resetFields()
      if (IS_SLH_ENV) {
        this.requestedByChange(record.requestedBy || USER_PERSON_ID, 2)
      }
      // 根据 procureEditMode 初始化form表单
      this.$nextTick(() => {
        this.procureForm.setFieldsValue({
          planMonth: record.planMonth ? moment(record.planMonth, 'YYYY-MM') : null,
          [this.materialKey]: !this.procureAdd ? record[this.materialKey] : [],
          orderQty: record.orderQty || '',
          jwspOrderQty: record.jwspOrderQty || '',
          usedFor: record.usedFor || '',
          requestedBy: record.requestedBy || USER_PERSON_ID,
          lineType: this.dateNow ? DEFAULT_LINE_TYPE_NJMZ : (record.lineType || (IS_HJ_ENV ? null : DEFAULT_LINE_TYPE)),
          description: record.description || '',
          remarks: record.remarks || '',
          projectId: record.projectId || '',
          usedCo: record.usedCo || '',
          demandDate: record.demandDate || '',
          modelSupply: record.modelSupply || '',
          maintech: record.maintech || '',
          quarequired: record.quarequired || '',
          useType: record.useType || '',
          workorderNo: record.workorderNo || '',
          departmentSysId: record.departmentSysId || '',
          assetSbNum: record.assetSbNum || this.$route.params.assetName || '',
          reqDeliveryDate: record.reqDeliveryDate ? moment(record.reqDeliveryDate, 'YYYY-MM-DD') : null,
          storehouseSysId: record.storehouseSysId || '',
          budgetUuid: record.budgetUuid || '',
          functionDept: record.functionDept || ''

        })
      })
      // 显示修改弹框
      this.procureVisible = true
    },
    // 关闭采购弹框
    hiddenProcureDrawer () {
      this.procureTitle = ''
      this.prlineNum = ''
      this.procureAction = ''
      this.procureEditMode = ''
      this.procureLineType = '1'
      this.procureLoading = false
      this.procureVisible = false
      this.procureAdd = false
      this.materials = []
      this.procureForm.resetFields()
      this.absenceVisible = false
    },
    // 打开物资弹框
    openMaterialDrawer () {
      // 采购弹框为非新增模式
      if (!this.procureAdd) {
        // 普通物资
        if (this.materialKey === 'materialNum') {
          this.curbalLoading = true
          const material = this.materials[0] || {}
          const materialNum = material.materialNum
          const param = requestBuilder('', {
            materialNum: this.materials[0].materialNum
          })
          if (IS_MD_ENV && this.procureLineType === '1') {
            this.materialParam.yearPlan = this.materials[0].yearPrlineNum || ''
            param.param.yearPrlineNum = this.materialParam.yearPlan
          }
          materialApi
            .getMaterialForm(param)
            .then(res => {
              if (res.code === '0000') {
                const [record = {}] = res.result.data
                this.cacheMaterials = this.materials.map(item => {
                  return {
                    ...item,
                    curbal: (item.materialNum === materialNum && record.curbal) || 0
                  }
                })
              }
            })
            .finally(() => {
              this.curbalLoading = false
            })
        }
        if (this.materialKey === 'prlineSysIdRel') {
          this.curbalLoading = true
          const material = this.materials[0] || {}
          const prlineSysIdRel = material.prlineSysIdRel
          const param = requestBuilder('', {
            prlineSysId: this.materials[0].prlineSysIdRel
          })
          prlineApi
            .queryPrlineRequest(param)
            .then(res => {
              if (res.code === '0000') {
                const [record = {}] = res.result.data
                this.cacheMaterials = this.materials.map(item => {
                  return {
                    ...item,
                    curbal: (item.prlineSysIdRel === prlineSysIdRel && record.jwspOrderQty - record.requestQty) || 0
                  }
                })
              }
            })
            .finally(() => {
              this.curbalLoading = false
            })
        }
      }
      this.materialVisible = true
      this.materialSelectedRows = []
      this.materialSelectedRowKeys = []
      this.materialRowSelection.type = this.procureAdd ? 'checkbox' : 'radio'
      this.materialTitle = this.materialKey === 'prlineSysIdRel' ? '领料列表' : '物资列表'
      if (this.procureLineType === '27') {
        const arr = []
        for (let i = 0; i < this.materials.length; i++) {
          for (let j = 0; j < this.cacheMaterials.length; j++) {
            if (this.materials[i].materialNum === this.cacheMaterials[j].materialNum) {
              arr.push(this.cacheMaterials[j])
            }
          }
        }
        this.$nextTick(() => {
          this.cacheMaterials = arr
          this.$refs.selectedMaterialTable.refresh(true)
        })
      } else {
        this.cacheMaterials = [...this.materials]
      }
      this.firstTime = 'y'
    },
    // 关闭物资弹框
    hiddenMaterialDrawer () {
      this.materialParam = {
        materialNum: '',
        materialName: '',
        jModel: '',
        requestedBy: '',
        prlineNum: '',
        storehouseSysId: IS_JX_ENV ? 'JX001' : '',
        belongToSite: IS_JYS_ENV ? 'site2' : '',
        isResupply: '',
        contractSysId: '',
        isRm: ''
      }
      this.materialVisible = false
      this.materialSelectedRows = []
      this.materialSelectedRowKeys = []
      this.materialRowSelection.type = 'checkbox'
      if (this.procureLineType !== '27') {
        this.cacheMaterials = []
      }
      this.materialTitle = ''
    },
    // 选择物资列表
    doSelectMaterial () {
      if (this.cacheMaterials.length === 0) {
        this.$message.error('请选择物资！')
        return
      }
      if (IS_MD_ENV && !this.materialParam.yearPlan) {
        this.checkPrline(this.cacheMaterials)
      }
      // 当前为领用计划时进行寄售物资判断
      if (this.materialKey === 'prlineSysIdRel') {
        this.jishou = false
        const arr = []
        for (const material of this.cacheMaterials) {
          if (material.prlineNum) {
            arr.push('0')
          } else {
            arr.push('1')
          }
        }
        if (arr.indexOf('0') !== -1 && arr.indexOf('1') !== -1) {
          this.$message.error('选择物资中包含寄售物资和非寄售物资，请重新选择！')
          return
        } else if (arr.indexOf('1') !== -1) {
          this.jishou = true // 是寄售物资
        } else {
          this.jishou = false // 非寄售物资
        }
      }
      if (this.materialRowSelection.type === 'radio') {
        this.procureForm.setFieldsValue({
          [this.materialKey]: this.cacheMaterials[0][this.materialKey]
        })
        this.materials = [...this.cacheMaterials]
      }
      if (this.materialRowSelection.type === 'checkbox') {
        this.procureForm.setFieldsValue({
          [this.materialKey]: this.cacheMaterials.map(item => item[this.materialKey])
        })
        this.materials = [...this.cacheMaterials]
      }
      this.hiddenMaterialDrawer()
      if (IS_BYJ_ENV) {
        this.selectUsedCo(this.materials)
      }
    },
    // 保存采购计划
    doSaveProcurePlan () {
      const toSubmit = (action, param, notice) => {
        return prlineApi.addDMYPrline(requestBuilder(action, param)).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || notice.error
            })
            return Promise.reject(res)
          }
          if (this.$route.params.emergencyRepairUuid) {
            delete this.$route.params.emergencyRepairUuid
            delete this.$route.params.assetName
          }
          this.assetList = []
          this.$notification.success({
            message: '系统消息',
            description: notice.success
          })
        })
      }
      if (this.procureAction === 'insert') {
        const noCheckProps = ['planMonth', 'jwspOrderQty']
        this.procureForm.validateFields((errors, values) => {
          if (errors) {
            if (Object.keys(errors).some(key => !noCheckProps.includes(key))) {
              return
            }
          }
          const action = this.procureAction
          const records = []
          const notice = {
            error: '采购计划新增失败！',
            success: '采购计划新增成功！'
          }
          const belowMinInventory = []
          for (const materialKey of values[this.materialKey]) {
            const material = this.materials.find(item => item[this.materialKey] === materialKey)
            console.log(material)
            console.log(this.prlineNum, 'prlineNum')
            records.push({
              ...values,
              prlineNum: this.prlineNum,
              hjPrlineNumber: '',
              hjPrlineSysId: '',
              prlineSysIdRel: material.prlineSysIdRel,
              yearPrlineNum: material.yearPrlineNum,
              yearPrlineSysId: material.yearPrlineSysId,
              prlineNumRel: material.prlineNumRel,
              materialNum: material.materialNum,
              materialName: material.materialName,
              budgetUuid: material.budgetUuid,
              functionDept: material.functionDept,
              unitCost: material.unitCost,
              jModel: material.jModel,
              agent: material.agent,
              planMonth: values.planMonth ? values.planMonth.format('YYYY-MM') : '',
              orderQty: this.jishou ? '1' : this.materialKey === 'prlineSysIdRel' ? material.curbal : values.orderQty,
              usedFor: USER_ORG_ID === '1.101.104' ? (values.usedFor || material.usedFor) : (material.usedFor || values.usedFor),
              usedCo: material.usedCo || values.usedCo,
              reqDeliveryDate: values.reqDeliveryDate ? values.reqDeliveryDate.format('YYYY-MM-DD') : '',
              emergencyRepairUuid: this.$route.params.emergencyRepairUuid || '',
              pMCMatapplyId: material.pMCMatapplyId,
              contractSysId: material.contractSysId,
              contractLineSysId: material.contractLineSysId,
              taxRate: material.taxRate,
              matuStatusHJ: material.matuStatusHJ
            })
            if (IS_WZG_ENV && this.materialKey !== 'prlineSysIdRel') {
              if (values.orderQty + material.curbal > material.maxAmount) {
                belowMinInventory.push({
                  materialNum: '物资名称：' + material.materialNum,
                  maxAmount: '最大库存：' + material.maxAmount
                })
              }
            }
          }
          if (belowMinInventory.length > 0) {
            const str = belowMinInventory.map(v => {
              return v.materialNum + '库存数加申报数大于' + v.maxAmount
            }).join(';')
            this.$info({
              title: '提醒',
              content: str
            })
          }
          // 温州港判断新增的物资是否是超出定额的办公用品物资(其他单位可不判断)
          if (IS_WZG_ENV || IS_MD_ENV) {
            this.judgeQuota(action, records, notice, toSubmit)
          } else {
            toSubmit(action, records, notice)
              .then(() => {
                this.hiddenMaterialDrawer()
                this.hiddenProcureDrawer()
                this.$emit('search')
                this.procureLoading = false
              })
              .catch(() => {
                this.procureLoading = false
              }).finally(() => {
                console.log('1111', 1111)
                this.$emit('search')
              })
            this.procureLoading = true
          }
        })
      }
      if (this.procureAction === 'update') {
        let noCheckProps = []
        if ([this.procureFormEdit].includes(this.procureEditMode)) {
          noCheckProps = ['jwspOrderQty']
        }
        if ([this.procureFormApprove].includes(this.procureEditMode)) {
          noCheckProps = ['lineType', 'planMonth', this.materialKey, 'orderQty', 'usedFor', 'requestedBy']
        }
        this.procureForm.validateFields((errors, values) => {
          console.log(values)
          if (errors) {
            if (Object.keys(errors).some(key => !noCheckProps.includes(key))) {
              return
            }
          }
          const action = this.procureAction
          const records = []
          const notice = {
            error: '采购计划修改失败！',
            success: '采购计划修改成功！'
          }
          console.log(this.materials)
          for (const record of this.procureSelectedRows) {
            records.push({
              ...record,
              ...values,
              yearPrlineNum: this.materials.find(item => item[this.materialKey] === values[this.materialKey]).yearPrlineNum,
              yearPrlineSysId: this.materials.find(item => item[this.materialKey] === values[this.materialKey]).yearPrlineSysId,
              prlineSysIdRel: this.materials.find(item => item[this.materialKey] === values[this.materialKey])
                .prlineSysIdRel,
              prlineNumRel: this.materials.find(item => item[this.materialKey] === values[this.materialKey])
                .prlineNumRel,
              materialNum: this.materials.find(item => item[this.materialKey] === values[this.materialKey])
                .materialNum,
              materialName: this.materials.find(item => item[this.materialKey] === values[this.materialKey])
                .materialName,
              unitCost: this.materials.find(item => item[this.materialKey] === values[this.materialKey]).unitCost,
              budgetUuid: this.materials.find(item => item[this.materialKey] === values[this.materialKey]).budgetUuid,
              functionDept: this.materials.find(item => item[this.materialKey] === values[this.materialKey]).functionDept,
              jModel: this.materials.find(item => item[this.materialKey] === values[this.materialKey]).jModel,
              agent: this.materials.find(item => item[this.materialKey] === values[this.materialKey]).agent,
              planMonth: values.planMonth ? values.planMonth.format('YYYY-MM') : record.planMonth || '',
              jwspOrderQty: [this.procureFormEdit].includes(this.procureEditMode)
                ? values.orderQty
                : values.jwspOrderQty,
              reqDeliveryDate: values.reqDeliveryDate ? values.reqDeliveryDate.format('YYYY-MM-DD') : ''
            })
          }
          toSubmit(action, records, notice)
            .then(() => {
              this.hiddenMaterialDrawer()
              this.hiddenProcureDrawer()
              this.$emit('search')
              this.procureLoading = false
            })
            .catch(() => {
              this.procureLoading = false
            })
          this.procureLoading = true
        })
      }
    },
    requestedByChange (value, type) {
      if (IS_SLH_ENV && this.procureLineType === '20') {
        if (type === 1) {
          this.procureForm.setFieldsValue({
            storehouseSysId: ''
          })
        }
        baseApi.getCommboxById({ id: 'getPersonInfo', sqlParams: { colName: 'collectable_warehouse', personSysId: value } })
          .then(res => {
            const str = res.result[0] ? res.result[0].value : ''
            if (str) {
              const arr = str.split(',')

              this.slhStorehouse = this.storehouse.filter(item => arr.includes(item.value))
            } else {
              this.slhStorehouse = []
            }
          })
      }
    },
    // materialTable 搜索事件
    doMaterialSearch () {
      this.$refs.materialTable.refresh(true)
    },
    // materialTable 移除已选择
    doMaterialRemove (records) {
      records.forEach(record => {
        this.cacheMaterials = this.cacheMaterials.filter(item => record[this.materialKey] !== item[this.materialKey])
        this.materialSelectedRows = this.materialSelectedRows.filter(
          item => record[this.materialKey] !== item[this.materialKey]
        )
        this.materialSelectedRowKeys = this.materialSelectedRows.map(item => item[this.materialKey])
        this.$refs.materialTable.triggerSelect(this.materialSelectedRowKeys, this.materialSelectedRows)
      })
    },
    // 获取下拉框选项文本
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || ''
    },
    // 计划类型激活事件
    onLineTypeFocus () {
      // 已选择的计划类型
      this.procureLineType = this.procureForm.getFieldValue('lineType')
    },
    // 计划类型更改事件
    onLineTypeChange (value, option) {
      if (IS_JYS_ENV || IS_SLH_ENV) {
        if ([value, this.procureLineType].includes(PICK_TYPE_ID)) {
          this.procureForm.setFieldsValue({
            [this.materialKey]: []
          })
          this.materials = []
        }
      }
      this.procureLineType = value
      // 取消使用单位禁止
      this.usedCoFlag = false
    },
    materialChange (record, key, value) {
      if (!this.cellChangeSource.includes(record)) {
        this.cellChangeSource.push(record)
      }
      this.$set(record, key, value)
    },
    materialConfirm (record, key, value) {
      this.materialState = true
    },
    // 申请数填写校验
    formCheckOrderQty (rule, value, callback) {
      const regex = /^\d+(\.\d*)?$/
      console.log(value, 'formCheckOrderQty')
      if (!value) {
        callback(new Error('请输入申请数'))
        return
      }
      if (+value === 0) {
        callback(new Error('申请数不可为 0'))
        return
      }
      if (!regex.test(value)) {
        callback(new Error('申请数输入格式有误'))
        return
      }
      callback()
    },
    // 判断所选物资是否为超出定额的办公用品
    judgeQuota (action, records, notice, toSubmit) {
      const excessQuota = []
      records.forEach(item => {
        // 判断申请人所在部门的定额是否超额
        if (this.wzgQuotaRecord[this.personDept[item.requestedBy] + item.materialNum] <= 0 ||
        this.wzgQuotaRecord[this.personDept[item.requestedBy] + item.materialNum] - item.orderQty < 0) {
          excessQuota.push({
            materialName: item.materialName,
            materialNum: item.materialNum
          })
        }
      })
      if (excessQuota.length > 0) {
        this.$confirm({
          title: '提示',
          content: '(' + excessQuota[0].materialName + ')' + '已超出该部门定额,是否继续?',
          onOk: () => {
            toSubmit(action, records, notice)
              .then(() => {
                this.doProcureSearch(true, true)
                this.procureLoading = false
                this.queryWZGQuota()
              })
              .catch(() => {
                this.procureLoading = false
              })
            this.procureLoading = true
          },
          onCancel () {}
        })
      } else {
        toSubmit(action, records, notice)
          .then(() => {
            this.doProcureSearch(true, true)
            this.procureLoading = false
            this.queryWZGQuota()
          })
          .catch(() => {
            this.procureLoading = false
          })
        this.procureLoading = true
      }
    },
    onUsedForChange (value, option) {
      if (IS_JX_ENV) {
        this.procureForm.setFieldsValue({
          useType: ''
        })
        let i = 1
        const name = this.takeSelectLabel(this.directions, value)
        if (name.includes('世航')) {
          i = 2
        } else if (name.includes('经营公司')) {
          i = 3
        } else if (name.includes('独山港务')) {
          i = 4
        } else if (name.includes('新建项目')) {
          i = 5
        }
        for (const item of this.useType) {
          if (item.label.includes('集装箱')) {
            item.disabled = (i === 1 || i === 4)
          } else if (item.label.includes('散杂货')) {
            item.disabled = (i === 3 || i === 4)
          } else if (item.label.includes('煤炭')) {
            item.disabled = (i === 2 || i === 1)
          } else if (item.label.includes('液体化工')) {
            item.disabled = !(i === 5)
          } else {
            item.disabled = !(i === 5)
          }
        }
      }
    },
    // 查询温州港办公用品定额
    queryWZGQuota () {
      this.wzgQuotaRecord = []
      const params = {
        quotaYear: getNowDate().substr(0, 4),
        wzgOrgId: USER_ORG_ID,
        departmentSysId: ''
      }
      quotaApi.queryOfficeSuppliesQuota(requestBuilder('', params, '1', '999999')).then(res => {
        if (res.code === '0000') {
          for (const item of res.result.data) {
            this.wzgQuotaRecord[item.departmentSysId + item.materialNum] = item.remainQuota
          }
        }
      })
    },
    // materialTable 勾选更改
    materialSelectChange (selectedRowKeys, selectedRows) {
      this.materialSelectedRowKeys = selectedRowKeys
      this.materialSelectedRows = selectedRows
      for (const item of this.materialDataSource) {
        const selected = this.materialSelectedRowKeys.includes(item[this.materialKey])
        const existed = this.materialDataSource.some(record => record[this.materialKey] === item[this.materialKey])
        const index = this.cacheMaterials.findIndex(item2 => item2[this.materialKey] === item[this.materialKey])
        if (selected && index === -1) {
          if (this.materialRowSelection.type === 'checkbox') {
            this.cacheMaterials.push({
              prlineNum: item.prlineNum,
              prlineNumRel: item.prlineNum,
              yearPrlineNum: this.materialParam.yearPlan ? item.yearPrlineNum : '',
              yearPrlineSysId: this.materialParam.yearPlan ? item.yearPrlineSysId : '',
              prlineSysIdRel: item.prlineSysId,
              materialNum: item.materialNum,
              materialName: item.materialName,
              orderUnit: item.orderUnit,
              unitCost: item.unitCost || item.refCost,
              jModel: item.jModel || item.jmodel,
              jmodel: item.jModel || item.jmodel,
              agent: item.agent || item.buyer,
              curbal: item.curbal,
              maxAmount: item.maxAmount,
              usedFor: item.usedFor,
              usedCo: item.usedCo,
              jwspOrderQty: item.jwspOrderQty,
              orderQty: item.orderQty,
              pMCMatapplyId: item.pMCMatapplyId,
              contractSysId: item.contractSysId,
              contractLineSysId: item.contractLineSysId,
              budgetUuid: item.budgetUuid,
              functionDept: item.functionDept,
              taxRate: item.taxRate
            })
          }
          if (this.materialRowSelection.type === 'radio') {
            this.cacheMaterials = [
              {
                prlineNum: item.prlineNum,
                prlineNumRel: item.prlineNum,
                prlineSysIdRel: item.prlineSysId,
                materialNum: item.materialNum,
                materialName: item.materialName,
                orderUnit: item.orderUnit,
                unitCost: item.unitCost || item.refCost,
                jModel: item.jModel || item.jmodel,
                jmodel: item.jModel || item.jmodel,
                agent: item.agent || item.buyer,
                curbal: item.curbal,
                usedFor: item.usedFor,
                usedCo: item.usedCo,
                jwspOrderQty: item.jwspOrderQty,
                budgetUuid: item.budgetUuid,
                functionDept: item.functionDept,
                orderQty: item.orderQty
              }
            ]
          }
        }
        if (index > -1) {
          if (!selected && existed) {
            this.cacheMaterials.splice(index, 1)
          }
        }
        if (this.$refs.selectedMaterialTable) {
          this.$refs.selectedMaterialTable.refresh(true)
        }
      }
    }
  }
}
</script>

<style>

</style>
