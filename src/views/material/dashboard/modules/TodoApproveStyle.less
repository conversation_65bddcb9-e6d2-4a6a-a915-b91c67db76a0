section {
  display: flex;
  height: 100%;
  ::v-deep {
    .linkRowStyle {
      background-color: #d2eef7;
    }
    .border-only-bottom {
      .ant-select-selection {
        border: none;
        border-bottom: 1px solid #d9d9d9;
        border-radius: initial;
        box-shadow: none;
        &:focus,
        &:active {
          box-shadow: none;
        }
      }
    }
  }
  .list-container {
    flex: 0 0 auto;
    width: 260px;
    height: 100%;
    padding-right: 20px;
    position: relative;
    &::after {
      content: '';
      width: 1px;
      height: calc(100% - 10px);
      background-color: #e9e9e9;
      position: absolute;
      top: 5px;
      right: 5px;
    }
    .list-header {
      width: 100%;
      height: 40px;
      position: relative;
      .title {
        width: 100%;
        height: 36px;
        padding-right: 15px;
        line-height: 36px;
        text-align: center;
        position: relative;
        border-bottom: dashed 1px #cfcfcf;
      }
      .button {
        color: #40a9ff;
        font-size: 10px;
        padding: 2px 5px;
        display: inline-block;
        cursor: pointer;
        position: absolute;
        top: 9px;
        right: 2px;
      }
    }
    .list-content {
      width: 100%;
      min-height: 250px;
      max-height: 364px;
      padding-left: 5px;
      overflow: auto;
      .list-item {
        width: 100%;
        min-height: 36px;
        padding: 3px 0;
        position: relative;
        cursor: pointer;
        display: flex;
        align-items: center;
        align-content: center;
        &::after {
          content: '';
          width: 100%;
          height: 1px;
          background-color: #f6f6f6;
          position: absolute;
          left: -2px;
          bottom: 0;
        }
        .left {
          width: 25px;
          height: 100%;
          flex: 0 0 auto;
        }
        .right {
          width: calc(100% - 25px);
          flex: 1 1 auto;
          & > .num {
            font-size: 14px;
            line-height: 21px;
            color: #606266;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          & > .description {
            padding-left: 2px;
            font-size: 12px;
            line-height: 21px;
            word-wrap: break-word;
            word-break: break-word;
            // overflow: hidden;
            // white-space: nowrap;
            // text-overflow: ellipsis;
          }
        }
        .control {
          width: 40px;
          height: 100%;
          flex: 0 0 auto;
          font-size: 12px;
          color: #606266;
          overflow: hidden;
        }
      }
    }
    .list-bottom {
      width: 100%;
      height: 68px;
      position: relative;
      background: #ffffff;
      bottom: 68px;
    }
  }
  .table-container {
    flex: 1 1 auto;
    width: calc(100% - 260px);
    height: 100%;
    padding: 6px 0 6px 8px;
  }
}
