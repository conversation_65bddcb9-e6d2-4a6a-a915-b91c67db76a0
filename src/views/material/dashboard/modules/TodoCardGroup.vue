<template>
  <section v-if="todoCardGroup.length>0">
    <a-row :gutter="24">
      <a-col
        :sm="minGrid.sm"
        :md="minGrid.md"
        :xl="minGrid.xl"
        :style="minGrid.style"
        v-show="isCollspan || index<4"
        v-for="(item,index) in arr"
        :key="index"
      >
        <component :is="item.name" @close="close" />
      </a-col>
    </a-row>
    <div style="height:10px" v-if="arr && arr.length>4">
      <a-button class="btn" @click="isCollspan=!isCollspan" v-if="!isCollspan" style="float: right; margin-top: -30px;">展开更多<a-icon type="down" /><div v-show="isShowIcon" class="icon"/></a-button>
      <a-button @click="closeBtn" v-if="isCollspan" style="float: right; margin-top: -30px;">收起<a-icon type="up" /></a-button>
    </div>
  </section>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'TodoCardGroup2',
  components: {
  },
  data () {
    return {
      isCollspan: false,
      minGrid: {
        xl: 6,
        md: 12,
        sm: 24,
        gutter: 24,
        style: {
          marginBottom: '24px'
        }
      },
      isShowIcon: false,
      arr: this.doArr()
    }
  },
  methods: {
    close () {
      this.$emit('close')
    },
    doArr () {
      return this.$nextTick(() => {
        this.arr = this.todoCardGroup.filter(item => {
          return item.name !== 'TodoCardDeclarationPrompt'
        })
      })
    },
    showIcon () {
      const list = this.arr.slice(4)
      this.isShowIcon = list.some(item => {
        return this.$refs[item.name][0].$children[0].total > 0
      })
    },
    closeBtn () {
      this.isCollspan = !this.isCollspan
      this.showIcon()
    }
  },
  updated () {
    // this.showIcon()
    this.intervalId = setTimeout(this.showIcon, 2500)
  },
  computed: {
    ...mapGetters(['todoCardGroup'])
  }
}
</script>
