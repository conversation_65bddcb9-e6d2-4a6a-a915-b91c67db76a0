<template>
  <div>
    <a-drawer
      title="编辑板块"
      width="100%"
      placement="right"
      :closable="false"
      :visible="visible"
      @close="onClose"
    >
      <div class="container-D">
        <div class="leftBox">
          <div class="head-area">
            <div class="container-head">
              <a-button type="primary" @click="doAdd">
                新增
              </a-button>
              <a-button type="primary" @click="reset">
                重置
              </a-button>
              <a-button type="danger" @click="clear">
                清空
              </a-button>
            </div>
          </div>
          <div class="list-area">
            <draggable v-model="leftList" :disabled="disableDra" :group="grpupA" animation="300" @end="end">
              <transition-group>
                <div class="small-sortBox" v-for="element in leftList" :key="element.boxNum">
                  <img style="width: 99%; height: 99%;" :src="returnSrc(element.boxName)">
                </div>
              </transition-group>
            </draggable>
            <draggable v-model="leftLargeList" :disabled="disableDra" :group="grpupB" animation="300" @end="end">
              <transition-group>
                <div class="large-sortBox" v-for="element in leftLargeList" :key="element.boxNum">
                  <img style="width: 99%; height: 99%;" :src="returnSrc(element.boxName)">
                </div>
              </transition-group>
            </draggable>
          </div>
        </div>
        <div class="rightBox">
          <div
            v-for="(item, index) in groupList"
            :key="index"
            :class="[item.className, 'borders']"
            @mouseenter="mouseenterLeft(item)"
            @mouseleave="mouseleaveLeft(item)"
          >
            <div v-show="item.showTip" class="tip">
              <a href="javascript:void(0)">
                {{ item.size }}
              </a>
              <a href="javascript:void(0)" :disabled="index === 0" @click="toUp(index)">
                <a-icon type="arrow-up" />
              </a>
              <a href="javascript:void(0)" :disabled="index === groupList.length - 1" @click="toDown(index)">
                <a-icon type="arrow-down" />
              </a>
              <a href="javascript:void(0)" @click="doClear(item, index)">
                <a-icon type="minus-circle" />
              </a>
              <a href="javascript:void(0)" @click="doDel(item, index)">
                <a-icon type="delete" />
              </a>
            </div>
            <draggable
              v-model="item.rightList"
              :group="item.group"
              :disabled="disableDra"
              animation="300"
              delay="200"
              :class="[item.className]"
            >
              <transition-group :class="[item.className]">
                <div :class="[item.boxClassName]" v-for="element in item.rightList" :key="element.boxName">
                  <component :is="element.boxName" :disabled="true" :dragging="true"/>
                </div>
              </transition-group>
            </draggable>
          </div>
        </div>
      </div>
      <div
        :style="{
          position: 'absolute',
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e8e8e8',
          padding: '10px 16px',
          textAlign: 'right',
          left: 0,
          background: '#fff',
          borderRadius: '0 0 4px 4px'
        }"
      >
        <a-button style="marginRight: 8px" @click="onClose">
          取消
        </a-button>
        <a-button type="primary" :loading="submitLoading" @click="onSubmit">
          提交
        </a-button>
      </div>
    </a-drawer>
    <createModal
      ref="createModal"
      @submit="createNew"
    />
  </div>
</template>
<script>
import draggable from 'vuedraggable'
import { ORG_ID } from '@/store/mutation-types'
import * as component from './CrewComponents.js'
import createModal from './modules/createModal'
import { modifyPersonalCockpitRecord } from '@/api/material/table'
import { requestBuilder, deepUpdate } from '@/utils/util'
import Vue from 'vue'
// import { deepUpdate } from '@/utils/util'
import { CrewMixin } from '@/utils/CrewMixin'

// 操作人 userNo
const USER_ORG_ID = Vue.ls.get(ORG_ID)
export default {
  name: 'CrewEditDrawer',
  components: {
    ...component,
    draggable,
    createModal
  },
  mixins: [CrewMixin],
  props: {
    currentList: {
      type: Array,
      default: () => {
        return []
      }
    },
    mainList: {
      type: Array,
      default: () => {
        return []
      }
    },
    mainLargeList: {
      type: Array,
      default: () => {
        return []
      }
    },
    origin: {
      type: String,
      required: true,
      default: 'WZ'
    }
  },
  data () {
    return {
      USER_ORG_ID,
      disableDra: false,
      submitLoading: false,
      visible: false,
      grpupA: {
        name: 'small-site',
        pull: true,
        put: false
      },
      grpupB: {
        name: 'large-site',
        pull: true,
        put: false
      },
      leftList: [],
      leftLargeList: [],
      groupList: [
        {
          className: 'small-area',
          boxClassName: 'small-sortBox',
          group: 'small-site',
          size: 'small',
          showTip: false,
          rightList: []
        },
        {
          className: 'large-area',
          boxClassName: 'large-sortBox',
          group: 'large-site',
          size: 'large',
          showTip: false,
          rightList: []
        }
      ]
    }
  },
  created () {
  },
  methods: {
    returnSrc (item) {
      return require(`../../pic/componentsPic/${item}.png`)
    },
    doAdd () {
      this.$refs.createModal.showModal()
    },
    createNew (value) {
      const obj = {
        className: `${value}-area`,
        boxClassName: `${value}-sortBox`,
        group: `${value}-site`,
        size: value,
        showTip: false,
        rightList: []
      }
      this.groupList.push(obj)
    },
    reset () {
      this.groupList = this.currentList.map(item => deepUpdate({ ...item }))
      const Array = []
      this.groupList.forEach(item => {
        Array.push(...item.rightList)
      })
      const arr = Array.map(item => item.boxName)
      this.leftList = this.mainList.filter(item => !arr.includes(item.boxName))
      this.leftLargeList = this.mainLargeList.filter(item => !arr.includes(item.boxName))
    },
    clear () {
      this.leftList = this.mainList.map(item => item)
      this.leftLargeList = this.mainLargeList.map(item => item)
      this.groupList.forEach(item => {
        item.rightList = []
      })
    },
    showDrawer () {
      this.reset()
      this.visible = true
    },
    toUp (index) {
      const obj = this.groupList[index]
      this.$set(this.groupList, index, this.groupList[index - 1])
      this.$set(this.groupList, [index - 1], obj)
    },
    toDown (index) {
      const obj = this.groupList[index]
      this.$set(this.groupList, index, this.groupList[index + 1])
      this.$set(this.groupList, [index + 1], obj)
    },
    doDel (item, index) {
      if (item.size === 'small') {
        this.leftList.push(...item.rightList)
      } else {
        this.leftLargeList.push(...item.rightList)
      }
      this.groupList.splice(index, 1)
    },
    doClear (item) {
      if (item.size === 'small') {
        this.leftList.push(...item.rightList)
      } else {
        this.leftLargeList.push(...item.rightList)
      }
      item.rightList = []
    },
    onClose () {
      this.visible = false
      this.reset()
    },
    onSubmit () {
      const arr = []
      this.groupList.forEach(item => {
        if (item.rightList.length > 0) {
          const obj = deepUpdate({
            className: '',
            boxClassName: '',
            index: 0,
            group: '',
            size: '',
            showTip: false,
            rightList: []
          }, {
            ...item,
            index: arr.length,
            rightList: item.rightList.map((info, index) => {
              return {
                ...info,
                boxNum: index
              }
            })
          })
          arr.push(obj)
        }
      })
      const param = requestBuilder('update', { cockpitType: this.origin, outBoxList: arr, orgId: USER_ORG_ID })
      modifyPersonalCockpitRecord(param).then(res => {
        if (res.code === '0000') {
          this.$notification.success({
            message: '系统消息',
            description: res.message || '保存成功！'
          })
          this.onClose()
          this.$store.dispatch('SettodoCockpitGroup')
          this.$emit('submit', arr)
        } else {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '系统异常！'
          })
        }
      }).finally(() => {
        this.disableDra = false
        this.submitLoading = false
      })
    },
    mouseenterLeft (item) {
      item.showTip = true
    },
    mouseleaveLeft (item) {
      item.showTip = false
    },
    end () {
      // 防抖测试
      this.disableDra = true
      setTimeout(() => {
        this.disableDra = false
      }, 300)
    }
  }
}
</script>

<style lang="less" scoped>
@keyframes toUp {
  from {
    transform: translate(-50%, -50%);
  }
  to {
    transform: translate(-50%, -10%);
  }
}
@keyframes toDown {
  from {
    transform: translate(-50%, -50%);
  }
  to {
    transform: translate(-50%, -90%);
  }
}
  .container-head {
    width: 100%;
    height: 50px;
    padding: 10px 0;
  }
  .container-D {
    display: flex;
    justify-content: space-between;
    height: 780px;
    .rightBox {
      background-color: #F5F5F5;
      width: 79%;
      height: 100%;
      border: 1px solid #343434;
      overflow-y: scroll;
      border-radius: 5px;
      padding: 5px;
      .borders {
        border: 2px dashed #F5F5F5;
      }
      .borders:hover {
        border: 2px dashed #fca327;
      }
      .small-area {
        position: relative;
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        min-height: 150px;
        .small-sortBox {
          width: 24%;
          height: 200px;
          border: 1px solid #4557FF;
          border-radius: 5px;
          margin-bottom: 5px;
          cursor: pointer;
          margin: .3% .5%;
        }
      }
      .large-area {
        position: relative;
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        min-height: 150px;
        .large-sortBox {
          width: 49%;
          height: 550px;
          border: 1px solid #4557FF;
          border-radius: 5px;
          margin-bottom: 5px;
          cursor: pointer;
          margin: .3% .5%;
        }
      }
      .tip {
        z-index: 888;
        display: block;
        position: absolute;
        top: 0;
        right: 0;
        background-color: #ffb245;
        a {
          margin: 3px;
          cursor: pointer;
        }
      }
    }
    .leftBox {
      position: relative;
      height: 780px;
      width: 20%;
      .head-area {
        height: 50px;
      }
      .list-area {
        height: 730px;
        width: 100%;
        background-color: #F5F5F5;
        border: 1px solid #343434;
        overflow-y: scroll;
        border-radius: 5px;
        padding: 5px;
      }
      .small-sortBox {
        width: 100%;
        height: 200px;
        border: 1px solid #4557FF;
        border-radius: 5px;
        margin-bottom: 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
      .large-sortBox {
        width: 100%;
        height: 300px;
        border: 1px solid #4557FF;
        border-radius: 5px;
        margin-bottom: 5px;
        cursor: pointer;
      }
    }
  }
</style>
