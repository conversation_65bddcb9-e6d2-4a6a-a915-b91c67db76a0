<template>
  <div>
    <a-modal title="选择尺寸" :visible="visible" width="300px" @ok="handleOk" @cancel="handleCancel">
      <a-select v-model="value" style="width: 100%">
        <a-select-option value="small">
          小号
        </a-select-option>
        <a-select-option value="large">
          大号
        </a-select-option>
      </a-select>
    </a-modal>
  </div>
</template>
<script>
export default {
  data () {
    return {
      value: 'small',
      visible: false
    }
  },
  methods: {
    showModal () {
      this.visible = true
    },
    handleOk (e) {
      this.$emit('submit', this.value)
      this.handleCancel()
    },
    handleCancel (e) {
      this.visible = false
      this.value = 'small'
    }
  }
}
</script>
