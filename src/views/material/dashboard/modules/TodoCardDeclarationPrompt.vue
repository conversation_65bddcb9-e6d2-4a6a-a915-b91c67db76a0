<template>
  <section>
    <a-row v-if="CockpitVisible" class="area" :gutter="24">
      <a-card title="集采物资申报情况">
        <template slot="extra">
          <a-date-picker
            mode="year"
            placeholder="请选择年份"
            format="YYYY"
            :allowClear="false"
            :open="yearShowOne"
            v-model="query.queryDate"
            @openChange="openChangeOne"
            @panelChange="panelChangeOne"
            @change="doSearch"
          />
          <a-button type="primary" icon="fresh" @click="doSearch">刷新</a-button>
        </template>
        <s-table
          ref="table"
          size="default"
          :rowKey="(item, index) => index"
          :columns="columns"
          :components="components"
          :data="loadData"
          :alert="options.alert"
          :scroll="scroll"
          :customRow="rowClick"
          :pageSizeOptions="pageSizeOptions"
          :immediate="true"
          :pageSize="defaultPageSize"
          :showPagination="true"
        >
          <span slot="serial" slot-scope="text, record, index">{{ index + 1 }}</span>
          <span slot="description" slot-scope="text">
            <ellipsis :length="8" tooltip>{{ text }}</ellipsis>
          </span>
          <span slot="status" slot-scope="text">{{ text === 'Y' ? '启用' : '禁用' }}</span>
          <span slot="status" slot-scope="text">{{ text === 'Y' ? '启用' : '禁用' }}</span>
          <span slot="declareCount" slot-scope="text, record, index, column">
            <!-- <div :class="['deptBox', getRealData(column) ? 'blue' : '']" @click="goRouter(column)">11</div> -->
            <div :class="['deptBox', 'blue']" @click="goRouter(column)">{{ column.declareCount !== '0' ? column.declareCount : '' }}</div>
          </span>
          <span slot="year" slot-scope="text">
            <div :class="['deptBox', 'blue']">{{ text }}</div>
          </span>
          <!-- <span slot="action" slot-scope="text, record">
            <a
              href="javascript:void(0)"
              v-action:upload
              style="margin-right: 5px;"
              @click="openUpload(record)"
            >
              附件{{ record.fileCount > 0 ? `(${record.fileCount})` : '*' }}
            </a>
            <a v-action:del href="javascript:void(0)" style="color: red;" @click="doBatchDel([record])">删除</a>
          </span> -->
        </s-table>
      </a-card>
    </a-row>
  </section>
</template>

<script>
// import { PERSON_ID, ORG_ID } from '@/store/mutation-types'
import { mapGetters } from 'vuex'
import { requestBuilder } from '@/utils/util'
// import Vue from 'vue'
import { STable, Ellipsis } from '@/components'
import * as planYearPurchaseApi from '@/api/device/planYearPurchase'
import moment from 'moment'
// const PERSON_SYS_ID = Vue.ls.get(PERSON_ID)
// const USER_ORG_ID = Vue.ls.get(ORG_ID)
export default {
  components: {
    STable,
    Ellipsis
  },
  name: 'TodoCardDeclarationPrompt',
  data () {
    this.components = {
      header: {
        cell: (h, props, children) => {
          const { key, ...restProps } = props
          const col = this.columns.find(col => {
            const k = col.dataIndex || col.key
            return k === key
          })

          if (!col || !col.width) {
            return h('th', { ...restProps }, [...children])
          }

          const dragProps = {
            key: col.dataIndex || col.key,
            class: 'table-draggable-handle',
            attrs: {
              w: 10,
              x: col.width,
              z: 1,
              axis: 'x',
              draggable: true,
              resizable: false
            },
            on: {
              dragging: (x, y) => {
                col.width = Math.max(x, 1)
              }
            }
          }
          const drag = h('vue-draggable-resizable', { ...dragProps })
          return h('th', { ...restProps, class: 'resize-table-th' }, [...children, drag])
        }
      }
    }
    return {
      // 操作人 userPersonId
      CockpitVisible: false,
      yearShowOne: false,
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20', '50', '100'],
      mdl: {},
      // 高级搜索 展开/关闭
      advanced: false,
      procureSelected: [],
      procureSelectedKey: [],
      scroll: {
        x: '100%'
      },
      // 表头定义
      columns: [
        {
          title: '年份',
          dataIndex: 'queryDate',
          align: 'center',
          // ellipsis: true,
          fixed: 'left',
          width: 80
        }
      ],
      query: {
        queryDate: moment()
      },
      dataSource: [],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const queryParam = {
          queryDate: this.query.queryDate ? this.query.queryDate.format('YYYY') : ''
        }
        const param = requestBuilder('', Object.assign(queryParam), parameter.pageNo, parameter.pageSize, parameter.sortField, parameter.sortOrder)
        return planYearPurchaseApi.queryYearPlanPurchaseHasPushed(param).then(res => {
          this.dataSource = [...res.result] || []
          this.genuColumns(res.result)
          return [{ queryDate: this.query.queryDate ? this.query.queryDate.format('YYYY') : '' }]
        })
      },
      // 行选择参数
      options: {
        alert: {
          show: false,
          clear: () => {
            this.selectedRowKeys = []
          }
        },
        rowSelection: {
          // selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        }
      },
      selectedRowKeys: [],
      selectedRows: [],
      rowClick: record => ({
        on: {
          // 点击事件
          dblclick: () => {
            this.openDrawer('update', record)
          }
        }
      })
    }
  },
  created () {
    this.JudgingCockpit()
  },
  computed: {
    ...mapGetters(['todoCardGroup'])
  },
  mounted () {
  },
  methods: {
    JudgingCockpit () {
      this.CockpitVisible = this.todoCardGroup.some(item => item.name === 'TodoCardDeclarationPrompt')
    },
    goRouter (column) {
      this.$router.push({
        name: 'PurchaseFrom',
        params: {
          orgName: column.orgName,
          orgId: column.orgId
        }
      })
    },
    // 弹出日历和关闭日历的回调
    openChangeOne (status) {
      if (status) {
        this.yearShowOne = true
      }
    },
    // 得到年份选择器的值
    panelChangeOne (value) {
      this.yearShowOne = false
      this.query.queryDate = value
      this.doSearch()
    },
    doSearch () {
      this.$refs.table.refresh(true)
    },
    genuColumns (list) {
      const arr = [
        {
          title: '年份',
          dataIndex: 'queryDate',
          scopedSlots: { customRender: 'year' },
          align: 'center',
          ellipsis: true,
          sorter: false,
          fixed: false,
          check: false,
          edit: false,
          width: 70
        }
      ]
      list.forEach(item => {
        arr.push({
          title: item.orgShortName,
          orgName: item.orgName,
          orgId: item.orgId,
          declareCount: item.declareCount,
          scopedSlots: { customRender: 'declareCount' },
          // dataIndex: 'declareCount',
          align: 'center',
          // ellipsis: true,
          width: 40
        })
      })
      this.columns = arr
    },
    getRealData (column) {
      const orgName = column.orgName
      const hasDeclare = this.dataSource.find(item => item.orgName === orgName).hasDeclare
      return hasDeclare === 'Y'
    }
  }
}
</script>

<style lang="less" scoped>
.area {
  margin: 20px 0 !important;
}
/deep/.ant-card-body {
  padding: 15px 30px;
}
.deptBox {
  height: 30px;
  width: 100%;
  border-radius: 5px;
  padding: 0px;
  // background: #d8eeff;
  padding: 0px;
  cursor: pointer;
}
.blue {
  color: #4557FF;
  font-size: 13px;
  font-weight: 700;
}
</style>
