<template>
  <section style="margin-bottom: 30px;">
    <a-card
      :bordered="false"
      :body-style="{ padding: '0 10px 0 0', marginBottom: '30px' }"
    >
      <div class="section-header">
        <div class="titles">
          <div
            :class="['title', { active: type === 'todo' } ]"
            @click="type = 'todo'"
          >
            <span class="count">{{ todoTotalCount || 0 }}</span>
            <div class="text">待办事项</div>
          </div>
          <div
            :class="['title', { active: type === 'ready' } ]"
            @click="type = 'ready'"
          >
            <span class="count">{{ readyTotalCount || 0 }}</span>
            <div class="text">已办事项</div>
          </div>
        </div>
        <div class="tip" v-if="IS_BYJ_ENV">
          <span :style="{backgroundColor: '#9acf85',width: '15px',height: '15px',display:'inline-block'}"/><span class="pointStyle">急件计划</span>
          <span :style="{backgroundColor: '#e7e081',width: '15px',height: '15px',display:'inline-block'}"/><span class="pointStyle">补报计划</span>
          <span :style="{backgroundColor: '#ddb5dfec',width: '15px',height: '15px',display:'inline-block'}"/><span class="pointStyle">临近日期</span>
          <span :style="{backgroundColor: '#a5e7e1',width: '15px',height: '15px',display:'inline-block'}"/><span class="pointStyle">退回数据</span>
        </div>
        <div class="buttons">
          <a-range-picker
            v-show="type === 'ready'"
            format="YYYY-MM-DD"
            :placeholder="['Start Time', 'End Time']"
            v-model="readyParam.dates"
            @change="dataChanges"
          />
          <a-button
            v-if="type === 'todo' && totalApprove > 0"
            v-action:approve
            icon="check"
            type="primary"
            size="large"
            style="margin: 0 5px"
            :loading="loading.approve"
            @click="doDrawerOpen"
          >审批</a-button>
          <a-button
            icon="reload"
            type="primary"
            size="large"
            style="margin: 0 5px"
            :loading="loading.query"
            @click="doQueryAll(true)"
          >刷新</a-button>
        </div>
      </div>
    </a-card>
    <a-card
      :bordered="false"
      :body-style="{ padding: '0 10px' }"
    >

      <div class="section-content">
        <!-- 待办板块 -->
        <a-spin
          v-show="type === 'todo'"
          class="todo-container"
          :spinning="loading.query"
        >
          <a-tabs
            v-if="todoTotalCount > 0"
            v-model="todoActiveKey"
            :tabBarStyle="barStyle"
            class="tabs-container"
            @change="doChangeTab"
          >
            <a-tab-pane
              v-if="renderTabPane(item)"
              v-for="item of todoTabs"
              :forceRender="true"
              :style="paneStyle"
              :key="'todo-' + item.origin"
            >
              <div
                slot="tab"
                class="tab-pane-header"
              >
                <span class="name">{{ renderTabPaneName(item) }}</span>
                <span class="count">{{ item.count }}</span>
              </div>
              <div class="tab-pane-bodyer">
                <component
                  :is="item.origin"
                  :ref="'todo-' + item.origin"
                  :origin="item.origin"
                  :loading="loading"
                  :queryApi="item.actionNameDone"
                  :useApprove="todoUseApprove"
                  :queryParam="todoParam"
                  :companyArr="companyArr"
                  :status="status"
                />
              </div>
            </a-tab-pane>
          </a-tabs>
          <div
            v-else
            class="todo-empty"
          >
            <div class="empty-text">{{ loading.query ? '正在查询中...' : '暂无待办事项...' }}</div>
          </div>
        </a-spin>

        <!-- 已办板块 -->
        <a-spin
          v-show="type === 'ready'"
          class="ready-container"
          :spinning="loading.query"
        >
          <a-tabs
            v-if="readyTotalCount > 0"
            v-model="readyActiveKey"
            :tabBarStyle="barStyle"
            class="tabs-container"
            @change="doChangeTab"
          >
            <a-tab-pane
              v-if="renderTabPane(item)"
              v-for="item of readyTabs"
              :forceRender="true"
              :style="paneStyle"
              :key="'ready-' + item.origin"
            >
              <div
                slot="tab"
                class="tab-pane-header"
              >
                <span class="name">{{ renderTabPaneName(item) }}</span>
              </div>
              <div class="tab-pane-bodyer">
                <component
                  :is="item.origin"
                  :ref="'ready-' + item.origin"
                  :origin="item.origin"
                  :loading="loading"
                  :queryApi="item.actionNameDone"
                  :useApprove="readyUseApprove"
                  :queryParam="readyParam"
                  :companyArr="companyArr"
                  :status="status"
                />
              </div>
            </a-tab-pane>
          </a-tabs>
          <div
            v-else
            class="ready-empty"
          >
            <div class="empty-text">{{ loading.query ? '正在查询中...' : '暂无已办事项...' }}</div>
          </div>
        </a-spin>
      </div>
    </a-card>
  </section>
</template>

<script>
import Vue from 'vue'
import { ORG_ID } from '@/store/mutation-types'
import { APPROVE_OPTIONS_HIDES } from '@/store/variable-types'
import { requestBuilder, deepUpdate } from '@/utils/util'
import * as components from './TodoApproveComponents'
import * as baseApi from '@/api/system/base'
import * as flowApi from '@/api/system/flow'
import moment from 'moment'
const TAB_PANES = []
const TAB_COUNTS = {}

for (const key in components) {
  if (!TAB_PANES.includes(key)) {
    TAB_PANES.push(key)
    TAB_COUNTS[key + 'Count'] = 0
  }
}

const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_ZP_ENV = USER_ORG_ID === '1.102'
const IS_JYS_ENV = USER_ORG_ID === '1.100.117'
// 北一
const IS_BYJ_ENV = USER_ORG_ID === '1.100.101'
const IS_NJMZ_EVN = USER_ORG_ID === '1.100.109'
const IS_MD_ENV = USER_ORG_ID === '1.100.104'
const IS_HJ_ENV = USER_ORG_ID === '1.100.131'
const IS_SLH_ENV = USER_ORG_ID === '1.100.111'
export default {
  name: 'TodoApproveGroup',
  components: {
    ...components
  },
  provide () {
    return {
      doQueryAll: this.doQueryAll
    }
  },
  data () {
    return {
      IS_BYJ_ENV,
      IS_NJMZ_EVN,
      IS_HJ_ENV,
      IS_SLH_ENV,
      // 类型
      type: 'todo',

      // 下拉框选项
      status: [],
      companyArr: [],

      // tabs组件 - 样式配置
      barStyle: {
        paddingLeft: '16px',
        marginBottom: '0px'
      },
      paneStyle: {
        maxHeight: '420px',
        padding: '8px 12px'
      },

      // 待办业务
      todoTabs: [],
      todoParam: {},
      todoActiveKey: '',
      todoUseApprove: true,
      totalApprove: 0,
      todoTotalCount: 0,
      ...TAB_COUNTS,

      // 已办业务
      readyTabs: [],
      readyParam: {
        dates: [moment().subtract(30, 'days'), moment()],
        todoCardStartTime: moment().subtract(30, 'days').format('YYYY-MM-DD'),
        todoCardEndTime: moment().format('YYYY-MM-DD')
      },
      readyActiveKey: '',
      readyUseApprove: false,
      readyTotalCount: 0,

      // loading
      loading: {
        query: false,
        approve: false
      }
    }
  },
  created () {
    // 初始化选项获取
    this.initOptions()
  },
  mounted () {
    // 初始化 query
    this.doQueryAll(true)
  },
  watch: {
    type: {
      handler () {
        this.$nextTick(() => {
          this.doQueryAll()
        })
      }
    }
  },
  methods: {
    // 下拉框数据获取
    initOptions () {
      // 审批状态
      baseApi.getTreeById({ id: 'appro' }).then(res => {
        if (res.code === '0000') {
          for (const item of res.result) {
            if (APPROVE_OPTIONS_HIDES.includes(item.value)) {
              item.disabled = true
            }
            this.status.push(item)
          }
        }
      })
      // 组织单位
      baseApi.getCommboxById({ id: 'org' }).then(res => {
        if (res.code === '0000') {
          for (const item of res.result) {
            this.companyArr.push({
              value: item.value,
              label: item.shortName || item.label
            })
          }
        }
      })
    },
    // 渲染 标签页
    renderTabPane (item) {
      switch (item.origin) {
        case 'poline': {
          return !IS_ZP_ENV
        }
      }
      return true
    },
    // 渲染 标签页 标题
    renderTabPaneName (item) {
      switch (item.origin) {
        case 'prline': {
          return IS_ZP_ENV
            ? '领料计划'
            : IS_JYS_ENV
              ? '物资计划'
              : IS_NJMZ_EVN
                ? '申报计划'
                : '计划申请'
        }

        case 'rfqline': {
          return IS_JYS_ENV
            ? '供应商审批'
            : '询价申请'
        }

        case 'Quo': {
          return IS_ZP_ENV
            ? '采购申请'
            : IS_JYS_ENV
              ? '报价审批'
              : '报价申请'
        }

        case 'poline': {
          return IS_JYS_ENV
            ? '验收入库'
            : '订单申请'
        }
        case 'prlineSummary': {
          return '汇总申请'
        }
      }
      return item.appChnName
    },
    // 查询待办统计
    doQueryTodoCount (force) {
      const activeKeys = []
      const param = requestBuilder(
        '',
        deepUpdate({
          ...this.todoParam,
          isSummary: IS_MD_ENV ? 'Y' : ''
        })
      )
      return flowApi.findTodoCount(param).then(res => {
        if (res.code === '0000') {
          // 初始化
          this.todoTabs = []
          this.todoTotalCount = 0

          // 待办数量
          for (const key in TAB_COUNTS) {
            this[key] = 0
          }

          // 储存组件
          for (const item of res.result) {
            if (TAB_PANES.includes(item.origin)) {
              activeKeys.push('todo-' + item.origin)
              this[item.origin + 'Count'] = item.count <= 99 ? item.count : '99+'
              this.todoTotalCount += item.count || 0
              if (item.origin !== 'read') {
                this.totalApprove = this.todoTotalCount
              }
              this.todoTabs.push(item)
            }
          }
          this.$store.commit('SET_TODONUM', this.todoTotalCount)

          // 当前激活的tab页
          if (activeKeys.length > 0) {
            if (!activeKeys.includes(this.todoActiveKey)) {
              this.todoActiveKey = activeKeys[0]
            }
            const mark = this.todoActiveKey
            const $active = this.$refs[mark] || {}
            const defLoading = { query: true, approve: false }
            const { loading = defLoading } = $active
            this.loading = loading
            return
          }

          // 默认设置
          this.loading = {
            query: false,
            approve: false
          }
          this.todoActiveKey = ''
        }
      })
    },
    // 查询待办数据
    doQueryTodoSource (force) {
      const mark = this.todoActiveKey
      const $active = mark && this.$refs[mark][0] || {}
      $active.doQuerySource && $active.doQuerySource()
    },
    // 查询已办统计
    doQueryReadyCount (force) {
      const activeKeys = []
      const param = requestBuilder(
        '',
        deepUpdate({
          ...this.readyParam
        })
      )
      return flowApi.findDoneCount(param).then(res => {
        if (res.code === '0000') {
          // 初始化
          this.readyTabs = []
          this.readyTotalCount = 0

          // 储存组件
          for (const item of res.result) {
            if (TAB_PANES.includes(item.origin)) {
              activeKeys.push('ready-' + item.origin)
              this.readyTotalCount += item.count || 0
              this.readyTabs.push(item)
            }
          }

          // 当前激活的tab页
          if (activeKeys.length > 0) {
            if (!activeKeys.includes(this.readyActiveKey)) {
              this.readyActiveKey = activeKeys[0]
            }
            const mark = this.readyActiveKey
            const $active = this.$refs[mark] || {}
            const defLoading = { query: false, approve: false }
            const { loading = defLoading } = $active
            this.loading = loading
            return
          }

          // 默认设置
          this.loading = {
            query: false,
            approve: false
          }
          this.readyActiveKey = ''
        }
      })
    },
    // 查询已办数据
    doQueryReadySource (force) {
      const mark = this.readyActiveKey
      const $active = this.$refs[mark][0] || {}
      $active.doQuerySource && $active.doQuerySource()
    },
    // 查询待办统计
    doQueryCount (force) {
      switch (this.type) {
        case 'todo': {
          return this.doQueryTodoCount(force)
        }
        case 'ready': {
          return this.doQueryReadyCount(force)
        }
      }
    },
    // 查询待办数据
    doQuerySource (force) {
      switch (this.type) {
        case 'todo': {
          return this.doQueryTodoSource(force)
        }
        case 'ready': {
          return this.doQueryReadySource(force)
        }
      }
    },
    // tab页切换事件
    doChangeTab () {
      const mark = this.todoActiveKey
      const $active = this.$refs[mark] || {}
      const defLoading = { query: false, approve: false }
      const { loading = defLoading } = $active
      this.loading = loading
      console.log(this.todoActiveKey)
      this.doCleanAll()
      this.doQueryAll(true)
    },
    // 清理待办缓存
    doCleanAll () {
      for (const mark of TAB_PANES) {
        if (this.$refs[mark] && this.$refs[mark].doCleanAll) {
          this.$refs[mark].doCleanAll()
        }
      }
    },
    // 查询待办事项
    doQueryAll (force) {
      if (!this.loading.query) {
        this.loading.query = true
        this.doQueryCount(force).then(() => {
          this.doQuerySource(force)
        })
      }
    },
    // 打开审批弹框
    doDrawerOpen () {
      const mark = this.todoActiveKey
      const $active = this.$refs[mark][0] || {}
      $active.doDrawerOpen && $active.doDrawerOpen()
    },
    // 选择供应商
    doSelectVendor () {
      const mark = this.todoActiveKey
      const $active = this.$refs[mark][0] || {}
      $active.doSelectVendor && $active.doSelectVendor()
    },
    // 关闭审批弹框
    doDrawerClose () {
      const mark = this.todoActiveKey
      const $active = this.$refs[mark] || {}
      $active.doDrawerClose && $active.doDrawerClose()
    },
    dataChanges (values) {
      this.readyParam.todoCardStartTime = values[0] ? values[0].format('YYYY-MM-DD') : null
      this.readyParam.todoCardEndTime = values[1] ? values[1].format('YYYY-MM-DD') : null
      this.doQueryAll(true)
    }
  }
}
</script>

<style lang="less" scoped>
@keyframes text {
  from {
    transform: translate(-50%, -50%);
  }
  to {
    transform: translate(-50%, -10%);
  }
}
@keyframes count {
  from {
    font-size: 16px;
    transform: translate(-50%, -50%);
    opacity: 0;
  }
  to {
    font-size: 24px;
    transform: translate(-50%, -90%);
    opacity: 1;
  }
}
.section-header {
  display: flex;
  width: 100%;
  height: 80px;
  padding: 5px 5px 0 10px;
  align-items: center;
  & > .titles {
    display: flex;
    justify-content: flex-start;
    align-content: center;
    align-items: center;
    flex: 1 1 auto;
    width: 60%;
    font-size: 16px;
    & > .title {
      position: relative;
      border-right: 1px solid #E3E5F1;
      width: 130px;
      height: 42px;
      color: #c9c9c9;
      text-align: center;
      cursor: pointer;
      font-size: 14px;
      .text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      .count {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #4557FF;
        font-size: 24px;
        font-weight: 700;
        opacity: 0;
      }
      &.active {
        color: #767676;
        & > .text {
          animation-name: text;
          animation-duration: .5s;
          animation-iteration-count: 1;
          animation-fill-mode: forwards;
        }
        & > .count {
          animation-name: count;
          animation-duration: .5s;
          animation-iteration-count: 1;
          animation-fill-mode: forwards;
        }
      }
    }
  }
  & > .buttons {
    display: flex;
    justify-content: center;
    align-content: center;
    align-items: center;
    border-left: 1px solid #E3E5F1;
    padding-left: 15px;
    height: 42px;
  }
  & > .tip {
    display: flex;
    justify-content: center;
    align-content: center;
    align-items: center;
    height: 42px;
    margin-right:8px;
  }
  .pointStyle{
    font-size:5px;
    margin-left:3px;
    margin-right:3px;
  }
}
.section-content {
  .todo-container {
    width: 100%;
    height: auto;
    .todo-empty {
      width: 100%;
      height: 100px;
      padding: 15px 20px;
      & > .empty-text {
        color: #909399;
        font-size: 15px;
      }
    }
  }
  .ready-container {
    width: 100%;
    height: auto;
    .ready-empty {
      width: 100%;
      height: 100px;
      padding: 15px 20px;
      & > .empty-text {
        color: #909399;
        font-size: 15px;
      }
    }
  }
  .tabs-container {
    .tab-pane-header {
      position: relative;
      .count {
        min-width: 18px;
        height: 18px;
        padding: 0 4px;
        font-size: 10px;
        line-height: 18px;
        font-weight: 400;
        text-align: center;
        color: #ffffff;
        border-radius: 9px;
        background-color: #f34d4d;
        transform: translateX(68%) scale(0.8);
        position: absolute;
        top: -3px;
        right: 0;
      }
    }
  }
}
</style>
