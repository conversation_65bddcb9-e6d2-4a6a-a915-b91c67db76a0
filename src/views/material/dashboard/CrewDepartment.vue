<template>
  <section class="m-art" style="height: 1500px;">
    <!-- <div class="m-commonheader">
    </div> -->
    <!-- 公共组件 -->
    <div class="bgc image">
      <div class="m-btn">
        <!-- <a-dropdown :getPopupContainer="triggerNode => triggerNode.parentNode" overlayClassName="rangePickerIceGai">
          <a-menu slot="overlay" @click="handleEditClick">
            <a-menu-item key="预览模式"> <a-icon type="user" />预览模式</a-menu-item>
            <a-menu-item key="拖拽模式"> <a-icon type="user" />拖拽模式</a-menu-item>
          </a-menu>
          <a-button style="margin-left: 8px"> {{ editStatus }}(测试) <a-icon type="down" /> </a-button>
        </a-dropdown> -->
        <a-button v-if="false" @click="openDrawer"><a-icon type="user" />编辑模式</a-button>
      </div>
      <div class="m-box">
        <SlickList
          :lockToContainerEdges="true"
          :pressDelay="300"
          v-model="checkTopList"
          axis="x"
          class="top-box"
        >
          <SlickItem
            v-for="(item, index) in checkTopList"
            :index="index"
            :disabled="editStatus === '预览模式'"
            :key="index"
            class="cardSlick"
          >
            <component :is="item" :disabled="editStatus === '拖拽模式'" :styleName="styleName" />
          </SlickItem>
        </SlickList>
        <SlickList
          :lockToContainerEdges="true"
          :pressDelay="300"
          v-model="checkmidList"
          axis="xy"
          class="mid-box"
        >
          <SlickItem
            v-for="(item, index) in checkmidList"
            :index="index"
            :disabled="editStatus === '预览模式'"
            :key="index"
            class="cardSlick"
          >
            <component :is="item" :disabled="editStatus === '拖拽模式'" :styleName="styleName" />
          </SlickItem>
        </SlickList>
        <!-- <div class="top-box">
          <CrewPriceDeviation :styleName="styleName"/>
          <CrewTemporaryPurchase :styleName="styleName"/>
          <CrewNonPurchase :styleName="styleName"/>
          <CrewWarningInformation :styleName="styleName"/>
        </div> -->
        <!-- <div class="mid-box">
          <CrewSplitPurchasing :styleName="styleName"/>
          <CrewSingleSource :styleName="styleName"/>
        </div>
        <div class="bottom-box">
          <CrewSupplierSensitivity :styleName="styleName"/>
          <CrewAbnormalSettlement :styleName="styleName"/>
        </div> -->
      </div>
    </div>
    <!-- <CrewEditDrawer
      ref="CrewEditDrawer"
      :smallBox="checkTopList"
      :largeBox="checkmidList"
    /> -->
  </section>
</template>
<script>
import CrewEditDrawer from './modules/CrewEdit/CrewEditDrawer'
import * as components from './modules/CrewDepartment/CrewComponents.js'
import '@/style/baseColor.less'
import { SlickList, SlickItem } from 'vue-slicksort'
import { mixin } from '@/utils/mixin'
export default {
  name: 'CrewDepartment',
  components: {
    ...components,
    SlickList,
    SlickItem,
    CrewEditDrawer
  },
  mixins: [mixin],
  data () {
    return {
      styleName: 'themea',
      imgUrl: require('@/assets/screen/head.png'),
      checkTopList: ['CrewPriceDeviation', 'CrewTemporaryPurchase', 'CrewNonPurchase', 'CrewWarningInformation'],
      checkmidList: ['CrewSplitPurchasing', 'CrewSingleSource', 'CrewSupplierSensitivity', 'CrewAbnormalSettlement'],
      editStatus: '预览模式'
    }
  },
  computed: {
  },
  mounted () {
    console.log(components)
    // document.getElementById('app').className = this.navTheme === 'light' ? 'themea' : 'themeb'
  },
  watch: {
    navTheme: {
      immediate: true,
      handler (val) {
        const value = val === 'light' ? 'a' : 'b'
        this.handleMenuClick({ key: value })
      }
    }
  },
  methods: {
    handleMenuClick (command) {
      document.getElementById('app').className = 'theme' + command.key
      this.styleName = 'theme' + command.key
    },
    handleEditClick (command) {
      this.editStatus = command.key
    },
    openDrawer () {
      this.$refs.CrewEditDrawer.showDrawer()
    }
  }
}
</script>
<style lang="less" scoped>
@import "../../../style/baseColor.less";
.bgc {
  background: url(./pic/head.png);
}
.m-box {
  /* background-color: #fffcaa; */
  width: 100%;
  height: 100%;
  .top-box {
    display: flex;
    justify-content: space-between;
    /* background-color: #ffaaaa; */
    width: 100%;
    height: 200px;
    margin-bottom: 15px;
    .cardSlick {
      width: 23.5%;
      height: 100%
    }
  }
  .mid-box {
    display: flex;
    justify-content: space-between;
    /* background-color: #9af7b3; */
    width: 100%;
    height: 550px;
    margin-bottom: 15px;
    flex-wrap: wrap;
    .cardSlick {
      width: 49%;
      height: 100%;
      margin-bottom: 15px;
    }
  }
  .bottom-box {
    display: flex;
    justify-content: space-between;
    /* background-color: #9e87e3; */
    width: 100%;
    height: 550px;
    flex-wrap: wrap;
  }
}
</style>
