<template>
  <section class="m-art" style="height: 100%;">
    <!-- 公共组件 -->
    <div class="bgc image">
      <div class="m-btn">
        <a-button @click="openDrawer"><a-icon type="user" />编辑模式</a-button>
      </div>
      <div class="m-box">
        <div
          v-for="(info, index) in groupList"
          :key="index"
          :class="[info.className]"
        >
          <div
            class="cardSlick"
            v-for="(item, ins) in info.rightList"
            :key="ins"
          >
            <component :is="item.boxName" :disabled="editStatus === '拖拽模式'" :styleName="styleName" />
          </div>
        </div>
      </div>
    </div>
    <CrewEditDrawer
      ref="CrewEditDrawer"
      origin="SB"
      :currentList="groupList"
      :mainList="checkTopList"
      :mainLargeList="checkmidList"
      @submit="onSubmit"
    />
  </section>
</template>
<script>
import * as components from './modules/EquipmentDepartment/CrewComponents.js'
import CrewEditDrawer from './modules/CrewEdit/CrewEditDrawer'
import { SlickList, SlickItem } from 'vue-slicksort'
import { mixin } from '@/utils/mixin'
import { mapGetters } from 'vuex'
import '@/style/baseColor.less'
export default {
  name: 'EquipmentDepartment',
  components: {
    ...components,
    SlickList,
    SlickItem,
    CrewEditDrawer
  },
  mixins: [mixin],
  data () {
    return {
      styleName: 'themea',
      imgUrl: require('@/assets/screen/head.png'),
      checkTopList: [{
        boxName: 'CrewWorkorderStatus',
        boxNum: 0
      }],
      checkmidList: [{
        boxName: 'CrewEquipmentStatus',
        boxNum: 0
      }, {
        boxName: 'CrewWorkorderBySearch',
        boxNum: 0
      }],
      groupList: this.getList(),
      editStatus: '预览模式'
    }
  },
  computed: {
    ...mapGetters(['todoCockpitGroup'])
  },
  mounted () {
  },
  watch: {
    navTheme: {
      immediate: true,
      handler (val) {
        const value = val === 'light' ? 'a' : 'b'
        this.handleMenuClick({ key: value })
      }
    }
  },
  methods: {
    getList () {
      return this.$nextTick(() => {
        const obj = this.todoCockpitGroup.find(item => item.cockpitType === 'SB')
        if (!obj) {
          return []
        }
        this.groupList = obj.outBoxList
        return obj
      })
    },
    handleMenuClick (command) {
      document.getElementById('app').className = 'theme' + command.key
      this.styleName = 'theme' + command.key
    },
    handleEditClick (command) {
      this.editStatus = command.key
    },
    openDrawer () {
      this.$refs.CrewEditDrawer.showDrawer()
    },
    onSubmit (list) {
      this.groupList = list
    }
  }
}
</script>
<style lang="less" scoped>
@import "../../../style/baseColor.less";
.bgc {
  background: url(./pic/equipmentTitle.png);
}
.m-box {
  width: 100%;
  height: 100%;
  .small-area {
    display: flex;
    width: 100%;
    height: auto;
    flex-wrap: wrap;
    .cardSlick {
      width: 24%;
      height: 100%;
      margin: .5%;
    }
  }
  .large-area {
    display: flex;
    width: 100%;
    height: auto;
    flex-wrap: wrap;
    .cardSlick {
      width: 49%;
      display: flex;
      height: 100%;
      margin: .5%;
    }
  }
}
</style>
