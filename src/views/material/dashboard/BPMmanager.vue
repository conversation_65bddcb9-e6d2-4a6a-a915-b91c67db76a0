<template>
  <div class="page">
    <iframe
      frameborder="0"
      :src="activityUrl"
      width="100%"
      :height="this.pageHeight"
      scrolling="yes"
      id="iframe"
    />
  </div>
</template>
<script>
import * as baseApi from '@/api/material/base'

import Vue from 'vue'
import { ORG_ID } from '@/store/mutation-types'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
// import { requestBuilder } from '@/utils/util'
export default {
  name: 'BPMmanager',
  data () {
    return {
      USER_ORG_ID,
      activityUrl: '',
      pageHeight: '',
      ccToken: '',
      orgNo: ''
    }
  },
  mounted () {
    this.getAdmin()
    this.pageHeight = document.body.clientHeight
    // this.activityUrl = 'http://***************:8085/WF/Port.htm'
  },
  methods: {
    async getAdmin () {
      const res = await baseApi.getCcAdminToken({ orgId: USER_ORG_ID })
      if (res.code === '0000') {
        this.ccToken = res.message
        this.orgNo = 'iam-' + USER_ORG_ID
        const url = process.env.VUE_APP_ENV === 'development' || process.env.VUE_APP_ENV === 'test' ? '***************:8085' : 'wf.nbport.com.cn'
        this.activityUrl = `http://${url}/WF/Port.htm?Token=${this.ccToken}&OrgNo=${this.orgNo}&DoWhat=Flows`
      }
    }
  }
}
</script>
<style lang="less" scoped>
  .page {
    overflow-x: hidden;
  }
</style>
