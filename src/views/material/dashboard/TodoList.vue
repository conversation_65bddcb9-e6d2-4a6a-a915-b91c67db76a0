<template>
  <section class="todo-list">
    <!-- 图表模块 -->
    <todo-card-group @close="close"/>
    <!-- 驾驶舱 -->
    <TodoCardDeclarationPrompt ref="TodoCardDeclarationPrompt"/>
    <!-- 待办模块 -->
    <todo-approve-group ref="todoApprove"/>
  </section>
</template>

<script>
import {
  TodoCardGroup,
  TodoApproveGroup,
  TodoCardDeclarationPrompt
} from './modules'

export default {
  name: 'TodoList',
  components: {
    TodoCardGroup,
    TodoApproveGroup,
    TodoCardDeclarationPrompt
  },
  methods: {
    close () {
      this.$refs.todoApprove.doQueryAll()
    }
  }
}
</script>

<style lang="less" scoped>
.todo-list {
  padding: 18px 18px 0;
}
</style>
