<template>
  <section>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="20">
            <a-col
              :xl="6"
              :md="4"
              :sm="24"
            >
              <a-form-item label="员工编码">
                <a-input
                  @pressEnter="doSearch"
                  v-model="queryParam.personNum"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <a-col
              :xl="6"
              :md="4"
              :sm="24"
            >
              <a-form-item label="员工姓名">
                <a-input
                  @pressEnter="doSearch"
                  v-model="queryParam.personName"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <a-col
              :xl="6"
              :md="4"
              :sm="24"
            >
              <a-form-item label="可用状态">
                <a-select v-model="queryParam.activity" @change="doSearch">
                  <a-select-option value>全部</a-select-option>
                  <a-select-option value="Y">可用</a-select-option>
                  <a-select-option value="N">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :xl="6"
              :md="4"
              :sm="24"
            >
              <a-form-item
                label="是否维修人员"
                v-if="IS_JYS">
                <a-select
                  v-model="queryParam.isRepairPersonnel"
                  @change="doSearch"
                  allowClear>
                  <a-select-option value="Y">是</a-select-option>
                  <a-select-option value="N">否</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <a-button
          type="primary"
          icon="search"
          @click="doSearch"
        >查询</a-button>
        <a-button
          v-action:add
          type="primary"
          icon="plus"
          @click="openDrawer('insert')"
        >新增</a-button>
        <a-button
          v-action:edit
          type="primary"
          icon="edit"
          @click="doEdit"
        >修改</a-button>
        <a-button
          v-action:del
          type="danger"
          icon="delete"
          @click="doPersonDel('')"
        >删除</a-button>
      </div>
    </a-card>
    <a-card :bordered="true">
      <a-row :gutter="10">
        <a-col
          :md="6"
          :sm="12"
        >
          <a-card title="行政组织列表">
            <a-button
              shape="circle"
              icon="reload"
              size="small"
              slot="extra"
              @click="initTree"
            />
            <a-tree
              :loadData="onLoadData"
              :loadedKeys="loadedKeys"
              :treeData="treeData"
              @select="onSelect"
            >
              <template
                slot="title"
                slot-scope="{title}"
              >
                <ellipsis
                  :length="24"
                  tooltip
                >{{ title }}</ellipsis>
              </template>
            </a-tree>
          </a-card>
        </a-col>
        <a-col
          :md="18"
          :sm="12"
        >
          <a-card title="明细信息">
            <s-table
              ref="table"
              size="small"
              rowKey="key"
              :columns="columns"
              :data="loadData"
              :alert="options.alert"
              :customRow="rowClick"
              :rowSelection="options.rowSelection"
              :pageSizeOptions="pageSizeOptions"
              :pageSize="defaultPageSize"
              :showPagination="true"
            >
              <!-- 表格表自定义显示转换 -->
              <span
                slot="serial"
                slot-scope="text, record, index"
              >{{ index + 1 }}</span>
              <span
                slot="description"
                slot-scope="text"
              >
                <ellipsis
                  :length="8"
                  tooltip
                >{{ text }}</ellipsis>
              </span>
              <span
                slot="status"
                slot-scope="text"
              >{{ (text==='Y')?'启用':'禁用' }}</span>
              <span
                slot="action"
                slot-scope="text, record"
              >
                <template>
                  <a-button
                    v-action:del
                    type="link"
                    href="javascript: void(0)"
                    @click="doPersonDel(record)"
                    style="margin-right:5px"
                  >删除</a-button>
                  <a-button
                    type="link"
                    href="javascript: void(0)"
                    @click.stop="openUploadDrawer(record)"
                    style="margin-right:5px"
                  >附件</a-button>
                </template>
              </span>
            </s-table>
          </a-card>
        </a-col>
      </a-row>
      <!-- 侧边滑动栏 -->
      <template>
        <a-drawer
          :title="subTitle"
          :width="360"
          :visible="visible"
          :mask="false"
          :bodyStyle="{paddingBottom: '80px'}"
          @close="hiddenDrawer"
        >
          <a-spin :spinning="confirmLoading">
            <a-form
              :form="form"
              layout="vertical"
              hideRequiredMark
            >
              <a-form-item
                v-show="false"
                label="personSysId:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input v-decorator="['personSysId', {rules: [{required: false}]}]" />
              </a-form-item>
              <a-form-item
                label="员工编码:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['personNum', {rules: [{required: true, min: 1, message: '请输入至少一个字符！'}]}]"
                />
              </a-form-item>
              <a-form-item
                label="姓名:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['personName', {rules: [{required: true, min: 1, message: '请输入至少一个字符！'}]}]"
                />
              </a-form-item>
              <a-form-item
                label="性别:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select v-decorator="['gender']">
                  <a-select-option value="男">男</a-select-option></a-select-option>
                  <a-select-option value="女">女</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item
                label="年龄:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input-number
                  :min="16"
                  size="small"
                  :step="1"
                  v-decorator="['age', {rules: [{required: false}]}]"
                />
              </a-form-item>
              <a-form-item
                label="手机号码:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['mobilePhone', {rules: [{required: true, len: 11, max:11, message: '请输入11位手机号码！'}]}]"
                />
              </a-form-item>
              <a-form-item
                label="身份证:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['idCard', {rules: [{required: false, min:18, len: 18, message: '请输入18位身份证号码!'}]}]"
                />
              </a-form-item>
              <a-form-item
                label="籍贯:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['homeTown', {rules: [{required: false}]}]"
                />
              </a-form-item>
              <a-form-item
                label="住址:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['address', {rules: [{required: false}]}]"
                />
              </a-form-item>
              <a-form-item
                label="职位:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['postName', {rules: [{required: false, min: 2, message: '请输入至少二个字符！'}]}]"
                />
              </a-form-item>
              <a-form-item label="所属组织:">
                <a-select
                  v-decorator="['orgId', { rules: [{ type: 'string', required: true, message: '请选择组织' }]}]"
                  allowClear
                  @change="handleOrgChange"
                >
                  <a-select-option
                    v-for="(item, index) in orgIds"
                    :value="item.value"
                    :key="index"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="所属部门:">
                <a-select
                  v-decorator="['departmentSysId', { rules: [{ type: 'string', required: true, message: '请选择部门' }] }]"
                  allowClear
                >
                  <a-select-option
                    v-for="(item, index) in departmentSysIds"
                    :value="item.value"
                    :key="index"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item
                v-if="IS_BYJ_ENV"
                label="组别代码:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select
                  placeholder="选择组别代码"
                  :value="zbdms"
                  style="width: 100%"
                  v-decorator="['zbdm', {rules: [{required: false }]}]"
                  allowClear
                  showSearch
                  optionFilterProp="label"
                >
                  <a-select-option
                    v-for="(item, index) in zbdms"
                    :key="index"
                    :value="item.value"
                    :label="item.label"
                    :title="item.label"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item
                label="是否维修人员:"
                :labelCol="labelCol"
                v-if="IS_JYS"
              >
                <a-select
                  v-decorator="['isRepairPersonnel', {rules: [{required: true, message: '请选择'}]}]"
                  @change="isRepairPersonnelChange">
                  <a-select-option value="Y">是</a-select-option>
                  <a-select-option value="N">否</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item
                label="工种:"
                v-if="this.isRepairPersonnel === 'Y'"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['typeOfWork', {rules: [{required: false}]}]"
                />
              </a-form-item>
              <a-form-item
                label="可用状态:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select v-decorator="['activity', {rules: [{required: true, message: '请选择状态'}]}]">
                  <a-select-option value="Y">启用</a-select-option>
                  <a-select-option value="N">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-form>
          </a-spin>
          <div
            :style="{
              position: 'absolute',
              right: 0,
              bottom: 0,
              width: '100%',
              borderTop: '1px solid #e9e9e9',
              padding: '10px 16px',
              background: '#fff',
              textAlign: 'right',
              zIndex: 1,
            }"
          >
            <a-button
              style="float: left;"
              @click.stop="openDrawer2"
            >紧急联系人</a-button>
            <a-button
              :style="{marginRight: '8px'}"
              @click="hiddenDrawer"
            >取消</a-button>
            <a-button
              @click="doSubmit"
              type="primary"
            >保存</a-button>
          </div>
        </a-drawer>
      </template>
      <!-- 附件 - 弹框 -->
      <a-drawer
        width="360"
        title="附件"
        :mask="true"
        :maskClosable="false"
        :getContainer="false"
        :visible="uploadVisible"
        @close="hiddenUploadDrawer"
      >
        <div class="drawer-content">
          <a-spin :spinning="fileListLoading">
            <a-upload
              class="drawer-content-upload"
              :multiple="true"
              :data="uploadData"
              :action="uploadAction"
              :headers="uploadHeaders"
              :file-list="uploadFileList"
              :beforeUpload="uploadBeforeUpload"
              :remove="uploadRemove"
              @change="uploadChange"
              @preview="uploadPreview"
            >
              <a-button
              >
                <a-icon type="upload" />上传
              </a-button>
            </a-upload>
          </a-spin>
        </div>
        <div class="drawer-footer">
          <div class="footer-fixed">
            <a-button @click="hiddenUploadDrawer">返回</a-button>
          </div>
        </div>
      </a-drawer>
      <!--紧急联系人-->
      <a-modal
        title="紧急联系人信息"
        v-model="visible2"
        cancelText="关闭"
        width="40%"
        @cancel="visible2 = false"
      >
        <div >
          <div>
            <!-- <a-row
              :gutter="6"
            > -->
            <!-- <a-col :sm="6">
                <span style="padding-left: 5px">联系人</span></span>
              </a-col>
              <a-col :sm="6">
                <span style="padding-left: 5px">联系方式</span>
              </a-col> -->
            <a-button
              type="link"
              class="button"
              @click="emergencyContactInfoAdd"
            >添加</a-button>
            <!-- </a-row> -->
          </div>
          <div>
            <div
              v-for="(item, index) of emergencyContactInfo"
              :key="index"
            >
              <a-row
                :gutter="6"
              >
                <a-col :sm="3">
                  <span style="padding-left: 5px">联系人:</span></span>
                </a-col>
                <a-col :sm="7">
                  <a-input
                    v-model="item.emergencyContactName"
                    placeholder="请输入联系人"

                  />
                </a-col>
                <a-col :sm="4">
                  <span style="padding-left: 5px">联系方式:</span>
                </a-col>
                <a-col :sm="7">
                  <a-input
                    v-model="item.emergencyContactPhone"
                    placeholder="请输入联系方式"
                  />
                </a-col>
                <a-button
                  type="link"
                  v-if="emergencyContactInfo.length > 1"
                  style="float: right; color: #f34d4d"
                  @click="emergencyContactInfoDel(item)"
                >移除</a-button>
              </a-row>
            </div>
          </div>
        </div>
        <div class="drawer-footer">
          <div class="footer-fixed">
            <a-button @click="visible2 = false">取消</a-button>
            <a-button
              type="primary"
              @click="doSubmitSave"
            >保存</a-button>
          </div>
        </div>
        </a-drawer>
      </a-modal>
    </a-card>
  </section>
</template>

<script>
// import moment from 'moment'
import { STable, Ellipsis } from '@/components'
// import { getPersonInfo, modifyPersonInfo, getOrgTreeInfo } from '@/api/material/base'
import { requestBuilder } from '@/utils/util'
import * as baseApi from '@/api/material/base'
import { ORG_ID, ACCESS_TOKEN } from '@/store/mutation-types'
import * as uploadApi from '@/api/material/upload'
import * as groupCodeApi from '@/api/material/groupCode'
import Vue from 'vue'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_JYS = USER_ORG_ID === '1.100.117'
const IS_BYJ_ENV = USER_ORG_ID === '1.100.101'
// 上传附件允许类型
const uploadAccept = [
  // Excel
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel',
  // WORD
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
  'application/vnd.ms-word.document.macroEnabled.12',
  'application/vnd.ms-word.template.macroEnabled.12',
  'application/msword',
  // PDF
  'application/pdf',
  // IMG
  'image/png',
  'image/jpeg',
  'image/bmp',
  'image/gif'
]
export default {
  name: 'Person',
  components: {
    STable,
    Ellipsis
  },
  data () {
    return {
      IS_JYS,
      IS_BYJ_ENV,
      isRepairPersonnel: 'N',
      // 分页参数
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20'],
      // 加载树形根节点
      treeData: [{ title: '组织管理', key: '0', isLeaf: false }],
      mdl: {},
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {
        activity: 'Y',
        isRepairPersonnel: '',
        orgId: USER_ORG_ID,
        departmentSysId: ''
      },
      // 树节点key
      treeNodeId: {},
      // 树节点信息
      loadedKeys: [],
      // 组织下拉框
      orgIds: [],
      // 部门下拉框
      departmentSysIds: [],
      // 紧急联系人信息
      emergencyContactInfo: [],
      loading: false,
      // 员工信息表头定义
      columns: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' }
        },
        {
          title: '员工姓名',
          dataIndex: 'personName',
          scopedSlots: { customRender: 'description' }
        },
        {
          title: '手机号',
          dataIndex: 'mobilePhone',
          scopedSlots: { customRender: 'description' }
        },
        {
          title: '职位',
          dataIndex: 'postName'
        },
        {
          title: '可用状态',
          dataIndex: 'activity',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: '150px',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 加载员工信息数据
      loadData: parameter => {
        const param = requestBuilder('', Object.assign(this.queryParam), parameter.pageNo, parameter.pageSize)
        return baseApi.getPersonInfo(param).then(res => {
          if (res.code === '0000') {
            console.log(res)
            return res.result
          } else {
            this.$message.error(res.message)
          }
        })
      },
      // 行选择参数
      options: {
        alert: {
          show: false,
          clear: () => {
            this.selectedRowKeys = []
          }
        },
        rowSelection: {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        }
      },
      selectedRowKeys: [],
      selectedRows: [],
      // 滑动抽屉参数
      form: this.$form.createForm(this),
      confirmLoading: false, // 加载图标是否出现
      selectedItems: [],
      visible: false,
      // 紧急联系人
      visible2: false,
      subTitle: '员工新建',
      personSysId: '',
      temppass: '',
      actionFlag: '',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      rowClick: record => ({
        on: {
          // 点击事件
          dblclick: () => {
            this.isRepairPersonnel = record.isRepairPersonnel
            this.openDrawer('edit', record)
            this.initOptions()
          }
        }
      }),

      // 上传参数
      uploadData: {},
      uploadAccept: uploadAccept.join(','),
      uploadHeaders: {
        token: Vue.ls.get(ACCESS_TOKEN)
      },
      uploadChange: options => {
        if (options.file && ['removed', 'error'].includes(options.file.status)) {
          this.uploadFileList = this.uploadFileList.filter(file => {
            return file.uid !== options.file.uid
          })
        }
        if (options.file && options.file.status === 'done') {
          if (options.file.response && options.file.response.code === '0000') {
            this.uploadFileList = this.uploadFileList.map(file => {
              if (file.uid === options.file.uid) {
                file.url = options.file.response.message
                file.uid = options.file.response.message
                file.status = options.file.status
              }
              return file
            })
          } else {
            this.uploadFileList = this.uploadFileList.filter(file => {
              return file.uid !== options.file.uid
            })
            this.$notification.error({
              message: '系统消息',
              description: options.file.response.message
            })
          }
        }
      },
      uploadPreview: file => {
        const baseUrl = process.env.VUE_APP_API_BASE_URL
        const keyId = this.uploadData.params.split('_')[1] || ''
        const businessName = this.uploadData.params.split('_')[0] || ''
        window.open(`${baseUrl}/file/fileDownload?keyId=${keyId}&businessName=${businessName}&fileName=${file.uid}`)
      },
      uploadBeforeUpload: file => {
        if (!uploadAccept.includes(file.type)) {
          this.$message.error('仅支持上传word、excel、图片等附件')
          return false
        }
        if (file.size / 1024 / 1024 > 50) {
          this.$message.error('上传附件大小不可超过50M')
          return false
        }
        this.uploadFileList.push(file)
      },
      uploadRemove: file => {
        return new Promise((resolve, reject) => {
          this.$confirm({
            title: '确认?',
            content: '是否删除该附件',
            onOk: () => {
              uploadApi
                .deleteFile({
                  keyId: this.uploadData.params.split('_')[1],
                  businessName: this.uploadData.params.split('_')[0],
                  fileName: file.uid
                })
                .then(res => {
                  if (res.code !== '0000') {
                    this.$notification.error({
                      message: '系统消息',
                      description: '删除附件失败！'
                    })
                    reject(new Error())
                  }
                  this.uploadFileList = this.uploadFileList.filter(item => {
                    return item.uid !== file.uid
                  })
                  resolve()
                })
            },
            onCancel () {
              reject(new Error())
            }
          })
        })
      },
      uploadFileList: [],
      uploadAction: process.env.VUE_APP_API_BASE_URL + '/file/fileUpload',
      uploadVisible: false,
      fileListLoading: false
    }
  },
  filters: {},
  created () {
    this.initOptions()
  },
  methods: {
    // 初始化树
    initTree () {
      this.$delete(this.treeData[0], 'children')
      // console.log(this.loadedKeys)
    },
    // 初始化下拉框
    initOptions () {
      baseApi.getCommboxById({ id: 'org' }).then(res => {
        this.orgIds = res.result
      })
      baseApi.getCommboxById({ id: 'dept', sqlParams: { isAll: '1' } }).then(res => {
        this.departmentSysIds = res.result
      })
      // 获取所有组别
      groupCodeApi.queryGroupCodeInfoAll(requestBuilder('', { activity: 'Y' })).then(res => {
        if (res.code === '0000') {
          this.zbdms = res.result.map(item => {
            return {
              label: item.zbdmNum + '[' + item.zbdmName + ']',
              value: item.zbdmNum
            }
          })
        }
      })
    },
    // 下拉框联动
    handleOrgChange (value) {
      this.form.setFieldsValue({
        departmentSysId: ''
      })
      this.departmentSysIds = []
      baseApi.getCommboxById({ id: 'dept', sqlParams: { byOrgId: '1', orgId: value } }).then(res => {
        if (res.code === '0000') {
          this.form.setFieldsValue({
            departmentSysId: res.result.length > 0 ? res.result[0].value : ''
          })
          this.departmentSysIds = res.result
        }
      })
    },
    // 加载组织树节点信息
    onLoadData (treeNode) {
      return new Promise(resolve => {
        if (treeNode.dataRef.children) {
          resolve()
          return
        }
        setTimeout(() => {
          // 获取当前节点的子节点信息
          this.param = {
            orgId: treeNode.dataRef.key
          }
          const param = requestBuilder('', Object.assign(this.param), '', '')
          baseApi.getOrgTreeInfo(param).then(res => {
            if (res.code === '0000') {
              console.log(res)
              for (const item of res.result) {
                item.scopedSlots = { title: 'title' }
              }
              treeNode.dataRef.children = res.result
              this.treeData = [...this.treeData]
              // console.log(treeNode.dataRef.children)
            } else {
              this.$message.error(res.message)
            }
          })
          resolve()
        }, 200)
      })
    },
    // 点击树节点后查询对应的员工信息
    onSelect (selectedKeys, info) {
      console.log(selectedKeys)
      // console.log(info)
      // 如果未选中，将参数清零
      if (selectedKeys.length === 0) {
        // console.log(selectedKeys.length)
        this.queryParam.orgId = ''
        this.queryParam.departmentSysId = ''
      } else {
        // 判断是组织节点还是部门节点
        if (info.node.isLeaf === false) {
          // 如果是根节点，则显示全部员工信息
          if (selectedKeys[0] === '0') {
            this.queryParam.orgId = ''
            this.queryParam.departmentSysId = ''
          } else {
            this.queryParam.orgId = selectedKeys[0]
            this.queryParam.departmentSysId = ''
          }
          // console.log(orgId)
        } else if (info.node.isLeaf === true) {
          this.queryParam.orgId = info.node.vcTreeNode.eventKey
          this.queryParam.departmentSysId = selectedKeys[0]
        }
      }
      console.log(this.queryParam)
      this.doSearch()
    },
    // 弹窗关闭
    hiddenDrawer () {
      this.emergencyContactInfo = []
      this.visible = false
    },
    openDrawer (action, record) {
      this.visible = true
      this.$nextTick(() => {
        if (action === 'insert') {
          this.isRepairPersonnel = 'N'
          this.form.resetFields()
          this.subTitle = '员工新增'
          this.actionFlag = 'insert'
          // 给表单下拉框赋值
          this.form.setFieldsValue({
            orgId: this.queryParam.orgId,
            departmentSysId: this.queryParam.departmentSysId
          })
        } else {
          this.subTitle = '员工修改'
          this.form.resetFields()
          this.form.setFieldsValue(record)
          this.actionFlag = 'update'
          this.personSysId = record.personSysId
          this.zbdm = record.zbdm
        }
      })
    },
    // 查询列表
    doSearch () {
      this.$refs.table.refresh(true)
      this.$refs.table.updateSelect([], [])
      this.$refs.table.rowSelection.onChange([], [])
    },
    // 修改列表
    doEdit () {
      if (this.selectedRows.length > 0) {
        this.isRepairPersonnel = this.selectedRows[0].isRepairPersonnel
        this.openDrawer('update', this.selectedRows[0])
      } else {
        this.$message.error('未选中任何记录')
      }
    },
    // 提交保存
    doSubmit () {
      const {
        form: { validateFields }
      } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        // 如果输入数据都合规
        if (!errors) {
          this.params = { ...values }
          this.params.emergencyContactList = this.emergencyContactInfo
          this.modifyPerson(this.params)
        } else {
          this.confirmLoading = false
        }
      })
    },
    // 保存员工信息
    modifyPerson (params) {
      baseApi
        .modifyPersonInfo(requestBuilder(this.actionFlag, params, '0', '0'))
        .then(res => {
          if (res.code === '0000') {
            this.doSearch()
            // this.initTree()
            this.$message.success(res.message)
            this.hiddenDrawer()
          } else if (res.code === '9999') {
            this.$message.error('发生异常' + res.message)
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.confirmLoading = false
        })
    },
    doPersonDel (record) {
      console.log(record)
      const personSysIds = []
      if (record !== '') {
        personSysIds.push(record.personSysId)
      } else {
        if (this.selectedRows.length > 0) {
          for (let i = 0; i < this.selectedRows.length; i++) {
            personSysIds.push(this.selectedRows[i].personSysId)
          }
        } else {
          this.$message.error('未选中任何记录')
          return
        }
      }
      this.$confirm({
        title: '确认?',
        content: '是否删除此记录',
        onOk: () => {
          this.handleDel(personSysIds.join(','))
        },
        onCancel () {}
      })
    },
    handleDel (value) {
      this.actionFlag = 'delete'
      const param = {
        personSysId: value
      }
      this.modifyPerson(param)
    },
    handleEdit (record) {},
    handleOk () {},
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    isRepairPersonnelChange (value) {
      this.isRepairPersonnel = value
    },
    // 附件相关的操作
    openUploadDrawer (record) {
      if (!this.fileListLoading) {
        uploadApi
          .listFiles({
            keyId: record.personSysId,
            businessName: 'person'
          })
          .then(res => {
            if (res.code !== '0000') {
              this.$notification.error({
                message: '系统消息',
                description: res.message || '获取附件列表失败！'
              })
            }
            for (const item of res.result) {
              this.uploadFileList.push({
                uid: item,
                url: item.replace(/_[^_]+$/, ''),
                name: item.replace(/_[^_]+$/, ''),
                status: 'done'
              })
            }
          })
          .finally(() => {
            this.fileListLoading = false
          })
        this.fileListLoading = true
      }
      this.uploadData = {
        params: 'person_' + record.personSysId
      }
      this.uploadFileList = []
      this.uploadVisible = true
    },
    // 关闭附件弹框
    hiddenUploadDrawer () {
      for (const file of this.uploadFileList) {
        if (file && file.status === 'uploading') {
          this.$message.error('有文件正在上传中..., 请稍后再试！')
          return
        }
      }
      this.uploadData = {}
      this.uploadFileList = []
      this.uploadVisible = false
    },
    // 紧急联系人 一系列方法
    // 增删
    emergencyContactInfoAdd () {
      this.emergencyContactInfo.push({
        personSysId: this.personSysId,
        emergencyContactName: '',
        emergencyContactPhone: ''
      })
    },
    emergencyContactInfoDel (record) {
      this.emergencyContactInfo.splice(0, this.emergencyContactInfo.length, ...this.emergencyContactInfo.filter(item => item !== record))
    },
    // 弹窗打开
    openDrawer2 () {
      if (this.actionFlag === 'insert') {
        if (this.emergencyContactInfo.length === 0) {
          this.emergencyContactInfoAdd()
        }
      } else if (this.actionFlag === 'update') {
        // 为零查询该人的紧急联系人
        if (this.emergencyContactInfo.length === 0) {
          baseApi.getEmergencyContact({ personSysId: this.personSysId }).then(
            res => {
              this.emergencyContactInfo = res.result
              if (this.emergencyContactInfo.length === 0) {
                this.emergencyContactInfoAdd()
              }
            }
          )
        }
      }
      this.visible2 = true
    },
    doSubmitSave () {
      for (const item of this.emergencyContactInfo) {
        item.emergencyContactName = item.emergencyContactName.trim()
        if (item.emergencyContactName.length <= 0) {
          this.$message.error('请正确完善联系人名称')
          return
        }
        item.emergencyContactPhone = item.emergencyContactPhone.trim()
        if (item.emergencyContactPhone.length !== 11 || !isFinite(item.emergencyContactPhone)) {
          this.$message.error('请正确完善联系方式(11位手机号)')
          return
        }
      }
      this.visible2 = false
    }
  }
}
</script>
<style scoped>
</style>
