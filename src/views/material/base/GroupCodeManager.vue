<template>
  <section>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="10">
            <a-col
              :xl="6"
              :md="6"
              :sm="24"
            >
              <a-form-item label="组别代码">
                <a-input
                  v-model="queryParam.zbdmNum"
                  @pressEnter="doSearch()"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <a-col
              :xl="6"
              :md="6"
              :sm="24"
            >
              <a-form-item label="组别名称">
                <a-input
                  v-model="queryParam.zbdmName"
                  @pressEnter="doSearch()"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <a-col
              :xl="6"
              :md="6"
              :sm="24"
            >
              <a-form-item label="状态">
                <a-select
                  v-model="queryParam.activity"
                  @pressEnter="doSearch()"
                  default-value="Y"
                  allowClear
                >
                  <a-select-option value="Y">启用</a-select-option>
                  <a-select-option value="N">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <a-button
          v-action:query
          type="primary"
          icon="search"
          @click="doSearch"
        >查询</a-button>
        <a-button
          v-action:add
          type="primary"
          icon="plus"
          @click="openDrawer('insert')"
        >新增</a-button>
        <a-button
          v-action:edit
          type="primary"
          icon="edit"
          @click="doEdit"
        >修改</a-button>
        <a-button
          v-action:del
          type="danger"
          icon="delete"
          @click="doBatchDel('')"
        >删除</a-button>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        size="default"
        rowKey="financialSysId"
        :columns="columns"
        :data="loadData"
        :alert="options.alert"
        :customRow="rowClick"
        :rowSelection="options.rowSelection"
        :pageSizeOptions="pageSizeOptions"
        :pageSize="defaultPageSize"
        :showPagination="true"
      >
        <!-- 表格表自定义显示转换 -->
        <span
          slot="serial"
          slot-scope="text, record, index"
        >{{ index + 1 }}</span>
        <span
          slot="description"
          slot-scope="text"
        >
          <ellipsis
            :length="8"
            tooltip
          >{{ text }}</ellipsis>
        </span>
        <span
          slot="status"
          slot-scope="text"
        >{{ (text==='Y')?'启用':'禁用' }}</span>
        <span
          slot="date"
          slot-scope="text"
        >{{ moment(text) }}</span>
        <!-- 表格表中操作栏 -->
        <span
          slot="action"
          slot-scope="text, record"
        >
          <template>
            <!-- <a-divider type="vertical" /> -->
            <a
              v-action:del
              @click="doBatchDel(record)"
            >删除</a>
          </template>
        </span>
      </s-table>
      <!-- 侧边滑动栏 -->
      <template>
        <a-drawer
          :title="subTitle"
          :width="360"
          :visible="visible"
          :mask="true"
          :bodyStyle="{paddingBottom: '80px'}"
          @close="visible = false"
        >
          <a-spin :spinning="confirmLoading">
            <a-form
              :form="form"
              layout="vertical"
              hideRequiredMark
            >
              <a-form-item
                label="组别名称:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"

              >
                <a-input
                  v-decorator="['zbdmName', {rules: [{required: true, message: '请输入组别名称！'}]}]"
                />
              </a-form-item>
              <a-form-item
                label="组别代码:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['zbdmNum', {rules:
                    IS_BYJ_ENV ? [{required: true, min: 9 , max: 9 ,message: '请输入9位组别代码！'}] : [{required: true, message: '请输入组别代码！'}]}]"
                />
              </a-form-item>
              <a-form-item
                v-if="IS_BYJ_ENV"
                label="领用部门(NBCT):"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['financialNBCTDept', {rules: [{required: IS_BYJ_ENV, message: '请输入领用部门！'}]}]"
                />
              </a-form-item>
              <a-form-item
                v-if="IS_BYJ_ENV"
                label="领用部门(涌和):"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['financialYHDept', {rules: [{required: IS_BYJ_ENV, message: '请输入领用部门！'}]}]"
                />
              </a-form-item>
              <a-form-item
                v-if="IS_BYJ_ENV"
                label="领用部门(通达):"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['financialTDDept', {rules: [{required: IS_BYJ_ENV, message: '请输入领用部门！'}]}]"
                />
              </a-form-item>
              <a-form-item
                v-if="IS_BYJ_ENV"
                label="成本科目主码:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['costSubNum', {rules: [{required: IS_BYJ_ENV, message: '请输入成本科目主码！'}]}]"
                />
              </a-form-item>
              <a-form-item
                label="组别状态:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select
                  v-decorator="['activity', {rules: [{required: true, defaultmessage: '请选择状态！'}]}]"
                >
                  <a-select-option value="Y">启用</a-select-option>
                  <a-select-option value="N">禁用</a-select-option>
                </a-select>
              </a-form-item>
              </a-form-item></a-form>
          </a-spin>
          <div
            :style="{
              position: 'absolute',
              right: 0,
              bottom: 0,
              width: '100%',
              borderTop: '1px solid #e9e9e9',
              padding: '10px 16px',
              background: '#fff',
              textAlign: 'right',
              zIndex: 1,
            }"
          >
            <a-button
              :style="{marginRight: '8px'}"
              @click="visible = false"
            >取消</a-button>
            <a-button
              @click="doSubmit"
              type="primary"
            >保存</a-button>
          </div>
        </a-drawer>
      </template>
    </a-card>
  </section>
</template>

<script>
import Vue from 'vue'
import { ORG_ID } from '@/store/mutation-types'
import { STable, Ellipsis } from '@/components'
import * as groupCodeApi from '@/api/material/groupCode'
import { requestBuilder } from '@/utils/util'
import moment from 'moment'

const IS_BYJ_ENV = Vue.ls.get(ORG_ID) === '1.100.101'
export default {
  name: 'GroupCodeManager',
  components: {
    STable,
    Ellipsis,
    moment
  },
  data () {
    return {
      IS_BYJ_ENV,
      // 分页参数
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20'],
      mdl: {},
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      // 表头定义
      columns: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' }
        },
        {
          title: '组别代码',
          dataIndex: 'zbdmNum',
          sorter: true
          // ,
          // sorter: (a, b) => {
          //   return a.zbdmNum.localeCompare(b.zbdmNum)
          // }
        },
        {
          title: '组别名称',
          dataIndex: 'zbdmName',
          ellipsis: true
        },
        ...(IS_BYJ_ENV ? [{
          title: '领用部门(NBCT)',
          dataIndex: 'financialNBCTDept'
        }] : []),
        ...(IS_BYJ_ENV ? [{
          title: '领用部门(涌和)',
          dataIndex: 'financialYHDept'
        }] : []),
        ...(IS_BYJ_ENV ? [{
          title: '领用部门(通达)',
          dataIndex: 'financialTDDept'
        }] : []),
        ...(IS_BYJ_ENV ? [{
          title: '成本科目主码',
          dataIndex: 'costSubNum'
        }] : []),
        {
          title: '状态',
          dataIndex: 'activity',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '创建时间',
          dataIndex: 'createDate',
          scopedSlots: { customRender: 'date' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: '80px',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const param = requestBuilder(
          '',
          Object.assign(this.queryParam),
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder
        )
        return groupCodeApi.queryGroupCodeInfo(param).then(res => {
          return res.result
        })
      },
      // 行选择参数
      options: {
        alert: {
          show: false,
          clear: () => {
            this.selectedRowKeys = []
          }
        },
        rowSelection: {
          // selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        }
      },
      selectedRowKeys: [],
      selectedRows: [],
      // 滑动抽屉参数
      form: this.$form.createForm(this),
      confirmLoading: false, // 加载图标是否出现
      visible: false,
      subTitle: '组别新增',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      rowClick: record => ({
        on: {
          // 点击事件
          dblclick: () => {
            this.openDrawer('edit', record)
          }
        }
      })
    }
  },
  filters: {},
  created () {
  },
  methods: {
    // 表格日期格式定义
    moment (text, pattern = 'YYYY-MM-DD') {
      return text ? this.$moment(text).format(pattern) : ''
    },
    doBatchDel (record) {
      console.log(record)
      const financialSysIds = []
      if (record !== '') {
        financialSysIds.push(record.financialSysId)
      } else {
        if (this.selectedRows.length > 0) {
          for (let i = 0; i < this.selectedRows.length; i++) {
            financialSysIds.push(this.selectedRows[i].financialSysId)
          }
        } else {
          this.$message.error('未选中任何记录')
          return
        }
      }
      this.$confirm({
        title: '确认?',
        content: '是否删除此记录',
        onOk: () => {
          this.handleDel(financialSysIds)
        },
        onCancel () {}
      })
    },
    handleDel (value) {
      this.actionFlag = 'delete'
      const param = {
        financialSysIdList: value
      }
      this.getResult(param)
    },
    doSearch () {
      this.$refs.table.refresh()
      this.visible = false
    },
    doEdit () {
      if (this.selectedRows.length > 0) {
        if (this.selectedRows.length > 1) {
          this.$message.warning('请勾选一条记录')
        } else {
          this.openDrawer('update', this.selectedRows[0])
        }
      } else {
        this.$message.error('未选中任何记录')
      }
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    openDrawer (action, record) {
      console.log('record', record)
      this.visible = true
      this.$nextTick(() => {
        if (action === 'insert') {
          this.form.resetFields()
          this.form.setFieldsValue({
            activity: 'Y'
          })
          this.subTitle = '组别新增'
          this.actionFlag = 'insert'
          this.updateGroupCode = {}
        } else {
          this.subTitle = '组别修改'
          this.form.setFieldsValue({
            costSubNum: record.costSubNum,
            financialNBCTDept: record.financialNBCTDept,
            financialTDDept: record.financialTDDept,
            financialYHDept: record.financialYHDept,
            zbdmNum: record.zbdmNum,
            zbdmName: record.zbdmName,
            activity: record.activity
          })
          this.updateGroupCode = {
            financialSysId: record.financialSysId,
            deptNum: record.deptNum,
            orgId: record.orgId
          }
          this.actionFlag = 'update'
        }
      })
    },
    // 保存数据
    doSubmit () {
      const {
        form: { validateFields }
      } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
      // 如果输入数据都合规
        if (!errors) {
          // this.params = { ...values }
          this.params = { ...values, ...this.updateGroupCode }
          console.log(this.params)
          if (this.actionFlag === 'insert') {
            this.getResult(this.params)
          } else {
            this.getResult(this.params)
          }
        } else {
          this.confirmLoading = false
        }
      })
    },
    getResult (params) {
      groupCodeApi.modifyGroupCode(requestBuilder(this.actionFlag, params, '0', '0'))
        .then(res => {
          if (res.code === '0000') {
            this.$message.success(res.message)
            this.doSearch()
          } else if (res.code === '9999') {
            this.$message.error(res.message)
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.confirmLoading = false
        })
    }
  }
}
</script>
<style scoped>
</style>
