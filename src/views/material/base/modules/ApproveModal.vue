<template>
  <div>
    <a-modal
      title="供应商审批"
      :visible="visible"
      :confirm-loading="confirmLoading"
      okText="提交审批"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-date-picker
        format="YYYY"
        mode="year"
        v-model="year"
        placeholder="请选择年份"
        :open="yearShowOneDrawer"
        style="width:100%"
        @openChange="openChangeOneDrawer"
        @panelChange="panelChangeOneDrawer"
      />
    </a-modal>
  </div>
</template>
<script>
import * as vendorAppraiseApi from '@/api/material/vendorAppraise'
import { requestBuilder } from '@/utils/util'
export default {
  name: 'ApproveModal',
  data () {
    return {
      yearShowOneDrawer: false,
      year: null,
      visible: false,
      confirmLoading: false
    }
  },
  methods: {
    showModal () {
      this.visible = true
      this.year = null
    },
    handleOk (e) {
      if (!this.year) {
        this.$message.error('请选择年份')
        return
      }
      this.confirmLoading = true
      vendorAppraiseApi.approveVendorAppraise(requestBuilder('', { year: this.year.format('YYYY') })).then(res => {
        if (res.code === '9999') {
          this.$message.error(res.message || '审批失败')
        } else if (res.code === '0000') {
          this.$message.success(res.message || '审批成功')
          this.handleCancel()
          this.$emit('search')
        }
      })
    },
    handleCancel (e) {
      this.visible = false
      this.confirmLoading = false
      this.year = null
    },
    // 弹出日历和关闭日历的回调
    openChangeOneDrawer (status) {
      if (status) {
        this.yearShowOneDrawer = true
      }
    },
    // 得到年份选择器的值
    panelChangeOneDrawer (value) {
      this.year = value
      this.yearShowOneDrawer = false
    }
  }
}
</script>
