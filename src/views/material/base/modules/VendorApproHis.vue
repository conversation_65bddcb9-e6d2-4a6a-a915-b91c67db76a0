<template>
  <div>
    <a-modal
      title="审批历史查询"
      :width="1000"
      :visible="visible"
      :confirm-loading="confirmLoading"
      :ok-button-props="{ props: { disabled: true } }"
      @ok="handleOk"
      @cancel="handleCancel">
      <a-spin :spinning="confirmLoading">
        <div class="formArea">
          <div class="formArea-content">
            <a-form-model layout="inline" :model="formInline" @submit="doSearch" @submit.native.prevent>
              <a-row>
                <a-col :span="8">
                  <a-form-model-item label="供应商名称">
                    <a-input v-model="formInline.vendorName" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item label="统计时间">
                    <a-date-picker
                      mode="year"
                      placeholder="请选择年份"
                      format="YYYY"
                      :open="yearShowOne"
                      v-model="formInline.statYear"
                      style="width:100%"
                      @openChange="openChangeOne"
                      @panelChange="panelChangeOne"
                      @change="changeDate()"
                    />
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-form-model>
          </div>
          <div class="formArea-btn">
            <a-button
              type="primary"
              icon="form"
              @click="reset()"
            >重置</a-button>
            <a-button
              type="primary"
              icon="search"
              @click="doSearch(true)"
            >查询</a-button>
          </div>
        </div>
        <s-table
          ref="table"
          rowKey="vendorScoreGeneralSysId"
          :columns="columns"
          :data="loadData"
          :customRow="rowClick"
          :pageSizeOptions="pageSizeOptions"
          :pageSize="defaultPageSize"
          :showPagination="true"
          :scroll="scroll"
        />
      </a-spin>
    </a-modal>
  </div>
</template>
<script>
import { STable, Ellipsis } from '@/components'
import * as vendorAppraiseApi from '@/api/material/vendorAppraise'
import { requestBuilder, takeTreeByKey } from '@/utils/util'
// import moment from 'moment'
export default {
  components: {
    STable,
    Ellipsis
  },
  data () {
    return {
      takeTreeByKey,
      yearShowOne: false,
      visible: false,
      confirmLoading: false,
      formInline: {
        vendorName: '',
        statYear: null
      },
      columns: [
        {
          title: '供应商名称',
          key: 'vendorName',
          width: 240,
          dataIndex: 'vendorName'
        },
        {
          title: '审核人',
          key: 'approMan',
          width: 120,
          dataIndex: 'approMan'
        },
        {
          title: '审批时间',
          key: 'approDate',
          width: 200,
          dataIndex: 'approDate'
        },
        {
          title: '审批意见',
          key: 'approMsg',
          width: 120,
          dataIndex: 'approMsg'
        },
        {
          title: '评分人数',
          key: 'count',
          width: 120,
          dataIndex: 'count'
        },
        {
          title: '信誉分',
          key: 'creditScore',
          width: 120,
          dataIndex: 'creditScore'
        },
        {
          title: '交货时间评分',
          key: 'deliverytimeScore',
          width: 120,
          dataIndex: 'deliverytimeScore'
        },
        {
          title: '价格评分',
          key: 'priceScore',
          width: 120,
          dataIndex: 'priceScore'
        },
        {
          title: '质量评分',
          key: 'qualityScore',
          width: 120,
          dataIndex: 'qualityScore'
        },
        {
          title: '服务评分',
          key: 'serviceScore',
          width: 120,
          dataIndex: 'serviceScore'
        },
        {
          title: '统计年份',
          key: 'statYear',
          width: 120,
          dataIndex: 'statYear'
        },
        {
          title: '下一步审批流程',
          key: 'statusName',
          width: 120,
          dataIndex: 'statusName'
        },
        {
          title: '综合评分',
          key: 'syntheticalScore',
          width: 120,
          dataIndex: 'syntheticalScore'
        }
      ],
      loadData: parameter => {
        const vendorparam = requestBuilder(
          '',
          {
            ...this.formInline,
            statYear: this.formInline.statYear ? this.formInline.statYear.format('YYYY') : ''
          },
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder
        )
        return vendorAppraiseApi.getApproHisPrint(vendorparam).then(res => {
          return res.result
        })
      },
      rowClick: () => {},
      pageSizeOptions: ['10', '20', '30'],
      defaultPageSize: 10,
      scroll: {
        x: 'max-content',
        y: 400,
        scrollToFirstRowOnChange: false
      }
    }
  },
  methods: {
    showModal () {
      this.visible = true
    },
    reset () {
      this.formInline = {
        vendorName: '',
        statYear: null
      }
    },
    doSearch (boo) {
      this.$refs.table.refresh(boo)
    },
    handleOk (e) {
      this.confirmLoading = true
      this.visible = false
      this.confirmLoading = false
    },
    handleCancel (e) {
      this.visible = false
    },
    // 弹出日历和关闭日历的回调
    openChangeOne (status) {
      if (status) {
        this.yearShowOne = true
      }
    },
    // 得到年份选择器的值
    panelChangeOne (value) {
      this.formInline.statYear = value
      this.yearShowOne = false
      this.doSearch()
    }
  }
}
</script>

<style lang="less" scoped>
  .formArea {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    .formArea-content {
      flex: 1;
    }
    .formArea-btn {
      width: auto;
    }
  }
</style>
