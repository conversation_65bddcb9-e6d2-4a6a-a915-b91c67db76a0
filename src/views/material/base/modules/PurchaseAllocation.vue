<template>
  <a-modal :width="640" :visible="visible" title="分配采购员" @ok="handleSubmit" @cancel="visible = false">
    <a-form @submit="handleSubmit" :form="form">
      <a-form-item
        label="前缀"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
      >
        <a-input v-decorator="['prefix', {rules:[{required: false, message: '请输入前缀'}]}]" />
      </a-form-item>
      <a-form-item
        label="财务类别"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
      >
        <a-input v-decorator="['financialType', {rules:[{required: false, message: '请输入财务类别'}]}]" />
      </a-form-item>
      <a-form-item
        label="采购员"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
      >
        <a-select
          v-decorator="['buyer', {rules:[{required: true, message: '请选择采购员'}]}]"
          optionFilterProp="label"
          showSearch
          allowClear
        >
          <a-select-option
            v-for="(item, index) in personSysIds"
            :value="item.value"
            :label="item.label"
            :key="index"
            >{{ item.label }}</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import * as materialApi from '@/api/material/material'
import { requestBuilder, takeTreeByKey } from '@/utils/util'
import * as baseApi from '@/api/material/base'
export default {
  name: 'TaskForm',
  data () {
    return {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 }
      },

      visible: false,
      form: this.$form.createForm(this),
      personSysIds: []
    }
  },
  created () {
    this.initApi()
  },
  methods: {
    initApi () {
      baseApi.getCommboxById({ id: 'personByRoleName', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.personSysIds = res.result
        }
      })
    },
    showModal () {
      this.visible = true
      this.$nextTick(() => {
        this.form.resetFields()
      })
    },
    handleSubmit () {
      this.visible = true
      this.form.validateFields((errors, values) => {
        if (!errors) {
          const params = values
          materialApi.updateBuyerByRoles(requestBuilder('', params)).then(res => {
            if (res.code === '0000') {
              this.$message.success(res.message || '提交成功')
              this.visible = false
              this.$emit('search')
            } else {
              this.$message.error(res.message || '系统异常')
            }
          })
        }
      })
    }
  }
}
</script>
