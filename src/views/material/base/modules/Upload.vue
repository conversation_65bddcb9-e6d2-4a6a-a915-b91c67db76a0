<template>
  <div class="clearfix" v-show="true">
    <a-upload
      list-type="picture-card"
      :disabled="disabled"
      :data="uploadData"
      :action="uploadAction"
      :headers="uploadHeaders"
      :file-list="uploadFileList"
      :beforeUpload="uploadBeforeUpload"
      :remove="uploadRemove"
      @change="uploadChange"
      @preview="handlePreviewPic"
    >
      <div v-if="fileList.length < 8 && !disabled">
        <a-icon type="plus" />
        <div class="ant-upload-text">
          上传
        </div>
      </div>
    </a-upload>
    <a-modal :visible="previewVisible" width="850px" :footer="null" @cancel="handleCancel()">
      <img alt="example" style="width: 100%" :src="previewImage" >
    </a-modal>
  </div>

</template>

<script>
import { ACCESS_TOKEN, ORG_ID, PERSON_ID } from '@/store/mutation-types'
import Vue from 'vue'
import * as fileApi from '@/api/system/file'
// 操作人 userPersonId
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)
const TOKEN = Vue.ls.get(ACCESS_TOKEN)
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const BASE_URL = process.env.VUE_APP_API_BASE_URL
const BASE_PREVIEW_URL = process.env.VUE_APP_PREVIEW_BASE_URL
// 上传附件允许类型
const uploadAccept = [
  // IMG
  'image/png',
  'image/jpeg',
  'image/bmp',
  'image/gif',
  'image/vnd.dwg',
  ''
]
function getBase64 (file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = error => reject(error)
  })
}
export default {
  data () {
    return {
      uploadData: {},
      actionFlag: '',
      uploadFileList: [],
      uploadHeaders: { token: TOKEN },
      uploadAction: BASE_URL + '/file/fileUpload',
      uploadAccept: uploadAccept.join(','),
      previewVisible: false,
      previewImage: '',
      fileList: [],
      uploadChange: options => {
        console.log(options, 'options')
        if (options.file && ['removed', 'error'].includes(options.file.status)) {
          this.uploadFileList = this.uploadFileList.filter(file => {
            return file.uid !== options.file.uid
          })
        }
        if (options.file && options.file.status === 'done') {
          if (options.file.response && options.file.response.code === '0000') {
            this.uploadFileList = this.uploadFileList.map(file => {
              if (file.uid === options.file.uid) {
                file.url = `${window.location.protocol}//${window.location.host}${BASE_PREVIEW_URL}/file/fileDownload?keyId=${this.keyId}&businessName=${this.businessName}&fileName=${options.file.response.message}`
                file.uid = options.file.response.message
                file.status = options.file.status
              }
              return file
            })
          } else {
            this.uploadFileList = this.uploadFileList.filter(file => {
              return file.uid !== options.file.uid
            })
            this.$notification.error({
              message: '系统消息',
              description: options.file.response.message
            })
          }
        }
      }
    }
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleCancel () {
      this.previewVisible = false
    },
    uploadBeforeUpload (file) {
      if (!uploadAccept.includes(file.type)) {
        this.$message.error('仅支持上传图片等附件')
        return false
      }
      if (file.size / 1024 / 1024 > 50) {
        this.$message.error('上传附件大小不可超过50M')
        return false
      }
      this.uploadFileList.push(file)
    },
    showModal (keyId, businessName) {
      this.keyId = keyId
      this.businessName = businessName
      fileApi
        .listFiles({
          keyId: this.keyId,
          businessName: this.businessName
        })
        .then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '获取附件列表失败！'
            })
          }
          // this.uploadFileList = []
          for (const file of res.result) {
            const item = file.minioFileName
            const typeList = item.split('.')
            this.uploadFileList.push({
              uid: item,
              url: `${window.location.protocol}//${window.location.host}${BASE_PREVIEW_URL}/file/fileDownload?keyId=${keyId}&businessName=${businessName}&fileName=${item}`,
              name: item.replace(/_[^_]+$/, ''),
              status: 'done',
              type: typeList[typeList.length - 1]
            })
          }
          console.log('this.uploadFileList', this.uploadFileList)
        })
      this.uploadFileList = []
      this.uploadData = {
        params: businessName + '_' + keyId + '_' + USER_PERSON_ID + '_' + USER_ORG_ID
      }
    },
    uploadRemove (file) {
      if (this.allowRemove) {
        this.$message.warning('禁止删除！')
        return false
      }
      return new Promise((resolve, reject) => {
        this.$confirm({
          title: '确认?',
          zIndex: 2001,
          content: '是否删除该附件',
          onOk: () => {
            // 处理参数
            const fileName = file.uid || ''
            const keyId = this.uploadData.params.split('_')[1] || ''
            const businessName = this.uploadData.params.split('_')[0] || ''
            // 调用接口
            fileApi.deleteFile({ keyId, businessName, fileName, personId: USER_PERSON_ID }).then(res => {
              if (res.code !== '0000') {
                this.$notification.error({
                  message: '系统消息',
                  description: '删除附件失败！'
                })
                reject(new Error())
              }
              this.uploadFileList = this.uploadFileList.filter(item => {
                return item.uid !== file.uid
              })
              resolve()
            })
          },
          onCancel () {
            reject(new Error())
          }
        })
      })
    },
    async handlePreviewPic (file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj)
      }
      this.previewImage = file.url || file.preview
      this.previewVisible = true
    }
  }
}
</script>
<style lang="less" scoped>
/* you can make up upload button and sample style by using stylesheets */
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
