<template>
  <a-drawer
    :title="subTitle"
    :width="600"
    :visible="visible"
    :mask="false"
    :bodyStyle="{paddingBottom: '80px'}"
    @close="hiddenMaterialDrawer"
  >
    <!-- 子抽屉开始 -->
    <a-drawer
      :title="childDrawerTitle"
      :visible="childDrawerVisible"
      :maskClosable="false"
      :destroyOnClose="true"
      width="800"
      @close="childDrawerVisible = false">
      <!-- 统购物资选择 -->
      <!--以普遍性理论而言，这里应该加个v-if决定显示内容-->
      <div class="drawer-body-header">
        <div class="body-header-fixed">
          <a-form layout="inline" class="materialForm">
            <div style="width: 100%; overflow: hidden;">
              <a-row :gutter="10" style="width: calc(100% - 60px); float: left">
                <a-col :xl="8" :md="8" :sm="24">
                  <a-form-item label="物资名称">
                    <a-input v-model="uniOrderParam.uniOrderName" @pressEnter="doUniOrderSearch" />
                  </a-form-item>
                </a-col>
                <a-col :xl="8" :md="8" :sm="24">
                  <a-form-item label="规格型号">
                    <a-input v-model="uniOrderParam.model" @pressEnter="doUniOrderSearch" />
                  </a-form-item>
                </a-col>
                <a-col :xl="8" :md="8" :sm="24">
                  <a-form-item label="计量单位">
                    <a-input v-model="uniOrderParam.orderUnit" @pressEnter="doUniOrderSearch" />
                  </a-form-item>
                </a-col>
                <a-col :xl="8" :md="8" :sm="24">
                  <a-form-item label="物资代码">
                    <a-input v-model="uniOrderParam.uniOrderNum" @pressEnter="doUniOrderSearch" />
                  </a-form-item>
                </a-col>
                <a-col :xl="8" :md="8" :sm="24">
                  <a-form-item label="统购物资类别">
                    <a-input v-model="uniOrderParam.uniOrderClass" @pressEnter="doUniOrderSearch" />
                  </a-form-item>
                </a-col>
              </a-row>
              <div style="width: 50px; margin-left: 10px; float: left; border-left: dashed 1px #cfcfcf">
                <a-button style="margin-left: 8px" @click="doUniOrderSearch" type="primary">查询</a-button>
              </div>
            </div>
          </a-form>
        </div>
      </div>
      <s-table
        ref="uniOrderTable"
        size="small"
        rowKey="uniOrderNum"
        :columns="uniOrderColumns"
        :data="loadUniOrderData"
        :clearSelection="uniOrderClearSelection"
        :rowSelection="uniOrderOptions.rowSelection"
        :pageSizeOptions="uniOrderPageSizeOptions"
        :pageSize="uniOrderDefaultPageSize"
        :showPagination="true" />
      <div class="drawer-footer">
        <div class="footer-fixed">
          <a-button @click="cancelChildDrawer()">取消</a-button>
          <a-button @click="setFormData()" type="primary">确认</a-button>
        </div>
      </div>
    </a-drawer>
    <!--子抽屉结束-->
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row :gutter="8">
          <!-- 前台不显示唯一编码 -->
          <a-form-item
            v-show="false"
            label="唯一编码:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input v-decorator="['materialSysId', {rules: [{required: false}]}]" />
          </a-form-item>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="物资代码:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                :disabled="IsProhibit !== '1'"
                v-decorator="IS_WZG ?
                  ['materialNum', {rules: [{required: true, validator: checkMaterialNum}]}] :
                  ['materialNum', {rules: [{required: true, message: '请输入至少一个字符！'}]}]"
                @blur="handlematerialNumBlur('materialNum')"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="物资名称:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                :disabled="IsProhibit === '2' && !(IS_BYJ_ENV && IS_NAME_UPDATE)"
                v-decorator="['materialName', {rules: [{required: true, message: '请输入至少一个字符！'}]}]"
              />
            </a-form-item>
          </a-col>
          <a-col
            v-if="IS_JX_ENV"
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="嘉兴26大类类别:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-tree-select
                v-decorator="['jxMaterialClass', { rules: [{ type: 'string', required: IS_JX_ENV, message: '请选择26大类类别' }] }]"
                treeNodeFilterProp="label"
                :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                :treeData=" jxMaterialClass "
                :disabled="IsEDIT === '1'"
                showSearch
                allowClear
                @change="jxMateNum"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              :label="() => {return IS_SZXD_ENV ? '财务科目' : '物资类别'}"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-tree-select
                v-decorator="['materialClass', { rules: [{ type: 'string', required: IS_SZXD_ENV, message: '请选择' }] }]"
                treeNodeFilterProp="label"
                :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                :treeData="materialClass"
                :disabled="IsEDIT === '1'"
                showSearch
                allowClear
                @change="wzgMateNum"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
            v-show="IS_SZXD_ENV"
          >
            <a-form-item
              label="工程物资分类:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-tree-select
                v-decorator="['szMaterial', { rules: [{ type: 'string', message: '请选择' }] }]"
                treeNodeFilterProp="label"
                :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                :treeData="szMaterials"
                :disabled="IsEDIT === '1'"
                showSearch
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
            v-show="IS_MD_ENV"
          >
            <a-form-item
              label="财务分类:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                :disabled="IsEDIT === '1'"
                v-decorator="['financialType']"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="26大类类别:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="IsEDIT === '1'"
                v-decorator="['materialType', { rules: [{ type: 'string', required: IS_TS_EVN, message: '请选择大类类别' }] }]"
                allowClear
                showSearch
                :filterOption="filterOption"
              >
                <a-select-option
                  v-for="(item, index) in materialType"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="规格型号:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                :disabled="IsEDIT === '1' && IsEDITYZ"
                v-decorator="['jmodel', {rules: [{required: IS_MD_ENV, message: '请输入至少一个字符！'}]}]"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="品牌:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                :disabled="IsEDIT === '1' && IsEDITYZ"
                v-decorator="['brand', {rules: [{required: IS_MD_ENV, message: '请输入至少一个字符！'}]}]"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
            v-if="xjStatus !== 'xjwz'"
          >
            <a-form-item
              label="采购员:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="IsEDIT === '1'"
                v-decorator="['buyer', { rules: [{ type: 'string', required: true, message: '请选择采购员' }] }]"
                allowClear
                showSearch
                :filterOption="filterOption"
              >
                <a-select-option
                  v-for="(item, index) in buyer"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
            v-show="IS_JX_ENV"
          >
            <a-form-item
              label="物资属性:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-decorator="['materialProperties', { rules: [{ type: 'string', required: IS_JX_ENV, message: '请选择物资属性' }] }]"
                allowClear
                showSearch
                :filterOption="filterOption"
              >
                <a-select-option
                  v-for="(item, index) in materialProperties"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
            v-if="xjStatus !== 'xjwz'"
          >
            <a-form-item
              label="仓库管理员:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="IsEDIT === '1'"
                v-decorator="['storeman', { rules: [{ type: 'string', required: true, message: '请选择仓库管理员' }] }]"
                allowClear
                showSearch
                :filterOption="filterOption"
              >
                <a-select-option
                  v-for="(item, index) in storeman"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="职能部门:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="IsEDIT === '1'"
                v-decorator="['deptno', { rules: [{ type: 'string', required: false, message: '请选择责任部门' }] }]"
                allowClear
                showSearch
                :filterOption="filterOption"
              >
                <a-select-option
                  v-for="(item, index) in deptno"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              v-show="IS_BYJ_ENV"
              label="二级仓管员:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="IsEDIT === '1'"
                v-decorator="['ejStoreman', { rules: [{ type: 'string', required: false, message: '请选择二级仓管员' }] }]"
                allowClear
                showSearch
                :filterOption="filterOption"
              >
                <a-select-option
                  v-for="(item, index) in storeman"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="是否统购物资:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              style="width: 200"
            >
              <a-select
                :disabled="IsEDIT === '1'"
                style="width: 100"
                @change="isUniOrderOrNot"
                v-decorator="['isUniOrder', {rules: [{required: true, message: '请选择是或否'}]}]"
              >
                <a-select-option value="Y">是</a-select-option>
                <a-select-option value="N">否</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            v-if="IS_UNI_ORDER === 'Y'"
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="统购物资:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              style="width: 200"
            >
              <a-input
                v-decorator="['uniOrderNum', {
                  rules: [{ required: false, message: '请选择统购物资' }]
                }]"
                :readOnly="true"
                @focus="openChildDrawer('UniOrder')" />
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="是否补库物资:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="IsEDIT === '1'"
                v-decorator="['isResupply', {rules: [{required: true, message: '请选择是或否'}]}]"
              >
                <a-select-option value="Y">是</a-select-option>
                <a-select-option value="N">否</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            v-if="IS_JX_ENV"
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="是否固定资产:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="IsEDIT === '1'"
                v-decorator="['isFixedAssets', {rules: [{required: true, message: '请选择是或否'}]}]"
              >
                <a-select-option value="Y">是</a-select-option>
                <a-select-option value="N">否</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="计量单位:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="IsEDIT === '1'"
                v-decorator="['issueUnit', { rules: [{ required: true, message: '请选择计量单位',type: 'string' }] }]"
                allowClear
                showSearch
                :filterOption="filterOption"
              >
                <a-select-option
                  v-for="(item, index) in invoiceUnits"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="物资编码账本号:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                :disabled="IsEDIT === '1'"
                v-decorator="['materialnum5', {rules: [{required: false, message: '物资编码账本号不能为空！'}]}]"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="批次类型:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="IsEDIT === '1'"
                v-decorator="['lotType', { rules: [{ type: 'string', required: false, message: '请选择是否批次' }] }]"
              >
                <a-select-option
                  v-for="(item, index) in lottype"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="类型细目:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="IsEDIT === '1'"
                v-decorator="['typeDetail', { rules: [{ type: 'string', required: IS_YZ_ENV, message: '请选择类型细目' }] }]"
                allowClear
              >
                <a-select-option
                  v-for="(item, index) in typedetail"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="最大库存:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                :disabled="IsEDIT === '1'"
                v-decorator="['maxAmount', {rules: [{required: false,type:'number', transform(value) { if(value || value === 0){ return Number(value); } },message: '最大库存不能为空且值为数字！'}]}]"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="最小库存:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                :disabled="IsEDIT === '1'"
                v-decorator="['minAmount', {rules: [{required: false, type:'number', transform(value) { if(value || value === 0){ return Number(value); } },message: '最小库存不能为空且值为数字！'}]}]"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="参考单价:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                :disabled="IsEDIT === '1' && IsEDITYZ"
                type="number"
                @blur="handlerefCostBlur"
                v-decorator="['refCost', {rules: [{required: true, type:'number', transform(value) { if(value || value === 0){ return Number(value); } }, message: '参考单价不能为空且值为数字！'}]}]"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="税率:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                :disabled="IsEDIT === '1' && IsEDITYZ"
                type="number"
                @blur="handletaxrateBlur('taxrate')"
                v-decorator="['taxrate', {rules: [{required: true, type:'number', transform(value) { if(value || value === 0){ return Number(value); } }, message: '参考单价不能为空且值为数字！'}]}]"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="税后单价:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                :disabled="IsEDIT === '1' && IsEDITYZ"
                type="number"
                @blur="handleunitcostAfterTaxBlur"
                v-decorator="['unitcostAfterTax', {rules: [{required: true, type:'number', transform(value) { if(value || value === 0){ return Number(value); } }, message: '税后单价不能为空且值为数字！'}]}]"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="是否二次利用:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="IsEDIT === '1'"
                v-decorator="['isReuse', {rules: [{required: true, message: '请选择是或否'}]}]"
              >
                <a-select-option value="Y">是</a-select-option>
                <a-select-option value="N">否</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="是否劳保用品:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="IsEDIT === '1'"
                v-decorator="['isLabourSupply', {rules: [{required: true, message: '请选择是或否'}]}]"
              >
                <a-select-option value="Y">是</a-select-option>
                <a-select-option value="N">否</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            v-if="IS_JX_ENV"
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="是否信息物资:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="IsEDIT === '1'"
                v-decorator="['isInformationMaterials', {rules: [{required: true, message: '请选择是或否'}]}]"
              >
                <a-select-option value="Y">是</a-select-option>
                <a-select-option value="N">否</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            v-if="IS_JYS_ENV"
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="所在单位"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-decorator="['belongToSite', { rules: [{ type: 'string', required: false, message: '请选择所在单位信息' }] }]"
                showSearch
                allowClear
              >
                <a-select-option
                  v-for="(item, index) in belongToSites"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            v-if="IS_JYS_ENV"
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="账龄物资"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-decorator="['zlwz', { rules: [{ type: 'string', required: false, message: '请选择是否为账龄物资' }] }]"
                allowClear
              >
                <a-select-option value="Y">是</a-select-option>
                <a-select-option value="N">否</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="库:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-decorator="['yardId', { rules: [{ type: 'string', required: false, message: '请选择库信息' }] }]"
                allowClear
                showSearch
                @change="handleStorehouseChange"
              >
                <a-select-option
                  v-for="(item, index) in storehouses"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="区:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-decorator="['zone', { rules: [{ type: 'string', required: false, message: '请选择库信息' }] }]"
                allowClear
                showSearch
                @change="handleZoneChange"
              >
                <a-select-option
                  v-for="(item, index) in zones"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="架:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-decorator="['frame', { rules: [{ type: 'string', required: false, message: '请选择库信息' }] }]"
                allowClear
                showSearch
                @change="handleFrameChange"
              >
                <a-select-option
                  v-for="(item, index) in frames"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="层:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-decorator="['floor', { rules: [{ type: 'string', required: false, message: '请选择库信息' }] }]"
                allowClear
                showSearch
                @change="handleFloorChange"
              >
                <a-select-option
                  v-for="(item, index) in floors"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="位:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-decorator="['bit', { rules: [{ type: 'string', required: false, message: '请选择库信息' }] }]"
                allowClear
                showSearch
              >
                <a-select-option
                  v-for="(item, index) in bits"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            v-if="IS_XT_ENV"
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="技术参数:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                :disabled="IsEDIT === '1'"
                v-decorator="['techParam', {rules: [{required: false, message: '请输入至少一个字符！'}]}]"
              />
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
            v-show="!existingProcess"
          >
            <a-form-item
              label="是否激活:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="IsEDIT === '1'"
                v-decorator="['activity', {rules: [{required: true, message: '请选择是或否'}]}]"
              >
                <a-select-option value="Y">是</a-select-option>
                <a-select-option value="N">否</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="审批状态:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-decorator="['status', { rules: [{ type: 'string' }] }]"
                allowClear
                :disabled="true"
              >
                <a-select-option
                  v-for="(item, index) in statusDetal"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="备注:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-textarea
                :disabled="IsEDIT === '1'"
                v-decorator="['matRemarks']"
                :auto-size="{ minRows: 2, maxRows: 6 }"
              />
            </a-form-item>
          </a-col>
          <a-col
            v-if="IS_HJ_ENV"
            :md="12"
            :sm="24"
          >
            <a-form-item
              label="水泥混合料:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="IsEDIT === '1'"
                v-decorator="['isCement', {rules: [{required: IS_HJ_ENV, message: '请选择是或否'}]}]"
              >
                <a-select-option value="Y">是</a-select-option>
                <a-select-option value="N">否</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="8">
          <!--前台不显示唯一编码-->
          <!--<a-form-item
            v-show="false"
            label="唯一编码:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input v-decorator="['materialSysId', {rules: [{required: false}]}]" />
          </a-form-item>-->
          <a-col
            :md="24"
            :sm="48"
          >
            <a-form-item
              label="二维码:"
              v-show="IsShowQr=== '1'"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <div class="material-qrCode">
                <img
                  v-if="materialQrCodeUrl"
                  :src="materialQrCodeUrl"
                  alt="qrCode"
                >
              </div>
              <a-button
                v-show="IsShowQr=== '1'"
                :style="{marginRight: '8px'}"
                @click="downloadMaterialQrCode()"
              >下载二维码</a-button>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="8">
          <!--前台不显示唯一编码-->
          <!--<a-form-item
            v-show="false"
            label="唯一编码:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input v-decorator="['materialSysId', {rules: [{required: false}]}]" />
          </a-form-item>-->
          <a-col
            :md="24"
            :sm="48"
          >
            <a-form-item
              label="物资图片:"
              v-show="actionFlag === 'update'"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <div class="clearfix">
                <a-upload
                  list-type="picture-card"
                  :data="uploadData"
                  :action="uploadAction"
                  :headers="uploadHeaders"
                  :file-list="uploadFileList"
                  :beforeUpload="uploadBeforeUpload"
                  :remove="uploadRemove"
                  @change="uploadChange"
                  @preview="handlePreviewPic"
                >
                  <div v-if="fileList.length < 8">
                    <a-icon type="plus" />
                    <div class="ant-upload-text">
                      上传
                    </div>
                  </div>
                </a-upload>
                <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
                  <img alt="example" style="width: 100%" :src="previewImage" >
                </a-modal>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button
        :style="{marginRight: '8px'}"
        @click="hiddenMaterialDrawer"
      >取消</a-button>
      <a-button
        @click="doSubmit"
        type="primary"
        :loading="saveLoading"
        :disabled="IsEDIT === '1' && IsEDITYZ"
      >保存</a-button>
    </div>
  </a-drawer>
</template>
<script>
import { Base64 } from 'js-base64'
import { STable, Ellipsis } from '@/components'
import { requestBuilder, takeTreeByKey } from '@/utils/util'
import * as baseApi from '@/api/material/base'
import * as uniOrderApi from '@/api/material/uniOrder'
import * as materialApi from '@/api/material/material'
import * as prlineApi from '@/api/material/prline'
import { OPERATOR, ORG_ID, PERSON_ID, ACCESS_TOKEN, ROLE_NAME, DEPT_NAME } from '@/store/mutation-types'
import Vue from 'vue'
import * as fileApi from '@/api/system/file'
// 操作人 userNo
const USER_OPERATOR = Vue.ls.get(OPERATOR)
const USER_ORG_ID = Vue.ls.get(ORG_ID)
// 操作人 userPersonId
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)
const USER_ROLE_NAME = Vue.ls.get(ROLE_NAME)
const USER_DEPT_NAME = Vue.ls.get(DEPT_NAME)
// 运行环境 ENV
const IS_JYS_ENV = USER_ORG_ID === '1.100.117'
const IS_MD_ENV = USER_ORG_ID === '1.100.104'

// 温州港
const IS_WZG = USER_ORG_ID.substr(0, 5) === '1.101'
const IS_WZG_LW = USER_ORG_ID === '1.101.106'
// 嘉兴乍浦
const IS_JX_ENV = USER_ORG_ID === '1.102'
// 北一集司
const IS_BYJ_ENV = USER_ORG_ID === '1.100.101'
// 南京明州
const IS_NJMZ_ENV = USER_ORG_ID === '1.100.109'
// 铁司
const IS_TS_EVN = USER_ORG_ID === '1.100.122'
const IS_SZXD_ENV = USER_ORG_ID === '1.100.106'
const IS_SLH_EVN = USER_ORG_ID === '1.100.111'
const IS_HJ_ENV = USER_ORG_ID === '1.100.131'
const IS_DMY_ENV = USER_ORG_ID === '1.122.801'
const IS_YZ_ENV = USER_ORG_ID === '1.100.107'
const IS_XT_ENV = USER_ORG_ID === '1.100.118'
const IS_GJ_ENV = USER_ORG_ID === '1.100.129'
// 上传附件允许类型
const uploadAccept = [
  // IMG
  'image/png',
  'image/jpeg',
  'image/bmp',
  'image/gif',
  'image/vnd.dwg',
  ''
]
const TOKEN = Vue.ls.get(ACCESS_TOKEN)
const BASE_URL = process.env.VUE_APP_API_BASE_URL
const BASE_PREVIEW_URL = process.env.VUE_APP_PREVIEW_BASE_URL
const VUE_APP_UPLOAD_PREVIEW_URL = process.env.VUE_APP_UPLOAD_PREVIEW_URL
function getBase64 (file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = error => reject(error)
  })
}
export default {
  name: 'MaterialEdit',
  components: {
    STable,
    Ellipsis
  },
  data () {
    return {
      // RUN_ENV
      IS_WZG,
      IS_YZ_ENV,
      IS_JYS_ENV,
      IS_JX_ENV,
      IS_BYJ_ENV,
      IS_NJMZ_ENV,
      IS_TS_EVN,
      IS_SLH_EVN,
      IS_MD_ENV,
      IS_SZXD_ENV,
      IS_WZG_LW,
      IS_HJ_ENV,
      IS_XT_ENV,
      IS_DMY_ENV,
      takeTreeByKey, // 用于将后台返回的数据（例如ID号）转换成对应的内容（id对应的名称）
      // 物资代码弹窗的样式
      // labelCol: {
      //   xs: { span: 28 },
      //   sm: { span: 9, offset: 0 }
      // },
      uploadData: {},
      actionFlag: '',
      uploadFileList: [],
      uploadHeaders: { token: TOKEN },
      uploadAction: BASE_URL + '/file/fileUpload',
      uploadAccept: uploadAccept.join(','),
      previewVisible: false,
      previewImage: '',
      fileList: [],
      labelCol: { style: { width: '100px' } },
      wrapperCol: {
        xs: { span: 20 },
        sm: { span: 14 }
      },
      // 是否是统购物资
      IS_UNI_ORDER: 'N',
      // 分页参数
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20'],
      mdl: {},
      // 高级搜索 展开/关闭
      advanced: false,
      saveLoading: false,
      // 查询参数
      queryParam: {
        activity: 'Y',
        materialNum: '',
        materialName: '',
        jmodel: '',
        status: ''
      },
      materialScroll: {
        x: 'max-content',
        scrollToFirstRowOnChange: false
      },
      // 加载基础代码类别数据
      loadMClassData: parameter => {
        const param = requestBuilder(
          '',
          Object.assign(this.queryParam),
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField === 'jmodel' ? 'j_model' : parameter.sortField,
          parameter.sortOrder
        )
        return materialApi.getMaterialInfo(param).then(res => {
          return res.result
        })
      },
      // 行选择参数
      options: {
        alert: {
          show: false,
          clear: () => {
            this.selectedRowKeys = []
          }
        },
        rowSelection: {
          onChange: this.onSelectChange
        }
      },
      selectedRowKeys: [],
      selectedRows: [],
      // 滑动抽屉参数
      form: this.$form.createForm(this),
      confirmLoading: false, // 加载图标是否出现
      rolesList: [], // 角色列表
      selectedItems: [],
      buyer: [], // 采购员
      materialProperties: [], // 物资属性
      storeman: [], // 仓库保管员
      status: [], // 审批状态
      statusDetal: [], // 审批详细状态
      materialType: [], // 26大类类别
      materialClass: [], // 物资类别
      szMaterials: [], // 苏州工程物资分类
      jxMaterialClass: [], // 嘉兴港物资类别
      deptno: [], // 责任部门
      typedetail: [], // 类型细目
      storehouses: [], // 库
      zones: [], // 区
      frames: [], // 架
      floors: [], // 层
      bits: [], // 位
      lottype: [], // 批次类型
      invoiceUnits: [], // 计量单位
      belongToSites: [], // 下拉框选项 - 所在单位
      materialQrCodeUrl: false, // 二维码url
      visible: false,
      xjStatus: '',
      visibleApprove: false,
      subTitle: '新增物资代码',
      subTitleApprove: '物资代码审批', // 审批弹窗名
      IsProhibit: '', // 判断物资代码弹窗的字段是否为只读
      IsEDIT: '', // 非新增和申请人发阶段都不可修改物资信息
      IsEDITYZ: true, // yz物资编辑
      IsShowQr: '', // 判断二维码是否显示
      IsApprove: '', // 判断选择的物资是否有权审批
      approveReturnDisabled: false, // 判断审批是否能退回操作
      approveCancelDisabled: false, // 判断审批是否能取消操作
      existingProcess: false, // 判断有无流程
      materialSysId: '',
      // 审批弹窗的样式
      labelColApprove: {
        xs: { span: 24 },
        sm: { span: 9, offset: 0 }
      },
      wrapperColApprove: {
        xs: { span: 24 },
        sm: { span: 14 }
      },
      // 审批表单
      formApprove: this.$form.createForm(this),
      // 审批记录
      materialNumRecords: {},
      currentPrlineNum: '',
      approveLoading: false,
      // 物资代码主表双击事件
      rowClick: record => ({
        on: {
          // 点击事件
          dblclick: () => {
            if (this.roleName) {
              this.IS_UNI_ORDER = record.isUniOrder
              this.openDrawer('edit', record)
            }
          }
        }
      }),
      // 子抽屉设置
      childDrawerTitle: '',
      childDrawerVisible: false,
      // 要展示的子抽屉
      SelectOfChildDrawer: '',
      // 统购物资子抽屉设置
      uniOrderSelectedRows: [],
      uniOrderSelectedRowKeys: [],
      uniOrderClearSelection: false,
      uniOrderPageSizeOptions: ['10', '20'],
      uniOrderDefaultPageSize: 10,
      // 统购物资选择行
      uniOrderOptions: {
        rowSelection: {
          type: 'radio',
          selectedRowKeys: this.uniOrderSelectedRowKeys,
          onChange: this.onUniOrderSelectChange
        }
      },
      // 统购物资列表
      uniOrderColumns: [
        {
          title: '统购物资名称',
          ellipsis: true,
          dataIndex: 'uniOrderName'
        },
        {
          title: '规格型号',
          ellipsis: true,
          dataIndex: 'model'
        },
        {
          title: '计量单位',
          ellipsis: true,
          dataIndex: 'orderUnit'
        },
        {
          title: '统购物资代码',
          ellipsis: true,
          dataIndex: 'uniOrderNum'
        },
        {
          title: '统购物资类别',
          ellipsis: true,
          dataIndex: 'uniOrderClass'
        },
        {
          title: '预估单价',
          ellipsis: true,
          dataIndex: 'refCost'
        }
      ],
      // 统购查询条件
      uniOrderParam: {
        activity: 1,
        uniOrderName: '',
        model: '',
        orderUnit: '',
        uniOrderNum: '',
        uniOrderClass: ''
      },
      loadUniOrderData: parameter => {
        const param = requestBuilder('', Object.assign(this.uniOrderParam), parameter.pageNo, parameter.pageSize)
        return uniOrderApi.getUniOrderInfo(param).then(res => {
          if (res.code === '0000') {
            return res.result
          } else {
            this.$message.error(res.message)
          }
        })
      },
      roleName: '',
      uploadChange: options => {
        console.log(options, 'options')
        if (options.file && ['removed', 'error'].includes(options.file.status)) {
          this.uploadFileList = this.uploadFileList.filter(file => {
            return file.uid !== options.file.uid
          })
        }
        if (options.file && options.file.status === 'done') {
          if (options.file.response && options.file.response.code === '0000') {
            this.uploadFileList = this.uploadFileList.map(file => {
              if (file.uid === options.file.uid) {
                file.url = `${window.location.protocol}//${window.location.host}${BASE_PREVIEW_URL}/file/fileDownload?keyId=${this.materialSysId}&businessName=${'materialPhoto'}&fileName=${options.file.response.message}`
                file.uid = options.file.response.message
                file.status = options.file.status
              }
              return file
            })
          } else {
            this.uploadFileList = this.uploadFileList.filter(file => {
              return file.uid !== options.file.uid
            })
            this.$notification.error({
              message: '系统消息',
              description: options.file.response.message
            })
          }
        }
      }
    }
  },
  created () {
    this.initOptions()
  },
  methods: {
    // 初始化下拉框数据获取
    initOptions () {
      baseApi.existingProcess({ origin: 'material', orgId: USER_ORG_ID }).then(res => {
        this.existingProcess = res.result
      })
      // materialApi.existingProcess({ orgId: USER_ORG_ID }).then(res => {
      //  this.existingProcess = res.result
      // })
      baseApi
        .getCommboxById({ id: 'materialProperties' }).then(res => {
          if (res.code === '0000') {
            this.materialProperties = res.result
          }
        })
      if (IS_BYJ_ENV) {
        baseApi
          .getCommboxById({ id: 'checkNameUpdate', sqlParams: { personId: USER_PERSON_ID } }).then(res => {
            if (res.code === '0000') {
              this.IS_NAME_UPDATE = res.result[0].label === 'Y'
            }
          })
      }
      baseApi
        .getCommboxById({ id: 'personByRoleName', sqlParams: { byRoleName: '1', roleName: '采购员' } })
        .then(res => {
          const arr = this.buyer.map(item => item.value)
          res.result.forEach(v => {
            if (!arr.includes(v.value)) {
              this.buyer.push(v)
            }
          })
        })
      if (IS_JYS_ENV) {
        baseApi
          .getCommboxById({ id: 'personByRoleName', sqlParams: { byRoleName: '1', roleName: '修理站站长' } })
          .then(res => {
            const arr = this.buyer.map(item => item.value)
            res.result.forEach(v => {
              if (!arr.includes(v.value)) {
                this.buyer.push(v)
              }
            })
          })
      }
      baseApi.getTreeById({ id: 'appro' }).then(res => {
        if (res.code === '0000') {
          this.status = res.result
        }
      })
      baseApi.getCommboxById({ id: 'appro' }).then(res => {
        if (res.code === '0000') {
          this.statusDetal = res.result
        }
      })
      baseApi.getCommboxById({ id: IS_JX_ENV ? 'matrtype' : 'matrtypenew' }).then(res => {
        if (res.code === '0000') {
          this.materialType = res.result
        }
      })
      if (IS_WZG) {
        baseApi.getTreeById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'wg_material_class' } }).then(res => {
          if (res.code === '0000') {
            this.disableSelectable(res.result)
            this.materialClass = res.result
          }
        })
      } else if (IS_MD_ENV) {
        baseApi.getTreeById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'md_material_class' } }).then(res => {
          if (res.code === '0000') {
            this.disableSelectable(res.result)
            this.materialClass = res.result
          }
        })
      } else if (IS_SZXD_ENV) {
        baseApi.getTreeById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'sz_material_class' } }).then(res => {
          if (res.code === '0000') {
            this.disableSelectable(res.result)
            this.materialClass = res.result
          }
        })
      } else {
        baseApi.getCommboxById({ id: 'materialClass' }).then(res => {
          if (res.code === '0000') {
            this.materialClass = res.result
          }
        })
      }
      if (IS_JX_ENV) {
        baseApi.getTreeById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'jx_material_class' } }).then(res => {
          if (res.code === '0000') {
            this.disableSelectable(res.result)
            this.jxMaterialClass = res.result
          }
        })
      }

      if (IS_SZXD_ENV) {
        baseApi.getTreeById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'sz_material' } }).then(res => {
          if (res.code === '0000') {
            this.disableSelectable(res.result)
            this.szMaterials = res.result
          }
        })
      }
      baseApi
        .getCommboxById({ id: 'personByRoleName', sqlParams: { byRoleName: '1', roleName: '仓库管理员' } })
        .then(res => {
          if (res.code === '0000') {
            this.storeman = res.result
            const index = this.storeman.map(item => item.value).indexOf(USER_PERSON_ID)
            if (index !== -1) {
              [this.storeman[0], this.storeman[index]] = [this.storeman[index], this.storeman[0]]
            }
          }
        })
      baseApi.getCommboxById({ id: 'typedetail' }).then(res => {
        if (res.code === '0000') {
          this.typedetail = res.result
        }
      })
      baseApi.getCommboxById({ id: 'lottype' }).then(res => {
        if (res.code === '0000') {
          this.lottype = res.result
        }
      })
      baseApi.getCommboxById({ id: 'dept', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.deptno = res.result
        }
      })
      baseApi.getCommboxById({ id: 'unit' }).then(res => {
        if (res.code === '0000') {
          this.invoiceUnits = res.result
        }
      })
      baseApi.getCommboxById({ id: 'storehouse' }).then(res => {
        if (res.code === '0000') {
          this.storehouses = res.result
        }
      })
      baseApi.getCommboxById({ id: 'zone', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.zones = res.result
        }
      })
      baseApi.getCommboxById({ id: 'frame', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.frames = res.result
        }
      })
      baseApi.getCommboxById({ id: 'floor', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.floors = res.result
        }
      })
      baseApi.getCommboxById({ id: 'bit', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.bits = res.result
        }
      })
      // 所在单位
      if (IS_JYS_ENV) {
        baseApi.getCommboxById({ id: 'belongToSite' }).then(res => {
          if (res.code === '0000') {
            this.belongToSites = res.result
          }
        })
      }
      // 获取当前角色名(获取计划员和管理员)
      baseApi.getPersonRoleName(requestBuilder('', Object.assign({}, {}), '', '')).then(res => {
        if (res.code === '0000') {
          if (res.result) {
            res.result.forEach(item => {
              if (item.indexOf('计划员') !== -1 || item.indexOf('管理员') !== -1) {
                this.roleName = item
              }
            })
          }
        }
      })
    },
    filterOption (input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    // 下拉框物资细目-物资类别联动
    handletypeDetailChange (value) {
      if (value === 'type_detail_1') {
        this.form.setFieldsValue({
          materialClass: 'material_class_1'
        })
      } else if (value === 'type_detail_2') {
        this.form.setFieldsValue({
          materialClass: 'material_class_4'
        })
      } else if (value === 'type_detail_3') {
        this.form.setFieldsValue({
          materialClass: 'material_class_3'
        })
      } else if (value === 'type_detail_4') {
        this.form.setFieldsValue({
          materialClass: 'material_class_2'
        })
      }
    },
    // 下拉框库-区联动
    handleStorehouseChange (value) {
      // this.cities = cityData[value]
      // this.secondCity = cityData[value][0]
      this.form.setFieldsValue({
        zone: '',
        frame: '',
        floor: '',
        bit: ''
      })
      this.zones = []
      baseApi
        .getCommboxById({ id: 'zone', sqlParams: { byStorehouseSysId: '1', storehouseSysId: value } })
        .then(res => {
          if (res.code === '0000') {
            // this.form.setFieldsValue({
            //   zone: res.result.length > 0 ? res.result[0].value : ''
            // })
            this.zones = res.result
          }
        })
    },
    // 下拉框区-架联动
    handleZoneChange (value) {
      // this.cities = cityData[value]
      // this.secondCity = cityData[value][0]
      this.form.setFieldsValue({
        frame: '',
        floor: '',
        bit: ''
      })
      this.frames = []
      baseApi.getCommboxById({ id: 'frame', sqlParams: { byZoneSysId: '1', zoneSysId: value } }).then(res => {
        if (res.code === '0000') {
          // this.form.setFieldsValue({
          //   zone: res.result.length > 0 ? res.result[0].value : ''
          // })
          this.frames = res.result
        }
      })
    },
    // 下拉框架-层联动
    handleFrameChange (value) {
      // this.cities = cityData[value]
      // this.secondCity = cityData[value][0]
      this.form.setFieldsValue({
        floor: '',
        bit: ''
      })
      this.floors = []
      baseApi.getCommboxById({ id: 'floor', sqlParams: { byFrameSysId: '1', frameSysId: value } }).then(res => {
        if (res.code === '0000') {
          // this.form.setFieldsValue({
          //   zone: res.result.length > 0 ? res.result[0].value : ''
          // })
          this.floors = res.result
        }
      })
    },
    // 下拉框层-位联动
    handleFloorChange (value) {
      // this.cities = cityData[value]
      // this.secondCity = cityData[value][0]
      this.form.setFieldsValue({
        bit: ''
      })
      this.bits = []
      baseApi.getCommboxById({ id: 'bit', sqlParams: { byFloorSysId: '1', floorSysId: value } }).then(res => {
        if (res.code === '0000') {
          // this.form.setFieldsValue({
          //   zone: res.result.length > 0 ? res.result[0].value : ''
          // })
          this.bits = res.result
        }
      })
    },
    // 物资税率改变
    handletaxrateBlur (value) {
      // 根据税后单价和税率计算税前单价
      if (
        this.form.getFieldValue('taxrate') &&
        this.form.getFieldValue('taxrate') &&
        this.form.getFieldValue('unitcostAfterTax') &&
        this.form.getFieldValue('unitcostAfterTax')
      ) {
        this.form.setFieldsValue({
          refCost: this.form.getFieldValue('unitcostAfterTax') / ((+this.form.getFieldValue('taxrate') || 0) + 1)
        })
      }
    },
    // 物资参考单价（税前单价）变化
    handlerefCostBlur () {
      // 根据税前单价和税率计算税后单价
      if (
        this.form.getFieldValue('refCost') === Number(0) &&
        this.form.getFieldValue('refCost') === Number(0)
      ) {
        this.form.setFieldsValue({
          unitcostAfterTax: Number(0)
        })
      } else if (
        this.form.getFieldValue('refCost') &&
        this.form.getFieldValue('refCost') &&
        this.form.getFieldValue('taxrate') &&
        this.form.getFieldValue('taxrate')
      ) {
        if (this.form.getFieldValue('refCost') === '0') {
          this.form.setFieldsValue({
            unitcostAfterTax: '0'
          })
        } else {
          this.form.setFieldsValue({
            unitcostAfterTax: this.form.getFieldValue('refCost') * ((+this.form.getFieldValue('taxrate') || 0) + 1)
          })
        }
      }
    },
    // 物资税后单价改变
    handleunitcostAfterTaxBlur () {
      // 根据税后单价和税率计算税前单价
      if (
        this.form.getFieldValue('unitcostAfterTax') &&
        this.form.getFieldValue('unitcostAfterTax') &&
        this.form.getFieldValue('taxrate') &&
        this.form.getFieldValue('taxrate')
      ) {
        this.form.setFieldsValue({
          refCost: this.form.getFieldValue('unitcostAfterTax') / ((+this.form.getFieldValue('taxrate') || 0) + 1)
        })
      }
    },
    // 禁用下拉框父类选项
    disableSelectable (array) {
      for (const item of array) {
        if (item.children && item.children.length > 0) {
          item.selectable = false
          this.disableSelectable(item.children)
        }
      }
    },
    // 修改物资信息
    doEdit (record) {
      if (this.selectedRows.length === 1) {
        this.IS_UNI_ORDER = this.selectedRows[0].isUniOrder
        this.openDrawer('update', this.selectedRows[0])
      } else {
        this.$message.error('请勾选一条记录')
      }
    },
    // 生成二维码
    queryMaterialQrCode (record) {
      const param = requestBuilder('', {
        materialName: record.materialName,
        materialNum: record.materialNum,
        orgId: record.orgId
      })
      materialApi.getMaterialQrCode(param).then(res => {
        if (res && res.data) {
          this.materialQrCodeUrl = window.URL.createObjectURL(res.data)
        }
      })
    },
    queryExistence (record) {
      materialApi.queryExistence({ materialNum: record.materialNum, orgId: record.orgId }).then(res => {
        this.IsProhibit = res.result ? '2' : '3'
      })
    },
    downloadMaterialQrCode () {
      if (!this.materialQrCodeUrl) {
        this.$message.error('暂无物资二维码！')
        return
      }
      const link = document.createElement('a')
      const filename = '物资二维码.jpg'
      document.body.appendChild(link)
      link.style.display = 'none'
      link.download = filename
      link.href = this.materialQrCodeUrl
      link.click()
      link.remove()
    },
    // 表格选择行
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    // 物资编码账本号自动设置为物资代码前5位数
    handlematerialNumBlur (value) {
      let materialClass = this.form.getFieldValue('materialClass')
      if (IS_WZG) {
        materialClass = this.form.getFieldValue('materialNum').substring(1, 7)
      } else if (IS_MD_ENV) {
        materialClass = this.form.getFieldValue('materialNum').substring(0, 5)
      }
      if (value === 'materialNum') {
        this.form.setFieldsValue({
          materialnum5: this.form.getFieldValue('materialNum').substring(0, 5),
          materialClass: materialClass
        })
      }
    },
    // 打开物资详细页弹窗
    openDrawer (action, record, type = '') {
      this.visible = true
      this.IsEDITYZ = true
      this.xjStatus = type
      this.materialQrCodeUrl = ''
      this.$nextTick(() => {
        if (action === 'insert') {
          this.IS_UNI_ORDER = 'N'
          if (IS_JX_ENV) {
            this.form.setFieldsValue({
              isStop: 'N',
              activity: 'Y',
              isUniOrder: 'N',
              isResupply: 'N',
              isFixedAssets: 'N',
              isReuse: 'N',
              isLabourSupply: 'N',
              isInformationMaterials: 'N',
              lotType: '1',
              maxAmount: '0',
              minAmount: '0',
              taxrate: '0.13',
              buyer: (this.buyer[0] && this.buyer[0].value) || '',
              materialProperties: (this.materialProperties[0] && this.materialProperties[0].value) || '',
              storeman: '27020136250676156',
              deptno: '27020136250675616'
            })
          } else {
            let buyer = (this.buyer[0] && this.buyer[0].value) || ''
            let storeman = (this.storeman[0] && this.storeman[0].value) || ''
            let taxrate = 0.13
            let refCost = 1
            if (type === 'xjwz') {
              taxrate = 0
            }
            if (IS_XT_ENV) {
              storeman = '26715531750982650'
            }
            if (IS_WZG_LW) {
              storeman = '27020136250646805'
            }
            if (IS_MD_ENV) {
              buyer = 'MD000038'
              storeman = '27566567726328440'
            }
            if (USER_ORG_ID === '1.101.104') {
              buyer = '27020136250646930'
            }
            if (IS_DMY_ENV) {
              buyer = '27020136250646439'
              taxrate = 0
              refCost = 0
            }
            if (IS_JYS_ENV) {
              if (USER_ROLE_NAME.includes('修理站站长')) {
                buyer = USER_PERSON_ID
              }
            }
            if (IS_GJ_ENV) {
              if (USER_DEPT_NAME.includes('金华')) {
                buyer = '27733271563651332' // 罗俊慧
              }
              if (USER_DEPT_NAME.includes('温州')) {
                buyer = '27733271563659264' // 张增婧
              }
            }

            this.form.setFieldsValue({
              isStop: 'N',
              activity: this.existingProcess ? 'N' : 'Y',
              isCement: 'N',
              isUniOrder: 'N',
              isResupply: 'N',
              isFixedAssets: 'N',
              isReuse: 'N',
              isLabourSupply: 'N',
              lotType: '1',
              maxAmount: '0',
              minAmount: '0',
              taxrate: taxrate,
              refCost: refCost,
              unitcostAfterTax: (1 + taxrate) * refCost,
              issueUnit: '',
              buyer: buyer,
              materialProperties: (this.materialProperties[0] && this.materialProperties[0].value) || '',
              storeman: storeman
            })
          }
          this.subTitle = this.xjStatus === 'xjwz' ? '新增修旧物资代码' : '新增物资代码'
          this.IsProhibit = '1'
          // 不显示二维码
          this.IsShowQr = '2'
          this.IsEDIT = false
          this.actionFlag = 'insert'
        } else {
          this.IsProhibit = '2'
          // 生产二维码
          this.openUploadDrawer(record)
          this.queryMaterialQrCode(record)
          this.queryExistence(record)
          // 显示二维码
          this.IsShowQr = '1'
          this.subTitle = '修改物资代码'

          if (record.status === 'approval1001' || record.status === 'approval1009') {
            this.IsEDIT = ''
          } else {
            this.IsEDIT = (USER_ROLE_NAME.includes('管理员') || USER_ROLE_NAME.includes('仓库管理员')) ? '' : '1'
          }
          if (IS_YZ_ENV && record.status === 'approval1003') {
            this.IsEDITYZ = false
          }
          this.form.setFieldsValue(record)
          this.form.setFieldsValue({
            materialnum5: record.materialNum ? record.materialNum.substring(0, 5) : ''
          })
          this.actionFlag = 'update'
        }
      })
    },
    // 打开附件弹框
    openUploadDrawer (record) {
      this.materialSysId = record.materialSysId
      fileApi
        .listFiles({
          keyId: record.materialSysId,
          businessName: 'materialPhoto'
        })
        .then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '获取附件列表失败！'
            })
          }
          for (const item of res.result) {
            const typeList = item.split('.')
            this.uploadFileList.push({
              uid: item,
              url: `${window.location.protocol}//${window.location.host}${BASE_PREVIEW_URL}/file/fileDownload?keyId=${record.materialSysId}&businessName=${'materialPhoto'}&fileName=${item}`,
              name: item.replace(/_[^_]+$/, ''),
              status: 'done',
              type: typeList[typeList.length - 1]
            })
          }
        })
      this.uploadData = {
        params: 'materialPhoto' + '_' + record.materialSysId + '_' + USER_PERSON_ID + '_' + USER_ORG_ID
      }
      this.uploadFileList = []
      this.uploadVisible = true
    },
    // 关闭物资代码弹框
    hiddenMaterialDrawer () {
      this.form.resetFields()
      this.visible = false
      this.previewVisible = false
      this.materialQrCodeUrl = ''
      this.IsEDIT = ''
      this.uploadFileList = []
      this.uploadData = {}
      this.xjStatus = ''
      this.IsEDITYZ = true
      this.IS_UNI_ORDER = 'N'
      this.$emit('hiddenMaterialDrawer')
    },
    wzgMateNum (num, item, c) {
      if ((IS_WZG || IS_MD_ENV) && this.actionFlag === 'insert') {
        baseApi.selectMateNum(requestBuilder('', { materialClass: num })).then(res => {
          this.form.setFieldsValue({
            materialNum: res.result,
            materialnum5: res.result ? res.result.substring(0, 5) : ''
          })
        })
      }
      if (IS_MD_ENV) {
        const str = c.triggerNode.$parent.value
        if (str) {
          this.form.setFieldsValue({
            financialType: str.substring(0, 3),
            materialType: str.slice(3)
          })
        }
      }
    },
    jxMateNum (num) {
      if (IS_JX_ENV) {
        baseApi.selectJxMateNum(requestBuilder('', { jxMaterialClass: num })).then(res => {
          this.form.setFieldsValue({
            materialNum: res.result
          })
        })
      }
    },
    isUniOrderOrNot (value) {
      this.IS_UNI_ORDER = value
    },
    // 打开子抽屉
    openChildDrawer (value) {
      if (value === 'UniOrder') {
        this.childDrawerVisible = true
        this.childDrawerTitle = '请选择统购物资'
        this.SelectOfChildDrawer = 'UniOrder'
      }
    },
    // 确定子抽屉弹出框选值
    setFormData (record) {
      if (this.SelectOfChildDrawer === 'UniOrder') {
        this.form.setFieldsValue({
          uniOrderNum: this.uniOrderSelectedRows[0].uniOrderNum
        })
      }
      this.cancelChildDrawer()
    },
    // 取消子抽屉
    cancelChildDrawer () {
      if (this.SelectOfChildDrawer === 'UniOrder') {
        this.uniOrderSelectedRows = []
        this.uniOrderSelectedRowKeys = []
        this.$refs.uniOrderTable.triggerSelect([], [])
      }
      this.childDrawerVisible = false
    },
    onUniOrderSelectChange (selectedRowKeys, selectedRows) {
      this.uniOrderSelectedRowKeys = selectedRowKeys
      this.uniOrderSelectedRows = selectedRows
    },
    doUniOrderSearch () {
      this.$refs.uniOrderTable.refresh(true)
    },
    // 校验温州港物资编码格式
    checkMaterialNum (rule, value, callback) {
      const regex = /^[a-zA-Z]{1}\d{6}[a-zA-Z]{1}\d{3}$/
      if (!regex.test(value)) {
        callback(new Error('格式为: A123456B123'))
        return
      }
      callback()
    },
    // 保存数据
    doSubmit () {
      const {
        form: { validateFields }
      } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        console.log(values, 'values')
        if (IS_WZG && errors && errors.materialNum && errors.materialNum.errors[0].message.indexOf('格式') !== -1) {
          if (Object.getOwnPropertyNames(errors).length === 1) {
            errors = null
          }
        }
        // 如果输入数据都合规(温州港忽略物资代码格式警告)
        if (!errors) {
          this.params = { ...values, issueUnit: values.issueUnit, orderUnit: values.issueUnit, isRm: this.xjStatus === 'xjwz' ? 'Y' : 'N' }
          this.getResult(this.params)
        } else {
          this.confirmLoading = false
        }
      })
    },
    getResult (params) {
      materialApi
        .modifyMaterialInfo(requestBuilder(this.actionFlag, this.params, '0', '0'))
        .then(res => {
          if (res.code === '0000') {
            this.hiddenMaterialDrawer()
            this.$emit('hiddenMaterialDrawer')
            this.$emit('doSearch')
            if (IS_WZG) {
              if (this.actionFlag === 'insert') {
                this.$info({
                  title: '操作成功',
                  content: '插入数据成功'
                })
              } else {
                this.$info({
                  title: '操作成功',
                  content: '修改数据成功'
                })
              }
              // this.success(this.actionFlag)
            } else {
              this.$message.success(res.message)
            }
          } else if (res.code === '9999') {
            this.$message.error('发生异常 ' + res.message)
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.confirmLoading = false
          this.saveLoading = false
        })
      this.saveLoading = true
    },
    handleCancel () {
      this.previewVisible = false
    },
    async handlePreview (file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj)
      }
      this.previewImage = file.url || file.preview
      this.previewVisible = true
    },
    handleChange ({ fileList }) {
      this.fileList = fileList
    },
    uploadBeforeUpload (file) {
      if (!uploadAccept.includes(file.type)) {
        this.$message.error('仅支持上传图片等附件')
        return false
      }
      if (file.size / 1024 / 1024 > 50) {
        this.$message.error('上传附件大小不可超过50M')
        return false
      }
      this.uploadFileList.push(file)
    },
    uploadRemove (file) {
      if (this.allowRemove) {
        this.$message.warning('禁止删除！')
        return false
      }
      return new Promise((resolve, reject) => {
        this.$confirm({
          title: '确认?',
          zIndex: 2001,
          content: '是否删除该附件',
          onOk: () => {
            // 处理参数
            const fileName = file.uid || ''
            const keyId = this.uploadData.params.split('_')[1] || ''
            const businessName = this.uploadData.params.split('_')[0] || ''
            // 调用接口
            fileApi.deleteFile({ keyId, businessName, fileName, personId: USER_PERSON_ID }).then(res => {
              if (res.code !== '0000') {
                this.$notification.error({
                  message: '系统消息',
                  description: '删除附件失败！'
                })
                reject(new Error())
              }
              this.uploadFileList = this.uploadFileList.filter(item => {
                return item.uid !== file.uid
              })
              resolve()
            })
          },
          onCancel () {
            reject(new Error())
          }
        })
      })
    },
    async handlePreviewPic (file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj)
      }
      this.previewImage = file.url || file.preview
      this.previewVisible = true
    }
  }
}
</script>
<style lang="less" scoped>
/* you can make up upload button and sample style by using stylesheets */
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
