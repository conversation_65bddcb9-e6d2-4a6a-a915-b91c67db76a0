<template>
  <a-modal
    :width="400"
    :visible="visible"
    :title="subTitle"
    :confirm-loading="confirmLoading"
    @ok="handleSubmit"
    @cancel="onClose"
  >
    <a-form @submit="handleSubmit" :form="form">
      <a-form-item
        label="供应商"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
      >
        <!-- <a-select
          showSearch
          :filterOption="filterOption"
          v-decorator="['vendorSysId', {rules:[{ required: true, message: '请选择供应商' }]}]"
        >
          <a-select-option v-for="item in vendors" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
        </a-select> -->
        <QuerySelect
          v-decorator="['vendorSysId', {rules:[{ required: true, message: '请选择供应商' }]}]"
          searchLabel="label"
          searchValue="value"
          urlName="/vendor/queryVendorInfoForSelectOption"
          :queryParam="{}"
        />
      </a-form-item>
      <a-form-item
        label="分值"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
      >
        <a-select v-decorator="['score', {rules:[{ required: true, message: '请评分' }]}]">
          <a-select-option :value="100">及格(100)</a-select-option>
          <a-select-option :value="0">不及格(0)</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item
        label="备注"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
      >
        <a-textarea v-decorator="['remarks']"/>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import * as baseApi from '@/api/material/base'
import { requestBuilder } from '@/utils/util'
import { QuerySelect } from '@/components'
import {
  insertSelfVendorScore
} from '@/api/material/vendor'
export default {
  name: 'CreateVenderScore',
  components: {
    QuerySelect
  },
  data () {
    return {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 }
      },
      action: '',
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      vendors: []
    }
  },
  created () {
    this.init()
  },
  computed: {
    subTitle () {
      return this.action === 'insert' ? '新增' : '修改'
    }
  },
  methods: {
    init () {
      baseApi.getCommboxById({ id: 'vendor' }).then(res => {
        this.vendors = res.result
      })
    },
    showModal (action, record = {}) {
      this.action = action
      if (action === 'update') {
        this.$nextTick(() => {
          this.form.setFieldsValue({ ...record })
        })
      }
      this.visible = true
    },
    filterOption (input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    handleSubmit () {
      const { form: { validateFields } } = this
      this.visible = true
      validateFields((errors, values) => {
        if (!errors) {
          this.confirmLoading = true
          insertSelfVendorScore(requestBuilder(this.action, values)).then(res => {
            if (res.code !== '0000') {
              this.$notification.error({
                message: '系统消息',
                description: res.message || '提交失败！'
              })
            } else {
              this.$notification.success({
                message: '系统消息',
                description: '提交成功！'
              })
              this.$emit('search')
              this.onClose()
            }
          }).finally(() => {
            this.confirmLoading = false
          })
        }
      })
    },
    onClose () {
      this.visible = false
      this.confirmLoading = false
      this.action = ''
      this.form.resetFields()
    }
  }
}
</script>
