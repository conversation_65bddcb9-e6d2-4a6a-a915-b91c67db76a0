<template>
  <div>
    <div>
      <div v-for="(item,index) in keysList" :key="item">
        <a-row :gutter="8">
          <a-col
            :md="12"
            :sm="24">
            <a-form-item
              :label="`库${index + 1}`"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select
                v-decorator="[
                  `materialShelfInfoList[${index}][storehouse]`,
                  {
                    initialValue: arr[item] ? arr[item].storehouse : undefined,
                    rules: [{ required: required, message: '请选择库!' }]
                  }
                ]"
                allowClear
                @change="handleStorehouseChange($event,item)"
              >
                <a-select-option
                  v-for="(item, index) in storehouses[item]"
                  :value="item.value"
                  :key="index"
                  :disabled="disabledArray.indexOf(item.value) > -1"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24">
            <a-form-item
              :label="`区${index + 1}`"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select
                v-decorator="[
                  `materialShelfInfoList[${index}][zone]`,
                  {
                    initialValue: arr[item] ? arr[item].zone : undefined,
                    rules: [{ required: required, message: '请选择区!' }]
                  }
                ]"
                allowClear
                @change="handleZoneChange($event,item)"
              >
                <a-select-option
                  v-for="(item, index) in zones[item]"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24">
            <a-form-item
              :label="`架${index + 1}`"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select
                v-decorator="[
                  `materialShelfInfoList[${index}][frame]`,
                  {
                    initialValue: arr[item] ? arr[item].frame : undefined,
                    rules: [{ required: required, message: '请选择架!' }]
                  }
                ]"
                allowClear
                @change="handleFrameChange($event,item)"
              >
                <a-select-option
                  v-for="(item, index) in frames[item]"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24">
            <a-form-item
              :label="`层${index + 1}`"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select
                v-decorator="[
                  `materialShelfInfoList[${index}][floor]`,
                  {
                    initialValue: arr[item] ? arr[item].floor : undefined,
                    rules: [{ required: required, message: '请选择层!' }]
                  }
                ]"
                allowClear
                @change="handleFloorChange($event,item)"
              >
                <a-select-option
                  v-for="(item, index) in floors[item]"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col
            :md="12"
            :sm="24">
            <a-form-item
              :label="`位${index + 1}`"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select
                v-decorator="[
                  `materialShelfInfoList[${index}][bit]`,
                  {
                    initialValue: arr[item] ? arr[item].bit : undefined,
                    rules: [{ required: required, message: '请选择位!' }]
                  }
                ]"
                allowClear
              >
                <a-select-option
                  v-for="(item, index) in bits[item]"
                  :value="item.value"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="2">
            <a-form-item>
              <template v-show="keysList.length > 1">
                <a-button
                  shape="circle"
                  type="danger"
                  icon="minus"
                  size="small"
                  slot="extra"
                  @click="removeRow(item)"
                />
              </template>
            </a-form-item>
          </a-col>
          <a-col :span="2">
            <a-form-item>
              <template>
                <a-button
                  shape="circle"
                  type="primary"
                  icon="plus"
                  size="small"
                  slot="extra"
                  @click="addRow"
                />
              </template>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </div>
  </div>
</template>

<script>
import * as baseApi from '@/api/material/base'
import Vue from 'vue'
import { ORG_ID, PERSON_ID } from '@/store/mutation-types'

const USER_ORG_ID = Vue.ls.get(ORG_ID)
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)
export default {
  name: 'DynamicForm',
  props: {
    arr: {
      type: Array,
      default: function () {
        return []
      }
    },
    isLocation: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      USER_ORG_ID,
      USER_PERSON_ID,
      id: 0,
      keysList: [],
      storehouses: [], // 库
      zones: [], // 区
      frames: [], // 架
      floors: [], // 层
      bits: [], // 位,
      disabledArray: [],
      labelCol: { style: { width: '100px' } },
      required: false,
      wrapperCol: {
        xs: { span: 20 },
        sm: { span: 14 }
      }
    }
  },
  watch: {
    arr: {
      handler (newVal) {
        console.log(newVal)
        this.keysList = []
        this.disabledArray = []
        this.id = 0
        if (newVal.length !== 0) {
          for (const i in newVal) {
            this.keysList = this.keysList.concat(this.id)
            this.id = this.id + 1
            // 初始化仓库锁定
            this.disabledArray[i] = newVal[i].storehouse
          }
        } else {
          this.keysList = this.keysList.concat(0)
        }
      },
      deep: true,
      immediate: true
    }
  },
  created () {
    this.form = this.$form.createForm(this)
    this.init()
  },
  methods: {
    // 初始化
    init () {
      const arr = [0]
      if (this.arr.length !== 0) {
        for (let i = 1; i < (this.arr).length; i++) {
          arr.push(i)
          this.id = this.id + 1
        }
      }
      this.keysList = arr

      this.getBaseData()
    },
    // 移除某行
    removeRow (k) {
      if (this.keysList.length === 1) { // 如果存在可以移除所有行的情况，把条件改为this.keysList.length === 0即可
        return
      }
      this.keysList = this.keysList.filter(item => item !== k)
      this.disabledArray[k] = null
    },
    // 新增一行
    addRow () {
      this.keysList = this.keysList.concat(this.id)
      this.id = this.id + 1
    },
    // 下拉框库-区联动
    handleStorehouseChange (value, item) {
      this.form.setFieldsValue({
        zone: '',
        frame: '',
        floor: '',
        bit: ''
      })
      baseApi
        .getCommboxById({ id: 'zone', sqlParams: { byStorehouseSysId: '1', storehouseSysId: value } })
        .then(res => {
          if (res.code === '0000') {
            this.$set(this.zones, item, res.result)
          }
        })
      // 仓库锁定
      this.disabledArray[item] = value
    },
    // 下拉框区-架联动
    handleZoneChange (value, item) {
      // this.cities = cityData[value]
      // this.secondCity = cityData[value][0]
      this.form.setFieldsValue({
        frame: '',
        floor: '',
        bit: ''
      })
      baseApi.getCommboxById({ id: 'frame', sqlParams: { byZoneSysId: '1', zoneSysId: value } }).then(res => {
        if (res.code === '0000') {
          this.$set(this.frames, item, res.result)
        }
      })
    },
    // 下拉框架-层联动
    handleFrameChange (value, item) {
      // this.cities = cityData[value]
      // this.secondCity = cityData[value][0]
      this.form.setFieldsValue({
        floor: '',
        bit: ''
      })
      baseApi.getCommboxById({ id: 'floor', sqlParams: { byFrameSysId: '1', frameSysId: value } }).then(res => {
        if (res.code === '0000') {
          this.$set(this.floors, item, res.result)
        }
      })
    },
    // 下拉框层-位联动
    handleFloorChange (value, item) {
      // this.cities = cityData[value]
      // this.secondCity = cityData[value][0]
      this.form.setFieldsValue({
        bit: ''
      })
      baseApi.getCommboxById({ id: 'bit', sqlParams: { byFloorSysId: '1', floorSysId: value } }).then(res => {
        if (res.code === '0000') {
          this.$set(this.bits, item, res.result)
        }
      })
    },
    getBaseData () {
      this.storehouses = []
      this.zones = []
      this.frames = []
      this.floors = []
      this.bits = []
      const param = this.isLocation ? { id: 'storehouseDept', sqlParams: { orgId: USER_ORG_ID, personSysId: USER_PERSON_ID } } : { id: 'storehouse' }
      baseApi.getCommboxById(param).then(res => {
        if (res.code === '0000') {
          for (let i = 0; i < 50; i++) {
            this.storehouses.push(res.result)
          }
        }
      })
      const zoneParam = this.isLocation ? { id: 'zoneDept', sqlParams: { isAll: '1', orgId: USER_ORG_ID, personSysId: USER_PERSON_ID } } : { id: 'zone', sqlParams: { isAll: '1' } }
      baseApi.getCommboxById(zoneParam).then(res => {
        if (res.code === '0000') {
          for (let i = 0; i < 50; i++) {
            this.zones.push(res.result)
          }
        }
      })
      const frameParam = this.isLocation ? { id: 'frameDept', sqlParams: { isAll: '1', orgId: USER_ORG_ID, personSysId: USER_PERSON_ID } } : { id: 'frame', sqlParams: { isAll: '1' } }
      baseApi.getCommboxById(frameParam).then(res => {
        if (res.code === '0000') {
          for (let i = 0; i < 50; i++) {
            this.frames.push(res.result)
          }
        }
      })
      const floorParam = this.isLocation ? { id: 'floorDept', sqlParams: { isAll: '1', orgId: USER_ORG_ID, personSysId: USER_PERSON_ID } } : { id: 'floor', sqlParams: { isAll: '1' } }
      baseApi.getCommboxById(floorParam).then(res => {
        if (res.code === '0000') {
          for (let i = 0; i < 50; i++) {
            this.floors.push(res.result)
          }
        }
      })
      const bitParam = this.isLocation ? { id: 'bitDept', sqlParams: { isAll: '1', orgId: USER_ORG_ID, personSysId: USER_PERSON_ID } } : { id: 'bit', sqlParams: { isAll: '1' } }
      baseApi.getCommboxById(bitParam).then(res => {
        if (res.code === '0000') {
          for (let i = 0; i < 50; i++) {
            this.bits.push(res.result)
          }
        }
      })
    }
  }
}
</script>
