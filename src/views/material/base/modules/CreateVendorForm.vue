<template>
  <a-drawer
    :title="subTitle"
    placement="right"
    :width="800"
    @close="handleCancel"
    :visible="visible"
    :getContainer="false"
    :mask="true"
    :bodyStyle="{paddingBottom: '80px'}"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row justify="space-around" align="middle">
          <a-col :span="12">
            <a-form-item
              label="供应商编码:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input :disabled="isApproEnd || this.record.isFromZc === 'Y'" v-decorator="['vendorNum', {rules: [{required: true, message: '请输入供应商编码！'}]}]" />
            </a-form-item>
            <a-form-item
              label="供应商名称:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input :disabled="isApproEnd || this.record.isFromZc === 'Y'" v-decorator="['vendorName', {rules: [{required: true, message: '请输入供应商名称！'}]}]" />
            </a-form-item>
            <a-form-item
              label="中标供应商:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="isBrowse"
                v-decorator="['winTheBid', {rules: [{required: false, message: '请选择'}]}]"
                allowClear
              >
                <a-select-option value="Y">是</a-select-option>
                <a-select-option value="N">否</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item
              label="公司类型:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input :disabled="isBrowse" v-decorator="['type', {rules: [{required: false, message: '请输入公司类型！'}]}]" />
            </a-form-item>
            <a-form-item
              label="税率:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input :disabled="isBrowse" v-decorator="['taxRate', {rules: [{required: false, pattern: new RegExp(/^(([1-9][0-9]{0,1}[.][0-9]{1,4})|([1-9][0-9]{1})|([0][.][0-9]{1}[1-9]{0,3}))$/), message: '请输入税率！'}]}]" />

            </a-form-item>
            <a-form-item
              label="税号:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input :disabled="isBrowse" v-decorator="['taxCode', {rules: [{required: false, message: '请输入税号！'}]}]" />
            </a-form-item>
            <a-form-item
              label="关联业务:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                v-decorator="['relatedBusArr']"
                allowClear
                mode="multiple"
                :disabled="isBrowse"
              >
                <a-select-option
                  v-for="item in typedetail"
                  :value="item.label"
                  :key="item.value"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item
              label="法人代表:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input :disabled="isBrowse" v-decorator="['register', {rules: [{required: false, message: '请输入法人代表！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="市:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input :disabled="isBrowse" v-decorator="['city', {rules: [{required: false, message: '请输入市！'}]}]" />
            </a-form-item>
            <a-form-item
              label="注册资金:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input :disabled="isBrowse" v-decorator="['capital', {rules: [{required: false, message: '请输入注册资金！'}]}]" />
            </a-form-item>
            <a-form-item
              label="工商注册号:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input :disabled="isBrowse || this.record.isFromZc === 'Y'" v-decorator="['busLicense', {rules: [{required: false, message: '请输入工商注册号！'}]}]" />
            </a-form-item>
            <a-form-item
              label="经营范围:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input :disabled="isBrowse" v-decorator="['bsCope', {rules: [{required: false, message: '请输入经营范围！'}]}]" />
            </a-form-item>
            <a-form-item
              label="银行账户编号:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input :disabled="isBrowse" v-decorator="['bankNum', {rules: [{required: false, message: '请输入银行账户！'}]}]" />
            </a-form-item>
            <a-form-item
              label="开户银行:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input :disabled="isBrowse || this.record.isFromZc === 'Y'" v-decorator="['bankAccount', {rules: [{required: false, message: '请输入开户银行！'}]}]" />
            </a-form-item>
            <a-form-item
              label="地址:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input :disabled="isBrowse " v-decorator="['address', {rules: [{required: false, message: '地址不能为空'}]}]" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-divider orientation="left">法务</a-divider>
        <a-row>
          <a-col :span="12">
            <a-form-item
              label="类型:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="isBrowse"
                v-decorator="['fwType', {rules: [{required: false, message: '请选择'}]}]"
                allowClear
              >
                <a-select-option v-for="(item, index) in fwTypes" :value="item.value" :key="index">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="性质:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="isBrowse"
                v-decorator="['fwNature', {rules: [{required: false, message: '请选择'}]}]"
                allowClear
              >
                <a-select-option v-for="(item, index) in fwNatures" :value="item.value" :key="index">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="组织性质:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="isBrowse"
                v-decorator="['fwOrgNature', {rules: [{required: false, message: '请选择'}]}]"
                allowClear
              >
                <a-select-option v-for="(item, index) in fwOrgNatures" :value="item.value" :key="index">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-show="form.getFieldValue('fwOrgNature') === 'QY'">
            <a-form-item
              label="组织性质二级:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="isBrowse"
                v-decorator="['fwOrgNature2', {rules: [{required: false, message: '请选择'}]}]"
                allowClear
              >
                <a-select-option v-for="(item, index) in fwOrgNature2s" :value="item.value" :key="index">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-show="['1', '2'].includes(form.getFieldValue('fwNature'))">
            <a-form-item
              label="是否涉港澳台:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="isBrowse"
                v-decorator="['fwGat', {rules: [{required: false, message: '请选择'}]}]"
                allowClear
              >
                <a-select-option v-for="(item, index) in YNList" :value="item.value" :key="index">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="是否集团内:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="isBrowse"
                v-decorator="['fwIsGroup', {rules: [{required: false, message: '请选择'}]}]"
                allowClear
              >
                <a-select-option v-for="(item, index) in YNList" :value="item.value" :key="index">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="证件类型:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-select
                :disabled="isBrowse"
                v-decorator="['fwZjType', {rules: [{required: false, message: '请选择'}]}]"
                allowClear
              >
                <a-select-option v-for="(item, index) in fwZjTypes" :value="item.value" :key="index">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="证件号码:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input :disabled="isBrowse" v-decorator="['fwZjNo', {rules: [{required: false, message: '请输入！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="tin号:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input :disabled="isBrowse" v-decorator="['fwTin', {rules: [{required: false, message: '请输入！'}]}]" />
            </a-form-item>
          </a-col>
        </a-row>
        <hr v-if="actionFlag === 'update'" style="height:1px;border:none;border-top:1px dashed rgb(213, 217, 220);" >
        <br>
        <a-row justify="space-around" align="middle" v-if="actionFlag === 'update'">
          <a-col :span="12">
            <a-form-item
              label="服务评分:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input type="number" @input="getSum" :disabled="isBrowse" v-decorator="['serviceScore', {rules: [{required: false, pattern: new RegExp(/^\d+$|^\d*\.\d+$/g), message: '请输入分数！'}]}]" />
            </a-form-item>
            <a-form-item
              label="交货时间评分:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input type="number" @input="getSum" :disabled="isBrowse" v-decorator="['deliverytimeScore', {rules: [{required: false, pattern: new RegExp(/^\d+$|^\d*\.\d+$/g), message: '请输入分数！'}]}]" />
            </a-form-item>
            <a-form-item
              label="质量评分:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input type="number" @input="getSum" :disabled="isBrowse" v-decorator="['qualityScore', {rules: [{required: false, pattern: new RegExp(/^\d+$|^\d*\.\d+$/g), message: '请输入分数！'}]}]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="价格评分:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input type="number" @input="getSum" :disabled="isBrowse" v-decorator="['priceScore', {rules: [{required: false, pattern: new RegExp(/^\d+$|^\d*\.\d+$/g), message: '请输入分数！'}]}]" />
            </a-form-item>
            <a-form-item
              label="信誉评分:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input type="number" @input="getSum" :disabled="isBrowse" v-decorator="['creditScore', {rules: [{required: false, pattern: new RegExp(/^\d+$|^\d*\.\d+$/g), message: '请输入分数！'}]}]" />
            </a-form-item>
            <a-form-item
              label="评价年份:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input type="number" :disabled="isBrowse" v-decorator="['scoreYear', {rules: [{required: false, max: 4, min: 4, pattern: new RegExp(/^\d{4}$/), message: '请输入年份！'}]}]" />
            </a-form-item>
            <a-form-item
              v-show="IS_XT"
              label="总分:"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input type="number" disabled v-decorator="['totalScore']" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item
          label="备注:"
          :labelCol="{ span: 3, offset: 2 }"
          :wrapperCol="{ span: 17 }"
          v-if="actionFlag === 'update'"
        >
          <a-input :disabled="isBrowse" v-decorator="['remark', {rules: [{required: false}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
    <div class="drawer-footer">
      <div class="footer-fixed">
        <a-button :style="{marginRight: '8px'}" @click="handleCancel">
          取消
        </a-button>
        <a-button
          v-show="!isBrowse"
          @click="handleSubmit"
          type="primary">
          提交
        </a-button>
      </div>
    </div>
  </a-drawer>
</template>

<script>
import Vue from 'vue'
import { modifyVendorInfo, isVendorScoreExist, modifyVendorScore } from '@/api/material/vendor'
import { requestBuilder, getPermission } from '@/utils/util'
import { ORG_ID, OPERATOR } from '@/store/mutation-types'
import * as baseApi from '@/api/material/base'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
// 温州港
const IS_WZG = USER_ORG_ID.substr(0, 5) === '1.101'
const IS_HJ = USER_ORG_ID === '1.100.131'
const IS_DMY = USER_ORG_ID === '1.122.801'
const IS_XT = USER_ORG_ID === '1.100.118'
export default {
  props: {
    typedetail: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      IS_XT,
      IS_HJ,
      IS_DMY,
      labelCol: {
        // xs: { span: 24 },
        // sm: { span: 8, offset: 2 }
        style: { width: '100px' }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 10 }
      },
      visible: false, // 窗口是否显示
      confirmLoading: false, // 加载图标是否出现
      requestBody: {
        orgId: '',
        operator: '',
        action: '',
        pageNo: 0,
        pageSize: 10,
        param: {}
      },
      params: {},
      paramsTwo: {},
      isScored: '',
      record: [],
      vendorRecord: [
        'vendorNum',
        'vendorName',
        'winTheBid',
        'type',
        'taxRate',
        'taxCode',
        'relatedBusArr',
        'register',
        'currencyCode',
        'city',
        'capital',
        'busLicense',
        'bsCope',
        'bankNum',
        'bankAccount',
        'address'
      ],
      scoreRecord: [
        'serviceScore',
        'deliverytimeScore',
        'qualityScore',
        'priceScore',
        'creditScore',
        'totalScore',
        'scoreYear',
        'remark'
      ],
      selectedRowKeys: [],
      selectedRows: [],
      actionFlag: 'insert',
      isBrowse: false,
      // 是审批完成状态
      isApproEnd: false,
      subTitle: '新增供应商',
      form: this.$form.createForm(this),
      YNList: [
        { value: '1', label: '是' },
        { value: '0', label: '否' }
      ],
      fwTypes: [],
      fwNatures: [],
      fwOrgNatures: [],
      fwOrgNature2s: [],
      fwZjTypes: []
    }
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      baseApi.getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'fw_vendor_type' } }).then(res => {
        if (res.code === '0000') {
          this.fwTypes = res.result
        }
      })
      baseApi.getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'fw_vendor_nature' } }).then(res => {
        if (res.code === '0000') {
          this.fwNatures = res.result
        }
      })
      baseApi.getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'fw_vendor_org_nature' } }).then(res => {
        if (res.code === '0000') {
          this.fwOrgNatures = res.result
        }
      })
      baseApi.getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'fw_vendor_org_nature2' } }).then(res => {
        if (res.code === '0000') {
          this.fwOrgNature2s = res.result
        }
      })
      baseApi.getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'fw_zj_type' } }).then(res => {
        if (res.code === '0000') {
          this.fwZjTypes = res.result
        }
      })
    },
    add () {
      this.subTitle = '新增供应商'
      this.form.resetFields()
      // 根据组织id查询供应商编码
      baseApi.selectVendorNum({ orgId: USER_ORG_ID }).then(res => {
        this.form.setFieldsValue({
          vendorNum: res.result,
          taxRate: 0.13,
          fwGat: '0',
          fwIsGroup: '0'
        })
      })

      this.actionFlag = 'insert'
      this.visible = true
    },
    edit (record) {
      if (!getPermission(this, ['edit'])) {
        this.isBrowse = true
      }
      this.isApproEnd = record.status === 'approval1003'
      this.actionFlag = 'update'
      this.record = record
      this.subTitle = this.isBrowse ? '浏览供应商信息' : '编辑供应商信息'
      this.actionFlag = 'update'
      this.$nextTick(() => {
        this.form.setFieldsValue(
          {
            vendorNum: record.vendorNum,
            vendorName: record.vendorName,
            winTheBid: record.winTheBid,
            type: record.type,
            taxRate: record.taxRate,
            taxCode: record.taxCode,
            relatedBusArr: record.relatedBusArr,
            register: record.register,
            currencyCode: record.currencyCode,
            city: record.city,
            capital: record.capital,
            busLicense: record.busLicense,
            bsCope: record.bsCope,
            bankNum: record.bankNum,
            bankAccount: record.bankAccount,
            address: record.address,
            serviceScore: record.serviceScore || 0,
            deliverytimeScore: record.deliverytimeScore || 0,
            qualityScore: record.qualityScore || 0,
            priceScore: record.priceScore || 0,
            creditScore: record.creditScore || 0,
            totalScore: record.totalScore || 0,
            scoreYear: record.scoreYear,
            fwType: record.fwType,
            fwNature: record.fwNature,
            fwOrgNature: record.fwOrgNature,
            fwOrgNature2: record.fwOrgNature2,
            fwGat: record.fwGat || '0',
            fwIsGroup: record.fwIsGroup || '0',
            fwZjNo: record.fwZjNo,
            fwZjType: record.fwZjType,
            fwTin: record.fwTin,
            remark: record.remark
          })
        // this.form.setFieldsValue(record)
        this.visible = true
      })
    },
    handleSubmit () {
      const { form: { validateFields } } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        console.log(values)
        // 如果输入数据都合规
        // 数据挑选方法
        const pick = (obj, arr) =>
          arr.reduce((iter, val) => {
            if (val in obj) {
              iter[val] = obj[val]
            }
            return iter
          }, {})
        if (!errors) {
          // 供应商字段数据
          this.params = { ...pick(values, this.vendorRecord), ...values }
          this.params.orgId = Vue.ls.get(ORG_ID)
          // 供应商评分字段数据
          this.paramsTwo = pick(values, this.scoreRecord)
          this.paramsTwo.vendorSysId = this.record.vendorSysId
          this.requestBody.orgId = Vue.ls.get(ORG_ID)
          this.requestBody.action = this.actionFlag
          this.requestBody.operator = Vue.ls.get(OPERATOR)
          // 新增供应商
          if (this.actionFlag === 'insert') {
            modifyVendorInfo(requestBuilder(this.actionFlag, this.params, '0', '0'))
              .then(res => {
                if (res.code === '9999') {
                  this.$message.error(res.message)
                } else if (res.code === '0000') {
                  if (IS_WZG && res.message === '插入成功') {
                    this.$info({
                      title: '操作成功',
                      content: '插入数据成功'
                    })
                  } else {
                    this.$message.success(res.message)
                  }
                  this.$emit('ok')
                  this.visible = false
                }
              })
              .catch(err => this.requestFailed(err))
              .finally(() => {
                this.confirmLoading = false
              })
          } else { // 修改
            this.params.vendorSysId = this.record.vendorSysId
            // 判断该供应商评分是否已经存在
            if (this.record.isFromZc !== 'Y') {
              isVendorScoreExist(this.paramsTwo)
                .then(res => {
                  this.isScored = res.message
                  // 评分不存在，供应商评分新增
                  if (this.isScored === 'false') {
                    this.requestBody.param = this.paramsTwo
                    this.requestBody.action = 'insert'
                    modifyVendorScore(this.requestBody)
                      .then(res => {
                        if (res.code === '0000') {
                          this.$message.success('分数' + res.message)
                          this.$emit('ok2')
                        } else {
                          this.$message.error(res.message)
                        }
                      })
                      .catch(err => this.requestFailed(err))
                      .finally(() => {
                        this.confirmLoading = false
                      })
                  } else {
                  // 更新供应商评分
                    this.requestBody.action = 'update'
                    this.requestBody.param = this.paramsTwo
                    modifyVendorScore(this.requestBody)
                      .then(res => {
                        if (res.code === '0000') {
                          this.$message.success(res.message)
                          this.$emit('ok2')
                        } else {
                          this.$message.error(res.message)
                        }
                      })
                      .catch(err => this.requestFailed(err))
                      .finally(() => {
                        this.confirmLoading = false
                      })
                  }
                })
            }
            modifyVendorInfo(requestBuilder(this.actionFlag, this.params, '0', '0'))
              .then(res => {
                if (res.code === '0000') {
                  this.$message.success(res.message)
                  this.$emit('ok')
                  this.visible = false
                } else {
                  this.$message.error(res.message)
                }
              })
              .catch(err => this.requestFailed(err))
              .finally(() => {
                this.confirmLoading = false
              })
          }
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleCancel () {
      this.isApproEnd = false
      this.isBrowse = false
      this.visible = false
    },
    getSum () {
      const obj = this.form.getFieldsValue()
      this.form.setFieldsValue({
        totalScore: +obj.serviceScore + +obj.deliverytimeScore + +obj.qualityScore + +obj.priceScore + +obj.creditScore
      })
    }
  }
}
</script>
