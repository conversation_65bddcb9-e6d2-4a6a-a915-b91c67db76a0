<template>
  <section>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="10">
            <a-col
              :md="4"
              :sm="24"
            >
              <a-form-item label="物资代码">
                <a-input
                  v-model="queryParam.materialNum"
                  placeholder
                  @pressEnter="doSearch(true)"
                />
              </a-form-item>
            </a-col>
            <a-col
              :md="4"
              :sm="24"
            >
              <a-form-item label="物资名称">
                <a-input
                  v-model="queryParam.materialName"
                  placeholder
                  @pressEnter="doSearch(true)"
                />
              </a-form-item>
            </a-col>
            <a-col
              :md="4"
              :sm="24"
            >
              <a-form-item label="规格型号">
                <a-input
                  v-model="queryParam.jmodel"
                  placeholder
                  @pressEnter="doSearch(true)"
                />
              </a-form-item>
            </a-col>
            <a-col
              :md="4"
              :sm="24"
            >
              <a-form-item label="审批状态">
                <a-select
                  v-model="queryParam.status"
                  allowClear
                  @change="doSearch(true)"
                >
                  <a-select-option
                    v-for="(item, index) in status"
                    :value="item.value"
                    :key="index"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :md="4"
              :sm="24"
            >
              <a-form-item label="26大类类别">
                <a-select
                  v-model="queryParam.materialType"
                  allowClear
                  showSearch
                  :filterOption="filterOption"
                  @change="doSearch(true)"
                >
                  <a-select-option
                    v-for="(item, index) in materialType"
                    :value="item.value"
                    :key="index"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :md="4"
              :sm="24"
            >
              <a-form-item label="统购物资">
                <a-select
                  v-model="queryParam.isUniOrder"
                  allowClear
                  @change="doSearch(true)"
                >
                  <a-select-option value="Y">是</a-select-option>
                  <a-select-option value="N">否</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :md="4"
              :sm="24"
            >
              <a-form-item label="补货物资">
                <a-select
                  v-model="queryParam.isResupply"
                  allowClear
                  @change="doSearch(true)"
                >
                  <a-select-option value="Y">是</a-select-option>
                  <a-select-option value="N">否</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :md="4"
              :sm="24"
            >
              <a-form-item label="采购员">
                <a-select
                  v-model="queryParam.buyer"
                  allowClear
                  showSearch
                  :filterOption="filterOption"
                  @change="doSearch(true)"
                >
                  <a-select-option
                    v-for="(item, index) in buyer"
                    :value="item.value"
                    :key="index"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :md="4"
              :sm="24"
            >
              <a-form-item label="仓库保管员">
                <a-select
                  v-model="queryParam.storeman"
                  allowClear
                  showSearch
                  :filterOption="filterOption"
                  @change="doSearch(true)"
                >
                  <a-select-option
                    v-for="(item, index) in storeman"
                    :value="item.value"
                    :key="index"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              v-if="IS_MD_ENV"
              :md="4"
              :sm="24"
            >
              <a-form-item label="创建人">
                <a-select
                  v-model="queryParam.createBy"
                  allowClear
                  showSearch
                  :filterOption="filterOption"
                  @change="doSearch(true)"
                >
                  <a-select-option
                    v-for="(item, index) in requestedBys"
                    :value="item.value"
                    :label="item.label"
                    :key="index"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :md="4"
              :sm="24"
            >
              <a-form-item label="责任部门">
                <a-select
                  v-model="queryParam.deptno"
                  allowClear
                  showSearch
                  :filterOption="filterOption"
                  @change="doSearch(true)"
                >
                  <a-select-option
                    v-for="(item, index) in deptno"
                    :value="item.value"
                    :key="index"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :md="4"
              :sm="24"
            >
              <a-form-item label="可用状态">
                <a-select
                  v-model="queryParam.activity"
                  allowClear
                  @change="doSearch(true)"
                >
                  <a-select-option value="Y">启用</a-select-option>
                  <a-select-option value="N">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <a-button
          type="primary"
          icon="search"
          @click="$refs.table.refresh(true)"
        >查询</a-button>
        <a-button
          v-action:add
          type="primary"
          icon="plus"
          @click="openDrawer('insert')"
        >新增</a-button>
        <a-button
          v-action:addXj
          type="primary"
          icon="plus"
          @click="openDrawer('insert',null,'xjwz')"
        >新增修旧物资</a-button>
        <a-button
          v-action:edit
          type="primary"
          icon="edit"
          @click="doEdit()"
        >修改</a-button>
        <a-button
          v-if="IS_MD_ENV"
          v-action:adminEdit
          type="primary"
          icon="edit"
          @click="allocation"
        >分配采购员</a-button>
        <a-button
          v-action:export
          type="primary"
          icon="download"
          @click="doExport()"
        >导出</a-button>
        <a-button
          v-action:del
          v-show="!existingProcess"
          type="danger"
          icon="delete"
          @click="doBatchDel('')"
        >删除</a-button>
        <a-button
          v-action:report
          type="primary"
          icon="file-add"
          @click="$refs.createModal.add()"
        >上报</a-button>
        <a-button
          v-action:del
          v-show="existingProcess"
          type="danger"
          icon="delete"
          @click="enableOrDisable('N')"
        >禁用</a-button>
        <a-button
          v-action:start
          v-show="existingProcess"
          type="primary"
          icon="delete"
          @click="enableOrDisable('Y')"
        >启用</a-button>
        <a-button
          v-action:approve
          v-show="existingProcess"
          type="primary"
          icon="check-square"
          @click="doApprove"
        >审批</a-button>
        <a-button
          v-action:upOfUni
          type="primary"
          icon="arrow-up"
          @click="doReportOfUniOrder"
        >统购物资上报</a-button>
        <a-button
          v-action:exportQrCode
          type="primary"
          icon="download"
          @click="getQrCodeList()"
        >导出二维码</a-button>
        <a-button
          v-if="IS_SLH_EVN"
          v-action:exportQrCode
          type="primary"
          icon="download"
          @click="getQrCodeListBySearch()"
        >查询导出二维码</a-button>
        <a-button
          v-action:upload
          type="primary"
          icon="download"
          @click="exportTemplate"
        >导出导入模板</a-button>
        <a-upload
          v-action:upload
          class="upload-control"
          :data="uploadData"
          :action="uploadActionMes"
          :headers="uploadHeaders"
          :file-list="uploadFileList"
          :beforeUpload="uploadBeforeUpload"
          @change="uploadChange"
        >
          <a-button>
            <a-icon type="upload" />导入添加
          </a-button>
        </a-upload>
        <!-- <a-upload
          v-action:upload
          class="upload-control"
          :data="uploadData"
          :action="uploadAction"
          :headers="uploadHeaders"
          :file-list="uploadFileList"
          :beforeUpload="uploadBeforeUpload"
          @change="uploadChange"
        >
          <a-button>
            <a-icon type="upload" />导入添加
          </a-button>
        </a-upload> -->
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        size="small"
        rowKey="materialNum"
        :columns="computedColumns"
        :components="components"
        :data="loadMClassData"
        :customRow="rowClick"
        :scroll="materialScroll"
        :rowClassName="() => ''"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        :pageSizeOptions="pageSizeOptions"
        :pageSize="defaultPageSize"
        :showPagination="true"
      >
        <!-- 自定义帅选组件 -->
        <template slot="title">
          <div class="tableTitle">
            <a-popover v-model="popVisible" trigger="click">
              <template slot="content">
                <DropdownChecked :showTitle="false" title="自定义表格排序" tableId="materialTable" v-model="columns" />
              </template>
              <span class="title-icons" >
                <a-icon type="setting" theme="filled" />
              </span>
            </a-popover>
            <span class="title-icons" style="border: none;">
              <a-tooltip>
                <template slot="title">
                  刷新
                </template>
                <a-icon type="sync" :spin="popLoading" @click="reColumns"/>
              </a-tooltip>
            </span>
          </div>
        </template>
        <!-- 表格表自定义显示转换 -->
        <span
          slot="serial"
          slot-scope="text, record, index"
        >{{ index + 1 }}</span>
        <span
          slot="description"
          slot-scope="key"
        ><Ellipsis :length="120">{{ key }}</Ellipsis></span>
        <span
          slot="orderUnit"
          slot-scope="key"
        >{{ (takeTreeByKey(invoiceUnits, key) || {})['label'] }}</span>
        <span
          slot="status"
          slot-scope="key"
        >{{ (takeTreeByKey(status, key) || {})['label'] }}</span>
        <span
          slot="materialClass"
          slot-scope="key"
        >{{ (takeTreeByKey(materialClass, key) || {})['label'] }}</span>
        <span
          slot="buyer"
          slot-scope="key"
        >{{ (takeTreeByKey(buyer, key) || {})['label'] }}</span>
        <span
          slot="storeman"
          slot-scope="key"
        >{{ (takeTreeByKey(storeman, key) || {})['label'] }}</span>
        <span
          slot="deptno"
          slot-scope="key"
        >{{ (takeTreeByKey(deptno, key) || {})['label'] }}</span>
        <span
          slot="isUniOrder"
          slot-scope="text"
        >{{ (text==='Y')?'是':'否' }}</span>
        <span
          slot="isResupply"
          slot-scope="text"
        >{{ (text==='Y')?'是':'否' }}</span>
        <span
          slot="isInformationMaterials"
          slot-scope="text"
        >{{ (text==='Y')?'是':'否' }}</span>
        <span
          slot="isFixedAssets"
          slot-scope="text"
        >{{ (text==='Y')?'是':'否' }}</span>
        <span
          slot="activity"
          slot-scope="text"
        >{{ (text==='Y')?'启用':'禁用' }}</span>
        <span
          slot="isCement"
          slot-scope="text"
        >{{ (text==='Y')?'是':'否' }}</span>
        <span
          slot="createDate"
          slot-scope="text"
        >{{ moment(text) }}</span>
        <span
          slot="action"
          slot-scope="text, record"
        >
          <template>
            <a
              style="margin: 3px;"
              href="javascript: void(0)"
              @click.stop="() => $refs.appendix.openUploadDrawer(record)"
            >附件</a>
            <a
              style="margin: 3px;"
              href="javascript: void(0)"
              v-show="existingProcess"
              @click.stop="openApproRecordDrawer(record)"
            >审批记录</a>
          </template>
        </span>
      </s-table>
      <!-- 引入模式对话框组件 -->
      <!-- 侧边滑动栏 物资代码新增、修改 -->
      <!-- 审批弹窗 -->
      <template>
        <a-drawer
          :title="subTitleApprove"
          placement="right"
          @close="handleCancelApprove"
          :visible="visibleApprove"
          :getContainer="false"
          :maskClosable="false"
          :mask="true"
          :width="350"
        >
          <a-spin :spinning="approveLoading">
            <a-form
              :form="formApprove"
              layout="horizontal"
              style="position: relative; z-index: 0"
            >
              <a-form-item
                label="审批的物资代码:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-show="false"
                  v-decorator="['materialSysId', {rules: [{required: false, message: '1'}]}]"
                />
                <a-input
                  v-decorator="['materialNum', {rules: [{required: false, message: '1'}]}]"
                />
              </a-form-item>
              <a-form-item
                label="审批操作:"
                :labelCol="labelColApprove"
                :wrapperCol="wrapperColApprove"
              >
                <a-select v-model="approveParam.action">
                  <a-select-option value="1">同意</a-select-option>
                  <a-select-option
                    value="0"
                    :disabled="approveReturnDisabled"
                  >退回</a-select-option>
                  <a-select-option
                    value="9"
                    :disabled="approveCancelDisabled"
                  >取消</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item
                label="审批意见:"
                :labelCol="labelColApprove"
                :wrapperCol="wrapperColApprove"
              >
                <a-textarea
                  placeholder="最多可输入200个文本"
                  v-model="approveParam.approveSuggest"
                  :autoSize="{ minRows: 3 }"
                  :maxLength="200"
                />
              </a-form-item>
              <a-form-item
                v-if="approveParam.showApprove && approveParam.action !== '9'"
                label="下个审批人:"
                :labelCol="labelColApprove"
                :wrapperCol="wrapperColApprove"
              >
                <a-select
                  v-model="approveParam.selectApprove"
                  allowClear
                >
                  <a-select-option
                    v-for="(item, index) in approveList"
                    :value="item.value"
                    :key="index"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-form>
          </a-spin>
          <div class="drawer-footer">
            <div class="footer-fixed">
              <a-button @click="handleCancelApprove">返回</a-button>
              <a-button
                type="primary"
                :loading="approveLoading"
                @click="approveMaterial"
              >提交审批</a-button>
            </div>
          </div>
        </a-drawer>
      </template>
    </a-card>
    <appendix
      ref="appendix"
      businessName="material"
      keyId="materialSysId"
    />
    <approve-records-drawer
      ref="approveRecordsDrawer"
      :recordSysId="materialSysId"
      origin="material"
    />
    <MaterialEdit
      ref="MaterialEdit"
      @hiddenMaterialDrawer="hiddenMaterialDrawer"
      @doSearch="doSearch"
    />
    <PurchaseAllocation
      ref="PurchaseAllocation"
      @doSearch="doSearch"
    />
  </section>
</template>
<script>
import { OPERATOR, ORG_ID, PERSON_ID, ACCESS_TOKEN, ROLE_NAME, DEPT_NAME } from '@/store/mutation-types'
import { STable, Ellipsis, DropdownChecked } from '@/components'
import * as materialApi from '@/api/material/material'
import { requestBuilder, takeTreeByKey } from '@/utils/util'
import * as baseApi from '@/api/material/base'
import * as uniOrderApi from '@/api/material/uniOrder'
import * as prlineApi from '@/api/material/prline'
import Vue from 'vue'
import Appendix from '@/views/system/components/Appendix'
import MaterialEdit from './modules/MaterialEdit'
import { tableMixin } from '@/utils/tableMixin'
import ApproveRecordsDrawer from '@/views/system/components/ApproveRecordsDrawer'
import PurchaseAllocation from './modules/PurchaseAllocation'
// 操作人 userNo
const USER_OPERATOR = Vue.ls.get(OPERATOR)

const USER_ORG_ID = Vue.ls.get(ORG_ID)
// 操作人 userPersonId
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)
const USER_ROLE_NAME = Vue.ls.get(ROLE_NAME)
// 运行环境 ENV
const IS_JYS_ENV = USER_ORG_ID === '1.100.117'
const IS_MD_ENV = USER_ORG_ID === '1.100.104'

// 温州港
const IS_WZG = USER_ORG_ID.substr(0, 5) === '1.101'
const IS_WZG_LW = USER_ORG_ID === '1.101.106'
// 嘉兴乍浦
const IS_JX_ENV = USER_ORG_ID === '1.102'
// 北一集司
const IS_BYJ_ENV = USER_ORG_ID === '1.100.101'
// 南京明州
const IS_NJMZ_ENV = USER_ORG_ID === '1.100.109'
// 铁司
const IS_TS_EVN = USER_ORG_ID === '1.100.122'
const IS_SZXD_ENV = USER_ORG_ID === '1.100.106'
const IS_SLH_EVN = USER_ORG_ID === '1.100.111'
const IS_HJ_ENV = USER_ORG_ID === '1.100.131'
const IS_DMY_ENV = USER_ORG_ID === '1.122.801'
const IS_YZ_ENV = USER_ORG_ID === '1.100.107'
export default {
  name: 'Material',
  components: {
    STable,
    Ellipsis,
    Appendix,
    ApproveRecordsDrawer,
    DropdownChecked,
    MaterialEdit,
    PurchaseAllocation
  },
  mixins: [tableMixin],
  data () {
    this.components = {
      header: {
        cell: (h, props, children) => {
          const { key, ...restProps } = props
          const col = this.columns.find(col => {
            const k = col.dataIndex || col.key
            return k === key
          })

          if (!col || !col.width) {
            return h('th', { ...restProps }, [...children])
          }

          const dragProps = {
            key: col.dataIndex || col.key,
            class: 'table-draggable-handle',
            attrs: {
              w: 10,
              x: col.width,
              z: 1,
              axis: 'x',
              draggable: true,
              resizable: false
            },
            on: {
              dragging: (x, y) => {
                col.width = Math.max(x, 1)
              }
            }
          }
          const drag = h('vue-draggable-resizable', { ...dragProps })
          return h('th', { ...restProps, class: 'resize-table-th' }, [...children, drag])
        }
      }
    }
    return {
      // RUN_ENV
      USER_PERSON_ID,
      USER_ORG_ID,
      IS_WZG,
      IS_YZ_ENV,
      IS_JYS_ENV,
      IS_JX_ENV,
      IS_BYJ_ENV,
      IS_NJMZ_ENV,
      IS_TS_EVN,
      IS_SLH_EVN,
      IS_MD_ENV,
      IS_SZXD_ENV,
      IS_WZG_LW,
      IS_HJ_ENV,
      IS_DMY_ENV,
      takeTreeByKey, // 用于将后台返回的数据（例如ID号）转换成对应的内容（id对应的名称）
      // 是否是统购物资
      IS_UNI_ORDER: 'N',
      popVisible: false,
      popLoading: false,
      // 是否是物资名称修改人
      IS_NAME_UPDATE: false,
      // 上传添加所需
      uploadData: {
        orgId: USER_ORG_ID,
        personId: USER_PERSON_ID,
        createBy: USER_PERSON_ID
      },
      uploadAction: process.env.VUE_APP_API_BASE_URL + '/material/importMaterial',
      uploadActionMes: process.env.VUE_APP_API_BASE_URL + '/material/importMaterialMes',
      uploadHeaders: {
        token: Vue.ls.get(ACCESS_TOKEN)
      },
      uploadFileList: [],
      uploadLoading: false,
      uploadPercent: 30,
      uploadChange: options => {
        if (options.file && ['uploading'].includes(options.file.status)) {
          this.uploadPercent = options.file.percent || 0
          this.uploadLoading = true
          return
        }
        if (options.file && ['removed', 'error'].includes(options.file.status)) {
          this.uploadLoading = false
          this.uploadFileList = []
          return
        }
        if (options.file && options.file.status === 'done') {
          if (options.file.response && options.file.response.code === '0000') {
            this.$notification.success({
              message: '系统消息',
              description: options.file.response.message
            })
            this.$refs.table.refresh(true)
          } else {
            this.$notification.error({
              message: '系统消息',
              description: options.file.response.message
            })
          }
          this.uploadLoading = false
          this.uploadFileList = []
          this.uploadPercent = 0
        }
      },
      uploadBeforeUpload: file => {
        if (![
          // Excel
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-excel'
        ].includes(file.type)) {
          this.$message.error('仅支持上传excel附件')
          return false
        }
        if (file.size / 1024 / 1024 > 20) {
          this.$message.error('上传附件大小不可超过20M')
          return false
        }
        this.uploadFileList.push(file)
      },
      // 分页参数
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20'],
      mdl: {},
      // 高级搜索 展开/关闭
      advanced: false,
      saveLoading: false,
      // 查询参数
      queryParam: {
        activity: 'Y',
        materialNum: '',
        materialName: '',
        jmodel: '',
        status: ''
      },
      // 物资类别表头定义
      columns: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' },
          ellipsis: true,
          check: true,
          sorter: false,
          align: 'center',
          fixed: false,
          edit: false,
          width: 50
        },
        {
          title: '物资代码',
          dataIndex: 'materialNum',
          ellipsis: true,
          check: true,
          sorter: false,
          align: 'center',
          fixed: false,
          edit: false,
          width: 100
        },
        ...(IS_MD_ENV ? [{
          title: '二级分类',
          dataIndex: 'materialClass2',
          ellipsis: true,
          check: true,
          sorter: false,
          align: 'center',
          fixed: false,
          edit: false,
          width: 100
        }] : []),
        {
          title: '物资名称',
          dataIndex: 'materialName',
          scopedSlots: { customRender: 'description' },
          ellipsis: true,
          check: true,
          sorter: false,
          align: 'center',
          fixed: false,
          edit: false,
          width: 120
        },
        {
          title: '单位',
          dataIndex: 'orderUnit',
          scopedSlots: { customRender: 'orderUnit' },
          ellipsis: true,
          check: true,
          sorter: false,
          align: 'center',
          fixed: false,
          edit: false,
          width: 60
        },
        {
          title: '审批状态',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' },
          ellipsis: true,
          check: true,
          sorter: false,
          align: 'center',
          fixed: false,
          edit: false,
          width: 120
        },
        {
          title: '26大类类别',
          dataIndex: 'materialClassByName',
          scopedSlots: { customRender: 'description' },
          ellipsis: true,
          check: true,
          sorter: false,
          align: 'center',
          fixed: false,
          edit: false,
          width: 120
        },
        ...(IS_SZXD_ENV ? [
          {
            title: '财务科目',
            dataIndex: 'materialClass',
            scopedSlots: { customRender: 'materialClass' },
            ellipsis: true,
            check: true,
            sorter: false,
            align: 'center',
            fixed: false,
            edit: false,
            width: 120
          }] : [
          {
            title: '物资类别',
            dataIndex: 'materialClass',
            scopedSlots: { customRender: 'materialClass' },
            ellipsis: true,
            check: true,
            sorter: false,
            align: 'center',
            fixed: false,
            edit: false,
            width: 120
          }
        ]),
        ...(IS_NJMZ_ENV ? [{
          title: '参考单价',
          dataIndex: 'refCost',
          ellipsis: true,
          check: true,
          sorter: false,
          align: 'center',
          fixed: false,
          edit: false,
          width: 100
        }] : []),
        {
          title: '统购',
          dataIndex: 'isUniOrder',
          scopedSlots: { customRender: 'isUniOrder' },
          ellipsis: true,
          check: true,
          sorter: false,
          align: 'center',
          fixed: false,
          edit: false,
          width: 80
        },
        {
          title: '统购代码',
          dataIndex: 'uniOrderNum',
          ellipsis: true,
          check: true,
          sorter: false,
          align: 'center',
          fixed: false,
          edit: false,
          width: 80
        },
        {
          title: '补库',
          dataIndex: 'isResupply',
          scopedSlots: { customRender: 'isResupply' },
          ellipsis: true,
          check: true,
          sorter: false,
          align: 'center',
          fixed: false,
          edit: false,
          width: 80
        },
        ...(IS_JX_ENV ? [
          { title: '信息',
            dataIndex: 'isInformationMaterials',
            scopedSlots: { customRender: 'isInformationMaterials' },
            ellipsis: true,
            check: true,
            sorter: false,
            align: 'center',
            fixed: false,
            edit: false,
            width: 100
          }
        ] : []),
        {
          title: '创建人',
          dataIndex: 'createByName',
          scopedSlots: { customRender: 'description' },
          ellipsis: true,
          check: true,
          sorter: false,
          align: 'center',
          fixed: false,
          edit: false,
          width: 120
        },
        {
          title: '采购员',
          dataIndex: 'buyerByName',
          scopedSlots: { customRender: 'description' },
          ellipsis: true,
          check: true,
          sorter: false,
          fixed: false,
          align: 'center',
          edit: false,
          width: 120
        },
        {
          title: '品牌',
          dataIndex: 'brand',
          scopedSlots: { customRender: 'description' },
          ellipsis: true,
          check: true,
          sorter: false,
          align: 'center',
          fixed: false,
          edit: false,
          width: 150
        },
        {
          title: '规格型号',
          dataIndex: 'jmodel',
          scopedSlots: { customRender: 'description' },
          ellipsis: true,
          check: true,
          align: 'center',
          sorter: false,
          fixed: false,
          edit: false,
          width: 150
        },
        ...(IS_BYJ_ENV ? [
          {
            title: '参考单价',
            dataIndex: 'refCost',
            ellipsis: true,
            check: true,
            sorter: false,
            align: 'center',
            fixed: false,
            edit: false,
            width: 150
          }
        ] : []),
        {
          title: '仓管员',
          dataIndex: 'storemanByName',
          scopedSlots: { customRender: 'description' },
          ellipsis: true,
          check: true,
          sorter: false,
          align: 'center',
          fixed: false,
          edit: false,
          width: 120
        },
        {
          title: '职能部门',
          dataIndex: 'deptnoByName',
          scopedSlots: { customRender: 'description' },
          ellipsis: true,
          check: true,
          sorter: false,
          align: 'center',
          fixed: false,
          edit: false,
          width: 120
        },
        {
          title: '可用状态',
          dataIndex: 'activity',
          scopedSlots: { customRender: 'activity' },
          ellipsis: true,
          check: true,
          sorter: false,
          align: 'center',
          fixed: false,
          edit: false,
          width: 100
        },
        ...(IS_HJ_ENV ? [
          { title: '水泥混合料',
            dataIndex: 'isCement',
            scopedSlots: { customRender: 'isCement' },
            ellipsis: true,
            check: true,
            sorter: false,
            align: 'center',
            fixed: false,
            edit: false,
            width: 100
          }
        ] : []),
        ...(IS_WZG ? [
          { title: '创建人',
            dataIndex: 'createByName',
            ellipsis: true,
            check: true,
            sorter: false,
            fixed: false,
            align: 'center',
            edit: false,
            width: 100
          },
          { title: '修改人',
            dataIndex: 'modifyByName',
            ellipsis: true,
            check: true,
            sorter: false,
            align: 'center',
            fixed: false,
            edit: false,
            width: 100
          }
        ] : []),
        {
          title: '创建时间',
          dataIndex: 'createDate',
          scopedSlots: { customRender: 'createDate' },
          ellipsis: true,
          check: true,
          sorter: false,
          align: 'center',
          fixed: false,
          edit: false,
          width: 100
        },
        {
          title: '操作',
          dataIndex: 'action',
          check: true,
          sorter: false,
          align: 'center',
          edit: false,
          width: 100,
          fixed: 'right',
          disabled: true,
          scopedSlots: { customRender: 'action', filterDropdown: 'filterDropdown' }
        }
      ],
      materialScroll: {
        x: '100%',
        scrollToFirstRowOnChange: false
      },
      // 加载基础代码类别数据
      loadMClassData: parameter => {
        const param = requestBuilder(
          '',
          Object.assign(this.queryParam),
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField === 'jmodel' ? 'j_model' : parameter.sortField,
          parameter.sortOrder
        )
        return materialApi.getMaterialInfo(param).then(res => {
          return res.result
        })
      },
      // 行选择参数
      options: {
        alert: {
          show: false,
          clear: () => {
            this.selectedRowKeys = []
          }
        },
        rowSelection: {
          onChange: this.onSelectChange
        }
      },
      selectedRowKeys: [],
      selectedRows: [],
      // 滑动抽屉参数
      form: this.$form.createForm(this),
      buyer: [], // 采购员
      materialProperties: [], // 物资属性
      storeman: [], // 仓库保管员
      status: [], // 审批状态
      materialType: [], // 26大类类别
      materialClass: [], // 物资类别
      szMaterials: [], // 苏州工程物资分类
      jxMaterialClass: [], // 嘉兴港物资类别
      deptno: [], // 责任部门
      typedetail: [], // 类型细目
      storehouses: [], // 库
      zones: [], // 区
      frames: [], // 架
      floors: [], // 层
      bits: [], // 位
      lottype: [], // 批次类型
      invoiceUnits: [], // 计量单位
      belongToSites: [], // 下拉框选项 - 所在单位
      materialQrCodeUrl: false, // 二维码url
      visible: false,
      xjStatus: '',
      visibleApprove: false,
      subTitle: '新增物资代码',
      subTitleApprove: '物资代码审批', // 审批弹窗名
      IsProhibit: '', // 判断物资代码弹窗的字段是否为只读
      IsEDIT: '', // 非新增和申请人发阶段都不可修改物资信息
      IsEDITYZ: true, // yz物资编辑
      IsShowQr: '', // 判断二维码是否显示
      IsApprove: '', // 判断选择的物资是否有权审批
      approveReturnDisabled: false, // 判断审批是否能退回操作
      approveCancelDisabled: false, // 判断审批是否能取消操作
      existingProcess: false, // 判断有无流程
      materialSysId: '',
      // 审批界面选项
      approveList: [],
      approveParam: {
        showApprove: false,
        selectMode: '',
        materialSysId: '',
        materialNum: '',
        approveSuggest: '',
        selectRfqlineNum: [],
        selectApprove: '',
        action: '1'
      },
      // 物资代码弹窗的样式
      labelCol: {
        xs: { span: 28 },
        sm: { span: 9, offset: 0 }
      },
      wrapperCol: {
        xs: { span: 20 },
        sm: { span: 14 }
      },
      // 审批弹窗的样式
      labelColApprove: {
        xs: { span: 24 },
        sm: { span: 9, offset: 0 }
      },
      wrapperColApprove: {
        xs: { span: 24 },
        sm: { span: 14 }
      },
      // 审批表单
      formApprove: this.$form.createForm(this),
      // 审批记录
      materialNumRecords: {},
      currentPrlineNum: '',
      approveLoading: false,
      // 物资代码主表双击事件
      rowClick: record => ({
        on: {
          // 点击事件
          dblclick: () => {
            if (this.roleName) {
              this.IS_UNI_ORDER = record.isUniOrder
              this.openDrawer('edit', record)
            }
          }
        }
      }),
      // 子抽屉设置
      childDrawerTitle: '',
      childDrawerVisible: false,
      // 要展示的子抽屉
      SelectOfChildDrawer: '',
      // 统购物资子抽屉设置
      uniOrderSelectedRows: [],
      uniOrderSelectedRowKeys: [],
      uniOrderClearSelection: false,
      uniOrderPageSizeOptions: ['10', '20'],
      uniOrderDefaultPageSize: 10,
      // 统购物资选择行
      uniOrderOptions: {
        rowSelection: {
          type: 'radio',
          selectedRowKeys: this.uniOrderSelectedRowKeys,
          onChange: this.onUniOrderSelectChange
        }
      },
      // 统购物资列表
      uniOrderColumns: [
        {
          title: '统购物资名称',
          ellipsis: true,
          dataIndex: 'uniOrderName'
        },
        {
          title: '规格型号',
          ellipsis: true,
          dataIndex: 'model'
        },
        {
          title: '计量单位',
          ellipsis: true,
          dataIndex: 'orderUnit'
        },
        {
          title: '统购物资代码',
          ellipsis: true,
          dataIndex: 'uniOrderNum'
        },
        {
          title: '统购物资类别',
          ellipsis: true,
          dataIndex: 'uniOrderClass'
        },
        {
          title: '预估单价',
          ellipsis: true,
          dataIndex: 'refCost'
        }
      ],
      // 统购查询条件
      uniOrderParam: {
        activity: 1,
        uniOrderName: '',
        model: '',
        orderUnit: '',
        uniOrderNum: '',
        uniOrderClass: ''
      },
      loadUniOrderData: parameter => {
        const param = requestBuilder('', Object.assign(this.uniOrderParam), parameter.pageNo, parameter.pageSize)
        return uniOrderApi.getUniOrderInfo(param).then(res => {
          if (res.code === '0000') {
            return res.result
          } else {
            this.$message.error(res.message)
          }
        })
      },
      roleName: '',
      requestedBys: []
    }
  },
  filters: {},
  created () {
    // 初始化选项获取
    this.initOptions()
  },
  computed: {
    // 新增可领数
    computedColumns () {
      const arr = this.columns.filter(item => {
        return item.check
      })
      return arr
    }
  },
  mounted () {
    this.initTable('columns', 'materialTable')
  },
  methods: {
    reColumns () {
      this.popLoading = true
      this.$store.dispatch('SetTodoTableGroup', { USER_ORG_ID, USER_PERSON_ID })
      setTimeout(() => {
        this.initTable('columns', 'materialTable')
        this.popLoading = false
      }, 500)
    },
    // 初始化下拉框数据获取
    initOptions () {
      baseApi.getCommboxById({ id: 'personByRoleName', sqlParams: { byOrgId: '1' } })
        .then(res => {
          this.requestedBys = res.result
        })
      baseApi.existingProcess({ origin: 'material', orgId: USER_ORG_ID }).then(res => {
        this.existingProcess = res.result
      })
      // materialApi.existingProcess({ orgId: USER_ORG_ID }).then(res => {
      //  this.existingProcess = res.result
      // })
      baseApi
        .getCommboxById({ id: 'materialProperties' }).then(res => {
          if (res.code === '0000') {
            this.materialProperties = res.result
          }
        })
      if (IS_BYJ_ENV) {
        baseApi
          .getCommboxById({ id: 'checkNameUpdate', sqlParams: { personId: USER_PERSON_ID } }).then(res => {
            if (res.code === '0000') {
              this.IS_NAME_UPDATE = res.result[0].label === 'Y'
            }
          })
      }
      baseApi
        .getCommboxById({ id: 'personByRoleName', sqlParams: { byRoleName: '1', roleName: '采购员' } })
        .then(res => {
          const arr = this.buyer.map(item => item.value)
          res.result.forEach(v => {
            if (!arr.includes(v.value)) {
              this.buyer.push(v)
            }
          })
        })
      if (IS_JYS_ENV) {
        baseApi
          .getCommboxById({ id: 'personByRoleName', sqlParams: { byRoleName: '1', roleName: '修理站站长' } })
          .then(res => {
            const arr = this.buyer.map(item => item.value)
            res.result.forEach(v => {
              if (!arr.includes(v.value)) {
                this.buyer.push(v)
              }
            })
          })
      }
      baseApi.getTreeById({ id: 'appro' }).then(res => {
        if (res.code === '0000') {
          this.status = res.result
        }
      })
      baseApi.getCommboxById({ id: IS_JX_ENV ? 'matrtype' : 'matrtypenew' }).then(res => {
        if (res.code === '0000') {
          this.materialType = res.result
        }
      })
      if (IS_WZG) {
        baseApi.getTreeById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'wg_material_class' } }).then(res => {
          if (res.code === '0000') {
            this.disableSelectable(res.result)
            this.materialClass = res.result
          }
        })
      } else if (IS_MD_ENV) {
        baseApi.getTreeById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'md_material_class' } }).then(res => {
          if (res.code === '0000') {
            this.disableSelectable(res.result)
            this.materialClass = res.result
          }
        })
      } else if (IS_SZXD_ENV) {
        baseApi.getTreeById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'sz_material_class' } }).then(res => {
          if (res.code === '0000') {
            this.disableSelectable(res.result)
            this.materialClass = res.result
          }
        })
      } else {
        baseApi.getCommboxById({ id: 'materialClass' }).then(res => {
          if (res.code === '0000') {
            this.materialClass = res.result
          }
        })
      }
      if (IS_JX_ENV) {
        baseApi.getTreeById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'jx_material_class' } }).then(res => {
          if (res.code === '0000') {
            this.disableSelectable(res.result)
            this.jxMaterialClass = res.result
          }
        })
      }

      if (IS_SZXD_ENV) {
        baseApi.getTreeById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'sz_material' } }).then(res => {
          if (res.code === '0000') {
            this.disableSelectable(res.result)
            this.szMaterials = res.result
          }
        })
      }
      baseApi
        .getCommboxById({ id: 'personByRoleName', sqlParams: { byRoleName: '1', roleName: '仓库管理员' } })
        .then(res => {
          if (res.code === '0000') {
            this.storeman = res.result
            const index = this.storeman.map(item => item.value).indexOf(USER_PERSON_ID)
            if (index !== -1) {
              [this.storeman[0], this.storeman[index]] = [this.storeman[index], this.storeman[0]]
            }
          }
        })
      baseApi.getCommboxById({ id: 'typedetail' }).then(res => {
        if (res.code === '0000') {
          this.typedetail = res.result
        }
      })
      baseApi.getCommboxById({ id: 'lottype' }).then(res => {
        if (res.code === '0000') {
          this.lottype = res.result
        }
      })
      baseApi.getCommboxById({ id: 'dept', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.deptno = res.result
        }
      })
      baseApi.getCommboxById({ id: 'unit' }).then(res => {
        if (res.code === '0000') {
          this.invoiceUnits = res.result
        }
      })
      baseApi.getCommboxById({ id: 'storehouse' }).then(res => {
        if (res.code === '0000') {
          this.storehouses = res.result
        }
      })
      baseApi.getCommboxById({ id: 'zone', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.zones = res.result
        }
      })
      baseApi.getCommboxById({ id: 'frame', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.frames = res.result
        }
      })
      baseApi.getCommboxById({ id: 'floor', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.floors = res.result
        }
      })
      baseApi.getCommboxById({ id: 'bit', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.bits = res.result
        }
      })
      // 所在单位
      if (IS_JYS_ENV) {
        baseApi.getCommboxById({ id: 'belongToSite' }).then(res => {
          if (res.code === '0000') {
            this.belongToSites = res.result
          }
        })
      }
      // 获取当前角色名(获取计划员和管理员)
      baseApi.getPersonRoleName(requestBuilder('', Object.assign({}, {}), '', '')).then(res => {
        if (res.code === '0000') {
          if (res.result) {
            res.result.forEach(item => {
              if (item.indexOf('计划员') !== -1 || item.indexOf('管理员') !== -1) {
                this.roleName = item
              }
            })
          }
        }
      })
    },
    filterOption (input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    // 下拉框物资细目-物资类别联动
    handletypeDetailChange (value) {
      if (value === 'type_detail_1') {
        this.form.setFieldsValue({
          materialClass: 'material_class_1'
        })
      } else if (value === 'type_detail_2') {
        this.form.setFieldsValue({
          materialClass: 'material_class_4'
        })
      } else if (value === 'type_detail_3') {
        this.form.setFieldsValue({
          materialClass: 'material_class_3'
        })
      } else if (value === 'type_detail_4') {
        this.form.setFieldsValue({
          materialClass: 'material_class_2'
        })
      }
    },
    // 下拉框库-区联动
    handleStorehouseChange (value) {
      // this.cities = cityData[value]
      // this.secondCity = cityData[value][0]
      this.form.setFieldsValue({
        zone: '',
        frame: '',
        floor: '',
        bit: ''
      })
      this.zones = []
      baseApi
        .getCommboxById({ id: 'zone', sqlParams: { byStorehouseSysId: '1', storehouseSysId: value } })
        .then(res => {
          if (res.code === '0000') {
            // this.form.setFieldsValue({
            //   zone: res.result.length > 0 ? res.result[0].value : ''
            // })
            this.zones = res.result
          }
        })
    },
    // 下拉框区-架联动
    handleZoneChange (value) {
      // this.cities = cityData[value]
      // this.secondCity = cityData[value][0]
      this.form.setFieldsValue({
        frame: '',
        floor: '',
        bit: ''
      })
      this.frames = []
      baseApi.getCommboxById({ id: 'frame', sqlParams: { byZoneSysId: '1', zoneSysId: value } }).then(res => {
        if (res.code === '0000') {
          // this.form.setFieldsValue({
          //   zone: res.result.length > 0 ? res.result[0].value : ''
          // })
          this.frames = res.result
        }
      })
    },
    // 下拉框架-层联动
    handleFrameChange (value) {
      // this.cities = cityData[value]
      // this.secondCity = cityData[value][0]
      this.form.setFieldsValue({
        floor: '',
        bit: ''
      })
      this.floors = []
      baseApi.getCommboxById({ id: 'floor', sqlParams: { byFrameSysId: '1', frameSysId: value } }).then(res => {
        if (res.code === '0000') {
          // this.form.setFieldsValue({
          //   zone: res.result.length > 0 ? res.result[0].value : ''
          // })
          this.floors = res.result
        }
      })
    },
    // 下拉框层-位联动
    handleFloorChange (value) {
      // this.cities = cityData[value]
      // this.secondCity = cityData[value][0]
      this.form.setFieldsValue({
        bit: ''
      })
      this.bits = []
      baseApi.getCommboxById({ id: 'bit', sqlParams: { byFloorSysId: '1', floorSysId: value } }).then(res => {
        if (res.code === '0000') {
          // this.form.setFieldsValue({
          //   zone: res.result.length > 0 ? res.result[0].value : ''
          // })
          this.bits = res.result
        }
      })
    },
    handleEdit (record) {
      this.$refs.createModal.edit(record)
    },
    // 物资税率改变
    handletaxrateBlur (value) {
      // 根据税后单价和税率计算税前单价
      if (
        this.form.getFieldValue('taxrate') &&
        this.form.getFieldValue('taxrate') &&
        this.form.getFieldValue('unitcostAfterTax') &&
        this.form.getFieldValue('unitcostAfterTax')
      ) {
        this.form.setFieldsValue({
          refCost: this.form.getFieldValue('unitcostAfterTax') / ((+this.form.getFieldValue('taxrate') || 0) + 1)
        })
      }
    },
    // 物资参考单价（税前单价）变化
    handlerefCostBlur () {
      // 根据税前单价和税率计算税后单价
      if (
        this.form.getFieldValue('refCost') === Number(0) &&
        this.form.getFieldValue('refCost') === Number(0)
      ) {
        this.form.setFieldsValue({
          unitcostAfterTax: Number(0)
        })
      } else if (
        this.form.getFieldValue('refCost') &&
        this.form.getFieldValue('refCost') &&
        this.form.getFieldValue('taxrate') &&
        this.form.getFieldValue('taxrate')
      ) {
        if (this.form.getFieldValue('refCost') === '0') {
          this.form.setFieldsValue({
            unitcostAfterTax: '0'
          })
        } else {
          this.form.setFieldsValue({
            unitcostAfterTax: this.form.getFieldValue('refCost') * ((+this.form.getFieldValue('taxrate') || 0) + 1)
          })
        }
      }
    },
    // 物资税后单价改变
    handleunitcostAfterTaxBlur () {
      // 根据税后单价和税率计算税前单价
      if (
        this.form.getFieldValue('unitcostAfterTax') &&
        this.form.getFieldValue('unitcostAfterTax') &&
        this.form.getFieldValue('taxrate') &&
        this.form.getFieldValue('taxrate')
      ) {
        this.form.setFieldsValue({
          refCost: this.form.getFieldValue('unitcostAfterTax') / ((+this.form.getFieldValue('taxrate') || 0) + 1)
        })
      }
    },
    // 禁用下拉框父类选项
    disableSelectable (array) {
      for (const item of array) {
        if (item.children && item.children.length > 0) {
          item.selectable = false
          this.disableSelectable(item.children)
        }
      }
    },
    // 删除物资代码
    doBatchDel (record) {
      if (this.visible) {
        this.$message.error('新增/修改模式下，不可进行删除操作！')
        return
      }
      if (this.visibleApprove) {
        this.$message.error('审批模式下，不可进行删除操作！')
        return
      }
      const materialSysIds = []
      if (record !== '') {
        materialSysIds.push(record.materialSysId)
      } else {
        if (this.selectedRows.length > 0) {
          for (let i = 0; i < this.selectedRows.length; i++) {
            materialSysIds.push(this.selectedRows[i].materialSysId)
            if (
              !(
                this.selectedRows[i].status === 'approval1011' ||
                this.selectedRows[i].status === 'approval1009' ||
                this.selectedRows[i].status === 'approval1004'
              )
            ) {
              this.$message.error('无法删除审批流程中的物资')
              return
            }
          }
        } else {
          this.$message.error('未选中任何记录')
          return
        }
      }
      this.$confirm({
        title: '确认?',
        content: '是否删除此记录',
        onOk: () => {
          this.handleDel(materialSysIds.join(','))
        },
        onCancel () {}
      })
    },
    handleDel (value) {
      this.actionFlag = 'delete'
      this.params = {
        materialSysId: value
      }
      this.getResult(this.params)
    },
    getResult (params) {
      materialApi
        .modifyMaterialInfo(requestBuilder(this.actionFlag, this.params, '0', '0'))
        .then(res => {
          if (res.code === '0000') {
            this.hiddenMaterialDrawer()
            this.doSearch()
            if (IS_WZG) {
              if (this.actionFlag === 'insert') {
                this.$info({
                  title: '操作成功',
                  content: '插入数据成功'
                })
              } else {
                this.$info({
                  title: '操作成功',
                  content: '修改数据成功'
                })
              }
              // this.success(this.actionFlag)
            } else {
              this.$message.success(res.message)
            }
          } else if (res.code === '9999') {
            this.$message.error('发生异常 ' + res.message)
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.saveLoading = false
        })
      this.saveLoading = true
    },
    // 查询物资信息
    doSearch () {
      this.$refs.table.refresh()
      // this.selectedRowKeys = []
      this.$refs.table.updateSelect([], [])
      this.$refs.table.rowSelection.onChange([], [])
    },
    // 修改物资信息
    doEdit (record) {
      if (this.selectedRows.length === 1) {
        this.IS_UNI_ORDER = this.selectedRows[0].isUniOrder
        this.openDrawer('update', this.selectedRows[0])
      } else {
        this.$message.error('请勾选一条记录')
      }
    },
    // 生成二维码
    queryMaterialQrCode (record) {
      const param = requestBuilder('', {
        materialName: record.materialName,
        materialNum: record.materialNum,
        orgId: record.orgId
      })
      materialApi.getMaterialQrCode(param).then(res => {
        if (res && res.data) {
          this.materialQrCodeUrl = window.URL.createObjectURL(res.data)
        }
      })
    },
    queryExistence (record) {
      materialApi.queryExistence({ materialNum: record.materialNum, orgId: record.orgId }).then(res => {
        this.IsProhibit = res.result ? '2' : '3'
      })
    },
    downloadMaterialQrCode () {
      if (!this.materialQrCodeUrl) {
        this.$message.error('暂无物资二维码！')
        return
      }
      const link = document.createElement('a')
      const filename = '物资二维码.jpg'
      document.body.appendChild(link)
      link.style.display = 'none'
      link.download = filename
      link.href = this.materialQrCodeUrl
      link.click()
      link.remove()
    },
    // 表格选择行
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    // 物资编码账本号自动设置为物资代码前5位数
    handlematerialNumBlur (value) {
      let materialClass = this.form.getFieldValue('materialClass')
      if (IS_WZG) {
        materialClass = this.form.getFieldValue('materialNum').substring(1, 7)
      } else if (IS_MD_ENV) {
        materialClass = this.form.getFieldValue('materialNum').substring(0, 5)
      }
      if (value === 'materialNum') {
        this.form.setFieldsValue({
          materialnum5: this.form.getFieldValue('materialNum').substring(0, 5),
          materialClass: materialClass
        })
      }
    },
    // 打开物资详细页弹窗
    openDrawer (action, record, type = '') {
      this.$refs.MaterialEdit.openDrawer(action, record, type)
      // this.visible = true
      // this.IsEDITYZ = true
      // this.xjStatus = type
      // this.materialQrCodeUrl = ''
      // this.$nextTick(() => {
      //   if (action === 'insert') {
      //     this.IS_UNI_ORDER = 'N'
      //     if (IS_JX_ENV) {
      //       this.form.setFieldsValue({
      //         isStop: 'N',
      //         activity: 'Y',
      //         isUniOrder: 'N',
      //         isResupply: 'N',
      //         isFixedAssets: 'N',
      //         isReuse: 'N',
      //         isLabourSupply: 'N',
      //         isInformationMaterials: 'N',
      //         lotType: '1',
      //         maxAmount: '0',
      //         minAmount: '0',
      //         taxrate: '0.13',
      //         buyer: (this.buyer[0] && this.buyer[0].value) || '',
      //         materialProperties: (this.materialProperties[0] && this.materialProperties[0].value) || '',
      //         storeman: '27020136250676156',
      //         deptno: '27020136250675616'
      //       })
      //     } else {
      //       let buyer = (this.buyer[0] && this.buyer[0].value) || ''
      //       let storeman = (this.storeman[0] && this.storeman[0].value) || ''
      //       let taxrate = 0.13
      //       let refCost = 1
      //       if (type === 'xjwz') {
      //         taxrate = 0
      //       }
      //       if (IS_WZG_LW) {
      //         storeman = '27020136250646805'
      //       }
      //       if (IS_MD_ENV) {
      //         buyer = 'MD000038'
      //         storeman = '27566567726328440'
      //       }
      //       if (USER_ORG_ID === '1.101.104') {
      //         buyer = '27020136250646930'
      //       }
      //       if (IS_DMY_ENV) {
      //         buyer = '27020136250646439'
      //         taxrate = 0
      //         refCost = 0
      //       }
      //       if (IS_JYS_ENV) {
      //         if (USER_ROLE_NAME.includes('修理站站长')) {
      //           buyer = USER_PERSON_ID
      //         }
      //       }
      //       this.form.setFieldsValue({
      //         isStop: 'N',
      //         activity: this.existingProcess ? 'N' : 'Y',
      //         isCement: 'N',
      //         isUniOrder: 'N',
      //         isResupply: 'N',
      //         isFixedAssets: 'N',
      //         isReuse: 'N',
      //         isLabourSupply: 'N',
      //         lotType: '1',
      //         maxAmount: '0',
      //         minAmount: '0',
      //         taxrate: taxrate,
      //         refCost: refCost,
      //         unitcostAfterTax: (1 + taxrate) * refCost,
      //         issueUnit: '',
      //         buyer: buyer,
      //         materialProperties: (this.materialProperties[0] && this.materialProperties[0].value) || '',
      //         storeman: storeman
      //       })
      //     }
      //     this.subTitle = this.xjStatus === 'xjwz' ? '新增修旧物资代码' : '新增物资代码'
      //     this.IsProhibit = '1'
      //     // 不显示二维码
      //     this.IsShowQr = '2'
      //     this.IsEDIT = false
      //     this.actionFlag = 'insert'
      //   } else {
      //     this.IsProhibit = '2'
      //     // 生产二维码
      //     this.queryMaterialQrCode(record)
      //     this.queryExistence(record)
      //     // 显示二维码
      //     this.IsShowQr = '1'
      //     this.subTitle = '修改物资代码'

      //     if (record.status === 'approval1001' || record.status === 'approval1009') {
      //       this.IsEDIT = ''
      //     } else {
      //       this.IsEDIT = (USER_ROLE_NAME.includes('管理员') || USER_ROLE_NAME.includes('仓库管理员')) ? '' : '1'
      //     }
      //     if (IS_YZ_ENV && record.status === 'approval1003') {
      //       this.IsEDITYZ = false
      //     }
      //     this.form.setFieldsValue(record)
      //     this.form.setFieldsValue({
      //       materialnum5: record.materialNum ? record.materialNum.substring(0, 5) : ''
      //     })
      //     this.actionFlag = 'update'
      //   }
      // })
    },
    // 关闭物资代码弹框
    hiddenMaterialDrawer () {
      this.form.resetFields()
      this.visible = false
      this.materialQrCodeUrl = ''
      this.IsEDIT = ''
      this.xjStatus = ''
      this.IsEDITYZ = true
      this.IS_UNI_ORDER = 'N'
    },
    // 判断当前用户能否审批选择的物资
    IsMaterialApprove () {
      if (this.selectedRows.length > 0) {
        for (let i = 0; i < this.selectedRows.length; i++) {
          if (
            !(
              (USER_PERSON_ID === this.selectedRows[i].createBy && this.selectedRows[i].status === 'approval1009') ||
              (!!this.selectedRows[i].userNext && this.selectedRows[i].userNext.split(',').includes(USER_PERSON_ID))
            )
          ) {
            this.IsApprove = this.IsApprove + ',' + this.selectedRows[i].materialNum
          }
        }
      } else {
        this.$message.error('未选中任何记录')
      }
    },
    // 判断能够审批的操作
    IsApproveAction () {
      if (this.selectedRows.length > 0) {
        for (let i = 0; i < this.selectedRows.length; i++) {
          if (
            this.selectedRows[i].status === 'approval1001' ||
            this.selectedRows[i].status === 'approval1009' ||
            this.selectedRows[i].status === 'approval1010'
          ) {
            this.approveReturnDisabled = true
          }
          if (!(this.selectedRows[i].status === 'approval1001')) {
            this.approveCancelDisabled = true
          }
        }
      } else {
        this.$message.error('未选中任何记录')
      }
    },
    queryApproveList (param) {
      // 重置审批人列表
      this.approveList = []
      // 根据 autoTag 查询
      return prlineApi.queryPrlineAssigee(requestBuilder('', param)).then(res => {
        if (res.code !== '0000') {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '审批人列表获取失败！'
          })
          return Promise.reject(res.result)
        }
        this.approveList = res.result || []
        return Promise.resolve(this.approveList)
      })
    },
    // 审批侧边栏
    openApprDrawer () {
      let autoTag = null
      const origin = 'material'
      if (this.selectedRows.length > 0) {
        this.IsApproveAction()
        const doApproveIds = []
        for (let i = 0; i < this.selectedRows.length; i++) {
          doApproveIds.push(this.selectedRows[i].materialSysId)
        }
        const doApproveNums = []
        for (let i = 0; i < this.selectedRows.length; i++) {
          doApproveNums.push(this.selectedRows[i].materialNum)
        }
        this.visibleApprove = true
        this.approveParam.selectMode = '1'
        this.$nextTick(() => {
          this.formApprove.resetFields()
          this.formApprove.setFieldsValue({
            // materialSysId: doApproveIds.join(',')
            materialSysId: doApproveIds,
            materialNum: doApproveNums
          })

          // 如当前页审批人类型不一致则提示
          for (const item of this.selectedRows) {
            if (autoTag === null || autoTag === item.autoTag) {
              autoTag = item.autoTag
            } else {
              this.$message.error('当前所选物资代码的审批人模式不一致！')
              return
            }
          }
          // 获取审批人
          if (this.selectedRows.length > 0) {
            if (IS_SLH_EVN) {
              if (autoTag === '4') {
                this.approveParam.showApprove = true
                this.queryApproveList({ autoTag, origin })
              }
            } else if (autoTag !== '1') {
              this.showApprove = true
              this.queryApproveList({ autoTag, origin })
            }
          }
        })
      } else {
        this.$message.error('未选中任何记录')
      }
    },
    // 关闭审批模式
    handleCancelApprove () {
      this.approveParam = {
        showApprove: false,
        selectPolineNum: [],
        materialSysId: '',
        approveSuggest: '',
        selectApprove: '',
        selectMode: '',
        action: '1'
      }
      this.visibleApprove = false
      this.IsApprove = ''
      this.approveReturnDisabled = false
      this.approveCancelDisabled = false
      // this.$refs.table.triggerSelect([], [])
    },
    // 审批事件
    doApprove () {
      // 是否选中审批数据
      if (this.selectedRows.length > 0) {
        // 是否有权审批选择的物资数据
        this.IsMaterialApprove()
        if (!this.IsApprove) {
          // 是否在新增、修改模式下触发
          if (this.visible) {
            this.$confirm({
              title: '系统提示',
              content: '是否切换至审批模式？',
              onOk: () => {
                // 关闭其他模式
                this.visible = false
                // 执行编辑事件
                this.openApprDrawer()
              }
            })
          } else {
            this.openApprDrawer()
          }
        } else {
          this.$message.error(this.IsApprove + '已审批或您不是当前审批环节的操作者')
          this.IsApprove = ''
        }
      } else {
        this.$message.error('未选中任何记录')
      }
    },
    // 提交审批
    approveMaterial () {
      const approveParam = this.approveParam
      const toApprove = (action, param) => {
        return materialApi.approveMaterial(requestBuilder(action, param)).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '审批失败！'
            })
          } else {
            this.$notification.success({
              message: '系统消息',
              description: '审批成功！'
            })
            this.doSearch()
          }
        })
      }
      if (!approveParam.selectMode) {
        this.$message.error('请选择审批模式！')
        return
      }
      const param = {
        op: USER_OPERATOR,
        userId: approveParam.action !== '9' ? approveParam.selectApprove : '',
        list: this.formApprove.getFieldValue('materialSysId'),
        msg: approveParam.approveSuggest,
        flag: approveParam.action
      }
      this.approveLoading = true
      toApprove('', param)
        .then(() => {
          this.handleCancelApprove()
          this.approveLoading = false
        })
        .catch(() => {
          this.approveLoading = false
        })
    },
    doReportOfUniOrder () {
      // 是否选中审批数据
      if (this.selectedRows.length > 0) {
        for (const i of this.selectedRows) {
          if (i.isUniOrder === 'N') {
            this.$message.error('你选中了非统购物资')
            return
          }
        }
        materialApi.reportOfUniOrder(requestBuilder('', this.selectedRows)).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '上传失败！'
            })
          } else {
            this.$notification.success({
              message: '系统消息',
              description: '上传成功！'
            })
          }
        })
        this.$refs.table.refresh(true)
      } else {
        this.$message.error('未选中任何记录')
      }
    },
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    onUniOrderSelectChange (selectedRowKeys, selectedRows) {
      this.uniOrderSelectedRowKeys = selectedRowKeys
      this.uniOrderSelectedRows = selectedRows
    },
    // 导出物资代码
    doExport () {
      materialApi.doExportMaterialInfo(requestBuilder('', Object.assign(this.queryParam))).then(res => {
        if (res && res.data && res.headers['content-disposition']) {
          const link = document.createElement('a')
          const url = window.URL.createObjectURL(res.data)
          const contentDisposition = res.headers['content-disposition']
          let filename = contentDisposition.replace(/(.*;)?filename=([^;]+).*/i, '$2')
          try {
            filename = decodeURIComponent(escape(filename))
          } catch (e) {
          }
          document.body.appendChild(link)
          link.style.display = 'none'
          link.download = filename
          link.href = url
          link.click()
          link.remove()
          window.URL.revokeObjectURL(url)
        } else {
          this.$notification.error({
            message: '系统消息',
            description: '下载失败！'
          })
        }
      })
    },
    // 导出物资二维码
    getQrCodeList () {
      if (this.selectedRows.length === 0) {
        this.$message.error('请选择下载的二维码')
        return
      }
      if (IS_JX_ENV || IS_JYS_ENV) {
        this.selectedRows.forEach(data => {
          const yardIdName = data.yardId ? (takeTreeByKey(this.storehouses, data.yardId) || {})['label'] || data.yardId
            : '  '
          data.yardIdName = yardIdName
        })
      }
      this.qrLoading = true
      materialApi.getQrCodeList(requestBuilder('', this.selectedRows)).then(res => {
        if (res && res.data && res.headers['content-disposition']) {
          const link = document.createElement('a')
          const url = window.URL.createObjectURL(res.data)
          const contentDisposition = res.headers['content-disposition']
          let filename = contentDisposition.replace(/(.*;)?filename=([^;]+).*/i, '$2')
          try {
            filename = decodeURIComponent(escape(filename))
          } catch (e) {
          }
          document.body.appendChild(link)
          link.style.display = 'none'
          link.download = filename
          link.href = url
          link.click()
          link.remove()
          window.URL.revokeObjectURL(url)
          this.selectedRows = []
          this.selectedRowKeys = []
          this.$refs.table.triggerSelect([], [])
          this.qrLoading = false
        } else {
          this.$notification.error({
            message: '系统消息',
            description: '下载失败！'
          })
          this.qrLoading = false
        }
      })
    },
    // 导出物资二维码
    getQrCodeListBySearch () {
      this.qrLoading = true
      materialApi.getQrCodeListSLH(requestBuilder('', [this.queryParam])).then(res => {
        if (res && res.data && res.headers['content-disposition']) {
          const link = document.createElement('a')
          const url = window.URL.createObjectURL(res.data)
          const contentDisposition = res.headers['content-disposition']
          let filename = contentDisposition.replace(/(.*;)?filename=([^;]+).*/i, '$2')
          try {
            filename = decodeURIComponent(escape(filename))
          } catch (e) {
          }
          document.body.appendChild(link)
          link.style.display = 'none'
          link.download = filename
          link.href = url
          link.click()
          link.remove()
          window.URL.revokeObjectURL(url)
          this.selectedRows = []
          this.selectedRowKeys = []
          this.$refs.table.triggerSelect([], [])
          this.qrLoading = false
        } else {
          this.$notification.error({
            message: '系统消息',
            description: '下载失败！'
          })
          this.qrLoading = false
        }
      })
    },
    exportTemplate () {
      materialApi.exportTemplate().then(res => {
        if (res && res.data && res.headers['content-disposition']) {
          const link = document.createElement('a')
          const url = window.URL.createObjectURL(res.data)
          const contentDisposition = res.headers['content-disposition']
          let filename = contentDisposition.replace(/(.*;)?filename=([^;]+).*/i, '$2')
          try {
            filename = decodeURIComponent(escape(filename))
          } catch (e) {
          }
          document.body.appendChild(link)
          link.style.display = 'none'
          link.download = filename
          link.href = url
          link.click()
          link.remove()
          window.URL.revokeObjectURL(url)
        } else {
          this.$notification.error({
            message: '系统消息',
            description: '下载失败！'
          })
        }
      })
    },
    moment (text, pattern = 'YYYY-MM-DD') {
      return text ? this.$moment(text).format(pattern) : ''
    },
    openApproRecordDrawer (record) {
      this.materialSysId = record.materialSysId
      this.$refs.Drawer.doOpen()
    },
    enableOrDisable (type) {
      const uuids = this.selectedRows.map(item => item.materialSysId)
      materialApi.enableOrDisable({ materialSysIds: uuids, type: type, personId: USER_PERSON_ID }).then(res => {
        if (res.code === '0000') {
          this.$message.success(res.message)
        } else if (res.code === '9999') {
          this.$message.error('发生异常 ' + res.message)
        }
      }).finally(() => {
        this.doSearch()
      })
    },
    inputchange (value) {
      console.log(value, 'value')
    },
    allocation () {
      // if (!this.selectedRows.length) {
      //   this.$message.error('请选择！')
      //   return
      // }
      this.$refs.PurchaseAllocation.showModal()
    }
  }
}
</script>
<style lang="less" scoped>
</style>
