<template>
  <section>
    <div class="nodes-container">
      <!--粗略信息-->
      <div
        :style="{ width: '25%' }"
        class="nodes-group">
        <div class="nodes-header">
          <span class="title">代码类别</span>
          <a-button
            shape="circle"
            icon="reload"
            size="small"
            slot="extra"
            @click="getCodeClass"
          />
        </div>
        <div class="nodes-body">
          <div
            v-if="codeClass.length > 0"
            class="list"
          >
            <div
              v-for="(item, index) in codeClass"
              :class="{ active: item.codeClassId === codeClassId, disabled: item.activity !=='Y' }"
              :key="index"
              @click="getCodeBas(item.codeClassId,item.existPrivate)"
              class="item"
            >
              <a-icon
                v-if="item.activity !== 'Y'"
                class="icon"
                type="minus-circle"
              />
              <a-icon
                v-else
                class="icon"
                type="check-circle"
              />
              <span class="text">{{ item.codeClassName }}</span>
              <a
                v-action:query
                href="javascript:void(0)"
                class="button"
                @click="codeClassBrowse(item)"
              >浏览</a>
              <a
                v-action:add
                v-if="item.existPrivate === '1'"
                href="javascript:void(0)"
                class="button"
                @click="Addto(item.codeClassId)"
              >添加</a>
            </div>
          </div>
          <a-empty
            v-else
            class="empty"
            :description="false"
          />
        </div>
      </div>
      <!--详细信息-->
      <div
        v-for="item in generate"
        :key="item.level"
        :style="{ width: 75 / series + '%' }"
        class="nodes-group"
      >
        <div class="nodes-header">
          <span class="title">{{ item.text }}级</span>
          <a
            v-action:add
            v-if="showAddButton(item.level)"
            href="javascript:void(0)"
            @click="doAdd(item.level)"
          >新增</a>
        </div>
        <div class="nodes-body">
          <div
            v-if="doRender(item.level).length > 0"
            class="list"
          >
            <div
              v-for="(item2, index2) in doRender(item.level)"
              :class="{ active: isActiveStatus(item.level, item2), disabled: isDisabledStatus(item2) }"
              :key="index2"
              @click="doChange(item2)"
              class="item"
            >
              <a-icon
                v-if="isDisabledStatus(item2)"
                class="icon"
                type="minus-circle"
              />
              <a-icon
                v-else
                class="icon"
                type="check-circle"
              />
              <span class="text">{{ item2.codeName }}</span>
              <a
                v-action:edit
                href="javascript:void(0)"
                class="button"
                @click="doEidt(item2)"
              >修改</a>
            </div>
          </div>
          <a-empty
            v-else
            class="empty"
            :description="false"
          />
        </div>
      </div>
    </div>
    <!--弹窗-->
    <a-modal
      v-model="visible"
      :width="350"
      :title="title"
      wrapClassName="small-modal"
      @cancel="handleCancel"
      @ok="handleOk"
    >
      <a-form layout="inline">
        <a-form-item label="名称">
          <a-input
            :disabled="disabled1"
            v-model="text" />
        </a-form-item>
        <a-form-item label="编码">
          <a-input
            :disabled="disabled1"
            v-model="coding" />
        </a-form-item>
        <a-form-item v-if="!disabled2" label="备用">
          <a-input
            v-model="component" />
        </a-form-item>
        <a-form-item label="显示顺序">
          <a-input-number
            :min="1"
            :step="1"
            :precision="0"
            :disabled="disabled2"
            v-model="showSequence" />
        </a-form-item>
        <a-form-item label="状态">
          <a-switch
            :disabled="disabled2"
            checkedChildren="有效"
            unCheckedChildren="无效"
            v-model="state"
          />
        </a-form-item>
      </a-form>
    </a-modal>
    <!--添加弹窗-->
    <a-modal
      v-model="visible2"
      :width="700"
      title="添加代码"
      wrapClassName="small-modal"
      @cancel="handleCancel2"
      @ok="handleOk2"
    >
      <template>
        <div>
          <transfer
            class="tree-transfer"
            :data-source="dataSource"
            :target-keys="targetKeys"
            :render="record => record.title"
            :rowKey="record => record.key"
            :show-select-all="true"
            @change="onChange"
          >
            <template
              slot="children"
              slot-scope="{ props: { direction, selectedKeys }, on: { itemSelect } }"
            >
              <a-tree
                v-if="direction === 'left'"
                blockNode
                checkable
                checkStrictly
                :checkedKeys="[...selectedKeys, ...targetKeys]"
                :treeData="treeData"
                @check="
                  (_, props) => {
                    onChecked(_, props, [...selectedKeys, ...targetKeys], itemSelect);
                  }
                "
                @select="
                  (_, props) => {
                    onChecked(_, props, [...selectedKeys, ...targetKeys], itemSelect);
                  }
                "
              />
            </template>
          </transfer>
        </div>
      </template>
    </a-modal>
  </section>
</template>

<script>
import { requestBuilder } from '@/utils/util'
import { queryCodeBasClass, queryCodeBasCode, modifyCodeBasCode, updateExclusiveCode } from '@/api/material/codebas'
import { ORG_ID } from '@/store/mutation-types'
import * as baseApi from '@/api/system/base'
import { Transfer } from 'ant-design-vue'
import Vue from 'vue'
// 组织机构 orgId
const USER_ORG_ID = Vue.ls.get(ORG_ID)

export default {
  name: 'CodeValue',
  components: {
    Transfer
  },
  data () {
    return {
      codeClass: [],
      // 当前展开的代码类别
      codeClassId: '',
      // 层级数量
      series: 0,
      // 激活节点
      actives: [],
      // 层级数据
      nodes: [],
      // 类型 1继承 2并列
      existPrivateType: '',
      type: '',
      // 当前codeBasCode的值
      codeBasCodeList: [],
      // 弹框配置
      node: null,
      parent: null,
      disabled1: false,
      disabled2: false,
      visible: false,
      codeClassVisible: false,
      title: '',
      text: '',
      coding: '',
      component: '',
      showSequence: 1,
      state: '',
      // 添加配置
      visible2: false,
      // 穿梭框配置
      targetKeys: [],
      dataSource: [],
      treeData: []
    }
  },
  computed: {
    // 创建层级dom（仅支持 1～99层级）
    generate () {
      let level = 0
      const reslut = []
      const library = ['十', '一', '二', '三', '四', '五', '六', '七', '八', '九']
      while (level < this.series) {
        let text = ''
        const srting = String(++level)
        const length = srting.length
        for (let i = 0; i < length; i++) {
          if (length === 1) {
            text += library[srting[i]]
          }
          if (length === 2) {
            switch (i) {
              case 0: {
                text += srting[i] === '1' ? '十' : library[srting[i]]
                break
              }
              case 1: {
                text +=
                  srting[0] !== '1'
                    ? srting[i] === '0'
                      ? '十'
                      : '十' + library[srting[i]]
                    : srting[i] === '0'
                      ? ''
                      : library[srting[i]]
                break
              }
            }
          }
        }
        reslut.push({ level, text })
      }
      return reslut
    }
  },
  mounted () {
    // 调用查询接口
    this.getCodeClass()
  },
  methods: {
    // 粗略信息操作
    // 查询CodeClass
    getCodeClass () {
      return queryCodeBasClass(requestBuilder('', { existPrivate: 'Y', activity: 'Y' }, null, null)).then(res => {
        this.codeClass = res.result.data
        this.existPrivateType = this.codeClass[0].existPrivate
        this.codeClassId = this.codeClass[0].codeClassId
        this.getCodeBas(this.codeClassId, this.existPrivateType)
      })
    },
    // 显示弹窗设置
    codeClassBrowse (item) {
      this.title = '预览'
      this.type = 'browse'
      this.text = item.codeClassName
      this.coding = item.codeClassId
      this.showSequence = item.showSequence
      this.state = item.activity === 'Y'
      this.disabled1 = true
      this.disabled2 = true
      this.visible = true
    },
    // -------下面页面设置操作------
    // 查询详细计划
    getCodeBas (value, type) {
      this.codeClassId = value
      this.existPrivateType = type
      queryCodeBasCode(requestBuilder('', { codeClassId: value, orgId: USER_ORG_ID }, null, null)).then(res => {
        if (res.code !== '0000') {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '查询失败！'
          })
          return
        }
        this.nodes = []
        this.codeBasCodeList = res.result.data
        const tree = this.doTrees(this.codeBasCodeList)
        this.series = this.getDeep(tree) + 1
        this.doArray(tree)
        this.doActive()
      })
    },
    getDeep (tree) {
      let deep = 0
      tree.forEach(item => {
        if (item.children) {
          deep = Math.max(deep, this.getDeep(item.children) + 1)
        } else {
          deep = Math.max(deep, 1)
        }
      })
      return deep
    },
    // 是否显示新增按钮
    showAddButton (level) {
      return (level === 1 || this.actives[level - 2]) && this.codeClass.length > 0 && this.existPrivateType === '2'
    },
    // 显示禁用状态
    isDisabledStatus (node) {
      return node.activity !== 'Y'
    },
    // 显示选中状态
    isActiveStatus (level, node) {
      const index = level - 1
      const actives = this.actives
      const activeNode = actives[index]
      const activeId = activeNode && activeNode.codeId
      const nodeId = node.codeId
      return activeId === nodeId
    },
    // 储存选中的节点
    doActive () {
      let index = 0
      const nodes = this.nodes
      const series = this.series
      const actives = this.actives
      while (index < series) {
        const currentNodes = nodes[index] || []
        const currentActiveNode = actives[index] || null
        const parentActiveNode = actives[index - 1] || null
        const parentActiveNodeId = parentActiveNode && parentActiveNode.codeId
        const currentActiveNodeId = currentActiveNode && currentActiveNode.codeId
        const parentNodeId = currentActiveNode && currentActiveNode.parent && currentActiveNode.parent.codeId
        const filterNodes = currentNodes.filter(item => index === 0 || item.parent.codeId === parentActiveNodeId)
        if (parentActiveNodeId !== parentNodeId) {
          this.$set(actives, index, undefined)
        }
        if (actives[index]) {
          this.$set(
            actives,
            index,
            filterNodes.find(item => item.codeId === currentActiveNodeId)
          )
        }
        if (!actives[index]) {
          index < series && this.$set(actives, index, filterNodes[0])
        }
        index++
      }
    },
    // 渲染指定层级的dom
    doRender (level) {
      const array = []
      const index = level - 1
      const nodes = this.nodes
      const actives = this.actives
      const current = nodes[index] || []
      for (const node of current) {
        const last = index - 1
        const nodeId = actives[last] && actives[last].codeId
        const parentNodeId = node.parent && node.parent.codeId
        if (index === 0 || nodeId === parentNodeId) {
          array.push(node)
        }
      }
      return array
    },
    // 扁平化结构 转换 树形结构
    doTrees (array) {
      const cache = {}
      const trees = []
      for (const node of array) {
        const parentId = node.parentId
        const nodeId = node.codeId
        if (!cache[nodeId]) {
          cache[nodeId] = {}
        }
        if (!cache[parentId]) {
          cache[parentId] = { children: [] }
        }
        if (!cache[parentId].children) {
          cache[parentId].children = []
        }
        if (!cache[parentId].children.includes(cache[nodeId])) {
          cache[parentId].children.push(cache[nodeId])
        }
        Object.assign(cache[nodeId], { ...node })
      }
      for (const key in cache) {
        if (cache[key] && !cache[key].parentId && cache[key].children) {
          trees.push(...cache[key].children)
        }
      }
      return trees
    },
    // tree 结构 转换为 层级数据
    doArray (tree, parent) {
      const empty = { level: 0 }
      const upper = parent || empty
      const level = upper.level + 1
      const index = level - 1
      if (tree && level <= this.series) {
        if (!this.nodes[index]) {
          this.$set(this.nodes, index, [])
        }
        for (const node of tree) {
          if (parent && !parent.children) {
            parent.children = [node]
          }
          if (parent && !parent.children.includes(node)) {
            parent.children.push(node)
          }
          this.nodes[index].push(
            Object.assign(node, {
              level: level || 1,
              parent: parent || null
            })
          )
          this.doArray(node.children, node)
        }
      }
    },
    // 选中节点更改，相应层级数据更改
    doChange (node) {
      const level = node.level
      const index = level - 1
      const actives = this.actives
      this.$set(actives, index, node)
      this.doActive()
    },
    // -------下面是详细信息的操作------
    // 打开编辑弹框
    doEidt (node) {
      if (node.parent && node.parent.activity !== 'Y') {
        this.$message.error('当前父节点已禁用，不可修改！')
        return
      }
      this.node = node
      this.title = '修改'
      this.type = 'update'
      this.disabled1 = true
      this.text = node.codeName
      this.coding = node.codeId
      this.component = node.component
      this.showSequence = node.showSequence || 1
      this.state = node.activity === 'Y'
      this.visible = true
    },
    // 打开新增弹框
    doAdd (level) {
      const index = level - 1
      const lastIndex = level - 2
      this.node = this.actives[index]
      this.parent = this.actives[lastIndex]
      if (this.parent && this.parent.activity !== 'Y') {
        this.$message.error('当前父节点已禁用，不可新增！')
        return
      }
      this.title = '新增'
      this.type = 'insert'
      this.state = true
      this.visible = true
    },
    // 保存(新增/更改)
    handleOk () {
      if (!this.text.trim()) {
        this.$message.error('请输入名称！')
        return
      }
      if (!this.coding.trim()) {
        this.$message.error('请输入对应编码！')
        return
      }
      if (this.type === 'insert') {
        const param = {
          activity: this.state ? 'Y' : 'N',
          parentId: (this.parent && this.parent.codeId) || '0',
          codeName: this.text,
          codeId: this.coding,
          component: this.component,
          codeClassId: this.codeClassId,
          showSequence: this.showSequence || 1,
          orgId: USER_ORG_ID
        }
        modifyCodeBasCode(requestBuilder('insert', param)).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '新增失败！'
            })
            return
          }
          this.handleCancel()
          this.getCodeBas(this.codeClassId, this.existPrivateType)
        })
      }
      if (this.type === 'update') {
        const param = {
          activity: this.state ? 'Y' : 'N',
          codeSysId: this.node.codeSysId,
          codeName: this.text,
          codeId: this.coding,
          component: this.component,
          codeClassId: this.codeClassId,
          showSequence: this.showSequence,
          orgId: USER_ORG_ID
        }
        modifyCodeBasCode(requestBuilder('update', param)).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '修改失败！'
            })
            return
          }
          this.handleCancel()
          this.getCodeBas(this.codeClassId, this.existPrivateType)
        })
      }
      if (this.type === 'browse') {
        this.handleCancel()
      }
    },
    // 取消(新增/更改)
    handleCancel () {
      this.title = ''
      this.type = ''
      this.text = ''
      this.state = ''
      this.coding = ''
      this.component = ''
      this.showSequence = ''
      this.disabled1 = false
      this.disabled2 = false
      this.node = null
      this.parent = null
      this.visible = false
    },
    // 从公共代码添加到专属代码等一系列方法
    Addto (id) {
      // 延迟一会，等待codeBasCode查询完成
      setTimeout(() => {
        baseApi.getTreeById({ id: 'basCodeByClassId', sqlParams: { codeClassId: id } }).then(res => {
          if (res.code === '0000') {
            const treeData = res.result
            this.setKeyAndTitle(treeData)
            this.flatten(JSON.parse(JSON.stringify(treeData)))
            this.targetKeys = this.codeBasCodeList.map(item => item.codeId)
            this.treeData = this.handleTreeData(treeData, this.targetKeys)
            this.visible2 = true
          }
        })
      }, 100)
    },
    setKeyAndTitle (data) {
      data.forEach(item => {
        item.key = item.value
        item.title = item.label
        if (item.children) {
          this.setKeyAndTitle(item.children)
        }
      })
    },
    // 关闭添加弹窗
    handleCancel2 () {
      this.visible2 = false
      this.dataSource = []
      this.targetKeys = []
    },
    handleOk2 () {
      updateExclusiveCode(requestBuilder('', { codeIdList: this.targetKeys, codeClassId: this.codeClassId })).then(res => {
        if (res.code !== '0000') {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '修改失败！'
          })
          return
        }
        this.handleCancel2()
        this.getCodeBas(this.codeClassId, this.existPrivateType)
      })
    },
    onChange (targetKeys) {
      this.targetKeys = targetKeys
    },
    onChecked (_, e, checkedKeys, itemSelect) {
      const { eventKey } = e.node
      itemSelect(eventKey, !this.isChecked(checkedKeys, eventKey))
    },
    flatten (list = []) {
      list.forEach(item => {
        this.dataSource.push(item)
        this.flatten(item.children)
      })
    },
    isChecked (selectedKeys, eventKey) {
      return selectedKeys.indexOf(eventKey) !== -1
    },
    handleTreeData (data, targetKeys = []) {
      data.forEach(item => {
        item['disabled'] = targetKeys.includes(item.key)
        if (item.children) {
          this.handleTreeData(item.children, targetKeys)
        }
      })
      return data
    }
  },
  watch: {
    targetKeys () {
      this.treeData = this.handleTreeData(this.treeData, this.targetKeys)
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep {
  .ant-empty-image {
    height: 65px;
  }
}
section {
  width: 100%;
  height: 100%;
  position: absolute;
  background-color: #ffffff;
}
.nodes-container {
  height: 100%;
  padding: 20px 10px 5px;
  box-sizing: border-box;
  flex-flow: row nowrap;
  overflow: auto;
  display: flex;
  & > .nodes-group {
    min-width: 250px;
    padding: 15px 25px 5px;
    box-sizing: border-box;
    position: relative;
    flex: 0 0 auto;
    &:not(:last-child)::after {
      content: '';
      width: 1px;
      height: calc(100% - 20px);
      background: #eeeeee;
      position: absolute;
      top: 10px;
      right: 1px;
    }
    .nodes-header {
      padding: 5px 5px 8px;
      text-align: center;
      border-bottom: solid 1px #f0f0f0;
      & > a {
        font-size: 13px;
        margin: 0 8px;
      }
      & > .title {
        font-size: 16px;
        margin: 0 8px;
      }
    }
    .nodes-body {
      height: calc(100% - 40px);
      padding: 10px 3px;
      & > .list {
        height: 100%;
        overflow: auto;
        & > .item {
          padding: 8px 8px;
          display: flex;
          cursor: default;
          &:hover,
          &:active {
            background-color: #f9f9f9;
            & > .button {
              display: inline-block;
            }
          }
          &.disabled {
            & > .icon {
              color: #f34d4d;
            }
            & > .text {
              color: #f34d4d;
            }
          }
          &.active {
            background-color: #f3f3f3;
          }
          & > .icon {
            height: 16px;
            padding: 3.5px;
            font-size: 16px;
            vertical-align: middle;
            display: inline-block;
          }
          & > .text {
            width: 100%;
            font-size: 16px;
            vertical-align: middle;
            display: inline-block;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            flex: 0 1 auto;
          }
          & > .button {
            font-size: 13px;
            padding: 2px 3px;
            vertical-align: middle;
            flex: 0 0 auto;
            display: none;
          }
        }
      }
      & > .empty {
        padding: 10px 0;
      }
    }
  }
}
</style>
<style lang="less">
.small-modal {
  .ant-modal {
    top: calc(50% - 200px);
  }
  .ant-form-inline {
    .ant-form-item {
      & > .ant-form-item-label {
        width: 50px;
      }
    }
  }
}
</style>
<style scoped>
.tree-transfer .ant-transfer-list:first-child {
  width: 50%;
  flex: none;
}
</style>
