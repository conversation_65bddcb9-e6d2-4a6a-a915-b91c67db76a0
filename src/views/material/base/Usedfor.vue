<template>
  <section>
    <div class="nodes-container">
      <div
        v-for="item in generate"
        :key="item.level"
        :style="{ width: 100 / series + '%' }"
        class="nodes-group"
      >
        <div class="nodes-header">
          <div class="num-header">{{ item.level }}</div>
          <div class="title-header">
            <span class="title">使用方向{{ item.text }}级</span>
            <a-button v-action:add v-if="showAddButton(item.level)" icon="plus" @click="doAdd(item.level)">新增</a-button>
            <!-- <a
              v-action:add
              v-if="showAddButton(item.level)"
              href="javascript:void(0)"
              @click="doAdd(item.level)"
            >新增</a> -->
          </div>
        </div>
        <div class="nodes-body">
          <div
            v-if="doRender(item.level).length > 0"
            class="list"
          >
            <div
              v-for="(item2, index2) in doRender(item.level)"
              :class="{ active: isActiveStatus(item.level, item2), disabled: isDisabledStatus(item2) }"
              :key="index2"
              @click="doChange(item2)"
              class="item"
            >
              <a-icon
                v-if="isDisabledStatus(item2)"
                class="icon"
                type="minus-circle"
              />
              <a-icon
                v-else
                class="icon"
                type="check-circle"
              />
              <span class="text">{{ item2.usedforName }}</span>
              <!-- <a
                v-action:edit
                href="javascript:void(0)"
                class="button"
                @click="doEidt(item2)"
              >修改</a> -->
              <a-icon v-action:edit type="edit" class="button" @click="doEidt(item2)"/>
            </div>
          </div>
          <a-empty
            v-else
            class="empty"
            :description="false"
          />
        </div>
      </div>
    </div>
    <a-modal
      v-model="visible"
      :width="400"
      :title="title"
      wrapClassName="small-modal"
      @cancel="handleCancel"
      @ok="handleOk"
    >
      <a-form layout="inline">
        <a-form-item label="使用方向编号">
          <a-input v-model="usedId" disabled/>
        </a-form-item>
        <a-form-item label="用途">
          <a-input v-model="text" />
        </a-form-item>
        <a-form-item label="财务" v-if=" level === series && IS_BYJ_ENV">
          <a-input v-model="financialUsedforId" />
        </a-form-item>
        <a-form-item label="审批人" v-if="IS_DMY_ENV">
          <a-select
            v-model="approvalDepart"
            optionFilterProp="label"
            placeholder="注：更改父级此选项，同步更新子级同选项"
            showSearch
            allowClear
          >
            <a-select-option
              v-for="(item, index) in depts"
              :value="item.value"
              :label="item.label"
              :key="index"
            >{{ item.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态">
          <a-switch
            checkedChildren="有效"
            unCheckedChildren="无效"
            v-model="state"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </section>
</template>

<script>
import { requestBuilder } from '@/utils/util'
import * as usedforApi from '@/api/material/usedfor'
import { ORG_ID } from '@/store/mutation-types'
import Vue from 'vue'
import { getCommboxById } from '@/api/material/base'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
// 北一
const IS_BYJ_ENV = USER_ORG_ID === '1.100.101'
const IS_DMY_ENV = USER_ORG_ID === '1.122.801'
export default {
  name: 'Usedfor',
  data () {
    return {
      IS_DMY_ENV,
      // 层级数量
      series: 3,
      // 激活节点
      actives: [],
      // 部门
      depts: [],
      // 层级数据
      nodes: [],
      // 弹框配置
      node: null,
      parent: null,
      visible: false,
      level: null,
      title: '',
      text: '',
      approvalDepart: '',
      state: '',
      // 提交类型
      type: '',
      usedId: '',
      financialUsedforId: '',
      IS_BYJ_ENV
    }
  },
  computed: {
    // 创建层级dom（仅支持 1～99层级）
    generate () {
      let level = 0
      const reslut = []
      const library = ['十', '一', '二', '三', '四', '五', '六', '七', '八', '九']
      while (level < this.series) {
        let text = ''
        const srting = String(++level)
        const length = srting.length
        for (let i = 0; i < length; i++) {
          if (length === 1) {
            text += library[srting[i]]
          }
          if (length === 2) {
            switch (i) {
              case 0: {
                text += srting[i] === '1' ? '十' : library[srting[i]]
                break
              }
              case 1: {
                text +=
                  srting[0] !== '1'
                    ? srting[i] === '0'
                      ? '十'
                      : '十' + library[srting[i]]
                    : srting[i] === '0'
                      ? ''
                      : library[srting[i]]
                break
              }
            }
          }
        }
        reslut.push({ level, text })
      }
      return reslut
    }
  },
  mounted () {
    // 调用查询接口
    this.getUsedfors()
  },
  created () {
    this.initOptions()
  },
  methods: {
    // 下拉框数据获取
    initOptions () {
      // 职能部门，变更 直接变成审批人
      getCommboxById({ id: 'personByRoleName', sqlParams: { byRoleName: '1', roleName: '部门经理' } })
        .then(res => {
          this.depts.push(...res.result)
        })
      if (IS_DMY_ENV) {
        getCommboxById({ id: 'personByRoleName', sqlParams: { byRoleName: '1', roleName: '工程技术部职能' } })
          .then(res => {
            this.depts.push(...res.result)
          })
      }
    },
    // 查询计划
    getUsedfors () {
      usedforApi.getUsedforInfo(requestBuilder('', {}, null, null)).then(res => {
        if (res.code !== '0000') {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '查询失败！'
          })
          return
        }
        this.nodes = []
        const data = res.result.data
        const tree = this.doTrees(data)
        this.doArray(tree)
        this.doActive()
      })
    },
    // 是否显示新增按钮
    showAddButton (level) {
      return level === 1 || this.actives[level - 2]
    },
    // 显示禁用状态
    isDisabledStatus (node) {
      return node.activity !== 'Y'
    },
    // 显示选中状态
    isActiveStatus (level, node) {
      const index = level - 1
      const actives = this.actives
      const activeNode = actives[index]
      const activeId = activeNode && activeNode.usedforId
      const nodeId = node.usedforId
      return activeId === nodeId
    },
    // 储存选中的节点
    doActive () {
      let index = 0
      const nodes = this.nodes
      const series = this.series
      const actives = this.actives
      while (index < series) {
        const currentNodes = nodes[index] || []
        const currentActiveNode = actives[index] || null
        const parentActiveNode = actives[index - 1] || null
        const parentActiveNodeId = parentActiveNode && parentActiveNode.usedforId
        const currentActiveNodeId = currentActiveNode && currentActiveNode.usedforId
        const parentNodeId = currentActiveNode && currentActiveNode.parent && currentActiveNode.parent.usedforId
        const filterNodes = currentNodes.filter(item => index === 0 || item.parent.usedforId === parentActiveNodeId)
        if (parentActiveNodeId !== parentNodeId) {
          this.$set(actives, index, undefined)
        }
        if (actives[index]) {
          this.$set(
            actives,
            index,
            filterNodes.find(item => item.usedforId === currentActiveNodeId)
          )
        }
        if (!actives[index]) {
          index < series && this.$set(actives, index, filterNodes[0])
        }
        index++
      }
    },
    // 渲染指定层级的dom
    doRender (level) {
      const array = []
      const index = level - 1
      const nodes = this.nodes
      const actives = this.actives
      const current = nodes[index] || []
      for (const node of current) {
        const last = index - 1
        const nodeId = actives[last] && actives[last].usedforId
        const parentNodeId = node.parent && node.parent.usedforId
        if (index === 0 || nodeId === parentNodeId) {
          array.push(node)
        }
      }
      return array
    },
    // 扁平化结构 转换 树形结构
    doTrees (array) {
      const cache = {}
      const trees = []
      for (const node of array) {
        const parentId = node.parentId
        const nodeId = node.usedforId
        if (!cache[nodeId]) {
          cache[nodeId] = {}
        }
        if (!cache[parentId]) {
          cache[parentId] = { children: [] }
        }
        if (!cache[parentId].children) {
          cache[parentId].children = []
        }
        if (!cache[parentId].children.includes(cache[nodeId])) {
          cache[parentId].children.push(cache[nodeId])
        }
        Object.assign(cache[nodeId], { ...node })
      }
      for (const key in cache) {
        if (cache[key] && !cache[key].parentId && cache[key].children) {
          trees.push(...cache[key].children)
        }
      }
      return trees
    },
    // tree 结构 转换为 层级数据
    doArray (tree, parent) {
      const empty = { level: 0 }
      const upper = parent || empty
      const level = upper.level + 1
      const index = level - 1
      if (tree && level <= this.series) {
        if (!this.nodes[index]) {
          this.$set(this.nodes, index, [])
        }
        for (const node of tree) {
          if (parent && !parent.children) {
            parent.children = [node]
          }
          if (parent && !parent.children.includes(node)) {
            parent.children.push(node)
          }
          this.nodes[index].push(
            Object.assign(node, {
              level: level || 1,
              parent: parent || null
            })
          )
          this.doArray(node.children, node)
        }
      }
    },
    // 选中节点更改，相应层级数据更改
    doChange (node) {
      const level = node.level
      const index = level - 1
      const actives = this.actives
      this.$set(actives, index, node)
      this.doActive()
    },
    // 打开编辑弹框
    doEidt (node) {
      if (node.parent && node.parent.activity !== 'Y') {
        this.$message.error('当前父节点已禁用，不可修改！')
        return
      }
      this.node = node
      this.title = '修改'
      this.type = 'update'
      this.text = node.usedforName
      this.approvalDepart = node.approvalDepart
      this.usedId = node.usedforId
      this.state = node.activity === 'Y'
      this.financialUsedforId = node.level === this.series ? node.financialUsedforId : ''
      this.level = node.level
      this.visible = true
    },
    // 打开新增弹框
    doAdd (level) {
      const index = level - 1
      const lastIndex = level - 2
      this.node = this.actives[index]
      this.parent = this.actives[lastIndex]
      if (this.parent && this.parent.activity !== 'Y') {
        this.$message.error('当前父节点已禁用，不可新增！')
        return
      }
      this.title = '新增'
      this.type = 'insert'
      this.state = true
      this.level = level
      this.visible = true
    },
    // 保存(新增/更改)
    handleOk () {
      if (!this.text.trim()) {
        this.$message.error('请输入使用用途！')
        return
      }
      if (IS_DMY_ENV && !this.approvalDepart.trim()) {
        this.$message.error('请选择审批部门！')
        return
      }
      if (this.type === 'insert') {
        const param = {
          activity: this.state ? 'Y' : 'N',
          parentId: this.parent && this.parent.usedforId,
          usedforName: this.text,
          approvalDepart: this.approvalDepart,
          financialUsedforId: this.financialUsedforId,
          level: this.level
        }
        usedforApi.modifyUsedforInfo(requestBuilder('insert', param)).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '新增失败！'
            })
            return
          }
          this.title = ''
          this.type = ''
          this.usedId = ''
          this.text = ''
          this.state = ''
          this.node = null
          this.parent = null
          this.financialUsedforId = ''
          this.approvalDepart = ''
          this.visible = false
          this.getUsedfors()
        })
      }
      if (this.type === 'update') {
        const param = {
          activity: this.state ? 'Y' : 'N',
          usedforId: this.node.usedforId,
          usedforName: this.text,
          financialUsedforId: this.financialUsedforId,
          approvalDepart: this.approvalDepart
        }
        usedforApi.modifyUsedforInfo(requestBuilder('update', param)).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '修改失败！'
            })
            return
          }
          this.title = ''
          this.type = ''
          this.usedId = ''
          this.text = ''
          this.state = ''
          this.node = null
          this.parent = null
          this.financialUsedforId = ''
          this.approvalDepart = ''
          this.visible = false
          this.getUsedfors()
        })
      }
    },
    // 取消(新增/更改)
    handleCancel () {
      this.title = ''
      this.type = ''
      this.usedId = ''
      this.text = ''
      this.state = ''
      this.node = null
      this.parent = null
      this.financialUsedforId = ''
      this.approvalDepart = ''
      this.visible = false
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep {
  .ant-empty-image {
    height: 65px;
  }
}
section {
  width: 100%;
  height: 100%;
  position: absolute;
  background-color: #ffffff;
}
.nodes-container {
  height: 100%;
  padding: 20px 10px 5px;
  box-sizing: border-box;
  flex-flow: row nowrap;
  overflow: auto;
  display: flex;
  & > .nodes-group {
    min-width: 250px;
    padding: 15px 25px 5px;
    box-sizing: border-box;
    position: relative;
    flex: 0 0 auto;
    &:not(:last-child)::after {
      content: '';
      width: 1px;
      height: calc(100% - 20px);
      background: #eeeeee;
      position: absolute;
      top: 10px;
      right: 1px;
    }
    .nodes-header {
      padding: 5px 5px 8px;
      text-align: center;
      height: 80px;
      border-bottom: solid 1px #f0f0f0;
      & > a {
        font-size: 13px;
        margin: 0 8px;
      }
      & > .title {
        font-size: 18px;
        font-weight: 700;
        margin: 0 8px;
      }
      .num-header {
        border-radius: 50%;
        border: 2px solid #BFCCFF;
        background: #F0F4FF;
        color: #4557FF;
        height: 35px;
        width: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        font-weight: 700;
        /* margin-bottom: 5px; */
      }
      .title-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title {
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
    .nodes-body {
      height: calc(100% - 80px);
      padding: 10px 3px;
      & > .list {
        height: 100%;
        overflow: auto;
        & > .item {
          padding: 8px 8px;
          display: flex;
          cursor: default;
          &:hover,
          &:active {
            background-color: #f9f9f9;
            & > .button {
              display: inline-block;
            }
          }
          &.disabled {
            & > .icon {
              color: #f34d4d;
            }
            & > .text {
              color: #f34d4d;
            }
          }
          &.active {
            background-color: #F0F4FF;
            color: #5263FF;
          }
          & > .icon {
            height: 16px;
            padding: 3.5px;
            font-size: 16px;
            vertical-align: middle;
            display: inline-block;
          }
          & > .text {
            width: 100%;
            font-size: 16px;
            vertical-align: middle;
            display: inline-block;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            flex: 0 1 auto;
          }
          & > .button {
            font-size: 16px;
            padding: 2px 3px;
            vertical-align: middle;
            flex: 0 0 auto;
            display: none;
          }
        }
      }
      & > .empty {
        padding: 10px 0;
      }
    }
  }
}
</style>
<style lang="less">
.small-modal {
  .ant-modal {
    top: calc(50% - 200px);
  }
  .ant-form-inline {
    .ant-form-item {
      & > .ant-form-item-label {
        width: 50px;
      }
    }
  }
}
</style>
