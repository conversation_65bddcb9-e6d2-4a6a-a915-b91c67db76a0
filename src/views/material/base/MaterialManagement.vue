<template>
  <section>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="10">
            <a-col
              :xl="4"
              :md="4"
              :sm="24"
            >
              <a-form-item label="物资代码">
                <a-input
                  @pressEnter="doSearch"
                  v-model="queryParam.materialNum"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <a-col
              :xl="4"
              :md="4"
              :sm="24"
            >
              <a-form-item label="物资名称">
                <a-input
                  @pressEnter="doSearch"
                  v-model="queryParam.materialName"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <!-- <a-col
              :xl="4"
              :md="4"
              :sm="24"
            >
              <a-form-item label="仓库">
                <a-input v-model="queryParam.storehouseSysId" />
              </a-form-item>
            </a-col> -->
            <a-col
              :xl="4"
              :md="4"
              :sm="24"
            >
              <a-form-item label="物资型号">
                <a-input v-model="queryParam.jModel" @pressEnter="doSearch" />
              </a-form-item>
            </a-col>
            <a-col
              :xl="4"
              :md="4"
              :sm="24"
            >
              <a-form-item label="选择仓库">
                <a-select
                  v-model="queryParam.storehouseSysId"
                  allowClear
                  @change="doSearch"
                  optionFilterProp="label"
                  showSearch
                >
                  <a-select-option
                    v-for="(item, index) in storehouse"
                    :value="item.value"
                    :key="index"
                    :label="item.label"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :xl="4"
              :md="4"
              :sm="24"
            >
              <a-form-item label="区">
                <a-select
                  v-model="queryParam.zone"
                  allowClear
                  @change="doSearch()"
                  optionFilterProp="label"
                  showSearch
                >
                  <a-select-option
                    v-for="(item, index) in zones"
                    :value="item.value"
                    :key="index"
                    :label="item.label"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :xl="4"
              :md="4"
              :sm="24"
            >
              <a-form-item label="架">
                <a-select
                  v-model="queryParam.frames"
                  allowClear
                  @change="doSearch()"
                  optionFilterProp="label"
                  showSearch
                >
                  <a-select-option
                    v-for="(item, index) in frame"
                    :value="item.value"
                    :key="index"
                    :label="item.label"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :xl="4"
              :md="4"
              :sm="24"
            >
              <a-form-item label="层">
                <a-select
                  v-model="queryParam.floor"
                  allowClear
                  @change="doSearch()"
                  optionFilterProp="label"
                  showSearch
                >
                  <a-select-option
                    v-for="(item, index) in floors"
                    :value="item.value"
                    :key="index"
                    :label="item.label"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :xl="4"
              :md="4"
              :sm="24"
            >
              <a-form-item label="位">
                <a-select
                  v-model="queryParam.bit"
                  allowClear
                  @change="doSearch()"
                  optionFilterProp="label"
                  showSearch
                >
                  <a-select-option
                    v-for="(item, index) in bits"
                    :value="item.value"
                    :key="index"
                    :label="item.label"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :xl="4"
              :md="4"
              :sm="24"
            >
              <a-form-item label="类型细目">
                <a-select
                  v-model="queryParam.typeDetail"
                  allowClear
                  @change="doSearch()"
                  optionFilterProp="label"
                  showSearch
                >
                  <a-select-option
                    v-for="(item, index) in typeDetails"
                    :value="item.value"
                    :key="index"
                    :label="item.label"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :xl="4"
              :md="4"
              :sm="24"
            >
              <a-form-item label="显示筛选:">
                <a-select
                  v-model="queryParam.displayAll"
                  allowClear
                  @change="doSearch()"
                >
                  <a-select-option :value="'Y'">所有库存</a-select-option>
                  <a-select-option :value="'N'">隐藏库存0物资</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <a-button type="primary" icon="search" @click="doSearch" >查询</a-button>
        <a-button type="primary" icon="download" @click="doExport" >导出办公用品库存</a-button>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        size="small"
        :columns="computedColumns"
        :data="loadlData"
        :customRow="rowClick"
        :rowSelection="null"
        :pageSizeOptions="pageSizeOptions"
        :pageSize="defaultPageSize"
        :showPagination="true"
      >
        <!-- 自定义帅选组件 -->
        <template slot="title">
          <div class="tableTitle">
            <a-popover v-model="popVisible" trigger="click">
              <template slot="content">
                <DropdownChecked :showTitle="false" title="自定义表格排序" tableId="MaterialManagementTable" v-model="columns" />
              </template>
              <span class="title-icons" >
                <a-icon type="setting" theme="filled" />
              </span>
            </a-popover>
            <span class="title-icons" style="border: none;">
              <a-tooltip>
                <template slot="title">
                  刷新
                </template>
                <a-icon type="sync" :spin="popLoading"  @click="reColumns"/>
              </a-tooltip>
            </span>
          </div>
        </template>
    </s-table>
    </a-card>
    <a-drawer
      title="出入库详情"
      :width="420"
      :visible="visible"
      :mask="true"
      :getContainer="false"
      :bodyStyle="{paddingBottom: '80px'}"
      @close="visible = false"
    >
      <a-tabs default-active-key="1" @change="callback">
        <a-tab-pane key="1" tab="入库详情" force-render>
          <a-empty v-if="matr.length===0" />
          <a-descriptions
            v-for="item in matr"
            :key="item.matuSysId"
            :column="{ xs: 1, sm: 1, md: 1}"
            :bordered="true"
            class="myDescriptions"
            size="small" >
            <a-descriptions-item label="供应商">
              {{ item.vendorName }}
            </a-descriptions-item>
            <a-descriptions-item label="批次号">
              {{ item.lotNum }}
            </a-descriptions-item>
            <a-descriptions-item label="仓库名称">
              {{ item.storehouseName }}
            </a-descriptions-item>
            <a-descriptions-item label="类型">
              {{ item.status }}
            </a-descriptions-item>
            <a-descriptions-item label="入库数量">
              {{ item.acceptedQty?item.acceptedQty:item.actReqNum }}
            </a-descriptions-item>
            <a-descriptions-item label="入库时间">
              {{ item.createDate }}
            </a-descriptions-item>
            <a-descriptions-item label="单价">
              {{ item.unitCost+'元/'+item.orderUnit }}
            </a-descriptions-item>
            <a-descriptions-item label="总价">
              {{ item.totalExclTax+'元' }}
            </a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>
        <a-tab-pane key="2" tab="出库详情">
          <a-empty v-if="matu.length===0" />

          <a-descriptions
            v-for="item in matu"
            :key="item.matrSysId"
            :column="{ xs: 1, sm: 1, md: 1}"
            :bordered="true"
            size="small"
            class="myDescriptions"
          >
            <a-descriptions-item label="供应商">
              {{ item.vendorName }}
            </a-descriptions-item>
            <a-descriptions-item label="批次号">
              {{ item.lotNum }}
            </a-descriptions-item>
            <a-descriptions-item label="出库数量">
              {{ item.acceptedQty?item.acceptedQty:item.actReqNum }}
            </a-descriptions-item>
            <a-descriptions-item label="单价">
              {{ item.actualCost+'元/'+item.orderUnit }}
            </a-descriptions-item>
            <a-descriptions-item label="出库仓库">
              {{ item.storehouseName }}
            </a-descriptions-item>
            <a-descriptions-item
              v-if="item.toStorehouseSysId"
              label="移库目的库">
              {{ item.toStorehouseName }}
            </a-descriptions-item>
            <a-descriptions-item
              v-if="item.toStorehouseSysId"
              label="移库批次号">
              {{ item.transferLotNumber }}
            </a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>
      </a-tabs>
    </a-drawer>
  </section>
</template>
<script>
// import linkParam from 'linkParam'
// import moment from 'moment'
import { ORG_ID, PERSON_ID } from '@/store/mutation-types'
import { STable, Ellipsis, DropdownChecked } from '@/components'
import { requestBuilder } from '@/utils/util'
import * as material from '@/api/material/material'
import * as baseApi from '@/api/material/base'
import Vue from 'vue'
import { tableMixin } from '@/utils/tableMixin'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_JX_ENV = USER_ORG_ID === '1.102'
// 操作人 userPersonId
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)

export default {
  name: 'Material',
  components: {
    STable,
    Ellipsis,
    DropdownChecked
  },
  mixins: [tableMixin],
  data () {
    return {
      IS_JX_ENV,
      popVisible: false,
      popLoading: false,
      // 下拉框选项 - 类型细目
      typeDetails: [],
      // 出库
      matu: [],
      // 入库
      matr: [],
      detailIndex: 0,
      storehouse: [],
      zones: [], // 区
      frames: [], // 架
      floors: [], // 层
      bits: [], // 位
      // 物资类别
      materialClass: [],
      visible: false,
      columns: [
        {
          title: '物资代码',
          dataIndex: 'materialNum',
          ellipsis: true,
          check: true,
          sorter: false,
          fixed: false,
          edit: false,
          width: 200
        },
        {
          title: '物资名称',
          dataIndex: 'materialName',
          ellipsis: true,
          check: true,
          sorter: false,
          fixed: false,
          edit: false,
          width: 200
        },
        {
          title: '物资型号',
          dataIndex: 'jModel',
          ellipsis: true,
          check: true,
          sorter: false,
          fixed: false,
          edit: false,
          width: 200
        },
        {
          title: '所属仓库',
          dataIndex: 'storehouseName',
          ellipsis: true,
          check: true,
          sorter: false,
          fixed: false,
          edit: false,
          width: 200
        },
        {
          title: !IS_JX_ENV ? '预入录库存' : '实物入库库存',
          dataIndex: 'totalCurbalN',
          sorter: true,
          ellipsis: true,
          check: true,
          fixed: false,
          edit: false,
          width: 100
        },
        {
          title: !IS_JX_ENV ? '正式入库库存' : '发票入库库存',
          dataIndex: 'totalCurbalY',
          sorter: true,
          ellipsis: true,
          check: true,
          fixed: false,
          edit: false,
          width: 100
        }
      ],
      queryParam: { displayAll: 'Y' },
      loadlData: parameter => {
        this.queryParam.type = 1
        const param = requestBuilder('', Object.assign(this.queryParam), parameter.pageNo, parameter.pageSize, parameter.sortField,
          parameter.sortOrder)
        return material.queryMaterialBalances(param).then(res => {
          // console.log(res)
          if (res.code !== '0000') {
            this.$message.error(res.message)
          } else {
            return res.result
          }
        })
      },
      options: {
        rowSelection: {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        }
      },
      selectedRowKeys: [],
      selectedRows: [],
      rowClick: record => ({
        on: {
          // 点击事件
          dblclick: () => {
            this.visible = true
            this.getDetail(record.materialNum)
          }
        }
      }),
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20']
    }
  },
  computed: {
    // 新增可领数
    computedColumns () {
      const arr = this.columns.filter(item => {
        return item.check
      })
      return arr
    }
  },
  created () {
    this.initOptions()
  },
  mounted () {
    this.initTable('columns', 'MaterialManagementTable')
  },
  methods: {
    reColumns () {
      this.popLoading = true
      this.$store.dispatch('SetTodoTableGroup', { USER_ORG_ID, USER_PERSON_ID })
      setTimeout(() => {
        this.initTable('columns', 'MaterialManagementTable')
        this.popLoading = false
      }, 500)
    },
    // 导出办公用品库存
    doExport () {
      const param = requestBuilder('', Object.assign({ 'typeDetail': 'type_detail_2' }), this.pageNo, this.pageSize)
      material.doExportByTypeDetail(param).then(res => {
        if (res && res.data && res.headers['content-disposition']) {
          const link = document.createElement('a')
          const url = window.URL.createObjectURL(res.data)
          const contentDisposition = res.headers['content-disposition']
          let filename = contentDisposition.replace(/(.*;)?filename=([^;]+).*/i, '$2')
          try {
            filename = decodeURIComponent(escape(filename))
          } catch (e) {
          }
          document.body.appendChild(link)
          link.style.display = 'none'
          link.download = filename
          link.href = url
          link.click()
          link.remove()
          window.URL.revokeObjectURL(url)
        } else {
          this.$notification.error({
            message: '系统消息',
            description: '下载失败！'
          })
        }
      })
    },
    callback (key) {
      // console.log(key)
    },
    initOptions () {
      baseApi.getCommboxById({ id: 'matrtype' }).then(res => {
        this.materialClass = res.result
      })
      baseApi.getCommboxById({ id: 'storehouse' }).then(res => {
        this.storehouse = res.result
      })
      baseApi.getCommboxById({ id: 'zone', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.zones = res.result
        }
      })
      baseApi.getCommboxById({ id: 'frame', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.frames = res.result
        }
      })
      baseApi.getCommboxById({ id: 'floor', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.floors = res.result
        }
      })
      baseApi.getCommboxById({ id: 'bit', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.bits = res.result
        }
      })
      // 类型细目
      baseApi.getCommboxById({ id: 'typedetail' }).then(res => {
        if (res.code === '0000') {
          this.typeDetails = res.result
        }
      })
    },
    doSearch () {
      this.$refs.table.refresh(true)
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    getDetail (materialNum) {
      const param = requestBuilder('', Object.assign({ 'type': 0, 'materialNum': materialNum }), this.pageNo, this.pageSize)
      material.queryMaterialBalances(param).then(res => {
        this.matr = []
        this.matu = []
        // console.log(res)
        if (res.code !== '0000') {
          this.$message.error(res.message)
        } else {
          for (const item of res.result.matr) {
            this.matr.push(item)
          }
          for (const item of res.result.matu) {
            this.matu.push(item)
          }
        }
      })
    }
  }
}
</script>
<style scoped>
.myDescriptions{
  margin: 0px 0px 10px 0px;
}
</style>
