<template>
  <section>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="10">
            <a-col
              :xl="4"
              :md="4"
              :sm="24"
            >
              <a-form-item label="类别编码">
                <a-input
                  @pressEnter="doSearch"
                  v-model="queryParam.codeClassId"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <a-col
              :xl="4"
              :md="4"
              :sm="24"
            >
              <a-form-item label="类别名称">
                <a-input
                  @pressEnter="doSearch"
                  v-model="queryParam.codeClassName"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <a-col
              :xl="4"
              :md="4"
              :sm="24"
            >
              <a-form-item label="状态">
                <a-select
                  v-model="queryParam.activity"
                  allowClear
                  default-value="Y"
                  @change="doSearch"
                >
                  >
                  <a-select-option value="Y">启用</a-select-option>
                  <a-select-option value="N">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <a-button
          v-action:query
          type="primary"
          icon="search"
          @click="doSearch"
        >查询</a-button>
        <a-button
          v-action:add
          type="primary"
          icon="plus"
          @click="openDrawer('insert')"
        >新增</a-button>
        <a-button
          v-action:edit
          type="primary"
          icon="edit"
          @click="doEdit"
        >修改</a-button>
        <a-button
          v-action:del
          type="danger"
          icon="delete"
          @click="doBatchDel('')"
        >删除</a-button>
      </div>
    </a-card>
    <a-card :bordered="false">
      <a-row :gutter="8">
        <a-col
          :md="12"
          :sm="12"
          :span="6"
        >
          <a-card title="代码类别">
            <a-button
              shape="circle"
              icon="reload"
              size="small"
              slot="extra"
              @click="doSearch"
            />
            <s-table
              ref="table"
              size="small"
              rowKey="codeClassId"
              :columns="columnsCode"
              :data="loadMClassData"
              :customRow="rowClick"
              :rowClassName="setRowClassName"
              :alert="options.alert"
              :rowSelection="options.rowSelection"
              :pageSizeOptions="pageSizeOptions"
              :pageSize="defaultPageSize"
              :bordered="false"
              :showPagination="true"
              :scroll="{ y: 500 }"
            >
              <span
                slot="serial"
                slot-scope="text, record, index"
              >{{ index + 1 }}</span>
              <span
                slot="description"
                slot-scope="text"
              >
                <ellipsis
                  :length="8"
                  tooltip
                >{{ text }}</ellipsis>
              </span>
              <span
                slot="activity"
                slot-scope="text"
              >{{ (text==='Y')?'启用':'禁用' }}</span>
            </s-table>
          </a-card>
        </a-col>
        <a-col
          :md="12"
          :sm="12"
          :span="18"
        >
          <a-card title="详细代码值">
            <a-button
              v-action:add
              shape="circle"
              type="primary"
              icon="plus"
              size="small"
              slot="extra"
              @click="openDrawer_Detail('insert')"
            />
            <a-button
              v-action:edit
              shape="circle"
              type="primary"
              icon="edit"
              size="small"
              slot="extra"
              @click="doEditDetail"
            />
            <a-button
              v-action:del
              shape="circle"
              type="danger"
              icon="delete"
              size="small"
              slot="extra"
              @click="doBatchDelDetail('')"
            />
            <s-table
              ref="tableDetail"
              size="small"
              rowKey="codeId"
              :columns="columnsDetail"
              :data="loadMCodeData"
              :alert="options.alert"
              :rowSelection="options.rowSelectionDetail"
              :showPagination="true"
              :immediate="false"
              :bordered="false"
            >
              <span
                slot="activity"
                slot-scope="text"
              >{{ (text==='Y')?'启用':'禁用' }}</span>
            </s-table>
          </a-card>
        </a-col>
      </a-row>
    </a-card>
    <a-card :bordered="false">
      <!-- 侧边滑动栏-基础代码类别 -->
      <template>
        <a-drawer
          :title="subTitle"
          :width="340"
          :visible="visible"
          :mask="false"
          :bodyStyle="{paddingBottom: '80px'}"
          @close="visible = false"
        >
          <a-spin :spinning="confirmLoading">
            <a-form :form="form">
              <a-row :gutter="24">
                <a-col
                  :md="24"
                  :sm="24"
                >
                  <a-form-item
                    label="类别编码:"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input
                      :disabled="IsProhibit !== '1'"
                      v-decorator="['codeClassId', {rules: [{required: true, message: '请输入至少一个字符！'}]}]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="24"
                  :sm="24"
                >
                  <a-form-item
                    label="类别名称:"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input
                      :disabled="IsProhibit !== '1'"
                      v-decorator="['codeClassName', {rules: [{required: true, message: '请输入至少一个字符！'}]}]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="24"
                  :sm="24"
                >
                  <a-form-item
                    label="显示顺序:"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input
                      v-decorator="['showSequence', {rules: [{required: true, message: '请输入至少一个字符！'}]}]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="24"
                  :sm="24"
                >
                  <a-form-item
                    label="状态:"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-select
                      v-decorator="['activity', {rules: [{required: true, message: '请选择是或否'}]}]"
                    >
                      <a-select-option value="Y">启用</a-select-option>
                      <a-select-option value="N">禁用</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :md="24"
                  :sm="24"
                >
                  <a-form-item
                    label="单位个性化:"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-select
                      v-decorator="['existPrivate', {rules: [{required: true, message: '请选择是或否'}]}]"
                    >
                      <a-select-option value="1">继承</a-select-option>
                      <a-select-option value="2">并列</a-select-option>
                      <a-select-option value="3">否</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-spin>
          <div
            :style="{
              position: 'absolute',
              right: 0,
              bottom: 0,
              width: '100%',
              borderTop: '1px solid #e9e9e9',
              padding: '10px 16px',
              background: '#fff',
              textAlign: 'right',
              zIndex: 1,
            }"
          >
            <a-button
              :style="{marginRight: '8px'}"
              @click="visible = false"
            >取消</a-button>
            <a-button
              @click="doSubmit"
              type="primary"
            >保存</a-button>
          </div>
        </a-drawer>
      </template>
      <!-- 侧边滑动栏 基础代码值-->
      <template>
        <a-drawer
          :title="subTitleDetail"
          :width="340"
          :visible="visibleDetail"
          :mask="false"
          :bodyStyle="{paddingBottom: '80px'}"
          @close="visibleDetail = false"
        >
          <a-spin :spinning="confirmLoading">
            <a-form :form="formDetail">
              <a-row :gutter="24">
                <a-form-item
                  v-show="false"
                  label="唯一编码:"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                  <a-input v-decorator="['codeSysId', {rules: [{required: false}]}]" />
                </a-form-item>
                <a-col
                  :md="24"
                  :sm="24"
                >
                  <a-form-item
                    label="类别编码:"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input
                      :disabled="true"
                      v-decorator="['codeClassId', {rules: [{required: true, message: '请输入至少一个字符！'}]}]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="24"
                  :sm="24"
                >
                  <a-form-item
                    label="代码编码:"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input
                      :disabled="IsProhibitDetail !== '1'"
                      v-decorator="['codeId', {rules: [{required: true, message: '请输入至少一个字符！'}]}]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="24"
                  :sm="24"
                >
                  <a-form-item
                    label="代码名称:"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input
                      :disabled="IsProhibitDetail !== '1'"
                      v-decorator="['codeName', {rules: [{required: true, message: '请输入至少一个字符！'}]}]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="24"
                  :sm="24"
                >
                  <a-form-item
                    label="补充说明1:"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input
                      v-decorator="['udf1', {rules: [{required: false, message: '请输入至少一个字符！'}]}]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="24"
                  :sm="24"
                >
                  <a-form-item
                    label="补充说明2:"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input
                      v-decorator="['udf2', {rules: [{required: false, message: '请输入至少一个字符！'}]}]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="24"
                  :sm="24"
                >
                  <a-form-item
                    label="补充说明3:"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input
                      v-decorator="['udf3', {rules: [{required: false, message: '请输入至少一个字符！'}]}]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="24"
                  :sm="24"
                >
                  <a-form-item
                    label="父节点:"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-select
                      optionFilterProp="label"
                      showSearch
                      allowClear
                      v-decorator="['parentId', {}]"
                    >
                      <a-select-option
                        v-for="(item, index) in parentIdList"
                        :value="item.value"
                        :label="item.label"
                        :key="index"
                      >{{ item.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :md="24"
                  :sm="24"
                >
                  <a-form-item
                    label="显示顺序:"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-input
                      v-decorator="['showSequence', {rules: [{required: true, message: '请输入至少一个字符！'}]}]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="24"
                  :sm="24"
                >
                  <a-form-item
                    label="状态:"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                  >
                    <a-select
                      v-decorator="['activity', {rules: [{required: true, message: '请选择是或否'}]}]"
                    >
                      <a-select-option value="Y">启用</a-select-option>
                      <a-select-option value="N">禁用</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-spin>
          <div
            :style="{
              position: 'absolute',
              right: 0,
              bottom: 0,
              width: '100%',
              borderTop: '1px solid #e9e9e9',
              padding: '10px 16px',
              background: '#fff',
              textAlign: 'right',
              zIndex: 1,
            }"
          >
            <a-button
              :style="{marginRight: '8px'}"
              @click="visibleDetail = false"
            >取消</a-button>
            <a-button
              :loading="confirmLoading"
              @click="doSubmitDetail"
              type="primary"
            >保存</a-button>
          </div>
        </a-drawer>
      </template>
    </a-card>
  </section>
</template>

<script>
import { STable, Ellipsis } from '@/components'
import { queryCodeBasClass, modifyCodeBasClass, queryCodeBasCode, modifyCodeBasCode } from '@/api/material/codebas'
import { requestBuilder } from '@/utils/util'
import * as baseApi from '@/api/material/base'

export default {
  name: 'CodeBas',
  components: {
    STable,
    Ellipsis
  },
  data () {
    return {
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20'],
      mdl: {},
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      // 联动表格参数
      linkParam: {},
      // 父节点
      parentIdList: [],
      // ==================左侧表格定义=====================
      // 基础代码类别表头定义
      columnsCode: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' }
        },
        {
          title: '类别编码',
          dataIndex: 'codeClassId'
        },
        {
          title: '类别名称',
          dataIndex: 'codeClassName'
        },
        {
          title: '显示顺序',
          dataIndex: 'showSequence',
          scopedSlots: { customRender: 'description' }
        },
        {
          title: '状态',
          dataIndex: 'activity',
          scopedSlots: { customRender: 'activity' }
        }
      ],
      // 加载基础代码类别数据
      loadMClassData: parameter => {
        const param = requestBuilder('', Object.assign(this.queryParam), parameter.pageNo, parameter.pageSize)
        return queryCodeBasClass(param).then(res => {
          return res.result
        })
      },
      rowClick: record => ({
        on: {
          click: () => {
            this.linkParam = {
              codeClassId: record.codeClassId
            }
            this.classId = record.codeClassId
            // 用于存储需要变色的行
            this.discolorationNum = {
              rowId: record.codeClassId
            }
            // 得到父节点
            this.getparentIdList(record.codeClassId)
            this.$refs.tableDetail.refresh(true)
          }
        }
      }),
      discolorationNum: {},
      setRowClassName: record => {
        return record.codeClassId === this.discolorationNum.rowId ? 'clickRowStyl' : ''
      },
      // ==================右侧表格定义=====================
      // 基础代码值详情表头定义
      columnsDetail: [
        {
          title: '代码编码',
          dataIndex: 'codeId'
        },
        {
          title: '代码名称',
          dataIndex: 'codeName'
        },
        {
          title: '补充说明1',
          dataIndex: 'udf1'
        },
        {
          title: '补充说明2',
          dataIndex: 'udf2'
        },
        {
          title: '补充说明3',
          dataIndex: 'udf3'
        },
        {
          title: '排序',
          dataIndex: 'showSequence'
        },
        {
          title: '状态',
          dataIndex: 'activity',
          scopedSlots: { customRender: 'activity' }
        }
      ],
      // 加载基础代码值数据
      loadMCodeData: parameter => {
        const param = requestBuilder('', Object.assign(this.linkParam), parameter.pageNo, parameter.pageSize)
        return queryCodeBasCode(param).then(res => {
          return res.result
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      selectedRowKeysDetail: [],
      selectedRowsDetail: [],
      // 自定义表信息及行选择
      options: {
        alert: {
          show: false,
          clear: () => {
            this.selectedRowKeys = []
            this.selectedRowKeysDetail = []
          }
        },
        rowSelection: {
          // selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        },
        rowSelectionDetail: {
          // selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChangeDetail
        }
      },
      form: this.$form.createForm(this),
      formDetail: this.$form.createForm(this),
      // 加载图标是否出现
      confirmLoading: false,
      optionAlertShow: false,
      selectedItems: [],
      // 用于保存公共代码表点击时当前列的codeClassId值
      classId: '',
      visible: false,
      visibleDetail: false,
      // 判断基础代码弹窗（Form）的字段是否为只读
      IsProhibit: '',
      // 判断基础代码值弹窗（FormDetail）的字段是否为只读
      IsProhibitDetail: '',
      subTitle: '新增公共代码类别',
      subTitleDetail: '新增公共代码值',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      }
    }
  },
  filters: {},
  created () {},
  methods: {
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    onSelectChangeDetail (selectedRowKeysDetail, selectedRowsDetail) {
      this.selectedRowKeysDetail = selectedRowKeysDetail
      this.selectedRowsDetail = selectedRowsDetail
    },
    doBatchDel (record) {
      const codeClassIds = []
      if (record !== '') {
        codeClassIds.push(record.codeClassId)
      } else {
        if (this.selectedRows.length > 0) {
          for (let i = 0; i < this.selectedRows.length; i++) {
            codeClassIds.push(this.selectedRows[i].codeClassId)
          }
        } else {
          this.$message.error('未选中任何记录')
          return
        }
      }
      this.$confirm({
        title: '确认?',
        content: '是否删除此记录',
        onOk: () => {
          this.handleDel(codeClassIds.join(','))
        },
        onCancel () {}
      })
    },
    doBatchDelDetail (record) {
      const codeSysIds = []
      if (record !== '') {
        codeSysIds.push(record.codeSysId)
      } else {
        if (this.selectedRowsDetail.length > 0) {
          for (let i = 0; i < this.selectedRowsDetail.length; i++) {
            codeSysIds.push(this.selectedRowsDetail[i].codeSysId)
          }
        } else {
          this.$message.error('未选中任何记录')
          return
        }
      }
      this.$confirm({
        title: '确认?',
        content: '是否删除此记录',
        onOk: () => {
          this.handleDelDetail(codeSysIds.join(','))
        },
        onCancel () {}
      })
    },
    handleDel (value) {
      this.actionFlag = 'delete'
      this.params = {
        codeClassId: value
      }
      this.getResult(this.params)
    },
    handleDelDetail (value) {
      this.actionFlag = 'delete'
      this.params = {
        codeSysId: value
      }
      this.getResultDetail(this.params)
    },
    doSearch () {
      this.visible = false
      this.classId = ''
      this.discolorationNum = {}
      this.$refs.table.refresh()
      this.$refs.table.updateSelect([], [])
      this.$refs.table.rowSelection.onChange([], [])
    },
    doSearchDetail () {
      this.$refs.tableDetail.refresh()
      this.$refs.tableDetail.updateSelect([], [])
      this.$refs.tableDetail.rowSelectionDetail.onChange([], [])
    },
    doEdit () {
      if (this.selectedRows.length === 1) {
        this.openDrawer('update', this.selectedRows[0])
      } else {
        this.$message.error('请勾选一条记录')
      }
    },
    doEditDetail () {
      if (this.selectedRowsDetail.length === 1) {
        this.openDrawer_Detail('update', this.selectedRowsDetail[0])
      } else {
        this.$message.error('请勾选一条记录')
      }
    },
    openDrawer (action, record) {
      this.visibleDetail = false
      this.visible = true
      // this.record = record
      this.$nextTick(() => {
        if (action === 'insert') {
          this.form.resetFields()
          this.form.setFieldsValue({
            isStop: 'N',
            activity: 'Y'
          })
          this.subTitle = '新增公共代码类别'
          this.IsProhibit = '1'
          this.actionFlag = 'insert'
        } else {
          this.subTitle = '修改公共代码类别'
          this.IsProhibit = record.codeClassId ? '2' : '1'
          this.form.setFieldsValue({
            codeClassId: record.codeClassId,
            codeClassName: record.codeClassName,
            showSequence: record.showSequence,
            activity: record.activity,
            existPrivate: record.existPrivate || ''
          })
          this.actionFlag = 'update'
        }
      })
    },
    openDrawer_Detail (action, record) {
      this.visible = false
      this.visibleDetail = true
      // this.record = record
      this.$nextTick(() => {
        if (action === 'insert') {
          // 判断是否选中父类编码
          if (this.classId) {
            this.formDetail.resetFields()
            this.formDetail.setFieldsValue({
              codeClassId: this.classId,
              // codeClassName: this.selectedRows[0].codeClassName,
              activity: 'Y'
            })
            this.subTitleDetail = '新增公共代码值'
            this.IsProhibitDetail = '1'
            this.actionFlag = 'insert'
          } else {
            // 当未选中父类时关闭抽屉
            this.visibleDetail = false
            this.$message.error('新增代码值时需点击一条代码类别')
          }
        } else {
          this.subTitleDetail = '修改公共代码值'
          this.IsProhibitDetail = record.codeClassId ? '2' : '1'
          this.formDetail.setFieldsValue({
            codeSysId: record.codeSysId,
            codeClassId: record.codeClassId,
            codeId: record.codeId,
            codeName: record.codeName,
            udf1: record.udf1,
            udf2: record.udf2,
            udf3: record.udf3,
            parentId: record.parentId || '0',
            showSequence: record.showSequence,
            activity: record.activity
          })
          // this.getRoleListByUserNo(record.userNo)
          // this.temppass = record.password
          this.actionFlag = 'update'
        }
      })
    },
    // 保存数据
    doSubmit () {
      const {
        form: { validateFields }
      } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        // 如果输入数据都合规
        if (!errors) {
          this.params = { ...values }
          this.getResult(this.params)
        } else {
          this.confirmLoading = false
        }
      })
    },
    // 保存详细代码数据
    doSubmitDetail () {
      const {
        formDetail: { validateFields }
      } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        // 如果输入数据都合规
        if (!errors) {
          this.params = { ...values }
          this.getResultDetail(this.params)
        } else {
          this.confirmLoading = false
        }
      })
    },
    // 公共代码类别数据增删改
    getResult (params) {
      modifyCodeBasClass(requestBuilder(this.actionFlag, this.params, '0', '0'))
        .then(res => {
          if (res.code === '0000') {
            this.doSearch()
            this.$message.success(res.message)
          } else if (res.code === '9999') {
            this.$message.error('发生异常 ')
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.confirmLoading = false
        })
    },
    // 公共代码值数据增删改
    getResultDetail (params) {
      modifyCodeBasCode(requestBuilder(this.actionFlag, this.params, '0', '0'))
        .then(res => {
          if (res.code === '0000') {
            this.visibleDetail = false
            this.doSearchDetail()
            this.$message.success(res.message)
          } else {
            this.$message.error(res.message || '发生异常')
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.confirmLoading = false
        })
    },
    getparentIdList (codeClassId) {
      baseApi.getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: codeClassId } }).then(res => {
        if (res.code === '0000') {
          this.parentIdList = res.result
          this.parentIdList.push({ value: '0', label: '无父节点' })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep {
  .clickRowStyl {
    background-color: #d2eef7;
  }
}
</style>
