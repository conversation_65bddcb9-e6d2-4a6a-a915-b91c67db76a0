<template>
  <section>
    <div class="nodes-container">
      <div :style="{ width: 100 / 5 + '%' }" class="nodes-group" v-for="(title, index) of titles" :key="title">
        <div class="nodes-header">
          <span class="title">{{ title }}</span>
          <a href="javascript:void(0)" v-action:add @click="openDrawer(index,'insert')">新增</a>
        </div>
        <div class="nodes-body">
          <div v-if="getData(index) && getData(index).length>0" class="list" >
            <div
              :class="{ active: isActive(index,item) } "
              class="item"
              v-for="item in getData(index)"
              :key="item.key"
              @click="doChange(item)"
            >
              <!-- <a-icon v-if="isDisabled()" class="icon" type="minus-circle" />
              <a-icon v-else class="icon" type="check-circle" /> -->
              <span class="text">{{ item.title }}</span>
              <a href="javascript:void(0)" class="button" v-action:edit @click.stop="openDrawer(index,'update',item)">修改</a>
              <a class="button" style="color:#F34D4D" v-action:del @click.stop="doDel(item)">删除</a>
            </div>
          </div>
          <a-empty
            v-else
            class="empty"
            :description="false"
          />
        </div>
      </div>
    </div>
    <a-drawer
      :title="drawerTitle"
      :width="360"
      :visible="drawerFlag"
      :mask="true"
      :bodyStyle="{paddingBottom: '80px'}"
      @close="drawerFlag = false"
    >
      <a-spin :spinning="spinFlag">
        <!-- 库开始 -->
        <a-form :form="houseForm" layout="vertical" hideRequiredMark v-show="showHouseForm">
          <a-form-item
            label="仓库系统编码:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            v-show="false"
          >
            <a-input v-decorator="['storehouseSysId']" :readOnly="true"/>
          </a-form-item>
          <a-form-item
            label="仓库类型:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-select v-decorator="['storehouseType', {rules: [{required: true, message: '请选择仓库类型'}]}]">
              <a-select-option value="一级库">
                一级库
              </a-select-option>
              <a-select-option value="二级库">
                二级库
              </a-select-option>
              <a-select-option value="报废库">
                报废库
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item
            label="所属部门:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-tree-select
              v-decorator="['deptList', {rules: []}]"
              multiple
              treeNodeFilterProp="label"
              :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
              :treeData="depts"
              showSearch
              allowClear
            />
          </a-form-item>
          <a-form-item
            label="仓库编码:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input v-decorator="['storehouseNum', {rules: [{required: true, message: '仓库编码不能为空！'}]}]" />
          </a-form-item>
          <a-form-item
            label="仓库名称:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input v-decorator="['description', {rules: [{required: true, message: '仓库名称不能为空！'}]}]" />
          </a-form-item>
          <a-form-item
            v-if="IS_DXZL_ENV"
            label="操作用户:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-select
              optionFilterProp="label"
              showSearch
              allowClear
              v-decorator="['operator', {rules: []}]">
              <a-select-option
                v-for="(item, index) in requestedBys"
                :value="item.value"
                :label="item.label"
                :key="index"
              >{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
        <!-- 库结束 -->
        <!-- 区开始 -->
        <a-form :form="zoneForm" layout="vertical" hideRequiredMark v-show="showZoneForm">
          <a-form-item
            label="区编码:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input v-decorator="['zoneNum', {rules: [{required: true, message: '区编码不能为空！'}]}]" />
          </a-form-item>
          <a-form-item
            label="区名称:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input v-decorator="['description', {rules: [{required: true, message: '区名称不能为空！'}]}]" />
          </a-form-item>
          <a-form-item
            label="库编码:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            v-show="false"
          >
            <a-input :readOnly="true" v-decorator="['storehouseSysId', {rules: [{required: true, message: '库编码不能为空！'}]}]" />
          </a-form-item>
          <a-form-item
            label="区系统编码:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            v-show="false"
          >
            <a-input :readOnly="true" v-decorator="['zoneSysId']" />
          </a-form-item>
          <a-form-item
            label="所属库:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['title']" />
          </a-form-item>
        </a-form>
        <!-- 区结束 -->
        <!-- 架开始 -->
        <a-form :form="frameForm" layout="vertical" hideRequiredMark v-show="showFrameForm">
          <a-form-item
            label="架编码:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input v-decorator="['frameNum', {rules: [{required: true, message: '架编码不能为空！'}]}]" />
          </a-form-item>
          <a-form-item
            label="架名:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input v-decorator="['description', {rules: [{required: true, message: '架名不能为空！'}]}]" />
          </a-form-item>
          <a-form-item
            label="H:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input v-decorator="['h', {rules: [{required: false, message: '该字段不能为空!'}]}]" />
          </a-form-item>
          <a-form-item
            label="W:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input v-decorator="['w', {rules: [{required: false, message: '该字段不能为空!'}]}]" />
          </a-form-item>
          <a-form-item
            label="X:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input v-decorator="['x', {rules: [{required: false, message: '该字段不能为空!'}]}]" />
          </a-form-item>
          <a-form-item
            label="Y:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input v-decorator="['y', {rules: [{required: false, message: '该字段不能为空!'}]}]" />
          </a-form-item>
          <a-form-item
            label="区编码"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            v-show="false"
          >
            <a-input v-decorator="['zoneSysId']" />
          </a-form-item>
          <a-form-item
            label="所属区:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['title']" />
          </a-form-item>
          <a-form-item
            label="架系统编码"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            v-show="false"
          >
            <a-input v-decorator="['frameSysId']" :readOnly="true" />
          </a-form-item>
        </a-form>
        <!-- 架结束 -->
        <!-- 层开始 -->
        <a-form :form="floorForm" layout="vertical" hideRequiredMark v-show="showFloorForm">
          <a-form-item
            label="层编码:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input v-decorator="['floorNum', {rules: [{required: true, message: '层编码不能为空！'}]}]" />
          </a-form-item>
          <a-form-item
            label="层名称:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input v-decorator="['description', {rules: [{required: true, message: '层名称不能为空！'}]}]" />
          </a-form-item>
          <a-form-item
            label="架编码:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            v-show="false"
          >
            <a-input v-decorator="['frameSysId']" />
          </a-form-item>
          <a-form-item
            label="所属架:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['title']" />
          </a-form-item>
          <a-form-item
            label="层系统编码:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            v-show="false"
          >
            <a-input v-decorator="['floorSysId']" :readonly="true" />
          </a-form-item>
        </a-form>
        <!-- 层结束 -->
        <!-- 位开始 -->
        <a-form :form="bitForm" layout="vertical" hideRequiredMark v-show="showBitForm">
          <a-form-item
            label="位编码:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input v-decorator="['bitNum', {rules: [{required: true, message: '位编码不能为空！'}]}]" />
          </a-form-item>
          <a-form-item
            label="位名称:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input v-decorator="['description', {rules: [{required: true, message: '位名称不能为空！'}]}]" />
          </a-form-item>
          <a-form-item
            label="层编码:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            v-show="false"
          >
            <a-input v-decorator="['floorSysId']" />
          </a-form-item>
          <a-form-item
            label="所属层:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input disabled v-decorator="['title']" />
          </a-form-item>
          <a-form-item
            label="位系统编码:"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            v-show="false"
          >
            <a-input :readonly="true" v-decorator="['bitSysId']" />
          </a-form-item>
        </a-form>
        <!-- 位结束 -->
      </a-spin>
      <div
        :style="{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
          zIndex: 1,
        }"
      >
        <a-button :style="{marginRight: '8px'}" @click="drawerFlag = false">
          取消
        </a-button>
        <a-button type="primary" @click="doSubmit()">保存</a-button>
      </div>
    </a-drawer>
    <div>
      <a-modal title="确定删除吗？" v-model="modalVisible" @ok="handleOk">
        <p>此操作不可逆</p>
      </a-modal>
    </div>
  </section>
</template>

<script>
import * as area from '@/api/material/area'
import * as baseApi from '@/api/material/base'
import { requestBuilder } from '@/utils/util'
import { ORG_ID } from '@/store/mutation-types'
import Vue from 'vue'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_DXZL_ENV = USER_ORG_ID === '2.001'
export default {
  name: 'AreaManagement',
  data () {
    return {
      IS_DXZL_ENV,
      depts: [],
      modalItem: '',
      modalVisible: false,
      bitForm: this.$form.createForm(this),
      floorForm: this.$form.createForm(this),
      frameForm: this.$form.createForm(this),
      zoneForm: this.$form.createForm(this),
      houseForm: this.$form.createForm(this),
      showBitForm: false,
      showFloorForm: false,
      showFrameForm: false,
      showZoneForm: false,
      showHouseForm: false,
      spinFlag: false,
      drawerTitle: '',
      drawerFlag: false,
      titles: ['库', '区', '架', '层', '位'],
      actionFlag: '',
      series: 5,
      queryParam: {},
      result: [],
      actives: [],
      treeData: [],
      nodes: [],
      requestedBys: [],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      }
    }
  },
  computed: {
  },
  mounted () {
    this.getStoreHouse()
  },
  watch: {},
  created () {
    this.initOptions()
  },
  methods: {
    initOptions () {
      baseApi.getTreeById({ id: 'dept', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.depts = res.result
        }
      })
      baseApi.getCommboxById({ id: 'applyPerson' }).then(res => {
        if (res.code === '0000') {
          this.requestedBys = res.result
        }
      })
    },
    handleOk () {
      const item = this.modalItem
      this.actionFlag = 'delete'
      if (item.level === '0') {
        this.modifyStorehouse({ storehouseSysId: item.key })
      } else if (item.level === '1') {
        this.modifyZone({ zoneSysId: item.key })
      } else if (item.level === '2') {
        this.modifyFrame({ frameSysId: item.key })
      } else if (item.level === '3') {
        this.modifyFloor({ floorSysId: item.key })
      } else if (item.level === '4') {
        this.modifyBit({ bitSysId: item.key })
      }
      this.modalVisible = false
    },
    doDel (item) {
      // console.log('del', JSON.stringify(item))
      this.modalVisible = true
      this.modalItem = item
    },
    doSubmit () {
      if (this.showHouseForm) {
        this.houseForm.validateFields((err, values) => {
          if (!err) {
            this.spinFlag = true
            this.modifyStorehouse(values)
            // console.log('Received values of form: ', values)
          } else {
            this.$message.error('字段未填写正确')
          }
        })
        // console.log(JSON.stringify(this.actives))
      }
      if (this.showZoneForm) {
        this.zoneForm.validateFields((err, values) => {
          if (!err) {
            this.spinFlag = true
            this.modifyZone(values)
            // this.modifyZone({ ...values, a: values.b })
          } else {
            this.$message.error('字段未填写正确')
          }
        })
      }
      if (this.showFrameForm) {
        this.frameForm.validateFields((err, values) => {
          if (!err) {
            this.spinFlag = true
            this.modifyFrame(values)
          } else {
            this.$message.error('字段未填写正确')
          }
        })
      }
      if (this.showFloorForm) {
        this.floorForm.validateFields((err, values) => {
          if (!err) {
            this.spinFlag = true
            this.modifyFloor(values)
          } else {
            this.$message.error('字段未填写正确')
          }
        })
      }
      if (this.showBitForm) {
        this.bitForm.validateFields((err, values) => {
          if (!err) {
            this.spinFlag = true
            this.modifyBit(values)
          } else {
            this.$message.error('字段未填写正确')
          }
        })
      }
    },
    modifyBit (params) {
      area.modifyBit(requestBuilder(this.actionFlag, params, '0', '0'))
        .then(res => {
          if (res.code === '0000') {
            this.doChange(this.actives[3])
            this.$message.success(res.message)
          } else if (res.code === '9999') {
            this.$message.error(res.message)
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.spinFlag = false
        })
    },
    modifyFloor (params) {
      area.modifyFloor(requestBuilder(this.actionFlag, params, '0', '0'))
        .then(res => {
          if (res.code === '0000') {
            this.doChange(this.actives[2])
            this.$message.success(res.message)
          } else if (res.code === '9999') {
            this.$message.error(res.message)
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.spinFlag = false
        })
    },
    modifyFrame (params) {
      area.modifyFrame(requestBuilder(this.actionFlag, params, '0', '0'))
        .then(res => {
          if (res.code === '0000') {
            this.doChange(this.actives[1])
            this.$message.success(res.message)
          } else if (res.code === '9999') {
            this.$message.error(res.message)
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.spinFlag = false
        })
    },
    modifyZone (params) {
      area.modifyZone(requestBuilder(this.actionFlag, params, '0', '0'))
        .then(res => {
          if (res.code === '0000') {
            this.doChange(this.actives[0])
            this.$message.success(res.message)
          } else if (res.code === '9999') {
            this.$message.error(res.message)
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.spinFlag = false
        })
    },
    modifyStorehouse (params) {
      area.modifyStorehouse(requestBuilder(this.actionFlag, params, '0', '0'))
        .then(res => {
          if (res.code === '0000') {
            this.actives.splice(0, 4)
            this.getStoreHouse()
            this.drawerFlag = false
            this.$message.success(res.message)
          } else if (res.code === '9999') {
            this.$message.error(res.message)
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.spinFlag = false
        })
    },
    openDrawer (index, action, item) {
      this.actionFlag = action
      if (action === 'insert') {
        if (index === 0) {
          this.drawerFlag = true
          this.drawerTitle = '新增库'
          this.showHouseForm = true
          this.showZoneForm = false
          this.showFrameForm = false
          this.showFloorForm = false
          this.showBitForm = false
          this.houseForm.resetFields()
        }
        if (index === 1) {
          this.zoneForm.resetFields()
          if (this.actives[index - 1]) {
            this.drawerFlag = true
            this.drawerTitle = '新增区'
            this.showHouseForm = false
            this.showZoneForm = true
            this.showFrameForm = false
            this.showFloorForm = false
            this.showBitForm = false
            this.$nextTick(() => {
              this.zoneForm.setFieldsValue({
                storehouseSysId: this.actives[index - 1].key,
                title: this.actives[index - 1].title
              })
            })
          } else {
            this.$message.error('未选择库！ ')
            return
          }
        }
        if (index === 2) {
          this.frameForm.resetFields()
          // console.log(JSON.stringify(this.actives))
          if (this.actives[index - 1]) {
            this.drawerFlag = true
            this.drawerTitle = '新增架'
            this.showHouseForm = false
            this.showZoneForm = false
            this.showFrameForm = true
            this.showFloorForm = false
            this.showBitForm = false
            this.$nextTick(() => {
              this.frameForm.setFieldsValue({
                zoneSysId: this.actives[index - 1].key,
                title: this.actives[index - 1].title
              })
            })
          } else {
            this.$message.error('未选择区！ ')
            return
          }
        }
        if (index === 3) {
          this.floorForm.resetFields()
          if (this.actives[index - 1]) {
            this.drawerFlag = true
            this.drawerTitle = '新增层'
            this.showHouseForm = false
            this.showZoneForm = false
            this.showFrameForm = false
            this.showFloorForm = true
            this.showBitForm = false
            this.$nextTick(() => {
              this.floorForm.setFieldsValue({
                frameSysId: this.actives[index - 1].key,
                title: this.actives[index - 1].title
              })
            })
          } else {
            this.$message.error('未选择架!')
          }
        }
        if (index === 4) {
          this.bitForm.resetFields()
          if (this.actives[index - 1]) {
            this.drawerFlag = true
            this.drawerTitle = '新增位'
            this.showHouseForm = false
            this.showZoneForm = false
            this.showFrameForm = false
            this.showFloorForm = false
            this.showBitForm = true
            this.$nextTick(() => {
              this.bitForm.setFieldsValue({
                floorSysId: this.actives[index - 1].key,
                title: this.actives[index - 1].title
              })
            })
          } else {
            this.$message.error('未选择层！')
          }
        }
      } else if (action === 'update') {
        console.log(item)
        if (index === 0) {
          this.houseForm.resetFields()
          this.drawerFlag = true
          this.drawerTitle = '修改库信息'
          this.showHouseForm = true
          this.showZoneForm = false
          this.showFrameForm = false
          this.showFloorForm = false
          this.showBitForm = false
          this.$nextTick(() => {
            this.houseForm.setFieldsValue({
              storehouseNum: item.num,
              deptList: item.deptList,
              storehouseSysId: item.key,
              description: item.title,
              storehouseType: item.type,
              operator: item.operator
            })
          })
        }
        if (index === 1) {
          this.zoneForm.resetFields()
          this.drawerFlag = true
          this.drawerTitle = '修改区信息'
          this.showHouseForm = false
          this.showZoneForm = true
          this.showFrameForm = false
          this.showFloorForm = false
          this.showBitForm = false
          this.$nextTick(() => {
            this.zoneForm.setFieldsValue({
              storehouseSysId: item.fatherid,
              description: item.title,
              zoneNum: item.num,
              zoneSysId: item.key,
              title: this.actives[index - 1].title
            })
          })
        }
        if (index === 2) {
          this.frameForm.resetFields()
          this.drawerFlag = true
          this.drawerTitle = '修改架信息'
          this.showHouseForm = false
          this.showZoneForm = false
          this.showFrameForm = true
          this.showFloorForm = false
          this.showBitForm = false
          this.$nextTick(() => {
            this.frameForm.setFieldsValue({
              w: item.x,
              h: item.h,
              x: item.x,
              y: item.y,
              frameNum: item.num,
              description: item.title,
              zoneSysId: item.fatherid,
              frameSysId: item.key,
              title: this.actives[index - 1].title
            })
          })
        }
        if (index === 3) {
          this.floorForm.resetFields()
          this.drawerFlag = true
          this.drawerTitle = '修改层信息'
          this.showHouseForm = false
          this.showZoneForm = false
          this.showFrameForm = false
          this.showFloorForm = true
          this.showBitForm = false
          this.$nextTick(() => {
            this.floorForm.setFieldsValue({
              floorSysId: item.key,
              description: item.title,
              floorNum: item.num,
              frameSysId: item.fatherid,
              title: this.actives[index - 1].title
            })
          })
        }
        if (index === 4) {
          this.bitForm.resetFields()
          this.drawerFlag = true
          this.drawerTitle = '修改位信息'
          this.showHouseForm = false
          this.showZoneForm = false
          this.showFrameForm = false
          this.showFloorForm = false
          this.showBitForm = true
          this.$nextTick(() => {
            this.bitForm.setFieldsValue({
              bitNum: item.num,
              description: item.title,
              floorSysId: item.fatherid,
              bitSysId: item.key,
              title: this.actives[index - 1].title
            })
          })
        }
      }
    },
    getData (level) {
      if (level === 0) {
        return this.treeData
      } else if (level === 1) {
        if (this.actives[0]) {
          return this.actives[0].children
        }
      } else if (level === 2) {
        if (this.actives[1]) {
          return this.actives[1].children
        }
      } else if (level === 3) {
        if (this.actives[2]) {
          return this.actives[2].children
        }
      } else if (level === 4) {
        if (this.actives[3]) {
          return this.actives[3].children
        }
      }
    },
    async getChild (treeNode) {
      const level = treeNode.level
      // console.log(level, treeNode)
      if (level === '0') {
        const response = await this.getZone()
        this.$set(treeNode, 'children', response.result)
        this.actives.splice(1, 4)
      } else if (level === '1') {
        const response = await this.getFrame()
        this.$set(treeNode, 'children', response.result)
        this.actives.splice(2, 3)
      } else if (level === '2') {
        const response = await this.getFloor()
        this.$set(treeNode, 'children', response.result)
        this.actives.splice(3, 2)
      } else if (level === '3') {
        const response = await this.getBit()
        this.$set(treeNode, 'children', response.result)
      }
    },
    async getStoreHouse () {
      await area.queryStorehouseTree(requestBuilder(this.actionFlag, {}, '0', '0'))
        .then(res => {
          this.treeData = res.result
          // console.log(JSON.stringify(this.treeData))
          this.doArray(res.result)
        })
    },
    getZone () {
      return area.queryZoneTree(requestBuilder(this.actionFlag, { 'storehouseSysId': this.actives[0].key }, '0', '0'))
    },
    getFrame () {
      return area.queryFrameTree(requestBuilder(this.actionFlag, { 'zoneSysId': this.actives[1].key }, '0', '0'))
    },
    getFloor () {
      return area.queryFloorTree(requestBuilder(this.actionFlag, { 'frameSysId': this.actives[2].key }, '0', '0'))
    },
    getBit () {
      return area.queryBitTree(requestBuilder(this.actionFlag, { 'floorSysId': this.actives[3].key }, '0', '0'))
    },
    doChange (item) {
      const index = item.level
      this.$set(this.actives, index, item)
      this.getChild(item)
      // console.log(JSON.stringify(this.nodes))
    },
    isActive (level, node) {
      const actives = this.actives
      return actives[level] && actives[level].key === node.key
    },
    doArray (tree, parent) {
      const empty = { level: 0 }
      const upper = parent || empty
      const level = upper.level
      // console.log('level', level)
      // console.log('nodes', this.nodes)
      if (!this.nodes[level]) {
        this.$set(this.nodes, level, [])
      }
      for (const item of tree) {
        this.$set(item, 'parent', upper)
        this.nodes[item.level].push(item)
        if (item.children) {
          this.doArray(item.children, item)
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep {
  .ant-empty-image {
    height: 65px;
  }
}
section {
  width: 100%;
  height: 100%;
  position: absolute;
  background-color: #ffffff;
}
.nodes-container {
  height: 100%;
  padding: 20px 10px 5px;
  box-sizing: border-box;
  flex-flow: row nowrap;
  overflow: auto;
  display: flex;
  & > .nodes-group {
    min-width: 250px;
    max-width: 350px;
    padding: 15px 25px 5px;
    box-sizing: border-box;
    position: relative;
    flex: 0 0 auto;
    &:not(:last-child)::after {
      content: '';
      width: 1px;
      height: calc(100% - 20px);
      background: #eeeeee;
      position: absolute;
      top: 10px;
      right: 1px;
    }
    .nodes-header {
      padding: 5px 5px 8px;
      text-align: center;
      border-bottom: solid 1px #f0f0f0;
      & > a {
        font-size: 13px;
        margin: 0 8px;
      }
      & > .title {
        font-size: 16px;
        margin: 0 8px;
      }
    }
    .nodes-body {
      height: calc(100% - 40px);
      padding: 10px 3px;
      & > .list {
        height: 100%;
        overflow: auto;
        & > .item {
          padding: 8px 8px;
          display: flex;
          cursor: default;
          &:hover,
          &:active {
            background-color: #f9f9f9;
            & > .button {
              display: inline-block;
            }
          }
          &.disabled {
             & > .icon {
               color: #f34d4d;
             }
             & > .text {
               color: #f34d4d;
             }
          }
          &.active {
            background-color: #f3f3f3;
          }
          & > .icon {
            height: 16px;
            padding: 3.5px;
            font-size: 16px;
            vertical-align: middle;
            display: inline-block;
          }
          & > .text {
            width: 100%;
            font-size: 16px;
            vertical-align: middle;
            display: inline-block;
            text-overflow: ellipsis;
            white-space:nowrap;
            overflow: hidden;
            flex: 0 1 auto;
          }
          & > .button {
            font-size: 13px;
            padding: 2px 3px;
            vertical-align: middle;
            flex: 0 0 auto;
            display: none;
          }
        }
      }
      & > .empty {
        padding: 10px 0;
      }
    }
  }
}
</style>
