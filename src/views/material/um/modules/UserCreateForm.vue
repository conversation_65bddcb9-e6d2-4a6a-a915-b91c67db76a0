<template>
  <a-modal
    :title="title"
    :width="640"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="ID:"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['id']" readonly/>
        </a-form-item>
        <a-form-item
          label="用户名:"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['userNo', {rules: [{required: true, min: 5, message: '请输入至少五个字符！'}]}]" />
        </a-form-item>
        <a-form-item
          label="姓名:"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['personName', {rules: [{required: true, min: 1, message: '请输入至少一个字符！'}]}]" />
        </a-form-item>
        <a-form-item
          label="手机号码:"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['mobilePhone', {rules: [{required: true, len: 11, max:11, message: '请输入11位手机号码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="密码:"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input
            v-decorator="['password', {rules: [{required: true, min: 8, message: '请输入8位以上密码'}]}]"
            type="password"
          />
        </a-form-item>
        <a-form-item
          label="身份证:"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <!-- <a-input v-decorator="['idCard', {rules: [{required: false, min:18, len: 18, message: '请输入18位身份证号码!'}]}]" /> -->
          <a-input v-decorator="['idCard']" />
        </a-form-item>
        <a-form-item
          label="所属组织:"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['orgName', {rules: [{required: false, min:18, len: 18, message: '组织不能为空'}]}]" />
        </a-form-item>
        <a-form-item
          label="所属部门:"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-input v-decorator="['departmentName', {rules: [{required: false, min:18, len: 18, message: '不能不能为空'}]}]" />
        </a-form-item>
        <a-form-item
          label="所属角色:"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-select
            mode="multiple"
            placeholder="选择角色..."
            :value="selectedItems"
            @change="handleChange"
            style="width: 100%"
          >
            <a-select-option v-for="item in rolesList" :key="item" :value="item.roleId">
              {{ item.roleName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label="用户状态:"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-select v-decorator="['activity', {rules: [{required: true, message: '请选择状态'}]}]" default-value="Y">
            <a-select-option value="Y">
              启用
            </a-select-option>
            <a-select-option value="N">
              禁用
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { getRoleInfo, getRoleInfoByUserNo } from '@/api/material/um'
import md5 from 'md5'
import { requestBuilder } from '@/utils/util'

export default {
  data () {
    return {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 }
      },
      rolesList: [], // 角色列表
      selectedItems: [],
      defaultValue: [],
      visible: false, // 窗口是否显示
      confirmLoading: false, // 加载图标是否出现
      params: {},
      actionFlag: 'add',
      title: '用户新增',
      form: this.$form.createForm(this),
      temppass: ''
    }
  },
  computed: {},
  methods: {
    // filteredOptions () {
    //   return roles.filter(o => !this.selectedItems.includes(o))
    // },
    handleChange (selectedItems) {
      this.selectedItems = selectedItems
    },
    add () {
      this.form.resetFields()
      this.form.setFieldsValue({
        orgName: '宁波港信息通信有限公司',
        departmentName: '软件开发部'
      })
      this.selectedItems = []
      this.actionFlag = 'add'
      this.title = '用户新增'
      this.visible = true
      this.getRoleList()
    },
    edit (record) {
      this.title = '用户编辑'
      // this.form.setFieldsValue({
      //   id: record.id,
      //   userNo: record.userNo,
      //   mobilePhone: record.mobilePhone,
      //   personName: record.personName,
      //   password: record.password,
      //   idCard: record.idCard,
      //   orgName: record.orgName,
      //   departmentName: record.departmentName,
      //   activity: record.activity
      // })
      this.getRoleListByUserNo(record.userNo)
      this.temppass = record.password
      this.actionFlag = 'update'
      this.visible = true
    },
    handleSubmit () {
      const { form: { validateFields } } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        // 如果输入数据都合规
        if (!errors) {
          this.params = { ...values }
          if (this.actionFlag === 'add') {
            this.params.password = md5(values.password)
            // addUserInfo(this.params)
            //   .then((res) => {
            //     if (res.resCode === '2') {
            //       this.$notification.warning({
            //         message: '警告',
            //         description: res.resDesc,
            //         duration: 1
            //       })
            //     } else if (res.resCode === '1') {
            //       this.$notification.success({
            //         message: '成功',
            //         description: '插入成功',
            //         duration: 1
            //       })
            //       this.$emit('ok')
            //       this.visible = false
            //     }
            //   })
            //   .catch(err => this.requestFailed(err))
            //   .finally(() => {
            //     this.confirmLoading = false
            //   })
          } else {
            console.log(this.params)
            if (this.temppass !== this.params.password) {
              this.params.password = md5(values.password)
            }
          }
        } else {
          this.confirmLoading = false
        }
      })
    },
    getRoleList () {
      this.confirmLoading = true
      getRoleInfo(requestBuilder('', { activity: 'Y' }, 0, 0))
        .then(res => {
          if (res.code === '0000') {
            this.rolesList = res.result
          } else {
            this.$message.error(' 获取角色列表失败 ')
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.confirmLoading = false
        })
    },
    getRoleListByUserNo (value) {
      this.confirmLoading = true
      getRoleInfoByUserNo(requestBuilder('', { userNo: value }, 0, 0))
        .then(res => {
          if (res.code === '0000') {
            this.rolesList = res.result
            this.selectedItems.push(res.result[0].roleId)
            alert(this.selectedItems)
          } else {
            this.$message.error('读取角色列表失败 ')
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.confirmLoading = false
        })
    },
    handleCancel () {
      this.visible = false
    }
  }
}
</script>
