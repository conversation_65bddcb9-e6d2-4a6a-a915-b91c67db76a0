<template>
  <section>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="10">
            <a-col
              :xl="4"
              :md="4"
              :sm="24"
            >
              <a-form-item label="用户名">
                <a-input
                  @pressEnter="doSearch"
                  v-model="queryParam.userNo"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <a-col
              :xl="4"
              :md="4"
              :sm="24"
            >
              <a-form-item label="姓名">
                <a-input
                  @pressEnter="doSearch"
                  v-model="queryParam.personName"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <a-col
              :xl="4"
              :md="4"
              :sm="24"
            >
              <a-form-item label="手机号">
                <a-input v-model="queryParam.mobilePhone" @pressEnter="doSearch"/>
              </a-form-item>
            </a-col>
            <a-col
              :xl="4"
              :md="4"
              :sm="24"
            >
              <a-form-item label="状态">
                <a-select
                  @change="doSearch"
                  v-model="queryParam.activity"
                  default-value="Y"
                  allowClear
                >
                  <a-select-option value="Y">启用</a-select-option>
                  <a-select-option value="N">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :xl="6"
              :md="6"
              :sm="24"
            >
              <a-form-item label="角色">
                <a-select
                  @change="doSearch"
                  v-model="queryParam.roleId[0]"
                  optionFilterProp="label"
                  showSearch
                  allowClear
                >
                  <a-select-option v-for="(item, index) in rolesList" :value="item.roleId" :key="index" :label="item.roleName">{{ item.roleName }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <a-button
          v-action:query
          type="primary"
          icon="search"
          @click="doSearch"
        >查询</a-button>
        <a-button
          v-action:add
          type="primary"
          icon="plus"
          @click="openDrawer('insert')"
        >新增</a-button>
        <a-button
          v-action:edit
          type="primary"
          icon="edit"
          @click="doEdit"
        >修改</a-button>
        <a-button
          v-action:del
          type="danger"
          icon="delete"
          @click="doBatchDel('')"
        >删除</a-button>
        <a-button
          v-action:ccSync
          type="primary"
          icon="redo"
          @click="ccSync"
          :loading="syncLoading"
        >用户同步到驰骋</a-button>
        <a-button
          v-action:ccImport
          type="primary"
          icon="download"
          @click="doImportExport"
        >下载驰骋导入模板</a-button>
        <a-button
          v-action:ccImport
          type="primary"
          icon="download"
          @click="doImportExportTest"
        >下载驰骋导入模板案例</a-button>
        <a-upload
          v-action:ccImport
          class="upload-control"
          :data="uploadData"
          :action="uploadAction"
          :headers="uploadHeaders"
          :file-list="uploadFileList"
          :beforeUpload="uploadBeforeUpload"
          @change="uploadChange"
        >
          <a-button>
            <a-icon type="upload" />上传人员信息到驰骋
          </a-button>
        </a-upload>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        size="default"
        rowKey="key"
        :columns="columns"
        :data="loadData"
        :alert="options.alert"
        :customRow="rowClick"
        :rowSelection="options.rowSelection"
        :pageSizeOptions="pageSizeOptions"
        :pageSize="defaultPageSize"
        :showPagination="true"
      >
        <!-- 表格表自定义显示转换 -->
        <span
          slot="serial"
          slot-scope="text, record, index"
        >{{ index + 1 }}</span>
        <span
          slot="description"
          slot-scope="text"
        >
          <ellipsis
            :length="8"
            tooltip
          >{{ text }}</ellipsis>
        </span>
        <span
          slot="status"
          slot-scope="text"
        >{{ (text==='Y')?'启用':'禁用' }}</span>
        <span
          slot="date"
          slot-scope="text"
        >{{ text | moment }}</span>
        <!-- 表格表中操作栏 -->
        <span
          slot="action"
          slot-scope="text, record"
        >
          <template>
            <!-- <a-divider type="vertical" /> -->
            <a
              v-action:del
              @click="doBatchDel(record)"
            >删除</a>
          </template>
        </span>
      </s-table>
      <!-- 侧边滑动栏 -->
      <template>
        <a-drawer
          :title="subTitle"
          :width="360"
          :visible="visible"
          :mask="true"
          :bodyStyle="{paddingBottom: '80px'}"
          @close="visible = false"
        >
          <a-spin :spinning="confirmLoading">
            <a-form
              :form="form"
              layout="vertical"
              hideRequiredMark
            >
              <a-form-item
                label="ID:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['id']"
                  :disabled="true"
                />
              </a-form-item>
              <a-form-item
                label="用户名:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['userNo', {rules: [{required: true, min: 5, message: '请输入至少五个字符！'}]}]"
                />
              </a-form-item>
              <a-form-item
                label="密码:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['password', {rules: [{required: true, min: 8, message: '请输入8位以上密码'}]}]"
                  type="password"
                />
              </a-form-item>
              <a-form-item
                label="手机号码:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  @blur="touchPersonInfo"
                  v-decorator="['mobilePhone', {rules: [{required: true, len: 11, max:11, message: '请输入11位手机号码！'}]}]"
                  :disabled="getDisableStatus()"
                />
              </a-form-item>
              <a-form-item
                label="姓名:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['personName', {rules: [{required: true, min: 1, message: '请输入至少一个字符！'}]}]"
                  :disabled="getDisableStatus()"
                />
              </a-form-item>
              <a-form-item
                label="人员编码:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['personNum', {rules: [{required: true, min: 5, message: '请输入至少五个字符！'}]}]"
                  :disabled="getDisableStatus()"
                />
              </a-form-item>
              <a-form-item
                label="职位:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['postName']"
                  :disabled="getDisableStatus()"
                />
              </a-form-item>
              <a-form-item
                label="身份证:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['idCard']"
                  :disabled="getDisableStatus()"
                />
              </a-form-item>
              <a-form-item
                v-show="false"
                label="personSysId:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['personSysId', {rules: [{required: false}]}]"
                  :disabled="getDisableStatus()"
                />
              </a-form-item>
              <a-form-item
                label="所属组织:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['orgName', {rules: [{required: true, min:1, message: '组织不能为空'}]}]"
                  :disabled="true"
                />
              </a-form-item>
              <a-form-item
                label="所属部门:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select
                  v-decorator="['departmentSysId', { rules: [{ type: 'string', required: true, message: '部门不能为空' }]}]"
                  allowClear
                  :disabled="getDisableStatus()"
                >
                  <a-select-option
                    v-for="(item, index) in deptList"
                    :value="item.value"
                    :key="index"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item
                label="所属角色:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select
                  showSearch
                  optionFilterProp="label"
                  mode="multiple"
                  placeholder="选择角色..."
                  :value="selectedItems"
                  @change="handleChange"
                  style="width: 100%"
                  v-decorator="['roleId', {rules: [{required: true, message: '角色不能为空'}]}]"
                >
                  <a-select-option
                    v-for="(item, index) in rolesList"
                    :key="index"
                    :label="item.roleName"
                    :value="item.roleId"
                  >{{ item.roleName }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item
                v-if="!IS_BYJ_ENV"
                label="组别代码:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select
                  placeholder="选择组别代码"
                  :value="zbdms"
                  @change="handleChange"
                  style="width: 100%"
                  v-decorator="['zbdm', {rules: [{required: false }]}]"
                  allowClear
                  showSearch
                  optionFilterProp="label"
                >
                  <a-select-option
                    v-for="(item, index) in zbdms"
                    :key="index"
                    :value="item.value"
                    :label="item.label"
                    :title="item.label"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item
                label="所属班组:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select
                  v-decorator="['checkGroupNum']"
                  showSearch
                  allowClear
                  :filterOption="(input, option) => option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0"
                >
                  <a-select-option
                    v-for="(item, index) in checkGroupNums"
                    :value="item.value"
                    :key="index"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item
                label="可领仓库:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select
                  mode="multiple"
                  placeholder="选择仓库..."
                  style="width: 100%"
                  allowClear
                  v-decorator="['collectableWarehouses']"
                >
                  <a-select-option
                    v-for="(item, index) in storehouse"
                    :key="index"
                    :value="item.value"
                  >{{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item
                label="所属工区:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select
                  placeholder="选择工区..."
                  style="width: 100%"
                  allowClear
                  v-decorator="['workAreaId']"
                  mode="multiple"
                  option-label-prop="label"
                  @change="changeWorkArea"
                >
                  <a-select-option
                    v-for="(item, index) in workArea"
                    :key="index"
                    :value="item.value"
                    :label="item.label"
                  ><a-button type="link" @click.stop="setDefault(index)" >
                    {{ item.selected?'取消默认':'设为默认' }}
                  </a-button>{{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item
                label="专业:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select
                  placeholder="选择专业..."
                  style="width: 100%"
                  allowClear
                  v-decorator="['speciality']"
                  option-label-prop="label"
                >
                  <a-select-option
                    v-for="(item, index) in speciality"
                    :key="index"
                    :value="item.value"
                    :label="item.label"
                  >{{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item
                label="用户状态:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select v-decorator="['activity', {rules: [{required: true, message: '请选择状态'}]}]">
                  <a-select-option value="Y">启用</a-select-option>
                  <a-select-option value="N">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-form>
          </a-spin>
          <div
            :style="{
              position: 'absolute',
              right: 0,
              bottom: 0,
              width: '100%',
              borderTop: '1px solid #e9e9e9',
              padding: '10px 16px',
              background: '#fff',
              textAlign: 'right',
              zIndex: 1,
            }"
          >
            <a-button
              :style="{marginRight: '8px'}"
              @click="visible = false"
            >取消</a-button>
            <a-button
              @click="doSubmit"
              type="primary"
            >保存</a-button>
          </div>
        </a-drawer>
      </template>
    </a-card>
  </section>
</template>

<script>
import Vue from 'vue'
import { ORG_NAME, ORG_ID, ACCESS_TOKEN } from '@/store/mutation-types'
import { STable, Ellipsis } from '@/components'
import { getUserInfo, getRoleInfo, getRoleInfoByUserNo, modifyUserInfo, isPersonExist, ccSync } from '@/api/material/um'
import * as baseApi from '@/api/material/base'
import * as groupCodeApi from '@/api/material/groupCode'
import { requestBuilder } from '@/utils/util'
import md5 from 'md5'
const USER_ACCESS_TOKEN = Vue.ls.get(ACCESS_TOKEN)
const uploadAccept = [
  // Excel
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel'
]
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_JX_ENV = USER_ORG_ID === '1.102'
const IS_BYJ_ENV = USER_ORG_ID === '1.100.101'
const IS_SLH_ENV = USER_ORG_ID === '1.100.111'
export default {
  name: 'UserManager',
  components: {
    STable,
    Ellipsis
  },
  data () {
    return {
      syncLoading: false,
      USER_ACCESS_TOKEN,
      IS_JX_ENV,
      IS_BYJ_ENV,
      IS_SLH_ENV,
      // 分页参数
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20'],
      mdl: {},
      zbdms: [],
      // 高级搜索 展开/关闭
      advanced: false,
      // 下拉框选择 班组
      checkGroupNums: [],
      storehouse: [],
      // 查询参数
      queryParam: {
        roleId: []
      },
      // 表头定义
      columns: [
        {
          title: '序号',
          scopedSlots: { customRender: 'serial' }
        },
        {
          title: '用户名',
          dataIndex: 'userNo',
          sorter: (a, b) => {
            return a.userNo.localeCompare(b.userNo)
          }
        },
        {
          title: '姓名',
          dataIndex: 'personName'
        },
        {
          title: '手机号码',
          dataIndex: 'mobilePhone'
        },
        {
          title: '身份证',
          dataIndex: 'idCard',
          scopedSlots: { customRender: 'description' }
        },
        {
          title: '职位',
          dataIndex: 'postName'
        },
        {
          title: '状态',
          dataIndex: 'activity',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '最后修改时间',
          dataIndex: 'modifyDate',
          scopedSlots: { customRender: 'date' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: '80px',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        if (this.queryParam.roleId.length > 0 && this.queryParam.roleId[0] == null) {
          this.$set(this.queryParam, 'roleId', [])
        }
        const param = requestBuilder('', Object.assign(this.queryParam), parameter.pageNo, parameter.pageSize)
        return getUserInfo(param).then(res => {
          return res.result
        })
      },
      // 行选择参数
      options: {
        alert: {
          show: false,
          clear: () => {
            this.selectedRowKeys = []
          }
        },
        rowSelection: {
          // selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        }
      },
      selectedRowKeys: [],
      selectedRows: [],
      // 滑动抽屉参数
      form: this.$form.createForm(this, { onValuesChange: this.onValuesChange }),
      confirmLoading: false, // 加载图标是否出现
      rolesList: [], // 角色列表
      deptList: [], // 所属部门列表
      workArea: [],
      speciality: [],
      selectedItems: [],
      orgName: '',
      visible: false,
      subTitle: '人员新建',
      temppass: '',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      rowClick: record => ({
        on: {
          // 点击事件
          dblclick: () => {
            this.openDrawer('edit', record)
          }
        }
      }),
      // 上传相关
      uploadData: {
      },
      uploadHeaders: {
        token: USER_ACCESS_TOKEN
      },
      uploadFileList: [],
      uploadLoading: false,
      uploadPercent: 30,
      uploadAccept: uploadAccept.join(','),
      uploadChange: options => {
        if (options.file && ['uploading'].includes(options.file.status)) {
          this.uploadPercent = options.file.percent || 0
          this.uploadLoading = true
          return
        }
        if (options.file && ['removed', 'error'].includes(options.file.status)) {
          this.uploadLoading = false
          this.uploadFileList = []
          return
        }
        if (options.file && options.file.status === 'done') {
          if (options.file.response && options.file.response.code === '0000') {
            this.$notification.success({
              message: '系统消息',
              description: options.file.response.message
            })
          } else {
            this.$notification.error({
              message: '系统消息',
              description: options.file.response.message
            })
          }
          this.uploadLoading = false
          this.uploadFileList = []
          this.uploadPercent = 0
        }
      },
      uploadBeforeUpload: file => {
        if (!uploadAccept.includes(file.type)) {
          this.$message.error('仅支持上传excel附件')
          return false
        }
        if (file.size / 1024 / 1024 > 20) {
          this.$message.error('上传附件大小不可超过20M')
          return false
        }
        this.uploadFileList.push(file)
      },
      uploadAction: process.env.VUE_APP_API_BASE_URL + '/importCcPersonInfo'
    }
  },
  filters: {},
  created () {
    // 获取角色列表
    this.getRoleList()
    this.getDeptList()
    this.orgName = Vue.ls.get(ORG_NAME)
    // 维修班组
    baseApi.getCommboxById({ id: 'basCodeByClassIdAndOrgId', sqlParams: { codeClassId: 'check_group_num' } }).then(res => {
      if (res.code === '0000') {
        this.checkGroupNums = res.result
      }
    })
    // 所属工区
    baseApi.getCommboxById({ id: 'workAreaTS', sqlParams: { orgId: Vue.ls.get(ORG_ID) } }).then(res => {
      if (res.code === '0000') {
        this.workArea = res.result
      }
    })
    // 所属专业
    baseApi.getCommboxById({ id: 'basCodeByClassIdAndOrgId', sqlParams: { codeClassId: 'speciality', orgId: Vue.ls.get(ORG_ID) } }).then(res => {
      if (res.code === '0000') {
        this.speciality = res.result
      }
    })

    // 选择仓库
    baseApi.getCommboxById({ id: 'storehouse' }).then(res => {
      if (IS_SLH_ENV) {
        this.storehouse = res.result.filter(v => v.shortName === '二级库')
      } else {
        this.storehouse = res.result
      }
    })
    // 获取所有组别
    groupCodeApi.queryGroupCodeInfoAll(requestBuilder('', { activity: 'Y' })).then(res => {
      if (res.code === '0000') {
        this.zbdms = res.result.map(item => {
          return {
            label: item.zbdmNum + '[' + item.zbdmName + ']',
            value: item.zbdmNum
          }
        })
      }
    })
  },
  watch: {
  },
  methods: {
    // 监听表单字段值的更新
    onValuesChange (props, value, values) {
      if (value.workAreaId) {
        this.changeColor()
      }
    },
    changeColor () {
      console.log('改变颜色')
      this.$nextTick(() => {
        const clsList = document.getElementsByClassName('ant-select-selection__choice__content')
        if (clsList) {
          for (const item of clsList) {
            this.workArea.forEach(element => {
              if (element.label === item.innerHTML) {
                // console.log(item.innerHTML + 'style=', item.style.color)
                item.style.removeProperty('color')
                // console.log(item.innerHTML + 'style=', item.style.color)
                // console.log(item.innerHTML + 'element.selected=', element.selected)
                if (element.selected) {
                  item.style.color = 'red'
                }
                // console.log(item.innerHTML + 'style=', item.style.color)
              }
            })
          }
        }
      })
    },
    changeWorkArea (e) {
      // console.log('下拉选择')
      if (!e || e.length === 0) {
        this.workArea.forEach(element => {
          element.selected = false
        })
      } else {
        let arr = this.form.getFieldValue('workAreaId')
        arr = arr.filter(item => !e.includes(item))
        this.workArea.forEach(element => {
          if (arr.includes(element.value)) {
            element.selected = false
          }
        })
      }
      this.$forceUpdate()
    },
    setDefault (index) {
      // console.log('设为默认')
      const oldSelected = this.workArea[index].selected
      let arr = this.form.getFieldValue('workAreaId') || []
      this.workArea.forEach(element => {
        element.selected = false
      })
      this.workArea[index].selected = !oldSelected
      if (this.workArea[index].selected && !arr.includes(this.workArea[index].value)) {
        arr.push(this.workArea[index].value)
        this.form.setFieldsValue({ workAreaId: arr })
      }
      if (!this.workArea[index].selected && arr.includes(this.workArea[index].value)) {
        arr = arr.filter(item => item !== this.workArea[index].value)
      }
      this.changeColor()
      this.$forceUpdate()
    },
    ccSync () {
      this.syncLoading = true
      ccSync(requestBuilder('', {})).then(res => {
        if (res.code === '0000') {
          this.$notification.success({
            message: '系统消息',
            description: res.message || '操作成功！'
          })
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.syncLoading = false
      })
    },
    getDisableStatus () {
      if (this.actionFlag === 'insert') {
        return false
      } else {
        return true
      }
    },
    handleChange (selectedItems) {
      this.selectedItems = selectedItems
    },
    doBatchDel (record) {
      console.log(record)
      const userNos = []
      if (record !== '') {
        userNos.push(record.userNo)
      } else {
        if (this.selectedRows.length > 0) {
          for (let i = 0; i < this.selectedRows.length; i++) {
            userNos.push(this.selectedRows[i].userNo)
          }
        } else {
          this.$message.error('未选中任何记录')
          return
        }
      }
      this.$confirm({
        title: '确认?',
        content: '是否删除此记录',
        onOk: () => {
          this.handleDel(userNos.join(','))
        },
        onCancel () {}
      })
    },
    handleDel (value) {
      this.actionFlag = 'delete'
      const param = {
        userNo: value
      }
      this.getResult(param)
    },
    doSearch () {
      this.$refs.table.refresh(true)
      this.visible = false
    },
    doEdit () {
      if (this.selectedRows.length > 0) {
        if (this.selectedRows.length > 1) {
          this.$message.warning('请勾选一条记录')
        } else {
          this.openDrawer('update', this.selectedRows[0])
        }
      } else {
        this.$message.error('未选中任何记录')
      }
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    touchPersonInfo () {
      if (this.form.getFieldValue('mobilePhone').length === 11) {
        this.getPersonInfo(this.form.getFieldValue('mobilePhone'))
      }
    },
    touchUserInfo () {
      if (this.form.getFieldValue('userNo').length >= 5) {
        this.getPersonInfo(this.form.getFieldValue('userNo'))
      }
    },
    openDrawer (action, record) {
      this.workArea.forEach(element => {
        element.selected = false
      })
      this.visible = true
      this.$nextTick(() => {
        if (action === 'insert') {
          this.form.resetFields()
          this.form.setFieldsValue({
            orgName: this.orgName,
            departmentSysId: ''
          })
          this.subTitle = '用户新增'
          this.actionFlag = 'insert'
        } else {
          this.subTitle = '用户修改'
          const arr = record.workAreaId ? record.workAreaId.split(',') : []
          this.workArea.forEach(element => {
            if (element.value === arr[0]) { element.selected = true }
          })
          this.form.setFieldsValue({
            id: record.id,
            userNo: record.userNo,
            mobilePhone: record.mobilePhone,
            personName: record.personName,
            password: record.password,
            idCard: record.idCard,
            orgName: record.orgName,
            departmentName: record.departmentName,
            activity: record.activity,
            postName: record.postName,
            personNum: record.personNum,
            departmentSysId: record.departmentSysId,
            personSysId: record.personSysId,
            zbdm: record.zbdm,
            checkGroupNum: record.checkGroupNum,
            collectableWarehouses: record.collectableWarehouses,
            roleId: [],
            workAreaId: arr,
            speciality: record.speciality
          })
          this.getRoleListByUserNo(record.userNo)
          this.temppass = record.password
          this.actionFlag = 'update'
        }
      })
    },
    getRoleList () {
      this.confirmLoading = true
      getRoleInfo(requestBuilder('select', { activity: 'Y' }, 0, 0))
        .then(res => {
          if (res.code === '0000') {
            this.rolesList = res.result
          } else {
            this.$message.error(' 获取角色列表失败 ')
          }
        })
        .catch(err => {
          alert(err)
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    getDeptList () {
      this.confirmLoading = true
      baseApi.getCommboxById({ id: 'dept', sqlParams: { byOrgId: '1', orgId: Vue.ls.get(ORG_ID) } }).then(res => {
        this.deptList = res.result
      })
      this.confirmLoading = false
    },
    getRoleListByUserNo (value) {
      this.confirmLoading = true
      getRoleInfoByUserNo(requestBuilder('', { userNo: value }, 0, 0))
        .then(res => {
          if (res.code === '0000') {
            const arr = []
            for (let i = 0; i < res.result.length; i++) {
              arr.push(res.result[i].roleId)
              this.form.setFieldsValue({
                roleId: arr
              })
            }
          } else {
            this.$message.error('读取角色列表失败 ')
          }
        })
        .catch(err => {
          alert(err)
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    getUserInfo (userNo) {},
    getPersonInfo (mobilePhone) {
      this.confirmLoading = true
      isPersonExist(requestBuilder('', { mobilePhone: mobilePhone }, 0, 0))
        .then(res => {
          if (res.code === '0000') {
            this.form.setFieldsValue({
              mobilePhone: res.result.mobilePhone,
              personName: res.result.personName,
              idCard: res.result.idCard,
              // orgName: res.result.orgName,
              // departmentName: res.result.departmentName,
              postName: res.result.postName,
              personNum: res.result.personNum,
              // departmentSysId: res.result.departmentSysId,
              personSysId: res.result.personSysId,
              zbdm: res.result.zbdm,
              checkGroupNum: res.result.checkGroupNum
            })
          } else if (res.code === '0002') {
            this.form.setFieldsValue({
              personName: '',
              idCard: '',
              // orgName: '',
              // departmentName: '',
              postName: '',
              personNum: '',
              // departmentSysId: '',
              personSysId: ''
            })
            this.$message.success(res.message)
          } else if (res.code === '0003') {
            this.$message.error(res.message)
            this.form.setFieldsValue({
              mobilePhone: '',
              personName: '',
              idCard: '',
              departmentName: '',
              postName: '',
              personNum: '',
              departmentSysId: '',
              personSysId: '',
              zbdm: '',
              checkGroupNum: ''
            })
          }
        })
        .catch(err => {
          alert(err)
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    // 保存数据
    doSubmit () {
      console.log(this.params)

      const arr = this.workArea.filter(item => item.selected) || []
      if (this.form.getFieldValue('workAreaId') && this.form.getFieldValue('workAreaId').length > 1 && arr.length !== 1) {
        this.$message.error('请设置默认工区')
        return
      }
      const {
        form: { validateFields }
      } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        // 如果输入数据都合规
        if (!errors) {
          this.params = { ...values }
          // console.log('params=', this.params)
          if (this.params.workAreaId && this.params.workAreaId.length > 0) {
            // console.log('workareaId=', this.params.workAreaId)
            if (this.params.workAreaId.length === 1) {
              // console.log('workareaId=1', this.params.workAreaId)
              this.params.workAreaId = this.params.workAreaId[0]
            } else {
              const defaultArea = this.workArea.filter(item => item.selected)[0]
              // console.log('defaultArea', defaultArea)
              // console.log('arr', this.params.workAreaId)
              const arr = this.params.workAreaId.filter(item => (item !== defaultArea.value))
              // console.log('arr', arr)
              if (arr && arr.length > 0) {
                this.params.workAreaId = defaultArea.value + ',' + arr.join(',')
              } else {
                this.params.workAreaId = defaultArea.value
              }
            }
          } else {
            this.params.workAreaId = ''
          }
          // console.log('params11=', this.params)
          if (this.actionFlag === 'insert') {
            this.params.password = md5(values.password)
            this.getResult(this.params)
          } else {
            if (this.temppass !== this.params.password) {
              this.params.password = md5(values.password)
            }
            this.getResult(this.params)
          }
        } else {
          this.confirmLoading = false
        }
      })
    },
    getResult (params) {
      modifyUserInfo(requestBuilder(this.actionFlag, params, '0', '0'))
        .then(res => {
          if (res.code === '0000') {
            this.$message.success(res.message)
            this.doSearch()
          } else if (res.code === '9999') {
            this.$message.error(res.message)
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.confirmLoading = false
        })
    },
    doImportExportTest () {
      baseApi.doCcImportExportTest().then(res => {
        if (res && res.data && res.headers['content-disposition']) {
          const link = document.createElement('a')
          const url = window.URL.createObjectURL(res.data)
          const contentDisposition = res.headers['content-disposition']
          let filename = contentDisposition.replace(/(.*;)?filename=([^;]+).*/i, '$2')
          try {
            filename = decodeURIComponent(escape(filename))
          } catch (e) {
          }
          document.body.appendChild(link)
          link.style.display = 'none'
          link.download = filename
          link.href = url
          link.click()
          link.remove()
          window.URL.revokeObjectURL(url)
        } else {
          this.$notification.error({
            message: '系统消息',
            description: '下载失败！'
          })
        }
      })
    },
    // 下载导入模版
    doImportExport () {
      baseApi.doCcImportExport().then(res => {
        if (res && res.data && res.headers['content-disposition']) {
          const link = document.createElement('a')
          const url = window.URL.createObjectURL(res.data)
          const contentDisposition = res.headers['content-disposition']
          let filename = contentDisposition.replace(/(.*;)?filename=([^;]+).*/i, '$2')
          try {
            filename = decodeURIComponent(escape(filename))
          } catch (e) {
          }
          document.body.appendChild(link)
          link.style.display = 'none'
          link.download = filename
          link.href = url
          link.click()
          link.remove()
          window.URL.revokeObjectURL(url)
        } else {
          this.$notification.error({
            message: '系统消息',
            description: '下载失败！'
          })
        }
      })
    }
    // 校验温州港物资编码格式
    // checkZBDM (rule, value, callback) {
    //   if (this.zbdms.indexOf(value) < 0 && value != null && value !== '') {
    //     callback(new Error('不存在或已禁用，请新建或开启！'))
    //     return
    //   }
    //   if (value === '') {
    //     this.form.setFieldsValue({
    //       zbdm: null
    //     })
    //   }
    //   callback()
    // }
  }
}
</script>
<style scoped>
</style>
