<template>
  <section>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="10">
            <a-col
              :xl="6"
              :md="6"
              :sm="24"
            >
              <a-form-item label="角色ID">
                <a-input
                  @pressEnter="doSearch"
                  v-model="queryParam.roleId"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <a-col
              :xl="6"
              :md="6"
              :sm="24"
            >
              <a-form-item label="角色名称">
                <a-input
                  @pressEnter="doSearch"
                  v-model="queryParam.roleName"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <a-col
              :xl="6"
              :md="6"
              :sm="24"
            >
              <a-form-item label="可用状态">
                <a-select
                  @change="doSearch"
                  v-model="queryParam.activity"
                  allowClear
                >
                  <a-select-option value="Y">启用</a-select-option>
                  <a-select-option value="N">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :xl="6"
              :md="6"
              :sm="24"
            >
              <a-form-item label="角色类型">
                <a-select
                  @change="doSearch"
                  v-model="queryParam.roleTypes"
                  allowClear
                >
                  <a-select-option value="goods">物资</a-select-option>
                  <a-select-option value="device">设备</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <a-button
          v-action:query
          type="primary"
          icon="search"
          @click="doSearch"
        >查询</a-button>
        <a-button
          v-action:add
          type="primary"
          icon="plus"
          @click="openDrawer('insert')"
        >新增</a-button>
        <a-button
          v-action:edit
          type="primary"
          icon="edit"
          @click="doEdit"
        >修改</a-button>
        <a-button
          v-action:del
          type="danger"
          icon="delete"
          @click="doBatchDel('')"
        >删除</a-button>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        size="default"
        rowKey="key"
        :columns="columns"
        :data="loadData"
        :customRow="rowClick"
        :alert="options.alert"
        :rowSelection="options.rowSelection"
        :pageSizeOptions="pageSizeOptions"
        :pageSize="defaultPageSize"
        :showPagination="true"
      >
        <!-- 表格表自定义显示转换 -->
        <span
          slot="serial"
          slot-scope="text, record, index"
        >{{ index + 1 }}</span>
        <span
          slot="description"
          slot-scope="text"
        >
          <ellipsis
            :length="8"
            tooltip
          >{{ text }}</ellipsis>
        </span>
        <span
          slot="status"
          slot-scope="text"
        >{{ text === 'Y' ? '启用' : '禁用' }}</span>
        <span
          slot="roleTypes"
          slot-scope="text"
        >{{ text === 'device' ? '设备' : text === 'goods' ? '物资' : '' }}</span>
        <span
          slot="date"
          slot-scope="text"
        >{{ text | moment }}</span>
        <!-- 表格表中操作栏 -->
        <span
          slot="action"
          slot-scope="text, record"
        >
          <template>
            <a
              v-action:edit
              @click="openResDrawer(record)"
            >资源配置</a>
            <a
              v-if="false"
              v-action:del
              @click="doBatchDel(record)"
            >删除</a>
          </template>
        </span>
      </s-table>
      <!-- 侧边滑动栏 -->
      <template>
        <!--角色主信息抽屉-->
        <a-drawer
          :title="roleDrawerTitle"
          :width="360"
          :visible="roleDrawerVisible"
          :mask="true"
          :bodyStyle="{ paddingBottom: '80px' }"
          :getContainer="false"
          @close="roleDrawerVisible = false"
        >
          <a-spin :spinning="confirmLoading">
            <a-form
              :form="form"
              layout="vertical"
              hideRequiredMark
            >
              <a-form-item
                label="角色ID:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['roleId']"
                  :disabled="true"
                />
              </a-form-item>
              <a-form-item
                label="角色名称:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['roleName', { rules: [{ required: true, min: 1, message: '角色名称不能为空' }] }]"
                />
              </a-form-item>
              <a-form-item
                label="数据范围:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select
                  v-decorator="['dataFlag', { rules: [{ required: true, message: '请指定数据可见范围' }] }]"
                >
                  <a-select-option value="0">用户级</a-select-option>
                  <a-select-option value="1">部门级</a-select-option>
                  <a-select-option value="2">公司级</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item
                label="角色状态:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select
                  v-decorator="['activity', { rules: [{ required: true, message: '请选择状态' }] }]"
                >
                  <a-select-option value="Y">启用</a-select-option>
                  <a-select-option value="N">禁用</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item
                label="角色类型:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select
                  v-decorator="['roleTypes', { rules: [{ required: true, message: '请选择角色类型' }] }]"
                >
                  <a-select-option value="goods">物资</a-select-option>
                  <a-select-option value="device">设备</a-select-option>
                </a-select>
              </a-form-item>
            </a-form>
          </a-spin>
          <div
            :style="{
              position: 'absolute',
              right: 0,
              bottom: 0,
              width: '100%',
              borderTop: '1px solid #e9e9e9',
              padding: '10px 16px',
              background: '#fff',
              textAlign: 'right',
              zIndex: 1
            }"
          >
            <a-button
              :style="{ marginRight: '8px' }"
              @click="roleDrawerVisible = false"
            >取消</a-button>
            <a-button
              @click="doSubmit"
              type="primary"
            >保存</a-button>
          </div>
        </a-drawer>
        <!--资源配置抽屉-->
        <a-drawer
          :title="resDrawerTitle"
          :width="1050"
          :visible="resDrawerVisible"
          :mask="true"
          :bodyStyle="{ paddingBottom: '80px' }"
          :getContainer="false"
          @close="resDrawerVisible = false"
        >
          <a-row :gutter="8">
            <a-spin :spinning="confirmLoading1">
              <a-col :span="6">
                <a-card title="菜单权限">
                  <a-tree
                    checkable
                    showLine
                    :checkedKeys="cacheKeys"
                    :checkStrictly="true"
                    :treeData="orgTree"
                    @select="onTreeSelect"
                    @check="onTreeCheck"
                    :replaceFields="{ key: 'value', title: 'label' }"
                  />
                </a-card>
              </a-col>
              <a-col :span="18">
                <a-card title="按钮权限">
                  <a-spin :spinning="confirmLoading2">
                    <a-form
                      :form="form1"
                      layout="vertical"
                    >
                      <a-row
                        v-if="permission"
                        :gutter="0"
                        v-for="(permission, index) in permissions"
                        :key="index"
                        style="margin-bottom: 8px;"
                      >
                        <a-col
                          :xl="4"
                          :lg="24"
                        >{{ permission.name }}：</a-col>
                        <a-col
                          :xl="20"
                          :lg="24"
                        >
                          <a-checkbox
                            v-if="permission.actionsOptions.length > 0"
                            :indeterminate="permission.indeterminate"
                            :checked="permission.checkedAll"
                            @change="onChangeCheckAll($event, permission)"
                          >全选</a-checkbox>
                          <a-checkbox-group
                            :options="permission.actionsOptions"
                            v-model="permission.selected"
                          />
                        </a-col>
                      </a-row>
                    </a-form>
                  </a-spin>
                </a-card>
              </a-col>
            </a-spin>
          </a-row>
          <div
            :style="{
              position: 'absolute',
              right: 0,
              bottom: 0,
              width: '100%',
              borderTop: '1px solid #e9e9e9',
              padding: '10px 16px',
              background: '#fff',
              textAlign: 'right',
              zIndex: 1
            }"
          >
            <a-button
              :style="{ marginRight: '8px' }"
              @click="resDrawerVisible = false"
            >取消</a-button>
            <a-button
              @click="doResSubmit"
              type="primary"
            >保存</a-button>
          </div>
        </a-drawer>
      </template>
    </a-card>
  </section>
</template>

<script>
import {
  getRoleInfoByPages,
  getButtonPermissonList,
  modifyRoleInfo,
  getMenuInfoList,
  saveResoureByRoleId
} from '@/api/material/um'
import STree from '@/components/Tree/Tree'
import { STable, Ellipsis } from '@/components'
import { requestBuilder, takeTreeByKey } from '@/utils/util'

export default {
  name: 'RoleManager',
  components: {
    STable,
    STree,
    Ellipsis
  },
  data () {
    return {
      // 分页参数
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20'],
      mdl: {},
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {
        activity: 'Y'
      },
      // 表头定义
      columns: [
        {
          title: '角色ID',
          dataIndex: 'roleId'
        },
        {
          title: '角色名称',
          dataIndex: 'roleName'
        },
        {
          title: '状态',
          dataIndex: 'activity',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '角色类型',
          dataIndex: 'roleTypes',
          scopedSlots: { customRender: 'roleTypes' }
        },
        {
          title: '最后修改时间',
          dataIndex: 'modifyDate',
          scopedSlots: { customRender: 'date' }
        },
        {
          title: '最后操作人',
          dataIndex: 'modifyBy'
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: '100px',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const param = requestBuilder('', Object.assign(this.queryParam), parameter.pageNo, parameter.pageSize)
        return getRoleInfoByPages(param).then(res => {
          if (res.code === '403') {
            this.$message.error(res.message)
          } else {
            return res.result
          }
        })
      },
      // 行选择参数
      options: {
        alert: {
          show: false,
          clear: () => {
            this.selectedRowKeys = []
          }
        },
        rowSelection: {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        }
      },
      selectedRowKeys: [],
      selectedRows: [],
      // 滑动抽屉参数
      form: this.$form.createForm(this),
      form1: this.$form.createForm(this),
      actionFlag: '',
      confirmLoading: false, // 加载图标是否出现,
      confirmLoading1: false, // 加载图标是否出现
      confirmLoading2: false, // 加载图标是否出现
      // 弹出抽屉配置
      roleDrawerVisible: false,
      roleDrawerTitle: '角色新建',
      resDrawerVisible: false,
      resDrawerTitle: '资源分配',
      roleId: '',
      orgTree: [],
      // 目前选中的key的数组
      // cacheKeys: [],
      cacheKeys: {
        checked: [],
        halfChecked: []
      },
      permissions: [],
      halfCheckedKeys: [],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      rowClick: record => ({
        on: {
          // 点击事件
          dblclick: () => {
            this.openDrawer('edit', record)
          }
        }
      })
    }
  },
  filters: {},
  created () {
    // getMenuInfoList().then(res => {
    //   this.orgTree = res.result
    // })
  },
  methods: {
    doBatchDel (record) {
      const roleIds = []
      if (record !== '') {
        roleIds.push(record.roleIds)
      } else {
        if (this.selectedRows.length > 0) {
          for (let i = 0; i < this.selectedRows.length; i++) {
            roleIds.push(this.selectedRows[i].roleId)
          }
        } else {
          this.$message.error('未选中任何记录')
          return
        }
      }
      this.$confirm({
        title: '确认?',
        content: '是否删除此记录',
        onOk: () => {
          this.handleDel(roleIds.join(','))
        },
        onCancel () {}
      })
    },
    handleDel (value) {
      this.actionFlag = 'delete'
      const param = {
        roleId: value
      }
      this.getResult(param)
    },
    doSearch () {
      this.roleDrawerVisible = false
      this.$refs.table.refresh(true)
      this.$refs.table.updateSelect([], [])
      this.$refs.table.rowSelection.onChange([], [])
    },
    doEdit () {
      if (this.selectedRows.length > 0) {
        this.openDrawer('update', this.selectedRows[0])
      } else {
        this.$message.error('未选中任何记录')
      }
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    openDrawer (action, record) {
      this.roleDrawerVisible = true
      if (action === 'insert') {
        this.form.resetFields()
        this.form.setFieldsValue({
          dataFlag: '0'
        })
        this.roleDrawerTitle = '角色新增'
        this.actionFlag = 'insert'
      } else {
        this.roleDrawerTitle = '角色修改'
        this.form.setFieldsValue(record)
        this.actionFlag = 'update'
      }
    },
    openResDrawer (record) {
      this.resDrawerVisible = true
      this.roleId = record.roleId
      // 清空权限列表
      this.permissions = []
      this.cacheKeys.checked = []
      this.orgTree = []
      this.confirmLoading1 = true
      getMenuInfoList(requestBuilder('', { roleId: this.roleId }, 0, 0)).then(res => {
        this.orgTree = res.result.treeNodes
        const cacheKeys = res.result.treeSelect || []
        const parmList = []
        for (let i = 0; i < cacheKeys.length; i++) {
          const key = cacheKeys[i]
          const data = takeTreeByKey(this.orgTree, key)
          const param = {
            menuId: key || '',
            menuName: data.label || '',
            roleId: this.roleId
          }
          parmList.push(param)
        }
        console.log('parmList', parmList)
        this.loadPermission(parmList)
      })
      this.confirmLoading1 = false
      this.form1.setFieldsValue({})
    },
    onTreeSelect (selectedKeys) {},
    onTreeCheck (selectedKeys, info) {
      this.halfCheckedKeys = info.halfCheckedKeys
      // 请求新增的
      if (info.checked === true) {
        console.log('info', info)
        for (let index = 0; index < selectedKeys.checked.length; index++) {
          const key = selectedKeys.checked[index]
          if (!this.cacheKeys.checked.includes(key)) {
            const data = takeTreeByKey(this.orgTree, key)
            const paramList = []
            const param = {
              menuId: (data && data.value) || '',
              menuName: (data && data.label) || '',
              roleId: this.roleId
            }
            paramList.push(param)
            this.loadPermission(paramList)
          }
        }
      } else {
        // 删除已取消的
        console.log('cacheKeys', this.cacheKeys)
        console.log('selectedKeys', selectedKeys)
        for (let index = 0; index < this.cacheKeys.checked.length; index++) {
          const key = this.cacheKeys.checked[index]
          if (!selectedKeys.checked.includes(key)) {
            console.log('key', key)
            this.permissions.splice(index, 1)
            this.cacheKeys.checked.splice(index--, 1)
            console.log('permissions', this.permissions)
          }
        }
      }
    },
    onChangeCheckAll (e, permission) {
      Object.assign(permission, {
        selected: e.target.checked ? permission.actionsOptions.map(obj => obj.value) : [],
        indeterminate: false,
        checkedAll: e.target.checked
      })
    },
    // 获取按钮权限list
    loadPermission (param) {
      this.confirmLoading2 = true
      getButtonPermissonList(requestBuilder('', param, 0, 0))
        .then(res => {
          console.log(this.permissions.length)
          console.log(this.cacheKeys.checked.length)
          if (res.code === '0000') {
            for (let i = 0; i < res.result.length; i++) {
              if (!this.cacheKeys.checked.includes(param[i].menuId)) {
                this.cacheKeys.checked.push(param[i].menuId)
                if (res.result[i].actionsOptions.length > 0) {
                  this.permissions.push(res.result[i])
                } else {
                  this.permissions.push('')
                }
              }
            }
          } else if (res.code === '0001') {
            // if (!this.cacheKeys.checked.includes(param.menuId)) {
            //   this.cacheKeys.checked.push(param.menuId)
            //   this.permissions.push(res.result)
            // }
          } else {
            this.$message.error(res.message)
          }
        })
        .finally(() => {
          this.confirmLoading2 = false
        })
    },
    // 保存角色信息数据
    doSubmit () {
      const {
        form: { validateFields }
      } = this
      this.confirmLoading = true
      validateFields((errors, values) => {
        // 如果输入数据都合规
        if (!errors) {
          this.params = { ...values }
          this.getResult(this.params)
        } else {
          this.confirmLoading = false
        }
      })
    },
    doResSubmit () {
      const cacheKeys = [...this.cacheKeys.checked]
      const permissions = [...this.permissions]
      for (let i = 0; i < permissions.length; i++) {
        if (permissions[i]) {
          for (let j = 0; j < permissions[i].selected.length; j++) {
            cacheKeys.push(permissions[i].selected[j])
          }
        }
      }
      const params = {
        resourceIds: cacheKeys.join(','),
        roleId: this.roleId
      }
      saveResoureByRoleId(requestBuilder('insert', params, '0', '0'))
        .then(res => {
          if (res.code === '0000') {
            this.$message.success(res.message)
            this.resDrawerVisible = false
          } else if (res.code === '9999') {
            this.$message.error(res.message)
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.confirmLoading = false
        })
    },
    getResult (params) {
      modifyRoleInfo(requestBuilder(this.actionFlag, params, '0', '0'))
        .then(res => {
          if (res.code === '0000') {
            this.selectedRows = []
            this.selectedRowKeys = []
            this.doSearch()
            this.$message.success(res.message)
          } else if (res.code === '9999') {
            this.$message.error(res.message)
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.confirmLoading = false
        })
    }
  }
}
</script>
<style scoped></style>
