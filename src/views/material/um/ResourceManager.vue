<template>
  <section>
    <a-card :bordered="false">
      <div
        v-if="false"
        class="table-page-search-wrapper"
      >
        <a-form layout="inline">
          <a-row :gutter="10">
            <a-col
              :xl="6"
              :md="6"
              :sm="24"
            >
              <a-form-item label="资源ID">
                <a-input
                  v-model="queryParam.resourceId"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <a-col
              :xl="6"
              :md="6"
              :sm="24"
            >
              <a-form-item label="资源名称">
                <a-input
                  v-model="queryParam.resourceName"
                  placeholder
                />
              </a-form-item>
            </a-col>
            <a-col
              :xl="6"
              :md="6"
              :sm="24"
            >
              <a-form-item label="可用状态">
                <a-select v-model="queryParam.activity">
                  <a-select-option value="Y">启用</a-select-option>
                  <a-select-option value="N">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <a-button
          v-action:add
          type="primary"
          icon="plus"
          @click="doTreeAdd"
        >新增菜单</a-button>
        <a-button
          v-action:edit
          type="primary"
          icon="edit"
          @click="doTreeEdit"
        >修改菜单</a-button>
        <a-button
          v-action:del
          type="danger"
          icon="delete"
          @click="doTreeDel"
        >删除菜单</a-button>
      </div>
    </a-card>
    <a-card :bordered="false">
      <a-row :gutter="8">
        <a-col :span="6">
          <a-card title="菜单资源">
            <a-button
              shape="circle"
              icon="reload"
              size="small"
              slot="extra"
              @click="getTree"
            />
            <a-tree
              showLine
              :defaultExpandAll="true"
              :treeData="orgTree"
              :bordered="false"
              @select="onTreeSelect"
              :replaceFields="{key:'value',title:'label'}"
            />
          </a-card>
        </a-col>
        <a-col :span="18">
          <a-card title="按钮、卡片资源">
            <a-button
              v-action:add
              shape="circle"
              type="primary"
              icon="plus"
              size="small"
              slot="extra"
              @click="doAdd"
            />
            <a-button
              v-action:edit
              shape="circle"
              type="primary"
              icon="edit"
              size="small"
              slot="extra"
              @click="doEdit"
            />
            <a-button
              v-action:del
              shape="circle"
              type="danger"
              icon="delete"
              size="small"
              slot="extra"
              @click="doDel"
            />
            <s-table
              ref="buttonTable"
              rowKey="resourceId"
              :columns="buttonColumns"
              :data="loadButtonData"
              :customRow="buttonRowClick"
              :rowSelection="rowSelection"
              :pageSizeOptions="pageSizeOptions"
              :pageSize="defaultPageSize"
              :showPagination="true"
            >
              <!-- 表格表自定义显示转换 -->
              <span
                slot="serial"
                slot-scope="text, record, index"
              >{{ index + 1 }}</span>
              <span
                slot="description"
                slot-scope="text"
              >
                <ellipsis
                  :length="8"
                  tooltip
                >{{ text }}</ellipsis>
              </span>
              <span
                slot="status"
                slot-scope="text"
              >{{ text === 'Y' ? '启用' : '禁用' }}</span>
              <span
                slot="date"
                slot-scope="text"
              >{{ text | moment }}</span>
              <!-- 表格表中操作栏 -->
              <span
                slot="action"
                slot-scope="text, record"
              >
                <template>
                  <a
                    v-action:del
                    @click="doBatchDel(record)"
                  >删除</a>
                </template>
              </span>
            </s-table>
          </a-card>
          <a-card title="服务资源">
            <a-button
              v-action:add
              shape="circle"
              type="primary"
              icon="plus"
              size="small"
              slot="extra"
              @click="doServiceAdd"
            />
            <a-button
              v-action:edit
              shape="circle"
              type="primary"
              icon="edit"
              size="small"
              slot="extra"
              @click="doServiceEdit"
            />
            <a-button
              v-action:del
              shape="circle"
              type="danger"
              icon="delete"
              size="small"
              slot="extra"
              @click="doServiceDel"
            />
            <s-table
              ref="serviceTable"
              rowKey="resourceId"
              :columns="serviceColumns"
              :data="loadServiceData"
              :customRow="serviceRowClick"
              :rowSelection="rowSelectionRes"
              :pageSizeOptions="pageSizeOptions"
              :pageSize="defaultPageSize"
              :showPagination="true"
            >
              <span
                slot="status"
                slot-scope="text"
              >{{ text === 'Y' ? '启用' : '禁用' }}</span>
            </s-table>
          </a-card>
        </a-col>
      </a-row>
      <!-- 侧边滑动栏 -->
      <template>
        <!--菜单树主信息抽屉-->
        <a-drawer
          :width="360"
          :mask="true"
          :title="treeDrawerTitle"
          :visible="treeDrawerVisible"
          @close="treeDrawerVisible = false"
        >
          <a-spin :spinning="treeConfirmLoading">
            <a-form
              :form="treeForm"
              layout="vertical"
              hideRequiredMark
            >
              <a-form-item
                label="菜单ID:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['resourceId']"
                  :disabled="true"
                />
              </a-form-item>
              <a-form-item
                label="菜单名称:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['title', {rules: [{required: true, min: 1, message: '资源名称不能为空'}]}]"
                />
              </a-form-item>
              <a-form-item
                label="菜单标识:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['resourceName', {rules: [{required: true, min: 1, message: '资源标识不能为空'}]}]"
                />
              </a-form-item>
              <a-form-item
                label="父节点ID:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['parentId', {rules: [{required: true, min: 1, message: '父节点不能为空'}]}]"
                  :disabled="true"
                />
              </a-form-item>
              <a-form-item
                label="资源类型:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['resourceType']"
                  :disabled="true"
                />
              </a-form-item>
              <a-form-item
                label="所属平台:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['platformCode']"
                  :disabled="true"
                />
              </a-form-item>
              <a-form-item
                v-show="false"
                label="是否可见:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select v-decorator="['isShow', {rules: [{required: true, message: '请选择状态'}]}]">
                  <a-select-option value="Y">可见</a-select-option>
                  <a-select-option value="N">不可见</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <span slot="label">
                  组件名称&nbsp;
                  <a-tooltip title="必须与前端定义的component的名称一致">
                    <a-icon type="question-circle-o" />
                  </a-tooltip>
                </span>
                <a-auto-complete
                  v-decorator="['component', {rules: [{required: true, min: 1, message: '组件名称不能为空'}]}]"
                >
                  <template slot="dataSource">
                    <a-select-option
                      v-for="item in layoutList.filter(item =>
                        !this.treeForm.getFieldValue('component') ||
                        this.layoutList.some(item => item.value === this.treeForm.getFieldValue('component')) ||
                        item.label.toLocaleLowerCase().indexOf(this.treeForm.getFieldValue('component').toLocaleLowerCase()) !== -1)"
                      :key="item.value"
                      :value="item.value"
                    >{{ item.label }}</a-select-option>
                  </template>
                </a-auto-complete>
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <span slot="label">
                  跳转路径&nbsp;
                  <a-tooltip title="设置非叶子指定默认叶子节点跳转路径">
                    <a-icon type="question-circle-o" />
                  </a-tooltip>
                </span>
                <a-input v-decorator="['redirect']" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <span slot="label">
                  访问路径&nbsp;
                  <a-tooltip title="填写该功能的访问路由">
                    <a-icon type="question-circle-o" />
                  </a-tooltip>
                </span>
                <a-input v-decorator="['path']" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <span slot="label">
                  菜单图标&nbsp;
                  <a-tooltip title="请填写https://www.antdv.com/components/icon-cn/中的对应icon的名称">
                    <a-icon type="question-circle-o" />
                  </a-tooltip>
                </span>

                <icon-selector v-decorator="['icon']" />
              </a-form-item>
              <a-form-item
                label="菜单排序:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input v-decorator="['sort']" />
              </a-form-item>
              <a-form-item
                label="菜单状态:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select v-decorator="['activity', {rules: [{required: true, message: '请选择状态'}]}]">
                  <a-select-option value="Y">启用</a-select-option>
                  <a-select-option value="N">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-form>
          </a-spin>
          <div class="drawer-footer">
            <div class="footer-fixed">
              <a-button @click="treeDrawerVisible = false">取消</a-button>
              <a-button
                @click="doTreeSubmit"
                type="primary"
              >保存</a-button>
            </div>
          </div>
        </a-drawer>
        <!--按钮主信息抽屉-->
        <a-drawer
          :width="360"
          :mask="true"
          :title="resDrawerTitle"
          :visible="resDrawerVisible"
          @close="resDrawerVisible = false"
        >
          <a-spin :spinning="resConfirmLoading">
            <a-form
              :form="resForm"
              layout="vertical"
              hideRequiredMark
            >
              <a-form-item
                label="资源ID:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['resourceId']"
                  :disabled="true"
                />
              </a-form-item>
              <a-form-item
                label="父节点ID:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['parentId', {rules: [{required: true, min: 1, message: '父节点不能为空'}]}]"
                  :disabled="true"
                />
              </a-form-item>
              <a-form-item
                label="资源类型:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['resourceType']"
                  :disabled="true"
                />
              </a-form-item>
              <a-form-item
                label="所属平台:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['platformCode']"
                  :disabled="true"
                />
              </a-form-item>
              <a-form-item
                label="对应模块:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['component', {rules: [{required: true, message: '对应模块不能为空'}]}]"
                  :disabled="true"
                />
              </a-form-item>
              <!-- <a-form-item
                label="展现类型:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select v-decorator="['showType', {rules: [{required: true, message: '请选择展现类型'}]}]" @change="selectShowChange">
                  <a-select-option value="button">
                    按钮
                  </a-select-option>
                  <a-select-option value="card">
                    卡片
                  </a-select-option>
                </a-select>
              </a-form-item>-->
              <a-form-item
                label="资源名称:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-auto-complete
                  v-decorator="['title', {rules: [{required: true, message: '请选择状态'}]}]"
                  @change="onSelectChange"
                >
                  <template slot="dataSource">
                    <a-select-option
                      v-for="item in buttonList.filter(item =>
                        !this.resForm.getFieldValue('title') ||
                        item.title.toLocaleLowerCase().indexOf(this.resForm.getFieldValue('title').toLocaleLowerCase()) !== -1)"
                      :key="item.value"
                      :value="item.title"
                    >{{ item.title }}</a-select-option>
                  </template>
                </a-auto-complete>
              </a-form-item>
              <a-form-item
                label="资源标识:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['resourceName', {rules: [{required: true, min: 1, message: '资源标识不能为空'}]}]"
                />
              </a-form-item>
              <a-form-item
                label="资源排序:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['sort', {initialValue: '', rules: [{required: true, message: '资源排序不能为空'}]}]"
                />
              </a-form-item>
              <a-form-item
                label="资源状态:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select v-decorator="['activity', {rules: [{required: true, message: '请选择状态'}]}]">
                  <a-select-option value="Y">启用</a-select-option>
                  <a-select-option value="N">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-form>
          </a-spin>
          <div class="drawer-footer">
            <div class="footer-fixed">
              <a-button @click="resDrawerVisible = false">取消</a-button>
              <a-button
                @click="doResSubmit"
                type="primary"
              >保存</a-button>
            </div>
          </div>
        </a-drawer>
        <!--服务信息抽屉-->
        <a-drawer
          :width="360"
          :mask="true"
          :title="serviceDrawerTitle"
          :visible="serviceDrawerVisible"
          @close="serviceDrawerVisible = false"
        >
          <a-spin :spinning="serviceConfirmLoading">
            <a-form
              :form="serviceForm"
              layout="vertical"
              hideRequiredMark
            >
              <a-form-item
                label="服务ID:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['resourceId']"
                  :disabled="true"
                />
              </a-form-item>
              <a-form-item
                label="父节点ID:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['parentId', {rules: [{required: true, min: 1, message: '父节点不能为空'}]}]"
                  :disabled="true"
                />
              </a-form-item>
              <a-form-item
                label="资源类型:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['resourceType']"
                  :disabled="true"
                />
              </a-form-item>
              <a-form-item
                label="所属平台:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['platformCode']"
                  :disabled="true"
                />
              </a-form-item>
              <a-form-item
                label="服务名称:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input v-decorator="['title', {rules: [{required: true, message: '请选择状态'}]}]" />
              </a-form-item>
              <a-form-item
                label="服务API路径:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-input
                  v-decorator="['resourceName', {rules: [{required: true, min: 1, message: '资源标识不能为空'}]}]"
                />
              </a-form-item>
              <a-form-item
                label="服务状态:"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
              >
                <a-select v-decorator="['activity', {rules: [{required: true, message: '请选择状态'}]}]">
                  <a-select-option value="Y">启用</a-select-option>
                  <a-select-option value="N">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-form>
          </a-spin>
          <div class="drawer-footer">
            <div class="footer-fixed">
              <a-button @click="serviceDrawerVisible = false">取消</a-button>
              <a-button
                @click="doServiceSubmit"
                type="primary"
              >保存</a-button>
            </div>
          </div>
        </a-drawer>
      </template>
    </a-card>
  </section>
</template>

<script>
import { STable, Ellipsis, IconSelector } from '@/components'
import { getResourceInfo, getMenuAllInfoList, modifyResourceInfo } from '@/api/material/um'
import { requestBuilder, takeTreeByKey } from '@/utils/util'

export default {
  name: 'ResourceManager',
  components: {
    STable,
    Ellipsis,
    IconSelector
  },
  data () {
    return {
      // 分页参数
      defaultPageSize: 10,
      pageSizeOptions: ['10', '20', '30'],
      // 查询参数
      queryParam: {
        activity: 'Y'
      },
      // 按钮权限表格
      buttonColumns: [
        {
          title: '按钮名称',
          dataIndex: 'title'
        },
        {
          title: '按钮标识',
          dataIndex: 'resourceName'
        },
        {
          title: '状态',
          dataIndex: 'activity',
          scopedSlots: { customRender: 'status' }
        }
      ],
      rowSelection: {
        onSelect: (record, selected, selectedRows, nativeEvent) => {
          if (selected) {
            this.queryParam = {
              parentId: record.resourceId,
              platformCode: 'web',
              resourceType: 's'
            }
            this.buttonTableSelectId = record.resourceId || ''
            this.$refs.serviceTable.refresh(true)
          } else {
            const record = selectedRows[0] || {}
            const parentId = record.resourceId || ''
            this.buttonTableSelectId = parentId
            this.queryParam = {
              parentId: parentId,
              platformCode: 'web',
              resourceType: 's'
            }
            this.queryParam.parentId ? this.$refs.serviceTable.refresh(true) : this.$refs.serviceTable.clear()
          }
        },
        onChange: (selectedRowKeys, selectedRows) => {
          this.selectedRows = selectedRows
          this.selectedRowKeys = selectedRowKeys
          this.buttonTableSelectId = (selectedRows[0] || {}).resourceId || ''
        }
      },
      selectedRows: [],
      selectedRowKeys: [],
      loadButtonData: parameter => {
        const param = requestBuilder('', Object.assign(this.queryParam), parameter.pageNo, parameter.pageSize)
        return getResourceInfo(param).then(res => {
          if (res.code === '0000') {
            return res.result
          } else if (res.code === '0001') {
          } else {
            this.$message.error(res.message)
          }
        })
      },
      buttonRowClick: record => ({
        on: {
          click: () => {
            this.queryParam = {
              parentId: record.resourceId,
              platformCode: 'web',
              resourceType: 's'
            }
            const resourceId = record.resourceId
            const index = this.selectedRowKeys.indexOf(resourceId)
            if (index === -1) {
              this.selectedRowKeys.push(resourceId)
              this.selectedRows.push(record)
            }
            this.buttonTableSelectId = record.resourceId || ''
            this.$refs.buttonTable.triggerSelect(this.selectedRowKeys, this.selectedRows)
            this.$refs.serviceTable.refresh(true)
          },
          dblclick: () => {}
        }
      }),
      // 服务权限表格
      serviceColumns: [
        {
          title: '服务名称',
          dataIndex: 'title'
        },
        {
          title: '服务API路径',
          dataIndex: 'resourceName'
        },
        {
          title: '状态',
          dataIndex: 'activity',
          scopedSlots: { customRender: 'status' }
        }
      ],
      rowSelectionRes: {
        onChange: (selectedRowKeys, selectedRows) => {
          this.selectedRowKeysRes = selectedRowKeys
          this.selectedRowsRes = selectedRows
        }
      },
      selectedRowKeysRes: [],
      selectedRowsRes: [],
      loadServiceData: parameter => {
        const param = requestBuilder('', Object.assign(this.queryParam), parameter.pageNo, parameter.pageSize)
        return getResourceInfo(param).then(res => {
          if (res.code === '0000') {
            return res.result
          } else if (res.code === '0001') {
          } else {
            this.$message.error(res.message)
          }
        })
      },
      serviceRowClick: record => ({
        on: {
          // 点击事件
          click: () => {}
        }
      }),
      // 滑动抽屉参数
      resForm: this.$form.createForm(this),
      treeForm: this.$form.createForm(this),
      serviceForm: this.$form.createForm(this),
      actionFlag: '',
      resConfirmLoading: false, // 加载图标是否出现
      treeConfirmLoading: false, // 加载图标是否出现
      serviceConfirmLoading: false, // 加载图标是否出现
      // 弹出抽屉配置
      treeDrawerVisible: false,
      treeDrawerTitle: '菜单新建',
      resDrawerVisible: false,
      resDrawerTitle: '按钮新建',
      serviceDrawerVisible: false,
      serviceDrawerTitle: '服务新建',
      roleId: '',
      orgTree: [],
      selectTreeNode: '',
      treeNodeInfo: '',
      buttonTableSelectId: '',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      buttonList: [
        {
          title: '新增',
          value: 'add',
          sort: 1
        },
        {
          title: '删除',
          value: 'del',
          sort: 2
        },
        {
          title: '修改',
          value: 'edit',
          sort: 3
        },
        {
          title: '查询',
          value: 'query',
          sort: 4
        },
        {
          title: '上传',
          value: 'upload',
          sort: 5
        },
        {
          title: '上报',
          value: 'up',
          sort: 6
        },
        {
          title: '审批',
          value: 'approve',
          sort: 7
        },
        {
          title: '导出',
          value: 'export',
          sort: 8
        }
      ],
      layoutList: [
        {
          label: 'PageView',
          value: 'PageView'
        },
        {
          label: 'PageFrame',
          value: 'PageFrame'
        },
        {
          label: 'RouteView',
          value: 'RouteView'
        }
      ]
    }
  },
  created () {
    this.getTree()
  },
  methods: {
    getTree () {
      getMenuAllInfoList(requestBuilder('', { resourceType: 'm', platformCode: 'web' }, 0, 0)).then(res => {
        this.orgTree = res.result.treeNodes
        this.selectTreeNode = ''
        this.treeNodeInfo = res.result.nodes
        this.$message.success('菜单资源加载完毕')
      })
    },
    doAdd () {
      this.openResDrawer('insert')
    },
    doEdit () {
      if (this.selectedRows.length > 0) {
        this.openResDrawer('update', this.selectedRows[0])
      } else {
        this.$message.error('未选中任何记录')
      }
    },
    doDel () {
      const resourceIds = []
      if (this.selectedRows.length > 0) {
        for (let i = 0; i < this.selectedRows.length; i++) {
          resourceIds.push(this.selectedRows[i].resourceId)
        }
        this.handleDel(resourceIds, 'b')
      } else {
        this.$message.error('未选中任何记录')
      }
    },
    doServiceAdd () {
      this.openServiceDrawer('insert', this.buttonTableSelectId)
    },
    doServiceEdit () {
      if (this.selectedRowsRes.length > 0) {
        this.openServiceDrawer('update', this.selectedRowsRes[0])
      } else {
        this.$message.error('未选中任何记录')
      }
    },
    doServiceDel () {
      const resourceIds = []
      if (this.selectedRowsRes.length > 0) {
        for (let i = 0; i < this.selectedRowsRes.length; i++) {
          resourceIds.push(this.selectedRowsRes[i].resourceId)
        }
        this.handleDel(resourceIds, 's')
      } else {
        this.$message.error('未选中任何记录')
      }
    },
    doTreeAdd () {
      if (this.selectTreeNode !== '') {
        this.openTreeDrawer('insert')
      } else {
        this.$message.error('未选中任何节点')
      }
    },
    doTreeEdit () {
      if (this.selectTreeNode !== '') {
        var index = this.treeNodeInfo.findIndex(item => item.resourceId === this.selectTreeNode)
        this.openTreeDrawer('update', this.treeNodeInfo[index])
      } else {
        this.$message.error('未选中任何节点')
      }
    },
    doTreeDel () {
      if (this.selectTreeNode !== '') {
        var ids = []
        ids.push(this.selectTreeNode)
        const data = takeTreeByKey(this.orgTree, this.selectTreeNode)
        if (data.children.length > 0) {
          this.$message.error('该节点下存在子节点，无法进行删除操作！')
        } else {
          this.handleDel(ids, 'm')
        }
      } else {
        this.$message.error('未选中任何节点')
      }
    },
    handleDel (value, flag) {
      this.$confirm({
        title: '确认?',
        content: '是否删除选中记录',
        onOk: () => {
          this.actionFlag = 'delete'
          const param = {
            ids: value.join(','),
            resourceType: flag
          }
          this.getResult(param, flag)
        },
        onCancel () {}
      })
    },
    openTreeDrawer (action, record) {
      this.treeDrawerVisible = true
      this.$nextTick(() => {
        if (action === 'insert') {
          this.treeForm.resetFields()
          this.treeForm.setFieldsValue({
            parentId: this.selectTreeNode,
            isShow: 'Y',
            activity: 'Y',
            resourceType: 'm',
            platformCode: 'web'
          })
          this.treeDrawerTitle = '菜单新增'
          this.actionFlag = 'insert'
        } else {
          this.treeDrawerTitle = '菜单修改'
          this.treeForm.resetFields()
          this.treeForm.setFieldsValue(record)
          this.actionFlag = 'update'
        }
      })
    },
    openResDrawer (action, record) {
      this.resDrawerVisible = true
      this.resConfirmLoading = true
      this.$nextTick(() => {
        if (action === 'insert') {
          var index = this.treeNodeInfo.findIndex(item => item.resourceId === this.selectTreeNode)
          this.resForm.resetFields()
          this.resForm.setFieldsValue({
            parentId: this.selectTreeNode,
            platformCode: 'web',
            resourceType: 'b',
            activity: 'Y',
            component: this.treeNodeInfo[index].component
          })
          this.resDrawerTitle = '（按钮、卡片）新增'
          this.actionFlag = 'insert'
        } else {
          this.resDrawerTitle = '（按钮、卡片）修改'
          this.resForm.setFieldsValue(record)
          this.actionFlag = 'update'
        }
        this.resConfirmLoading = false
      })
    },
    openServiceDrawer (action, record) {
      this.serviceDrawerVisible = true
      this.serviceConfirmLoading = true
      this.$nextTick(() => {
        if (action === 'insert') {
          this.serviceForm.resetFields()
          this.serviceForm.setFieldsValue({
            parentId: record,
            platformCode: 'web',
            resourceType: 's',
            activity: 'Y'
          })
          this.serviceDrawerTitle = '服务新增'
          this.actionFlag = 'insert'
        } else {
          this.serviceDrawerTitle = '服务修改'
          this.serviceForm.setFieldsValue(record)
          this.actionFlag = 'update'
        }
        this.serviceConfirmLoading = false
      })
    },
    onTreeSelect (selectedKeys, info) {
      this.selectTreeNode = info.node.value
      this.queryParam = {
        parentId: info.node.value,
        platformCode: 'web',
        resourceType: 'b'
      }
      this.$refs.buttonTable.refresh(true)
      this.$refs.buttonTable.updateSelect([], [])
      this.$refs.buttonTable.rowSelection.onChange([], [])
      this.$refs.serviceTable.refresh((this.queryParam = {}))
    },
    onSelectChange (key) {
      var index = this.buttonList.findIndex(item => item.title === key)
      if (index < 0) {
        this.resForm.setFieldsValue({
          resourceName: '',
          sort: ''
        })
      } else {
        this.resForm.setFieldsValue({
          resourceName: this.buttonList[index].value,
          sort: this.buttonList[index].sort
        })
      }
    },
    // 保存按钮信息数据
    doResSubmit () {
      const {
        resForm: { validateFields }
      } = this
      this.resConfirmLoading = true
      validateFields((errors, values) => {
        // 如果输入数据都合规
        if (!errors) {
          this.params = { ...values }
          this.getResult({ list: [this.params] }, 'b')
        } else {
        }
        this.resConfirmLoading = false
      })
    },
    // 保存服务信息数据
    doServiceSubmit () {
      const {
        serviceForm: { validateFields }
      } = this
      this.serviceConfirmLoading = true
      validateFields((errors, values) => {
        // 如果输入数据都合规
        if (!errors) {
          this.params = { ...values }
          this.getResult({ list: [this.params] }, 's')
        } else {
        }
        this.serviceConfirmLoading = false
      })
    },
    // 保存菜单资源数据
    doTreeSubmit () {
      const {
        treeForm: { validateFields }
      } = this
      this.treeConfirmLoading = true
      validateFields((errors, values) => {
        // 如果输入数据都合规
        if (!errors) {
          this.params = { ...values }
          this.getResult({ list: [this.params] }, 'm')
        } else {
        }
        this.treeConfirmLoading = false
      })
    },
    // 统一提交方法
    getResult (params, resourceType) {
      modifyResourceInfo(requestBuilder(this.actionFlag, params, '0', '0'))
        .then(res => {
          if (res.code === '0000') {
            if (resourceType === 'm') {
              this.getTree() // 刷新菜单树
            } else if (resourceType === 'b') {
              this.$refs.buttonTable.refresh(
                (this.queryParam = { parentId: this.selectTreeNode, resourceType: 'b', platformCode: 'web' })
              )
              this.$refs.buttonTable.updateSelect([], [])
              this.$refs.buttonTable.rowSelection.onChange([], [])
            } else if (resourceType === 's') {
              this.$refs.serviceTable.refresh(
                (this.queryParam = { parentId: this.buttonTableSelectId, resourceType: 's', platformCode: 'web' })
              )
              this.$refs.serviceTable.updateSelect([], [])
              this.$refs.serviceTable.rowSelection.onChange([], [])
            } else {
            }
            this.$message.success(res.message)
          } else if (res.code === '9999') {
            this.$message.error(res.message)
          }
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.confirmLoading = false
        })
    }
  }
}
</script>

<style scoped>
::v-deep .ant-table {
  border: 1px solid #e8e8e8;
}
</style>
