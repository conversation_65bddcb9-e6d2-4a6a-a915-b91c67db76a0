<template>
  <section>
    <a-card :bordered="false">
      <!-- 搜索区域 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="10">
            <a-col :xl="6" :md="6" :sm="24">
              <a-form-item label="名称">
                <a-input v-model="queryParam.label" placeholder="请输入名称" allowClear @change="handleSearch" />
              </a-form-item>
            </a-col>
            <a-col :xl="6" :md="6" :sm="24">
              <a-form-item label="编码">
                <a-input v-model="queryParam.code" placeholder="请输入编码" allowClear @change="handleSearch" />
              </a-form-item>
            </a-col>
            <a-col :xl="6" :md="6" :sm="24">
              <a-form-item>
                <a-button type="primary" icon="search" @click="handleSearch">搜索</a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <a-divider />

      <!-- 主体内容区域 -->
      <div class="content-wrapper">
        <!-- 左侧配置类别列表 -->
        <div class="left-panel">
          <div class="panel-header">
            <h3>配置类别</h3>
            <a-button type="primary" icon="plus" @click="handleAddCategory">新增类别</a-button>
          </div>
          <a-list :dataSource="categoryList" class="category-list">
            <a-list-item slot="renderItem" slot-scope="item" @click="handleCategorySelect(item)" :class="{ active: currentCategory.id === item.id }">
              <a-list-item-meta>
                <div slot="title">{{ item.label }}</div>
                <div slot="description">{{ item.code }}</div>
              </a-list-item-meta>
              <a-dropdown>
                <a-button type="link">
                  <a-icon type="more" />
                </a-button>
                <a-menu slot="overlay">
                  <a-menu-item @click="handleEditCategory(item)"><a-icon type="edit" />编辑</a-menu-item>
                  <a-menu-item @click="handleDeleteCategory(item)"><a-icon type="delete" />删除</a-menu-item>
                </a-menu>
              </a-dropdown>
            </a-list-item>
          </a-list>
        </div>

        <!-- 右侧树形配置内容 -->
        <div class="right-panel">
          <div class="panel-header">
            <h3>{{ currentCategory.label || '请选择配置类别' }}</h3>
            <div class="header-actions">
              <a-button type="link" @click="toggleExpandAll">
                <a-icon :type="expandAll ? 'minus-square' : 'plus-square'" />
                {{ expandAll ? '收起全部' : '展开全部' }}
              </a-button>
              <a-button type="primary" icon="plus" @click="handleAddConfigItem" :disabled="!currentCategory.id">新增配置项</a-button>
            </div>
          </div>
          <a-tree
            v-if="currentCategory.id"
            :treeData="configTreeData"
            :replaceFields="{ title: 'label', key: 'id', value: 'id' }"
            :expandedKeys="expandedKeys"
            @select="handleTreeSelect"
            @expand="handleExpand"
          >
            <template slot="title" slot-scope="{ node }">
              <div class="tree-node-content">
                <div class="node-info">
                  <span class="node-label">{{ node.dataRef.label }}</span>
                  <span class="node-code">({{ node.dataRef.code }})</span>
                </div>
                <div class="node-actions">
                  <a-button type="link" @click.stop="() => handleAddChild(node.dataRef)">
                    <a-icon type="plus" />
                  </a-button>
                  <a-button type="link" @click.stop="() => handleEditConfig(node.dataRef)">
                    <a-icon type="edit" />
                  </a-button>
                  <a-button type="link" @click.stop="() => handleDeleteConfig(node.dataRef)">
                    <a-icon type="delete" />
                  </a-button>
                </div>
              </div>
            </template>
          </a-tree>
        </div>
      </div>
    </a-card>

    <!-- 新增/编辑类别弹窗 -->
    <a-modal
      :title="categoryModal.title"
      :visible="categoryModal.visible"
      @ok="handleCategoryModalOk"
      @cancel="() => categoryModal.visible = false"
    >
      <a-form-model ref="categoryForm" :model="categoryForm" :rules="categoryRules">
        <a-form-model-item label="名称" prop="label">
          <a-input v-model="categoryForm.label" placeholder="请输入名称" />
        </a-form-model-item>
        <a-form-model-item label="编码" prop="code">
          <a-input v-model="categoryForm.code" placeholder="请输入编码" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>

    <!-- 新增/编辑配置项弹窗 -->
    <a-modal
      :title="configModal.title"
      :visible="configModal.visible"
      @ok="handleConfigModalOk"
      @cancel="() => configModal.visible = false"
    >
      <a-form-model ref="configForm" :model="configForm" :rules="configRules">
        <a-form-model-item label="名称" prop="label">
          <a-input v-model="configForm.label" placeholder="请输入名称" />
        </a-form-model-item>
        <a-form-model-item label="编码" prop="code">
          <a-input v-model="configForm.code" placeholder="请输入编码" />
        </a-form-model-item>
        <a-form-model-item label="父节点">
          <a-input v-model="configForm.parentLabel" disabled />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </section>
</template>

<script>
export default {
  name: 'ITSMBaseInfoManagement',
  data () {
    return {
      // 查询参数
      queryParam: {
        label: '',
        code: ''
      },

      // 当前选中的类别
      currentCategory: {},

      // 配置类别列表
      categoryList: [
        { id: 1, label: '变更种类', code: 'CHANGE_TYPE' },
        { id: 2, label: '设备类型', code: 'DEVICE_TYPE' },
        { id: 3, label: '紧急程度', code: 'URGENCY_LEVEL' }
      ],

      // 配置项树形数据
      configTreeData: [],

      // 展开的节点key列表
      expandedKeys: [],

      // 是否全部展开
      expandAll: false,

      // 类别弹窗
      categoryModal: {
        title: '',
        visible: false,
        type: 'add' // add or edit
      },

      // 类别表单
      categoryForm: {
        label: '',
        code: ''
      },

      // 类别表单校验规则
      categoryRules: {
        label: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入编码', trigger: 'blur' }]
      },

      // 配置项弹窗
      configModal: {
        title: '',
        visible: false,
        type: 'add' // add or edit
      },

      // 配置项表单
      configForm: {
        label: '',
        code: '',
        parentId: null,
        parentLabel: ''
      },

      // 配置项表单校验规则
      configRules: {
        label: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入编码', trigger: 'blur' }]
      }
    }
  },

  created () {
    // 初始化示例数据
    this.loadConfigTree(1)
  },

  methods: {
    // 树节点选择处理
    handleTreeSelect (selectedKeys, info) {
      console.log('选中的节点:', selectedKeys, info)
      // 这里可以根据业务需求添加更多处理逻辑
    },

    // 树节点选择事件
    select (selectedKeys, info) {
      this.handleTreeSelect(selectedKeys, info)
    },

    // 搜索处理
    handleSearch () {
      const { label, code } = this.queryParam
      this.categoryList = this.categoryList.filter(item => {
        return (!label || item.label.includes(label)) && (!code || item.code.includes(code))
      })
    },

    // 展开/收起相关操作
    toggleExpandAll () {
      this.expandAll = !this.expandAll
      if (this.expandAll) {
        this.expandAllNodes()
      } else {
        this.expandedKeys = []
      }
    },

    expandAllNodes () {
      const keys = []
      const getKeys = nodes => {
        nodes.forEach(node => {
          keys.push(node.id)
          if (node.children) {
            getKeys(node.children)
          }
        })
      }
      getKeys(this.configTreeData)
      this.expandedKeys = keys
    },

    handleExpand (expandedKeys) {
      this.expandedKeys = expandedKeys
      this.expandAll = this.isAllExpanded()
    },

    isAllExpanded () {
      const allKeys = []
      const getKeys = nodes => {
        nodes.forEach(node => {
          allKeys.push(node.id)
          if (node.children) {
            getKeys(node.children)
          }
        })
      }
      getKeys(this.configTreeData)
      return allKeys.length > 0 && allKeys.every(key => this.expandedKeys.includes(key))
    },

    // 类别相关操作
    handleAddCategory () {
      this.categoryModal.type = 'add'
      this.categoryModal.title = '新增配置类别'
      this.categoryModal.visible = true
      this.categoryForm = { label: '', code: '' }
    },

    handleEditCategory (category) {
      this.categoryModal.type = 'edit'
      this.categoryModal.title = '编辑配置类别'
      this.categoryModal.visible = true
      this.categoryForm = { ...category }
    },

    async handleDeleteCategory (category) {
      const confirm = await this.$confirm({
        title: '确认删除',
        content: `是否确认删除配置类别「${category.label}」？删除后将无法恢复。`,
        okText: '确认',
        cancelText: '取消',
        okType: 'danger'
      })

      if (confirm) {
        const index = this.categoryList.findIndex(item => item.id === category.id)
        if (index > -1) {
          this.categoryList.splice(index, 1)
          if (this.currentCategory.id === category.id) {
            this.currentCategory = {}
            this.configTreeData = []
            this.expandedKeys = []
            this.expandAll = false
          }
          this.$message.success('删除成功')
        }
      }
    },

    handleCategorySelect (category) {
      this.currentCategory = category
      // 加载对应的配置树数据
      this.loadConfigTree(category.id)
      // 重置展开状态
      this.expandedKeys = []
      this.expandAll = false
    },

    async handleCategoryModalOk () {
      try {
        await this.$refs.categoryForm.validate()
        if (this.categoryModal.type === 'add') {
          const newCategory = {
            id: Date.now(),
            ...this.categoryForm
          }
          this.categoryList.push(newCategory)
          this.$message.success('新增成功')
        } else {
          const index = this.categoryList.findIndex(item => item.id === this.categoryForm.id)
          if (index > -1) {
            this.categoryList[index] = { ...this.categoryForm }
            if (this.currentCategory.id === this.categoryForm.id) {
              this.currentCategory = { ...this.categoryForm }
            }
            this.$message.success('更新成功')
          }
        }
        this.categoryModal.visible = false
      } catch (error) {
        console.error('表单验证失败:', error)
      }
    },

    // 配置项相关操作
    handleAddConfigItem () {
      this.configModal.type = 'add'
      this.configModal.title = '新增配置项'
      this.configModal.visible = true
      this.configForm = {
        label: '',
        code: '',
        parentId: null,
        parentLabel: '无'
      }
    },

    handleAddChild (node) {
      this.configModal.type = 'add'
      this.configModal.title = '新增子配置项'
      this.configModal.visible = true
      this.configForm = {
        label: '',
        code: '',
        parentId: node.id,
        parentLabel: node.label
      }
    },

    handleEditConfig (node) {
      this.configModal.type = 'edit'
      this.configModal.title = '编辑配置项'
      this.configModal.visible = true
      this.configForm = {
        ...node,
        parentLabel: this.getParentLabel(node)
      }
    },

    async handleDeleteConfig (node) {
      const hasChildren = node.children && node.children.length > 0
      const confirm = await this.$confirm({
        title: '确认删除',
        content: `是否确认删除配置项「${node.label}」？${hasChildren ? '该节点包含子节点，删除后所有子节点将一并删除。' : ''}删除后将无法恢复。`,
        okText: '确认',
        cancelText: '取消',
        okType: 'danger'
      })
      if (confirm) {
        this.deleteTreeNode(this.configTreeData, node.id)
        this.$message.success('删除成功')
      }
    },

    async handleConfigModalOk () {
      try {
        await this.$refs.configForm.validate()
        const newNode = {
          id: this.configModal.type === 'add' ? Date.now() : this.configForm.id,
          label: this.configForm.label,
          code: this.configForm.code
        }

        if (this.configModal.type === 'add') {
          if (this.configForm.parentId) {
            this.addChildNode(this.configTreeData, this.configForm.parentId, newNode)
          } else {
            this.configTreeData.push(newNode)
          }
          this.$message.success('新增成功')
        } else {
          this.updateTreeNode(this.configTreeData, newNode)
          this.$message.success('更新成功')
        }
        this.configModal.visible = false
      } catch (error) {
        console.error('表单验证失败:', error)
      }
    },

    // 树操作辅助方法
    loadConfigTree (categoryId) {
      // 模拟加载数据
      if (categoryId === 1) {
        this.configTreeData = [
          {
            id: 1,
            label: '常规变更',
            code: 'NORMAL_CHANGE',
            children: [
              {
                id: 11,
                label: '系统升级',
                code: 'SYS_UPGRADE',
                children: [
                  { id: 111, label: '操作系统升级', code: 'OS_UPGRADE' },
                  { id: 112, label: '数据库升级', code: 'DB_UPGRADE' },
                  { id: 113, label: '应用系统升级', code: 'APP_UPGRADE' }
                ]
              },
              {
                id: 12,
                label: '硬件更换',
                code: 'HARDWARE_REPLACE',
                children: [
                  { id: 121, label: '服务器更换', code: 'SERVER_REPLACE' },
                  { id: 122, label: '网络设备更换', code: 'NETWORK_REPLACE' },
                  { id: 123, label: '存储设备更换', code: 'STORAGE_REPLACE' }
                ]
              }
            ]
          }
        ]
      } else {
        this.configTreeData = []
      }
    },

    getParentLabel (node) {
      if (!node.parentId) return '无'
      let parent = null
      const findParent = nodes => {
        for (const item of nodes) {
          if (item.id === node.parentId) {
            parent = item
            break
          }
          if (item.children) {
            findParent(item.children)
          }
        }
      }
      findParent(this.configTreeData)
      return parent ? parent.label : '无'
    },

    addChildNode (nodes, parentId, newNode) {
      for (const node of nodes) {
        if (node.id === parentId) {
          if (!node.children) {
            node.children = []
          }
          node.children.push(newNode)
          return true
        }
        if (node.children && this.addChildNode(node.children, parentId, newNode)) {
          return true
        }
      }
      return false
    },

    updateTreeNode (nodes, updatedNode) {
      for (const node of nodes) {
        if (node.id === updatedNode.id) {
          Object.assign(node, updatedNode)
          return true
        }
        if (node.children && this.updateTreeNode(node.children, updatedNode)) {
          return true
        }
      }
      return false
    },

    deleteTreeNode (nodes, nodeId) {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].id === nodeId) {
          nodes.splice(i, 1)
          return true
        }
        if (nodes[i].children && this.deleteTreeNode(nodes[i].children, nodeId)) {
          return true
        }
      }
      return false
    }
  }
}
</script>

<style lang="less" scoped>
.content-wrapper {
  display: flex;
  margin-top: 16px;
  min-height: calc(100vh - 300px);
  background: #fff;

  .left-panel {
    width: 37.5%;
    border-right: 1px solid #f0f0f0;
    padding: 16px;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h3 {
        margin: 0;
        font-size: 16px;
      }
    }

    .category-list {
      .ant-list-item {
        cursor: pointer;
        padding: 12px;
        border-radius: 4px;
        transition: all 0.3s;

        &:hover {
          background: #f5f5f5;
        }

        &.active {
          background: #e6f7ff;
        }

        .ant-list-item-meta-title {
          margin-bottom: 4px;
        }

        .ant-list-item-meta-description {
          color: #999;
        }
      }
    }
  }

  .right-panel {
    flex: 1;
    padding: 16px;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h3 {
        margin: 0;
        font-size: 16px;
      }
    }

    .tree-node-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        background-color: #f5f5f5;
      }

      .node-info {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
        margin-right: 8px;

        .node-label {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.85);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .node-code {
          margin-left: 8px;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.45);
          white-space: nowrap;
        }
      }

      .node-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        white-space: nowrap;

        .ant-btn {
          padding: 0 4px;
          height: 24px;
          line-height: 24px;
          background: transparent;
          border: none;

          &:hover {
            color: #1890ff;
          }
        }
      }
    }
  }
}

.table-page-search-wrapper {
  margin-bottom: 16px;
}
</style>
