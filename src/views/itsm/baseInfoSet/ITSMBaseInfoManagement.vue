<template>
  <section>
    <a-card :bordered="false">
      <!-- 搜索区域 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="10">
            <a-col :xl="6" :md="6" :sm="24">
              <a-form-item label="类别名称">
                <a-input v-model="queryParam.codeClassName" placeholder="请输入类别名称" allowClear @change="handleSearch" />
              </a-form-item>
            </a-col>
            <a-col :xl="6" :md="6" :sm="24">
              <a-form-item label="类别编码">
                <a-input v-model="queryParam.codeClassId" placeholder="请输入类别编码" allowClear @change="handleSearch" />
              </a-form-item>
            </a-col>
            <a-col :xl="6" :md="6" :sm="24">
              <a-form-item label="状态">
                <a-select v-model="queryParam.activity" allowClear placeholder="请选择状态" @change="handleSearch">
                  <a-select-option value="Y">启用</a-select-option>
                  <a-select-option value="N">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :md="6" :sm="24">
              <a-form-item>
                <a-button type="primary" icon="search" @click="handleSearch">搜索</a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <a-divider />

      <!-- 主体内容区域 -->
      <div class="content-wrapper">
        <!-- 左侧配置类别列表 -->
        <div class="left-panel">
          <div class="panel-header">
            <h3>配置类别</h3>
            <a-button type="primary" icon="plus" @click="handleAddCategory">新增类别</a-button>
          </div>
          <a-list :dataSource="categoryList" class="category-list">
            <a-list-item slot="renderItem" slot-scope="item" @click="handleCategorySelect(item)" :class="{ active: currentCategory.codeClassId === item.codeClassId }">
              <a-list-item-meta>
                <div slot="title">{{ item.codeClassName }}</div>
                <div slot="description">{{ item.codeClassId }}</div>
              </a-list-item-meta>
              <a-dropdown>
                <a-button type="link">
                  <a-icon type="more" />
                </a-button>
                <a-menu slot="overlay">
                  <a-menu-item @click="handleEditCategory(item)"><a-icon type="edit" />编辑</a-menu-item>
                  <a-menu-item @click="handleDeleteCategory(item)"><a-icon type="delete" />删除</a-menu-item>
                </a-menu>
              </a-dropdown>
            </a-list-item>
          </a-list>
        </div>

        <!-- 右侧树形配置内容 -->
        <div class="right-panel">
          <div class="panel-header">
            <h3>{{ currentCategory.codeClassName || '请选择配置类别' }}</h3>
            <div class="header-actions">
              <a-button type="link" @click="toggleExpandAll">
                <a-icon :type="expandAll ? 'minus-square' : 'plus-square'" />
                {{ expandAll ? '收起全部' : '展开全部' }}
              </a-button>
              <a-button type="primary" icon="plus" @click="handleAddConfigItem" :disabled="!currentCategory.codeClassId">新增配置项</a-button>
            </div>
          </div>
          <a-tree
            v-if="currentCategory.codeClassId"
            :treeData="configTreeData"
            :replaceFields="{ title: 'codeName', key: 'codeId', value: 'codeId' }"
            :expandedKeys="expandedKeys"
            @select="handleTreeSelect"
            @expand="handleExpand"
          >
            <template slot="title" slot-scope="{ node }">
              <div class="tree-node-content">
                <div class="node-info">
                  <span class="node-label">{{ node.dataRef.codeName }}</span>
                  <span class="node-code">({{ node.dataRef.codeId }})</span>
                </div>
                <div class="node-actions">
                  <a-button type="link" @click.stop="() => handleAddChild(node.dataRef)">
                    <a-icon type="plus" />
                  </a-button>
                  <a-button type="link" @click.stop="() => handleEditConfig(node.dataRef)">
                    <a-icon type="edit" />
                  </a-button>
                  <a-button type="link" @click.stop="() => handleDeleteConfig(node.dataRef)">
                    <a-icon type="delete" />
                  </a-button>
                </div>
              </div>
            </template>
          </a-tree>
        </div>
      </div>
    </a-card>

    <!-- 新增/编辑类别弹窗 -->
    <a-modal
      :title="categoryModal.title"
      :visible="categoryModal.visible"
      @ok="handleCategoryModalOk"
      @cancel="() => categoryModal.visible = false"
    >
      <a-form-model ref="categoryForm" :model="categoryForm" :rules="categoryRules">
        <a-form-model-item label="类别名称" prop="codeClassName">
          <a-input v-model="categoryForm.codeClassName" placeholder="请输入类别名称" />
        </a-form-model-item>
        <a-form-model-item label="类别编码" prop="codeClassId">
          <a-input v-model="categoryForm.codeClassId" placeholder="请输入类别编码" />
        </a-form-model-item>
        <a-form-model-item label="显示顺序" prop="showSequence">
          <a-input v-model="categoryForm.showSequence" placeholder="请输入显示顺序" />
        </a-form-model-item>
        <a-form-model-item label="状态" prop="activity">
          <a-select v-model="categoryForm.activity" placeholder="请选择状态">
            <a-select-option value="Y">启用</a-select-option>
            <a-select-option value="N">禁用</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="单位个性化" prop="existPrivate">
          <a-select v-model="categoryForm.existPrivate" placeholder="请选择单位个性化">
            <a-select-option value="1">继承</a-select-option>
            <a-select-option value="2">并列</a-select-option>
            <a-select-option value="3">否</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>

    <!-- 新增/编辑配置项弹窗 -->
    <a-modal
      :title="configModal.title"
      :visible="configModal.visible"
      @ok="handleConfigModalOk"
      @cancel="() => configModal.visible = false"
    >
      <a-form-model ref="configForm" :model="configForm" :rules="configRules">
        <a-form-model-item label="代码名称" prop="codeName">
          <a-input v-model="configForm.codeName" placeholder="请输入代码名称" />
        </a-form-model-item>
        <a-form-model-item label="代码编码" prop="codeId">
          <a-input v-model="configForm.codeId" placeholder="请输入代码编码" />
        </a-form-model-item>
        <a-form-model-item label="补充说明1" prop="udf1">
          <a-input v-model="configForm.udf1" placeholder="请输入补充说明1" />
        </a-form-model-item>
        <a-form-model-item label="补充说明2" prop="udf2">
          <a-input v-model="configForm.udf2" placeholder="请输入补充说明2" />
        </a-form-model-item>
        <a-form-model-item label="补充说明3" prop="udf3">
          <a-input v-model="configForm.udf3" placeholder="请输入补充说明3" />
        </a-form-model-item>
        <a-form-model-item label="父节点" prop="parentId">
          <a-select v-model="configForm.parentId" placeholder="请选择父节点" allowClear>
            <a-select-option value="0">无父节点</a-select-option>
            <a-select-option v-for="item in parentIdList" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="显示顺序" prop="showSequence">
          <a-input v-model="configForm.showSequence" placeholder="请输入显示顺序" />
        </a-form-model-item>
        <a-form-model-item label="状态" prop="activity">
          <a-select v-model="configForm.activity" placeholder="请选择状态">
            <a-select-option value="Y">启用</a-select-option>
            <a-select-option value="N">禁用</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </section>
</template>

<script>
import { queryCodeBasClass, modifyCodeBasClass, queryCodeBasCode, modifyCodeBasCode } from '@/api/material/codebas'
import { requestBuilder } from '@/utils/util'
import * as baseApi from '@/api/material/base'

export default {
  name: 'ITSMBaseInfoManagement',
  data () {
    return {
      // 查询参数
      queryParam: {
        codeClassName: '',
        codeClassId: '',
        activity: ''
      },

      // 当前选中的类别
      currentCategory: {},

      // 配置类别列表
      categoryList: [],

      // 配置项树形数据
      configTreeData: [],

      // 展开的节点key列表
      expandedKeys: [],

      // 是否全部展开
      expandAll: false,

      // 父节点列表
      parentIdList: [],

      // 类别弹窗
      categoryModal: {
        title: '',
        visible: false,
        type: 'add' // add or edit
      },

      // 类别表单
      categoryForm: {
        codeClassName: '',
        codeClassId: '',
        showSequence: '',
        activity: 'Y',
        existPrivate: '3'
      },

      // 类别表单校验规则
      categoryRules: {
        codeClassName: [{ required: true, message: '请输入类别名称', trigger: 'blur' }],
        codeClassId: [{ required: true, message: '请输入类别编码', trigger: 'blur' }],
        showSequence: [{ required: true, message: '请输入显示顺序', trigger: 'blur' }],
        activity: [{ required: true, message: '请选择状态', trigger: 'change' }],
        existPrivate: [{ required: true, message: '请选择单位个性化', trigger: 'change' }]
      },

      // 配置项弹窗
      configModal: {
        title: '',
        visible: false,
        type: 'add' // add or edit
      },

      // 配置项表单
      configForm: {
        codeId: '',
        codeName: '',
        codeClassId: '',
        udf1: '',
        udf2: '',
        udf3: '',
        parentId: '0',
        showSequence: '',
        activity: 'Y'
      },

      // 配置项表单校验规则
      configRules: {
        codeId: [{ required: true, message: '请输入代码编码', trigger: 'blur' }],
        codeName: [{ required: true, message: '请输入代码名称', trigger: 'blur' }],
        showSequence: [{ required: true, message: '请输入显示顺序', trigger: 'blur' }],
        activity: [{ required: true, message: '请选择状态', trigger: 'change' }]
      }
    }
  },

  created () {
    this.loadCategoryList()
  },

  methods: {
    // 加载类别列表
    loadCategoryList () {
      const param = requestBuilder('', this.queryParam, 1, 100)
      queryCodeBasClass(param).then(res => {
        if (res.code === '0000') {
          this.categoryList = res.result.data || []
        }
      })
    },

    // 树节点选择处理
    handleTreeSelect (selectedKeys, info) {
      console.log('选中的节点:', selectedKeys, info)
      // 这里可以根据业务需求添加更多处理逻辑
    },

    // 树节点选择事件
    select (selectedKeys, info) {
      this.handleTreeSelect(selectedKeys, info)
    },

    // 搜索处理
    handleSearch () {
      this.loadCategoryList()
    },

    // 展开/收起相关操作
    toggleExpandAll () {
      this.expandAll = !this.expandAll
      if (this.expandAll) {
        this.expandAllNodes()
      } else {
        this.expandedKeys = []
      }
    },

    expandAllNodes () {
      const keys = []
      const getKeys = nodes => {
        nodes.forEach(node => {
          keys.push(node.codeId)
          if (node.children) {
            getKeys(node.children)
          }
        })
      }
      getKeys(this.configTreeData)
      this.expandedKeys = keys
    },

    handleExpand (expandedKeys) {
      this.expandedKeys = expandedKeys
      this.expandAll = this.isAllExpanded()
    },

    isAllExpanded () {
      const allKeys = []
      const getKeys = nodes => {
        nodes.forEach(node => {
          allKeys.push(node.codeId)
          if (node.children) {
            getKeys(node.children)
          }
        })
      }
      getKeys(this.configTreeData)
      return allKeys.length > 0 && allKeys.every(key => this.expandedKeys.includes(key))
    },

    // 类别相关操作
    handleAddCategory () {
      this.categoryModal.type = 'add'
      this.categoryModal.title = '新增配置类别'
      this.categoryModal.visible = true
      this.categoryForm = {
        codeClassName: '',
        codeClassId: '',
        showSequence: '',
        activity: 'Y',
        existPrivate: '3'
      }
    },

    handleEditCategory (category) {
      this.categoryModal.type = 'edit'
      this.categoryModal.title = '编辑配置类别'
      this.categoryModal.visible = true
      this.categoryForm = { ...category }
    },

    async handleDeleteCategory (category) {
      const confirm = await this.$confirm({
        title: '确认删除',
        content: `是否确认删除配置类别「${category.codeClassName}」？删除后将无法恢复。`,
        okText: '确认',
        cancelText: '取消',
        okType: 'danger'
      })

      if (confirm) {
        const param = requestBuilder('delete', { codeClassId: category.codeClassId }, 0, 0)
        modifyCodeBasClass(param).then(res => {
          if (res.code === '0000') {
            this.$message.success('删除成功')
            this.loadCategoryList()
            if (this.currentCategory.codeClassId === category.codeClassId) {
              this.currentCategory = {}
              this.configTreeData = []
              this.expandedKeys = []
              this.expandAll = false
            }
          } else {
            this.$message.error(res.message || '删除失败')
          }
        })
      }
    },

    handleCategorySelect (category) {
      this.currentCategory = category
      // 加载对应的配置树数据
      this.loadConfigTree(category.codeClassId)
      // 获取父节点列表
      this.getParentIdList(category.codeClassId)
      // 重置展开状态
      this.expandedKeys = []
      this.expandAll = false
    },

    async handleCategoryModalOk () {
      try {
        await this.$refs.categoryForm.validate()
        const actionFlag = this.categoryModal.type === 'add' ? 'insert' : 'update'
        const param = requestBuilder(actionFlag, this.categoryForm, 0, 0)

        modifyCodeBasClass(param).then(res => {
          if (res.code === '0000') {
            this.$message.success(this.categoryModal.type === 'add' ? '新增成功' : '更新成功')
            this.categoryModal.visible = false
            this.loadCategoryList()
            if (this.categoryModal.type === 'edit' && this.currentCategory.codeClassId === this.categoryForm.codeClassId) {
              this.currentCategory = { ...this.categoryForm }
            }
          } else {
            this.$message.error(res.message || '操作失败')
          }
        })
      } catch (error) {
        console.error('表单验证失败:', error)
      }
    },

    // 配置项相关操作
    handleAddConfigItem () {
      if (!this.currentCategory.codeClassId) {
        this.$message.error('请先选择配置类别')
        return
      }
      this.configModal.type = 'add'
      this.configModal.title = '新增配置项'
      this.configModal.visible = true
      this.configForm = {
        codeId: '',
        codeName: '',
        codeClassId: this.currentCategory.codeClassId,
        udf1: '',
        udf2: '',
        udf3: '',
        parentId: '0',
        showSequence: '',
        activity: 'Y'
      }
    },

    handleAddChild (node) {
      this.configModal.type = 'add'
      this.configModal.title = '新增子配置项'
      this.configModal.visible = true
      this.configForm = {
        codeId: '',
        codeName: '',
        codeClassId: this.currentCategory.codeClassId,
        udf1: '',
        udf2: '',
        udf3: '',
        parentId: node.codeId,
        showSequence: '',
        activity: 'Y'
      }
    },

    handleEditConfig (node) {
      this.configModal.type = 'edit'
      this.configModal.title = '编辑配置项'
      this.configModal.visible = true
      this.configForm = { ...node }
    },

    async handleDeleteConfig (node) {
      const hasChildren = node.children && node.children.length > 0
      const confirm = await this.$confirm({
        title: '确认删除',
        content: `是否确认删除配置项「${node.codeName}」？${hasChildren ? '该节点包含子节点，删除后所有子节点将一并删除。' : ''}删除后将无法恢复。`,
        okText: '确认',
        cancelText: '取消',
        okType: 'danger'
      })
      if (confirm) {
        const param = requestBuilder('delete', { codeSysId: node.codeSysId }, 0, 0)
        modifyCodeBasCode(param).then(res => {
          if (res.code === '0000') {
            this.$message.success('删除成功')
            this.loadConfigTree(this.currentCategory.codeClassId)
          } else {
            this.$message.error(res.message || '删除失败')
          }
        })
      }
    },

    async handleConfigModalOk () {
      try {
        await this.$refs.configForm.validate()
        const actionFlag = this.configModal.type === 'add' ? 'insert' : 'update'
        const param = requestBuilder(actionFlag, this.configForm, 0, 0)

        modifyCodeBasCode(param).then(res => {
          if (res.code === '0000') {
            this.$message.success(this.configModal.type === 'add' ? '新增成功' : '更新成功')
            this.configModal.visible = false
            this.loadConfigTree(this.currentCategory.codeClassId)
          } else {
            this.$message.error(res.message || '操作失败')
          }
        })
      } catch (error) {
        console.error('表单验证失败:', error)
      }
    },

    // 树操作辅助方法
    loadConfigTree (codeClassId) {
      if (!codeClassId) {
        this.configTreeData = []
        return
      }

      const param = requestBuilder('', { codeClassId }, 1, 1000)
      queryCodeBasCode(param).then(res => {
        if (res.code === '0000') {
          const data = res.result.data || []
          this.configTreeData = this.buildTree(data)
        }
      })
    },

    // 构建树形结构
    buildTree (data) {
      const map = {}
      const roots = []

      // 创建映射
      data.forEach(item => {
        map[item.codeId] = { ...item, children: [] }
      })

      // 构建树形结构
      data.forEach(item => {
        if (item.parentId && item.parentId !== '0' && map[item.parentId]) {
          map[item.parentId].children.push(map[item.codeId])
        } else {
          roots.push(map[item.codeId])
        }
      })

      return roots
    },

    // 获取父节点列表
    getParentIdList (codeClassId) {
      baseApi.getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: codeClassId } }).then(res => {
        if (res.code === '0000') {
          this.parentIdList = res.result || []
          this.parentIdList.push({ value: '0', label: '无父节点' })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.content-wrapper {
  display: flex;
  margin-top: 16px;
  min-height: calc(100vh - 300px);
  background: #fff;

  .left-panel {
    width: 37.5%;
    border-right: 1px solid #f0f0f0;
    padding: 16px;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h3 {
        margin: 0;
        font-size: 16px;
      }
    }

    .category-list {
      .ant-list-item {
        cursor: pointer;
        padding: 12px;
        border-radius: 4px;
        transition: all 0.3s;

        &:hover {
          background: #f5f5f5;
        }

        &.active {
          background: #e6f7ff;
        }

        .ant-list-item-meta-title {
          margin-bottom: 4px;
        }

        .ant-list-item-meta-description {
          color: #999;
        }
      }
    }
  }

  .right-panel {
    flex: 1;
    padding: 16px;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h3 {
        margin: 0;
        font-size: 16px;
      }
    }

    .tree-node-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        background-color: #f5f5f5;
      }

      .node-info {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
        margin-right: 8px;

        .node-label {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.85);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .node-code {
          margin-left: 8px;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.45);
          white-space: nowrap;
        }
      }

      .node-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        white-space: nowrap;

        .ant-btn {
          padding: 0 4px;
          height: 24px;
          line-height: 24px;
          background: transparent;
          border: none;

          &:hover {
            color: #1890ff;
          }
        }
      }
    }
  }
}

.table-page-search-wrapper {
  margin-bottom: 16px;
}
</style>
