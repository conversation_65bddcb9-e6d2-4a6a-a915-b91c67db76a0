<template>
  <div class="statistic-workorder-container">
    <!-- 筛选条件 -->
    <a-card :bordered="false" class="filter-card" :bodyStyle="{ padding: '12px' }">
      <a-form layout="inline">
        <a-row :gutter="16">
          <a-col :span="10">
            <a-form-item label="时间范围" style="margin-bottom: 0; width: 100%">
              <a-range-picker
                v-model="queryParam.dateRange"
                :ranges="{
                  今天: [moment(), moment()],
                  本周: [moment().startOf('week'), moment().endOf('week')],
                  本月: [moment().startOf('month'), moment().endOf('month')],
                  本季度: [moment().startOf('quarter'), moment().endOf('quarter')]
                }"
                style="width: 100%"
                @change="handleDateChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item label="部门" style="margin-bottom: 0; width: 100%">
              <a-select
                v-model="queryParam.departments"
                mode="multiple"
                style="width: 100%"
                placeholder="请选择部门"
                @change="handleDepartmentChange"
              >
                <a-select-option value="系统运维部">系统运维部</a-select-option>
                <a-select-option value="软件部">软件部</a-select-option>
                <a-select-option value="数据部">数据部</a-select-option>
                <a-select-option value="合作单位">合作单位</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="4" style="text-align: right">
            <a-form-item style="margin-bottom: 0">
              <a-button type="primary" @click="handleSearch">查询</a-button>
              <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <chart-card
            class="card-item change-card"
            title="变更工单"
            :total="statistics.change"
            :loading="loading"
            :bodyStyle="{ padding: '12px' }"
          >
            <template slot="avatar">
              <a-icon type="sync" />
            </template>
            <a-tooltip slot="action" title="变更工单总数">
              <a-icon type="info-circle-o" />
            </a-tooltip>
            <div class="card-content">
              <div class="data-item">
                <span>同比</span>
                <trend :flag="statistics.changeYearTrend > 0 ? 'up' : 'down'">
                  {{ Math.abs(statistics.changeYearTrend) }}%
                </trend>
              </div>
              <div class="data-item">
                <span>环比</span>
                <trend :flag="statistics.changeTrend > 0 ? 'up' : 'down'">
                  {{ Math.abs(statistics.changeTrend) }}%
                </trend>
              </div>
            </div>
          </chart-card>
        </a-col>
        <a-col :span="6">
          <chart-card
            class="card-item event-card"
            title="事件工单"
            :total="statistics.event"
            :loading="loading"
            :bodyStyle="{ padding: '12px' }"
          >
            <template slot="avatar">
              <a-icon type="alert" />
            </template>
            <a-tooltip slot="action" title="事件工单总数">
              <a-icon type="info-circle-o" />
            </a-tooltip>
            <div class="card-content">
              <div class="data-item">
                <span>同比</span>
                <trend :flag="statistics.eventYearTrend > 0 ? 'up' : 'down'">
                  {{ Math.abs(statistics.eventYearTrend) }}%
                </trend>
              </div>
              <div class="data-item">
                <span>环比</span>
                <trend :flag="statistics.eventTrend > 0 ? 'up' : 'down'">
                  {{ Math.abs(statistics.eventTrend) }}%
                </trend>
              </div>
            </div>
          </chart-card>
        </a-col>
        <a-col :span="6">
          <chart-card
            class="card-item problem-card"
            title="问题工单"
            :total="statistics.problem"
            :loading="loading"
            :bodyStyle="{ padding: '12px' }"
          >
            <template slot="avatar">
              <a-icon type="exception" />
            </template>
            <a-tooltip slot="action" title="问题工单总数">
              <a-icon type="info-circle-o" />
            </a-tooltip>
            <div class="card-content">
              <div class="data-item">
                <span>同比</span>
                <trend :flag="statistics.problemYearTrend > 0 ? 'up' : 'down'">
                  {{ Math.abs(statistics.problemYearTrend) }}%
                </trend>
              </div>
              <div class="data-item">
                <span>环比</span>
                <trend :flag="statistics.problemTrend > 0 ? 'up' : 'down'">
                  {{ Math.abs(statistics.problemTrend) }}%
                </trend>
              </div>
            </div>
          </chart-card>
        </a-col>
        <a-col :span="6">
          <chart-card
            class="card-item other-card"
            title="其他工单"
            :total="statistics.other"
            :loading="loading"
            :bodyStyle="{ padding: '12px' }"
          >
            <template slot="avatar">
              <a-icon type="file-text" />
            </template>
            <a-tooltip slot="action" title="其他工单总数">
              <a-icon type="info-circle-o" />
            </a-tooltip>
            <div class="card-content">
              <div class="data-item">
                <span>同比</span>
                <trend :flag="statistics.otherYearTrend > 0 ? 'up' : 'down'">
                  {{ Math.abs(statistics.otherYearTrend) }}%
                </trend>
              </div>
              <div class="data-item">
                <span>环比</span>
                <trend :flag="statistics.otherTrend > 0 ? 'up' : 'down'">
                  {{ Math.abs(statistics.otherTrend) }}%
                </trend>
              </div>
            </div>
          </chart-card>
        </a-col>
      </a-row>
    </div>

    <!-- 图表区域 -->
    <a-row class="chart-row">
      <a-col :span="24">
        <a-card :bordered="false" title="工单趋势分析" :bodyStyle="{ padding: '12px' }">
          <div class="chart-header">
            <a-radio-group v-model="chartDimension" @change="handleDimensionChange">
              <a-radio-button value="department">按部门</a-radio-button>
              <a-radio-button value="type">按类型</a-radio-button>
              <a-radio-button value="priority">按优先级</a-radio-button>
            </a-radio-group>
          </div>
          <div ref="lineChart" style="height: 300px"/>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16" class="chart-row">
      <a-col :span="8">
        <a-card :bordered="false" title="工单类型分布" :bodyStyle="{ padding: '12px' }">
          <div ref="pieChart" style="height: 250px; overflow: visible"/>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card :bordered="false" title="部门工单处理情况" :bodyStyle="{ padding: '12px' }">
          <div ref="barChart" style="height: 250px"/>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card :bordered="false" title="工程师工作效率分析" :bodyStyle="{ padding: '12px' }">
          <div style="height: 250px; overflow: auto">
            <a-table :columns="engineerColumns" :dataSource="engineerData" :pagination="false" size="small">
              <template slot="department" slot-scope="text">
                <a-tag>{{ text }}</a-tag>
              </template>
              <template slot="efficiency" slot-scope="text">
                <a-progress :percent="text" size="small" />
              </template>
            </a-table>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<style lang="less" scoped>
// .statistics-cards {
//   margin-top: 16px;

//   .card-item {
//     border-radius: 4px;
//     transition: all 0.3s;
//     box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);

//     &:hover {
//       box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
//       transform: translateY(-2px);
//     }

//     .card-content {
//       display: flex;
//       justify-content: space-between;
//       align-items: center;
//       margin-top: 8px;

//       .data-item {
//         display: flex;
//         align-items: center;
//         gap: 8px;
//       }
//     }

//     &.change-card {
//       background: linear-gradient(135deg, #1890ff11 0%, #1890ff22 100%);
//       .ant-card-head-title { color: #1890ff; }
//     }

//     &.event-card {
//       background: linear-gradient(135deg, #722ed111 0%, #722ed122 100%);
//       .ant-card-head-title { color: #722ed1; }
//     }

//     &.problem-card {
//       background: linear-gradient(135deg, #fa541c11 0%, #fa541c22 100%);
//       .ant-card-head-title { color: #fa541c; }
//     }

//     &.other-card {
//       background: linear-gradient(135deg, #52c41a11 0%, #52c41a22 100%);
//       .ant-card-head-title { color: #52c41a; }
//     }
//   }
// }
 .statistics-cards {
    margin-bottom: 12px;

    .card-item {
      border-radius: 8px;
      overflow: hidden;
      transition: all 0.3s;
      position: relative;
      padding: 20px;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      :deep(.ant-card-head) {
        border-bottom: none;
        padding: 0;
        min-height: auto;

        .ant-card-head-title {
          padding: 0;
          font-size: 16px;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.95);
        }
      }

      :deep(.ant-card-body) {
        padding: 12px 0 0;
      }

      :deep(.number) {
        font-size: 28px;
        font-weight: bold;
        color: rgba(255, 255, 255, 0.95);
        margin: 8px 0;
      }

      :deep(.ant-card-action) {
        color: rgba(255, 255, 255, 0.85);
        position: absolute;
        top: 20px;
        right: 20px;
      }

      .card-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 16px;

        .data-item {
          display: flex;
          align-items: center;

          span {
            margin-right: 8px;
            color: rgba(255, 255, 255, 0.85);
          }
        }
      }

      .anticon {
        font-size: 24px;
        color: rgba(255, 255, 255, 0.85);
      }
    }

    .change-card {
      background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);
    }

    .event-card {
      background: linear-gradient(135deg, #722ed1 0%, #eb2f96 100%);
    }

    .problem-card {
      background: linear-gradient(135deg, #fa541c 0%, #faad14 100%);
    }

    .other-card {
      background: linear-gradient(135deg, #52c41a 0%, #13c2c2 100%);
    }
  }

.statistic-workorder-container {
  padding: 12px;

  .chart-header {
    margin-bottom: 16px;

    .ant-radio-button-wrapper {
      &.ant-radio-button-wrapper-checked {
        background-color: #1890ff;
        border-color: #1890ff;
        color: #fff;
        box-shadow: -1px 0 0 0 #1890ff;

        &:hover {
          color: #fff;
          background-color: #40a9ff;
          border-color: #40a9ff;
        }

        &:before {
          background-color: #1890ff;
        }
      }

      &:hover {
        color: #1890ff;
      }
    }
  }
}
</style>

<script>
import moment from 'moment'
import ChartCard from '@/components/Charts/ChartCard'
import Trend from '@/components/Trend'
import * as echarts from 'echarts'

export default {
  name: 'StatisticWorkorderManagement',
  components: {
    ChartCard,
    Trend
  },
  data () {
    return {
      moment,
      loading: false,
      chartDimension: 'department',
      queryParam: {
        dateRange: [moment().startOf('month'), moment().endOf('month')],
        departments: []
      },
      statistics: {
        change: 156,
        changeYearTrend: 5.2,
        changeTrend: -2.3,
        event: 298,
        eventYearTrend: -3.1,
        eventTrend: 4.5,
        problem: 85,
        problemYearTrend: 2.8,
        problemTrend: 8.7,
        other: 45,
        otherYearTrend: -1.5,
        otherTrend: 3.1
      },
      charts: {
        pie: null,
        bar: null,
        line: null,
        engineer: null
      },
      engineerColumns: [
        {
          title: '工程师',
          dataIndex: 'name',
          width: '20%'
        },
        {
          title: '所属部门',
          dataIndex: 'department',
          width: '20%',
          scopedSlots: { customRender: 'department' }
        },
        {
          title: '处理工单数',
          dataIndex: 'count',
          width: '20%',
          sorter: (a, b) => a.count - b.count
        },
        {
          title: '平均处理时长',
          dataIndex: 'avgTime',
          width: '20%'
        },
        {
          title: '工作效率',
          dataIndex: 'efficiency',
          scopedSlots: { customRender: 'efficiency' }
        }
      ],
      engineerData: [
        {
          key: '1',
          name: '张工程师',
          department: '系统运维部',
          count: 156,
          avgTime: '2.5小时',
          efficiency: 95
        },
        {
          key: '2',
          name: '李工程师',
          department: '软件部',
          count: 142,
          avgTime: '3.1小时',
          efficiency: 88
        },
        {
          key: '3',
          name: '王工程师',
          department: '数据部',
          count: 138,
          avgTime: '2.8小时',
          efficiency: 92
        }
      ]
    }
  },
  mounted () {
    this.initCharts()
    this.loadData()
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.resizeCharts)
  },
  methods: {
    handleDateChange (dates) {
      this.queryParam.dateRange = dates
      this.loadData()
    },
    handleDepartmentChange (value) {
      this.queryParam.departments = value
      this.loadData()
    },
    handleSearch () {
      this.loadData()
    },
    handleReset () {
      this.queryParam = {
        dateRange: [moment().startOf('month'), moment().endOf('month')],
        departments: []
      }
      this.loadData()
    },
    initCharts () {
      // 初始化饼图
      if (this.$refs.pieChart) {
        this.charts.pie = echarts.init(this.$refs.pieChart)
        this.charts.pie.setOption({
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            right: 10,
            top: 'center'
          },
          series: [
            {
              name: '工单类型',
              type: 'pie',
              radius: ['50%', '70%'],
              avoidLabelOverlap: true,
              label: {
                show: true,
                position: 'outside',
                formatter: '{b}: {c} ({d}%)'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '16',
                  fontWeight: 'bold'
                }
              },
              data: [
                {
                  value: 156,
                  name: '变更工单',
                  itemStyle: { color: '#1890ff' }
                },
                {
                  value: 298,
                  name: '事件工单',
                  itemStyle: { color: '#722ed1' }
                },
                {
                  value: 85,
                  name: '问题工单',
                  itemStyle: { color: '#fa541c' }
                },
                {
                  value: 45,
                  name: '其他工单',
                  itemStyle: { color: '#52c41a' }
                }
              ]
            }
          ]
        })
      }

      // 初始化柱状图
      if (this.$refs.barChart) {
        this.charts.bar = echarts.init(this.$refs.barChart)
        this.charts.bar.setOption({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              data: ['系统运维部', '软件部', '数据部', '合作单位'],
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis: [
            {
              type: 'value'
            }
          ],
          series: [
            {
              name: '工单数量',
              type: 'bar',
              barWidth: '60%',
              data: [156, 142, 138, 98]
            }
          ]
        })
      }

      // 初始化折线图
      if (this.$refs.lineChart) {
        this.charts.line = echarts.init(this.$refs.lineChart)
        this.charts.line.setOption({
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['变更工单', '事件工单', '问题工单', '其他工单']
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '变更工单',
              type: 'line',
              data: [20, 25, 30, 35, 28, 32, 38, 42, 35, 30, 25, 20]
            },
            {
              name: '事件工单',
              type: 'line',
              data: [45, 48, 52, 58, 50, 45, 55, 60, 52, 48, 42, 45]
            },
            {
              name: '问题工单',
              type: 'line',
              data: [12, 15, 18, 22, 16, 20, 25, 28, 24, 20, 15, 12]
            },
            {
              name: '其他工单',
              type: 'line',
              data: [8, 10, 12, 15, 11, 13, 16, 18, 14, 12, 9, 8]
            }
          ]
        })
      }

      // 添加窗口resize事件监听
      window.addEventListener('resize', this.resizeCharts)
    },
    resizeCharts () {
      Object.values(this.charts).forEach(chart => {
        chart && chart.resize()
      })
    },
    loadData () {
      // 这里添加实际的数据加载逻辑
      this.loading = true
      setTimeout(() => {
        this.loading = false
      }, 1000)
    },
    handleDimensionChange (e) {
      const dimension = e.target.value
      this.chartDimension = dimension

      // 根据不同维度更新图表数据
      let data = []
      switch (dimension) {
        case 'department':
          data = [
            { name: '系统运维部', value: [120, 132, 101, 134, 90, 230, 210] },
            { name: '软件部', value: [220, 182, 191, 234, 290, 330, 310] },
            { name: '数据部', value: [150, 232, 201, 154, 190, 330, 410] },
            { name: '合作单位', value: [320, 332, 301, 334, 390, 330, 320] }
          ]
          break
        case 'type':
          data = [
            { name: '变更工单', value: [320, 332, 301, 334, 390, 330, 320] },
            { name: '事件工单', value: [220, 182, 191, 234, 290, 330, 310] },
            { name: '问题工单', value: [150, 232, 201, 154, 190, 330, 410] },
            { name: '其他工单', value: [120, 132, 101, 134, 90, 230, 210] }
          ]
          break
        case 'priority':
          data = [
            { name: '高优先级', value: [220, 182, 191, 234, 290, 330, 310] },
            { name: '中优先级', value: [150, 232, 201, 154, 190, 330, 410] },
            { name: '低优先级', value: [120, 132, 101, 134, 90, 230, 210] }
          ]
          break
      }

      // 更新图表配置
      this.charts.line.setOption({
        legend: {
          data: data.map(item => item.name)
        },
        series: data.map(item => ({
          name: item.name,
          type: 'line',
          data: item.value,
          smooth: true
        }))
      })
    }
  }
}
</script>
