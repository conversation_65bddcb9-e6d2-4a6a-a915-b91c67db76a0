<template>
  <section class="flex-section">
    <a-card :bordered="false">
      <!-- 统计卡片区域 -->
      <div class="queryParam-cards">
        <a-row :gutter="24">
          <a-col :span="6">
            <div
              @click="handleCardClick('change')"
            >
              <chart-card
                class="card-item change-card"
                title="变更工单"
                :total="statistics.change"
                :loading="loading"
                :bodyStyle="{ padding: '12px' }"
              >
                <template slot="avatar">
                  <a-icon type="sync" />
                </template>
                <a-tooltip slot="action" title="变更工单总数">
                  <a-icon type="info-circle-o" />
                </a-tooltip>
                <div class="card-content">
                  <div class="data-item">
                    <span>同比</span>
                    <trend :flag="statistics.changeYearTrend > 0 ? 'up' : 'down'">
                      {{ Math.abs(statistics.changeYearTrend) }}%
                    </trend>
                  </div>
                  <div class="data-item">
                    <span>环比</span>
                    <trend :flag="statistics.changeTrend > 0 ? 'up' : 'down'">
                      {{ Math.abs(statistics.changeTrend) }}%
                    </trend>
                  </div>
                </div>
              </chart-card>
            </div>

          </a-col>
          <a-col :span="6">
            <div @click="handleCardClick('event')">
              <chart-card
                class="card-item event-card"
                title="事件工单"
                :total="statistics.event"
                :loading="loading"
                :bodyStyle="{ padding: '12px' }"
                @click="handleCardClick('event')"
              >
                <template slot="avatar">
                  <a-icon type="alert" />
                </template>
                <a-tooltip slot="action" title="事件工单总数">
                  <a-icon type="info-circle-o" />
                </a-tooltip>
                <div class="card-content">
                  <div class="data-item">
                    <span>同比</span>
                    <trend :flag="statistics.eventYearTrend > 0 ? 'up' : 'down'">
                      {{ Math.abs(statistics.eventYearTrend) }}%
                    </trend>
                  </div>
                  <div class="data-item">
                    <span>环比</span>
                    <trend :flag="statistics.eventTrend > 0 ? 'up' : 'down'">
                      {{ Math.abs(statistics.eventTrend) }}%
                    </trend>
                  </div>
                </div>
              </chart-card>
            </div>
          </a-col>
          <a-col :span="6">
            <chart-card
              class="card-item problem-card"
              title="问题工单"
              :total="statistics.problem"
              :loading="loading"
              :bodyStyle="{ padding: '12px' }"
              @click="handleCardClick('problem')"
            >
              <template slot="avatar">
                <a-icon type="exception" />
              </template>
              <a-tooltip slot="action" title="问题工单总数">
                <a-icon type="info-circle-o" />
              </a-tooltip>
              <div class="card-content">
                <div class="data-item">
                  <span>同比</span>
                  <trend :flag="statistics.problemYearTrend > 0 ? 'up' : 'down'">
                    {{ Math.abs(statistics.problemYearTrend) }}%
                  </trend>
                </div>
                <div class="data-item">
                  <span>环比</span>
                  <trend :flag="statistics.problemTrend > 0 ? 'up' : 'down'">
                    {{ Math.abs(statistics.problemTrend) }}%
                  </trend>
                </div>
              </div>
            </chart-card>
          </a-col>
          <a-col :span="6">
            <chart-card
              class="card-item other-card"
              title="其他工单"
              :total="statistics.other"
              :loading="loading"
              :bodyStyle="{ padding: '12px' }"
              @click="handleCardClick('other')"
            >
              <template slot="avatar">
                <a-icon type="file-text" />
              </template>
              <a-tooltip slot="action" title="其他工单总数">
                <a-icon type="info-circle-o" />
              </a-tooltip>
              <div class="card-content">
                <div class="data-item">
                  <span>同比</span>
                  <trend :flag="statistics.otherYearTrend > 0 ? 'up' : 'down'">
                    {{ Math.abs(statistics.otherYearTrend) }}%
                  </trend>
                </div>
                <div class="data-item">
                  <span>环比</span>
                  <trend :flag="statistics.otherTrend > 0 ? 'up' : 'down'">
                    {{ Math.abs(statistics.otherTrend) }}%
                  </trend>
                </div>
              </div>
            </chart-card>
          </a-col>
        </a-row>
      </div>

      <!-- 搜索区域 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="工单号">
                <a-input
                  v-model="queryParam.workorderNo"
                  placeholder="请输入工单号"
                  allowClear
                  @pressEnter="handleSearch"
                />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="日期">
                <a-range-picker
                  v-model="queryParam.dateRange"
                  style="width: 100%"
                  @change="handleSearch"
                />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="处理状态">
                <a-select v-model="queryParam.status" placeholder="请选择处理状态" allowClear>
                  <a-select-option value="pending">待处理</a-select-option>
                  <a-select-option value="processing">处理中</a-select-option>
                  <a-select-option value="completed">已完成</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
                <a-button type="primary" style="margin-left: 8px" @click="audit">审批</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <s-table
        ref="table"
        :rowKey="(record) => record.id"
        :columns="columns"
        :data="loadData"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <span
          slot="status"
          slot-scope="key"
        >{{ takeSelectLabel(statusList, key) }}
        </span>
        <div
          slot="action"
          slot-scope="text, record"
        >
          <template>
            <a
              style="margin: 3px;"
              @click.stop="showModal(record)"
            >处理</a>
          </template>
        </div>
      </s-table>
      <approve-drawer
        ref="approveDrawer"
        keyName="uuid"
        origin="EventWorkOrder"
        :dataSource="dataSource"
        :selectedRows="selectedRows"
        :selectChanged="selectChanged"
        :dataChanged="dataChanged"
        :getApproveId="getApproveId"
        :editBusinessMode="editBusinessMode"
        :editApproveMode="editApproveMode"
        :showNeedDevice="showNeedDevice"
        :showIsWX="showIsWX"
        @toApprove="toApprove"
      />
    </a-card>
    <!-- 使用 EventFormModal 组件 -->
    <EventFormModal
      ref="EventFormModal"
    />
    <ChangeFormModal
      ref="ChangeFormModal"
      :database-list="databaseList"
    />
  </section>
</template>

<script>
import { ChartCard, STable } from '@/components'
import Trend from '@/components/Charts/Trend'
import { mixinDevice } from '@/utils/mixin'
import ApproveDrawer from '@/views/system/components/ApproveDrawer.vue'
import {
  APPROVE_NODES_BUS_FREEZE,
  APPROVE_NODES_BUS_INIT,
  APPROVE_OPTIONS_HIDES,
  APPROVE_STATUS_CONFIRM_CODE,
  APPROVE_STATUS_FLOWING_CODE,
  APPROVE_STATUS_READY_CODE,
  APPROVE_STATUS_START_CODE
} from '@/store/variable/approve'
import Vue from 'vue'
import { DEPT_ID, OPERATOR, ORG_ID, PERSON_ID } from '@/store/mutation-types'
import { ITSMapproval, ITSMEventapproval, queryById, queryDatabase, queryTodo, queryTodoCount } from '@/api/device/itsm'
import { requestBuilder, takeTreeByKey } from '@/utils/util'
import moment from 'moment'
import * as baseApi from '@/api/system/base'
import EventFormModal from '@/views/itsm/eventWorkorder/modules/EventFormModal.vue'
import ChangeFormModal from '@/views/itsm/changeWorkorder/modules/ChangeFormModal.vue'

const USER_ORG_ID = Vue.ls.get(ORG_ID)
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)
const USER_OPERATOR = Vue.ls.get(OPERATOR)
const USER_DEPT_ID = Vue.ls.get(DEPT_ID)
const APPROVE_OPTIONS_MORE_HIDES = [APPROVE_STATUS_READY_CODE, APPROVE_STATUS_FLOWING_CODE, APPROVE_STATUS_CONFIRM_CODE]
const typeMap = {
  'change': '变更工单',
  'event': '事件工单',
  'issue': '问题工单'
}
export default {
  name: 'TodoWorkorderManagement',
  components: {
    ChangeFormModal,
    EventFormModal,
    STable,
    ApproveDrawer,
    ChartCard,
    Trend
  },
  mixins: [mixinDevice],
  data () {
    return {
      USER_ORG_ID,
      USER_PERSON_ID,
      USER_OPERATOR,
      USER_DEPT_ID,
      typeMap,
      loading: false,
      queryParam: {
        workorderNo: null,
        dateRange: null,
        type: 'change'
      },
      statistics: {
        change: 0,
        changeYearTrend: 12,
        changeTrend: -5,
        event: 0,
        eventYearTrend: 8,
        eventTrend: 15,
        problem: 0,
        problemYearTrend: -3,
        problemTrend: 7,
        other: 0,
        otherYearTrend: 5,
        otherTrend: -2
      },
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      columns: [
        {
          title: '工单号',
          dataIndex: 'workOrderNum'
        },
        {
          title: '工单类型',
          dataIndex: 'workorderType',
          customRender: (text, row, index) => {
            return this.typeMap[text]
          }
        },
        {
          title: '工单描述',
          dataIndex: 'description'
        },
        {
          title: '创建时间',
          dataIndex: 'createDate',
          sorter: true,
          customRender: (text, row, index) => {
            return moment(text).format('YYYY-MM-DD HH:mm:ss')
          }
        },
        {
          title: '状态',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '操作',
          scopedSlots: { customRender: 'action' },
          fixed: 'right',
          width: 120
        }
      ],
      dataSource: [
        {
          id: 1,
          workorderNo: 'WO20230601001',
          type: '变更工单',
          description: '系统升级变更申请',
          createTime: '2023-06-01 10:00:00',
          status: '待处理'
        },
        {
          id: 2,
          workorderNo: 'WO20230601002',
          type: '事件工单',
          description: '服务器异常报警',
          createTime: '2023-06-01 11:30:00',
          status: '处理中'
        },
        {
          id: 3,
          workorderNo: 'WO20230601003',
          type: '变更工单',
          description: '数据库配置变更',
          createTime: '2023-06-01 14:20:00',
          status: '待处理'
        },
        {
          id: 4,
          workorderNo: 'WO20230601004',
          type: '变更工单',
          description: '网络架构优化',
          createTime: '2023-06-01 15:45:00',
          status: '处理中'
        },
        {
          id: 5,
          workorderNo: 'WO20230601005',
          type: '变更工单',
          description: '安全策略更新',
          createTime: '2023-06-01 16:30:00',
          status: '已完成'
        }
      ],
      loadData: parameter => {
        // console.log('loadData.parameter', parameter)
        // const param = { ...this.queryParam }
        // console.log('param', param)
        // if (param.applicationDate) {
        //   param.applicationDate =
        // }
        console.log(this.queryParam)
        const params = this.queryParam
        const queryParam = {
          id: params.id,
          applyDate: params.applyDate ? moment(params.applyDate).format('YYYY-MM-DD') : null,
          status: params.status,
          applicant: params.applicant,
          changeDescription: params.changeDescription,
          changeCategory: params.changeCategory,
          changeType: params.changeType,
          changeClass: params.changeClass
        }
        const param = requestBuilder(
          this.queryParam.type,
          queryParam,
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder)
        return queryTodo(param).then(res => {
          console.log(res)
          this.dataSource = res.result.data
          return res.result
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      showNeedDevice: false,
      showIsWX: false,
      status: [],
      statusDetail: [],
      databaseList: [],
      personList: [],
      statusList: [],
      showModalDetail: false
    }
  },
  filters: {
    statusFilter (status) {
      const statusMap = {
        '待处理': '待处理',
        '处理中': '处理中',
        '已完成': '已完成',
        '已关闭': '已关闭'
      }
      return statusMap[status]
    },
    statusTypeFilter (status) {
      const statusMap = {
        '待处理': 'warning',
        '处理中': 'processing',
        '已完成': 'success',
        '已关闭': 'default'
      }
      return statusMap[status]
    }
  },
  methods: {
    initOptions () {
      baseApi.getCommboxById({ id: 'applyPerson' }).then(res => {
        if (res.code === '0000') {
          this.personList = res.result
        }
      })
      // 审批状态
      baseApi.getTreeById({ id: 'appro' }).then(res => {
        if (res.code === '0000') {
          const list = []
          for (const item of res.result) {
            if (APPROVE_OPTIONS_HIDES.includes(item.value)) {
              item.disabled = true
            }
            list.push(item)
          }
          this.statusList = list
          console.log('status list', this.statusList)
        }
      })
      baseApi.getCommboxById({ id: 'appro' }).then(res => {
        this.statusDetail = res.result
        console.log('this.statusDetail', this.statusDetail)
      })
      // 审批状态
      baseApi.getTreeById({ id: 'appro' }).then(res => {
        if (res.code === '0000') {
          for (const item of res.result) {
            if (APPROVE_OPTIONS_MORE_HIDES.includes(item.value)) {
              item.disabled = true
            }
            this.status.push(item)
          }
        }
      })
      // 获取数据库列表
      queryDatabase(requestBuilder()).then(res => {
        console.log(res)
        if (res.code === '0000') {
          this.databaseList = res.result.map(item => {
            return {
              label: item.name,
              value: String(item.id)
            }
          })
        }
      })
    },
    handleSearch () {
      this.pagination.current = 1
    },
    handleTableChange (pagination, filters, sorter) {
      this.pagination = pagination
    },
    handleView (record) {
      // 查看工单详情
      console.log('查看工单:', record)
    },
    handleProcess (record) {
      // 处理工单
      console.log('处理工单:', record)
    },
    handleCardClick (type) {
      console.log('type', type)
      this.queryParam.type = type
      this.$refs.table.refresh(true)
    },
    audit () {
      this.showIsWX = this.selectedRows.some(item => {
        return item.status === 'approval1270'
      })
      this.showNeedDevice = this.selectedRows.some(item => {
        return item.status === 'approval1268'
      })
      console.log('this.showNeedDevice', this.showNeedDevice)
      // if (this.queryParam.type === 'event') {
      //   if (this.doCanApproval()) {
      //     this.$refs.approveDrawer.doOpen()
      //   } else {
      //     this.$message.error('请完善信息')
      //   }
      // } else {
      //   this.$refs.approveDrawer.doOpen()
      // }
      this.$refs.approveDrawer.doOpen()
    },
    onChange1 () {
      console.log('change')
      console.log(this.queryParam.changeClass)
    },
    // 审批
    toApprove (info) {
      console.log('审批信息', info)
      const type = this.queryParam.type
      if (type === 'event') {
        this.EventApproval(info)
      }
      if (type === 'change') {
        this.changeApproval(info)
      }
    },
    selectChanged () {
      this.$refs.table.triggerSelect(...arguments)
    },
    showModal (record) {
      console.log(record)
      switch (record.workorderType) {
        case 'change':
          queryById(requestBuilder(this.queryParam.type, { 'uuid': record.uuid })).then(res => {
            if (res.code === '0000') {
              this.$refs.ChangeFormModal.showModal('view', res.result)
            } else {
              this.$message.error('获取数据失败')
            }
          })
          break
        case 'event':
          queryById(requestBuilder(this.queryParam.type, { 'uuid': record.uuid })).then(res => {
            if (res.code === '0000') {
              this.$refs.EventFormModal.showModal('edit', res.result)
            } else {
              this.$message.error('获取数据失败')
            }
          })
          break
        case 'issue':
          console.log('问题工地那')
          break
        default:
          console.log('未知事件类型')
      }
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      console.log(this.selectedRows)
    },
    dataChanged () {
      this.doSearch(true)
    },
    getApproveId (status) {
      for (const item of this.status) {
        if (status === item.value) {
          return item.value
        }
        if (item.children) {
          for (const item2 of item.children) {
            if (status === item2.value) {
              return item.value
            }
          }
        }
      }
    },
    editBusinessMode (record) {
      if (APPROVE_STATUS_READY_CODE === this.getApproveId(record.status)) {
        return true
      }
      if (
        !!record.userNext &&
        record.userNext.split(',').includes(USER_OPERATOR) &&
        APPROVE_STATUS_START_CODE === this.getApproveId(record.status)
      ) {
        return true
      }
      return false
    },
    editApproveMode (record) {
      if (
        !!record.userNext &&
        record.userNext.split(',').includes(USER_OPERATOR) &&
        !APPROVE_NODES_BUS_FREEZE.includes(this.getApproveId(record.status)) &&
        !APPROVE_NODES_BUS_INIT.includes(this.getApproveId(record.status))
      ) {
        return true
      }
      return false
    },
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || key
    },
    doCanApproval () {

    },
    EventApproval (info) {
      ITSMEventapproval(requestBuilder('', info)).then(res => {
        if (res.code !== '0000') {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '审批失败！'
          })
          return Promise.reject(res)
        }
        this.$notification.success({
          message: '系统消息',
          description: res.message || '审批成功！'
        })
        this.$refs.approveDrawer.visible = false
        this.$refs.table.refresh(true)
      })
    },
    changeApproval (info) {
      console.log('审批信息', info)
      ITSMapproval(requestBuilder('', info)).then(res => {
        if (res.code !== '0000') {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '审批失败！'
          })
          return Promise.reject(res)
        }
        this.$notification.success({
          message: '系统消息',
          description: res.message || '审批成功！'
        })
        this.$refs.approveDrawer.visible = false
        this.$refs.table.refresh(true)
      })
    }

  },
  created () {
    this.initOptions()
  },
  mounted () {
    queryTodoCount(requestBuilder('', {})).then(res => {
      console.log(res)
      if (res.code === '0000') {
        res.result.forEach(item => {
          if (this.statistics.hasOwnProperty(item.name)) {
            this.statistics[item.name] = item.total
          }
        })
      }
    })
  }
}
</script>

<style lang="less" scoped>
.queryParam-cards {
  margin-bottom: 24px;

  .card-item {
    .ant-card-body {
      padding: 20px 24px 8px;
    }

    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &.change-card {
      background: linear-gradient(135deg, #36cfc9 0%, #1890ff 100%);
    }

    &.event-card {
      background: linear-gradient(135deg, #ffa940 0%, #fa8c16 100%);
    }

    &.problem-card {
      background: linear-gradient(135deg, #ff7875 0%, #f5222d 100%);
    }

    &.other-card {
      background: linear-gradient(135deg, #95de64 0%, #52c41a 100%);
    }

    :deep(.ant-card-meta-title) {
      color: rgba(255, 255, 255, 0.85);
    }

    :deep(.ant-card-meta-description) {
      color: rgba(255, 255, 255, 0.65);
    }

    .card-content {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;

      .data-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        span {
          color: rgba(255, 255, 255, 0.65);
          font-size: 14px;
          line-height: 22px;
          margin-bottom: 4px;
        }
      }
    }
  }
}

.table-page-search-wrapper {
  margin-bottom: 16px;

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }
}
</style>
