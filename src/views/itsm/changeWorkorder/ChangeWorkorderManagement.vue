<template>
  <a-card title="变更管理">
    <a-form layout="inline" :form="formData" class="search-form">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item label="工单号">
            <a-input
              v-model="queryParam.workOrderNum"
              placeholder="请输入工单号"
              allowClear
              @pressEnter="doSearch(true)"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="申报日期">
            <a-date-picker
              v-model="queryParam.applyDate"
              style="width: 100%"
              allowClear
              @change="doSearch(true)"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="流程状态">
            <a-select
              v-model="queryParam.status"
              placeholder="请选择流程状态"
              allowClear
              style="width: 100%"
              @change="doSearch(true)"
            >
              <a-select-option
                v-for="(item, index) in statusList"
                :value="item.value"
                :label="item.label"
                :key="index"
              >{{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="申请人">
            <a-input
              v-model="queryParam.applicant"
              placeholder="请输入申请人"
              allowClear
              @pressEnter="doSearch(true)"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item label="申请描述">
            <a-input
              v-model="queryParam.changeDescription"
              placeholder="请输入申请描述"
              allowClear
              @pressEnter="doSearch(true)"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="变更分类">
            <a-select
              v-model="queryParam.changeCategory"
              placeholder="请选择变更类型"
              allowClear
              style="width: 100%"
              @change="doSearch(true)"
            >
              <a-select-option
                v-for="(item, index) in changeCategorys"
                :value="item.value"
                :label="item.label"
                :key="index"
              >{{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="变更类型">
            <a-radio-group :options="filterList" :default-value="value1" @change="doSearch(true)" v-model="queryParam.changeClass"/>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item>
            <a-button type="primary" icon="search" @click="doSearch(true)">查询</a-button>
            <a-button
              type="primary"
              icon="plus"
              style="margin-left: 8px"
              @click="showModal('add')"
            >
              新增变更
            </a-button>
            <a-button type="primary" style="margin-left: 8px" @click="handleReset">
              重置条件
            </a-button>
            <a-button type="primary" style="margin-left: 8px" @click="audit">
              审批
            </a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <s-table
      ref="table"
      rowKey="uuid"
      :columns="columns"
      :data="loadData"
      bordered
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
    >
      <span
        slot="person"
        slot-scope="key"
      >{{ takeSelectLabel(personList, key) }}
      </span>
      <span
        slot="changeCategory"
        slot-scope="key"
      >
        <span :class="{'emergency-change': key === '3'}">
          <!-- {{ takeSelectLabel(changeCategorys, key) }} -->
          <a-tag v-if="key === '3'" color="red">{{ takeSelectLabel(changeCategorys, key) }}</a-tag>
          <a-tag v-else-if="key === '2'" color="orange">{{ takeSelectLabel(changeCategorys, key) }}</a-tag>
          <span v-else>
            {{ takeSelectLabel(changeCategorys, key) }}
          </span>
        </span>
      </span>
      <span
        slot="status"
        slot-scope="key"
      >{{ takeSelectLabel(statusList, key) }}
      </span>
      <div
        slot="action"
        slot-scope="text, record"
        class="action-buttons"
      >
        <a-tooltip title="查看">
          <a-button
            type="link"
            icon="eye"
            class="action-button"
            @click.stop="showModal('view',record)"
          />
        </a-tooltip>
        <a-tooltip title="编辑">
          <a-button
            type="link"
            icon="edit"
            class="action-button"
            @click.stop="showModal('edit',record)"
          />
        </a-tooltip>
        <a-tooltip title="删除">
          <a-popconfirm
            title="确认删除该条记录？"
            ok-text="删除"
            cancel-text="取消"
            @confirm="confirm(record)"
            @cancel="cancel"
            placement="topRight"
          >
            <a-button
              type="link"
              icon="delete"
              class="action-button danger"
            />
          </a-popconfirm>
        </a-tooltip>
      </div>
      <!-- 自定义帅选组件 -->
      <!--      <template slot="title">-->
      <!--        <div class="tableTitle">-->
      <!--          <a-popover v-model="popVisible" trigger="click">-->
      <!--            <template slot="content">-->
      <!--              <DropdownChecked-->
      <!--                :showTitle="false"-->
      <!--                title="自定义表格排序"-->
      <!--                tableId="personMaintainTable"-->
      <!--                v-model="columns"-->
      <!--              />-->
      <!--            </template>-->
      <!--            <span class="title-icons">-->
      <!--                <a-icon type="setting" theme="filled" />-->
      <!--              </span>-->
      <!--          </a-popover>-->
      <!--          <span class="title-icons" style="border: none">-->
      <!--              <a-tooltip>-->
      <!--                <template slot="title"> 刷新 </template>-->
      <!--                <a-icon type="sync" :spin="popLoading" @click="reColumns" />-->
      <!--              </a-tooltip>-->
      <!--            </span>-->
      <!--        </div>-->
      <!--      </template>-->
    </s-table>

    <!-- 使用 ChangeFormModal 组件 -->
    <ChangeFormModal
      ref="ChangeFormModal"
      @refresh="doSearch"
      :database-list="databaseList"
      :show-detail="showModalDetail"
    />
    <!--    <ApprovePage-->
    <!--      ref="approveDrawer"-->
    <!--      keyName="uuid"-->
    <!--      origin="ITSMWorkOrder"-->
    <!--      :dataSource="dataSource"-->
    <!--      :selectedRows="selectedRows"-->
    <!--      @toApprove="toApprove"-->
    <!--      :selectTag="selectTag"-->
    <!--    ></ApprovePage>-->
    <approve-drawer
      ref="approveDrawer"
      keyName="uuid"
      origin="ITSMWorkOrder"
      :dataSource="dataSource"
      :selectedRows="selectedRows"
      :selectChanged="selectChanged"
      :dataChanged="dataChanged"
      :getApproveId="getApproveId"
      :editBusinessMode="editBusinessMode"
      :editApproveMode="editApproveMode"
      :showNeedDevice="showNeedDevice"
      :showIsWX="showIsWX"
      @toApprove="toApprove"
    />
  </a-card>
</template>

<script>
import ChangeFormModal from './modules/ChangeFormModal.vue'
import * as baseApi from '@/api/system/base'
import { DropdownChecked, STable } from '@/components'
import moment from 'moment/moment'
import { deleteITSM, ITSMapproval, queryDatabase, queryITSMList } from '@/api/device/itsm'
import { requestBuilder, takeTreeByKey } from '@/utils/util'
import {
  APPROVE_NODES_BUS_FREEZE,
  APPROVE_NODES_BUS_INIT,
  APPROVE_OPTIONS_HIDES,
  APPROVE_STATUS_CONFIRM_CODE,
  APPROVE_STATUS_FLOWING_CODE,
  APPROVE_STATUS_READY_CODE,
  APPROVE_STATUS_START_CODE
} from '@/store/variable-types'
import Vue from 'vue'
import { DEPT_ID, OPERATOR, ORG_ID, PERSON_ID } from '@/store/mutation-types'
import ApprovePage from '@/views/itsm/changeWorkorder/modules/ApprovePage.vue'
import ApproveDrawer from '@/views/system/components/ApproveDrawer'

const USER_ORG_ID = Vue.ls.get(ORG_ID)
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)
const USER_OPERATOR = Vue.ls.get(OPERATOR)
const USER_DEPT_ID = Vue.ls.get(DEPT_ID)

const defaultQueryParam = {
  workOrderNum: undefined,
  applyDate: undefined,
  status: undefined,
  applicant: undefined,
  changeDescription: undefined,
  changeCategory: undefined,
  changeType: undefined,
  changeClass: '0'
}
const APPROVE_OPTIONS_MORE_HIDES = [APPROVE_STATUS_READY_CODE, APPROVE_STATUS_FLOWING_CODE, APPROVE_STATUS_CONFIRM_CODE]

export default {
  name: 'ChangeManagement',
  components: {
    ApprovePage,
    ApproveDrawer,
    STable,
    DropdownChecked,
    ChangeFormModal
  },
  data () {
    return {
      queryParam: JSON.parse(JSON.stringify(defaultQueryParam)),
      personList: [],
      statusList: [],
      USER_ORG_ID,
      USER_PERSON_ID,
      USER_OPERATOR,
      USER_DEPT_ID,
      columns: [
        { title: '工单号', dataIndex: 'workOrderNum', width: '8%', align: 'center' },
        { title: '申报日期',
          dataIndex: 'applyDate',
          width: '12%',
          align: 'center',
          customRender: (text, row, index) => {
            return moment(text).format('YYYY-MM-DD HH:mm:ss')
          }
        },
        {
          title: '变更类型',
          dataIndex: 'changeCategory',
          width: '8%',
          align: 'center',
          scopedSlots: { customRender: 'changeCategory' }
        },
        {
          title: '流程状态',
          dataIndex: 'status',
          width: '10%',
          align: 'center',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '申请人',
          dataIndex: 'applicant',
          width: '8%',
          align: 'center',
          scopedSlots: { customRender: 'person' }
        },
        {
          title: '当前审批人',
          dataIndex: 'userNext',
          width: '8%',
          align: 'center',
          scopedSlots: { customRender: 'person' }
        },
        {
          title: '申请描述',
          dataIndex: 'changeDescription',
          width: '30%',
          ellipsis: true,
          customRender: text => {
            return text ? <a-tooltip placement="topLeft" title={text}>{text}</a-tooltip> : '-'
          }
        },
        {
          title: '操作',
          align: 'center',
          scopedSlots: { customRender: 'action' },
          width: '10%'
        }
      ],
      dataSource: [],
      filterList: [
        {
          value: '0',
          label: '我发起的'
        },
        {
          value: '1',
          label: '待办'
        },
        {
          value: '2',
          label: '已办'
        }
      ],
      value1: '0',
      pagination: { current: 1, pageSize: 10, total: 3 },
      modalVisible: false,
      modalTitle: '',
      formData: {
        changeCategory: '',
        type: '',
        applicant: '',
        contact: '',
        department: '',
        applyDate: null,
        description: ''
      },
      changeCategorys: [],
      loadData: parameter => {
        // console.log('loadData.parameter', parameter)
        // const param = { ...this.queryParam }
        // console.log('param', param)
        // if (param.applicationDate) {
        //   param.applicationDate =
        // }
        console.log(this.queryParam)
        const params = this.queryParam
        const queryParam = {
          workOrderNum: params.workOrderNum,
          applyDate: params.applyDate ? moment(params.applyDate).format('YYYY-MM-DD') : null,
          status: params.status,
          applicant: params.applicant,
          changeDescription: params.changeDescription,
          changeCategory: params.changeCategory,
          changeType: params.changeType,
          changeClass: params.changeClass
        }
        const param = requestBuilder(
          '',
          queryParam,
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder)
        return queryITSMList(param).then(res => {
          console.log(res)
          this.dataSource = res.result.data
          return res.result
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
      showNeedDevice: false,
      showIsWX: false,
      status: [],
      statusDetail: [],
      databaseList: [],
      showModalDetail: false
    }
  },
  computed: {
    // selectTag () {
    //   return this.selectedRows.length === 1 ? this.selectedRowKeys[0].status : null
    // }
  },
  created () {
    // 初始化选项获取
    this.initOptions()
    // console.log(this.selectTag)
  },
  methods: {
    initOptions () {
      baseApi.getCommboxById({
        id: 'basCodeByClassId',
        sqlParams: { codeClassId: 'itsm_newChange_changeType' }
      }).then(res => {
        if (res.code === '0000') {
          this.changeCategorys = res.result
        }
      })
      baseApi.getCommboxById({ id: 'applyPerson' }).then(res => {
        if (res.code === '0000') {
          this.personList = res.result
        }
      })
      // 审批状态
      baseApi.getTreeById({ id: 'appro' }).then(res => {
        if (res.code === '0000') {
          const list = []
          for (const item of res.result) {
            if (APPROVE_OPTIONS_HIDES.includes(item.value)) {
              item.disabled = true
            }
            list.push(item)
          }
          this.statusList = list
          console.log('status list', this.statusList)
        }
      })
      baseApi.getCommboxById({ id: 'appro' }).then(res => {
        this.statusDetail = res.result
        console.log('this.statusDetail', this.statusDetail)
      })
      // 审批状态
      baseApi.getTreeById({ id: 'appro' }).then(res => {
        if (res.code === '0000') {
          for (const item of res.result) {
            if (APPROVE_OPTIONS_MORE_HIDES.includes(item.value)) {
              item.disabled = true
            }
            this.status.push(item)
          }
        }
      })
      // 获取数据库列表
      queryDatabase(requestBuilder()).then(res => {
        console.log(res)
        if (res.code === '0000') {
          this.databaseList = res.result.map(item => {
            return {
              label: item.name,
              value: String(item.id)
            }
          })
        }
      })
    },
    handleSearch () {
      // 查询逻辑
      console.log(this.queryParam)
    },
    doSearch () {
      this.$refs.table.refresh()
    },
    handleReset () {
      this.queryParam = JSON.parse(JSON.stringify(defaultQueryParam))
      this.$refs.table.refresh()
    },
    showModal (action, record = {}) {
      this.showModalDetail = record.changeType ? record.changeType.startsWith('1-1') : false
      this.$refs.ChangeFormModal.showModal(action, record)
    },
    deleteChange (record) {
      // 删除逻辑
    },
    handleTableChange (pagination) {
      this.pagination = pagination
      this.fetchData()
    },
    fetchData () {
      // 获取数据逻辑
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      console.log(this.selectedRows)
    },
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || key
    },
    confirm (record) {
      console.log('confirm', record)
      deleteITSM(requestBuilder('', {
        ...record
      })).then(res => {
        console.log(res)
        if (res.code === '0000') {
          this.handleReset()
          this.$message.success(res.message)
        }
      })
    },
    cancel () {
      console.log('cancel')
    },
    audit () {
      this.showIsWX = this.selectedRows.some(item => {
        return item.status === 'approval1270'
      })
      this.showNeedDevice = this.selectedRows.some(item => {
        return item.status === 'approval1268'
      })
      console.log('this.showNeedDevice', this.showNeedDevice)
      this.$refs.approveDrawer.doOpen()
    },
    onChange1 () {
      console.log('change')
      console.log(this.queryParam.changeClass)
    },
    // 审批
    toApprove (info) {
      console.log('审批信息', info)
      ITSMapproval(requestBuilder('', info)).then(res => {
        if (res.code !== '0000') {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '审批失败！'
          })
          return Promise.reject(res)
        }
        this.$notification.success({
          message: '系统消息',
          description: res.message || '审批成功！'
        })
        this.$refs.approveDrawer.visible = false
        this.doSearch(true)
      })
    },
    // 数据更改事件
    dataChanged () {
      this.doSearch(true)
    },
    selectChanged () {
      this.$refs.table.triggerSelect(...arguments)
    },
    getApproveId (status) {
      for (const item of this.status) {
        if (status === item.value) {
          return item.value
        }
        if (item.children) {
          for (const item2 of item.children) {
            if (status === item2.value) {
              return item.value
            }
          }
        }
      }
    },
    editBusinessMode (record) {
      if (APPROVE_STATUS_READY_CODE === this.getApproveId(record.status)) {
        return true
      }
      if (
        !!record.userNext &&
        record.userNext.split(',').includes(USER_OPERATOR) &&
        APPROVE_STATUS_START_CODE === this.getApproveId(record.status)
      ) {
        return true
      }
      return false
    },
    editApproveMode (record) {
      if (
        !!record.userNext &&
        record.userNext.split(',').includes(USER_OPERATOR) &&
        !APPROVE_NODES_BUS_FREEZE.includes(this.getApproveId(record.status)) &&
        !APPROVE_NODES_BUS_INIT.includes(this.getApproveId(record.status))
      ) {
        return true
      }
      return false
    }
  },
  mounted () {
    this.fetchData()
  }
}
</script>

<style scoped>
.search-form {
  margin-bottom: 16px;
}

/* :deep(.table-striped) {
  background-color: #fafafa;
} */

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.action-button {
  padding: 0 8px;
  font-size: 16px;
}

.action-button.danger {
  color: #ff4d4f;
}

.action-button.danger:hover {
  color: #ff7875;
}

/* 紧急变更样式 */
.emergency-change {
  font-weight: bold;
  color: #ff4d4f;
}

/* 表格样式 */
.custom-table .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 500;
}

.custom-table .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}
</style>
