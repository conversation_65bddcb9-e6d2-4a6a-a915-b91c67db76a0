<template>
  <a-modal
    v-model="visible"
    :title="title"
    :width="800"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form :form="form" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item
            label="变更分类"
            :rules="[{ required: true, message: '请选择变更分类' }]"
          >
            <a-select v-model="formData.category" placeholder="请选择变更分类">
              <a-select-option value="正常">正常</a-select-option>
              <a-select-option value="重大">重大</a-select-option>
              <a-select-option value="紧急">紧急</a-select-option>
              <a-select-option value="预授权变更">预授权变更</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item
            label="变更种类"
            :rules="[{ required: true, message: '请选择变更种类' }]"
          >
            <a-select v-model="formData.type" placeholder="请选择变更种类">
              <a-select-option value="网络">网络</a-select-option>
              <a-select-option value="服务器">服务器</a-select-option>
              <a-select-option value="通信系统">通信系统</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item
            label="申请人"
            :rules="[{ required: true, message: '请输入申请人' }]"
          >
            <a-input v-model="formData.applicant" placeholder="请输入申请人" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item
            label="联系电话"
            :rules="[{ required: true, message: '请输入联系电话' }]"
          >
            <a-input v-model="formData.contact" placeholder="请输入联系电话" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item
            label="申请部门"
            :rules="[{ required: true, message: '请输入申请部门' }]"
          >
            <a-input
              v-model="formData.department"
              placeholder="请输入申请部门"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item
            label="申请日期"
            :rules="[{ required: true, message: '请选择申请日期' }]"
          >
            <a-date-picker v-model="formData.applyDate" style="width: 100%" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="资源类型">
            <a-checkbox-group v-model="formData.resourceTypes">
              <a-row>
                <a-col :span="8">
                  <a-checkbox value="数据库">数据库</a-checkbox>
                </a-col>
                <a-col :span="8">
                  <a-checkbox value="网络设备">网络设备</a-checkbox>
                </a-col>
                <a-col :span="8">
                  <a-checkbox value="应用系统版本升级">应用系统版本升级</a-checkbox>
                </a-col>
                <a-col :span="8">
                  <a-checkbox value="中间件">中间件</a-checkbox>
                </a-col>
                <a-col :span="8">
                  <a-checkbox value="基础设备">基础设备</a-checkbox>
                </a-col>
                <a-col :span="8">
                  <a-checkbox value="应用系统部署">应用系统部署</a-checkbox>
                </a-col>
                <a-col :span="8">
                  <a-checkbox value="服务器">服务器</a-checkbox>
                </a-col>
                <a-col :span="8">
                  <a-checkbox value="账号变更">账号变更</a-checkbox>
                </a-col>
                <a-col :span="8">
                  <a-checkbox value="系统管理软件">系统管理软件</a-checkbox>
                </a-col>
                <a-col :span="8">
                  <a-checkbox value="数据变更">数据变更</a-checkbox>
                </a-col>
                <a-col :span="8">
                  <a-checkbox value="应用代码">应用代码</a-checkbox>
                </a-col>
                <a-col :span="8">
                  <a-checkbox value="其他">其他</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item
            label="预计开始时间"
            :rules="[{ required: true, message: '请选择预计开始时间' }]"
          >
            <a-date-picker v-model="formData.startTime" style="width: 100%" show-time />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item
            label="预计结束时间"
            :rules="[{ required: true, message: '请选择预计结束时间' }]"
          >
            <a-date-picker v-model="formData.endTime" style="width: 100%" show-time />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item
            label="变更描述"
            :rules="[{ required: true, message: '请输入变更描述' }]"
          >
            <a-textarea
              v-model="formData.description"
              placeholder="请输入变更描述"
              rows="4"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-card style="margin-top: 16px;">
        <a-steps :current="currentStep" direction="horizontal">
          <a-step
            v-for="(step, index) in steps"
            :key="index"
            :title="step.title"
            :description="step.description"
          />
        </a-steps>
      </a-card>
      <a-card style="margin-top: 16px;">
        <a-tabs default-active-key="1">
          <a-tab-pane key="1" tab="附件">
            <AttachmentTable :attachments="formData.attachments" />
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </a-form>
  </a-modal>
</template>

<script>
import AttachmentTable from './AttachmentTable.vue'

export default {
  name: 'ChangeFormModal',
  components: {
    AttachmentTable
  },
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      default: '变更工单'
    },
    formData: {
      type: Object,
      default: () => ({
        category: '',
        type: '',
        applicant: '',
        contact: '',
        department: '',
        applyDate: null,
        description: '',
        resourceTypes: [],
        startTime: null,
        endTime: null,
        attachments: []
      })
    }
  },
  data () {
    return {
      currentStep: 1,
      steps: [
        { title: '提交申请', description: '申请人: 张三' },
        { title: '部门审批', description: '审批人: 李四' },
        { title: '完成', description: '审批人: 王五' }
      ]
    }
  },
  methods: {
    handleOk () {
      this.$emit('submit', this.formData)
    },
    handleCancel () {
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped>
/* 添加自定义样式 */
</style>
