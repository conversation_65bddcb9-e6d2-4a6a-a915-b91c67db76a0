<template>
  <section>
    <a-drawer
      width="360"
      title="审批"
      :mask="true"
      :maskClosable="true"
      :getContainer="false"
      :visible="visible"
      @close="doClose"
    >
      <a-spin :spinning="loading">
        <a-form
          layout="vertical"
          style="position: relative; z-index: 0"
        >
          <a-form-item label="审批模式:">
            <a-select v-model="approveParam.selectMode">
              <a-select-option
                v-for="(item, index) in approveModeList"
                :disabled="item.disabled"
                :value="item.value"
                :key="index"
              >{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="审批操作:">
            <a-select v-model="approveParam.action">
              <a-select-option value="1">同意</a-select-option>
              <a-select-option
                value="0"
                :disabled="approveReturnDisabled"
              >退回</a-select-option>
              <a-select-option
                value="9"
                :disabled="approveCancelDisabled"
              >取消</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="选择部门审核人" v-if="selectTag==='approval1009'">
            <a-select
              v-model="approveParam.selectApprove"
              placeholder="请选择变更类型"
              allowClear
              style="width: 100%"
            >
              <a-select-option
                v-for="(item, index) in approveList"
                :value="item.value"
                :label="item.label"
                :key="index"
              >{{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="指定工程师" v-if="selectTag === 'approval1219'">
            <a-select
              v-model="approveParam.selectApprove"
              placeholder="请选择变更类型"
              allowClear
              style="width: 100%"
            >
              <a-select-option
                v-for="(item, index) in enginnerList"
                :value="item.value"
                :label="item.label"
                :key="index"
              >{{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="选择技术中心审批人" v-if="selectTag === 'approval1223' && changeCategory === '2'">
            <a-select
              v-model="approveParam.selectApprove"
              placeholder="请选择变更类型"
              allowClear
              style="width: 100%"
            >
              <a-select-option
                v-for="(item, index) in JSZXApprove"
                :value="item.value"
                :label="item.label"
                :key="index"
              >{{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="选择部门负责人" v-if="selectTag==='approval1226' || (selectTag==='approval1223' && changeCategory !== '2')">
            <a-select
              v-model="approveParam.selectApprove"
              placeholder="请选择部门负责人"
              allowClear
              style="width: 100%"
            >
              <a-select-option
                v-for="(item, index) in approveList"
                :value="item.value"
                :label="item.label"
                :key="index"
              >{{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="申请描述:">
            <a-textarea
              placeholder=""
              v-model="approveParam.approveSuggest"
              :autoSize="{ minRows: 3 }"
              :maxLength="200"
              disabled='disabled'
            />
          </a-form-item>
          <a-form-item label="审批意见:">
            <a-textarea
              placeholder="最多可输入200个文本"
              v-model="approveParam.approveSuggest"
              :autoSize="{ minRows: 3 }"
              :maxLength="200"
            />
          </a-form-item>
          <a-form-item
            v-if="approveParam.showApprove && approveParam.action !== '9'"
            :label="labelApprove"
          >
            <a-select
              v-model="approveParam.selectApprove"
              :mode="approveAutoTag === 'checkbox' ? 'multiple' : 'default'"
              allowClear
            >
              <a-select-option
                v-for="(item, index) in approveList"
                :value="item.value"
                :key="index"
              >{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
        <div v-if="recordSysId">
          <a-divider style="width: calc(100% + 20px); margin: 20px -10px 15px;" />
          <div class="approve-records-select">
            <a-select
              class="border-only-bottom"
              v-model="recordSysId"
              style="width: 100%"
            >
              <a-select-option
                v-for="(item, index) in sysIdOptions"
                :value="item.value"
                :key="index"
              >{{ item.label }}</a-select-option>
            </a-select>
          </div>
          <div class="approve-records-line">
            <div
              v-if="!sysIdRecord"
              style="padding: 8px 14px; font-size: 14px; color: #cccccc"
            >正在查询中...</div>
            <div
              v-else-if="!sysIdRecord.length"
              style="padding: 8px 14px; font-size: 14px; color: #909399"
            >暂无审批记录</div>
            <a-timeline
              v-else
              style="padding: 12px"
            >
              <a-timeline-item
                v-for="(item, index) in sysIdRecord"
                :key="index"
                color="green"
              >
                <div>{{ item.msg }}</div>
                <div>{{ item.flag }}</div>
                <div>{{ item.createBy }}</div>
                <div>{{ item.createDate }}</div>
              </a-timeline-item>
              <a-timeline-item>
                <span slot="dot" />
              </a-timeline-item>
            </a-timeline>
          </div>
        </div>
      </a-spin>
      <div class="drawer-footer">
        <div class="footer-fixed">
          <a-button @click="doClose">返回</a-button>
          <a-button
            type="primary"
            :loading="loading"
            @click="doApprove"
          >提交</a-button>
        </div>
      </div>
    </a-drawer>
  </section>
</template>

<script>
import { APPROVE_NODES_ALLOW_CANCEL, APPROVE_NODES_NOT_RETURN } from '@/store/variable-types'

import { STable } from '@/components'
import { OPERATOR, PERSON_ID } from '@/store/mutation-types'
import * as approveApi from '@/api/system/approve'
import Vue from 'vue'
import * as baseApi from '@/api/system/base'
// 操作人 userId
const USER_OPERATOR = Vue.ls.get(OPERATOR)
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)

export default {
  name: 'ApproveDrawer',
  components: {
    STable
  },
  props: {
    dataSource: {
      type: Array,
      default: function () {
        return []
      }
    },
    selectedRows: {
      type: Array,
      default: function () {
        return []
      }
    },
    dataChanged: {
      type: Function,
      default: function () {}
    },
    drawerClosed: {
      type: Function,
      default: function () {}
    },
    selectChanged: {
      type: Function,
      default: function () {}
    },
    getApproveId: {
      type: Function,
      default: function () {}
    },
    keyName: {
      type: String,
      default: ''
    },
    origin: {
      type: String,
      default: ''
    },
    statusName: {
      type: String,
      default: 'status'
    }
  },
  data () {
    return {
      labelApprove: '',
      selectTag: null,
      changeCategory: null,
      // 审批抽屉框
      approveList: [],
      enginnerList: [],
      approveModeList: [
        {
          label: '审批选中数据',
          value: '1'
        }
      ],
      approveParam: {
        showApprove: false,
        selectMode: '',
        selectSysIds: [],
        selectApprove: undefined,
        approveSuggest: '',
        action: '1',
        enginner: ''
      },
      approveAutoTag: null,
      visible: false,
      loading: false,
      refresh: false,

      // 审批记录缓存
      sysIdRecords: {},
      recordSysId: '',
      deptApproList: [],
      JSZXApprove: [],
      record: undefined
    }
  },
  computed: {
    // 审批记录下拉框
    sysIdOptions () {
      return this.approveParam.selectSysIds.map(item => {
        return {
          label: '申报单号:' + item[this.keyName],
          value: item[this.keyName]
        }
      })
    },
    // 审批记录查询
    sysIdRecord () {
      if (!this.sysIdRecords.hasOwnProperty(this.recordSysId)) {
        this.queryRecords(this.recordSysId)
        return false
      }
      return this.sysIdRecords[this.recordSysId] || []
    },
    // 审批退回禁用与否
    approveReturnDisabled () {
      if (this.approveParam.selectMode !== '1') {
        return true
      }
      for (const select of this.approveParam.selectSysIds) {
        const item = this.selectedRows.find(item => item[this.keyName] === select[this.keyName])
        if (!item) {
          return true
        }
        if (APPROVE_NODES_NOT_RETURN.includes(this.getApproveId(item[this.statusName]))) {
          return true
        }
      }
      return false
    },
    // 审批取消禁用与否
    approveCancelDisabled () {
      if (this.origin === 'discardMatu') {
        return false
      }
      if (this.approveParam.selectMode !== '1') {
        return true
      }
      for (const select of this.approveParam.selectSysIds) {
        const item = this.dataSource.find(item => item[this.keyName] === select[this.keyName])
        if (!APPROVE_NODES_ALLOW_CANCEL.includes(this.getApproveId(item[this.statusName]))) {
          return true
        }
      }
      return false
    }
  },
  created () {
    // 初始化选项获取
  },
  watch: {
    // 监听 - 审批选定采购计划更改时 (默认查询审批记录下拉框中第一条)
    'approveParam.selectSysIds': {
      handler (list) {
        if (!list.includes(this.recordSysId)) {
          this.recordSysId = ''
        }
        if (!this.recordSysId) {
          this.recordSysId = list.length > 0 ? list[0][this.keyName] : ''
        }
      }
    },
    // 监听 - 审批退回禁用时 (如果当时审批操作是“退回”， 则更改为“同意“)
    'approveReturnDisabled': {
      handler (disabled) {
        if (disabled && this.approveParam.action === '0') {
          this.approveParam.action = '1'
        }
      }
    },
    // 监听 - 审批取消禁用时 (如果当时审批操作是“取消”， 则更改为“同意“)
    'approveCancelDisabled': {
      handler (disabled) {
        if (disabled && this.approveParam.action === '9') {
          this.approveParam.action = '1'
        }
      }
    },
    // 监听 - 审批操作更改时 (重置下一个审批人选项)
    'approveParam.action': {
      handler () {
        if (this.approveAutoTag === 'checkbox') {
          this.approveParam.selectApprove = []
        }
        this.approveParam.selectApprove = ''
      }
    }
  },
  methods: {
    // 下拉框数据获取
    initOptions () {
      // 获取审批人
      baseApi.getCommboxById({ id: 'queryDepAppro', sqlParams: { deptId: this.selectedRows[0].department } }).then(res => {
        console.log(res)
        this.approveList = res.result
      })
      // 工程师名单
      baseApi.getCommboxById({ id: 'queryITSMEngineer' }).then(res => {
        console.log(res)
        this.enginnerList = res.result
      })
      // 技术中心审批人
      baseApi.getCommboxById({ id: 'queryJSZXappro' }).then(res => {
        console.log(res)
        this.JSZXApprove = res.result
      })
    },
    // 根据单号查询审批记录
    queryRecords (keyName) {
      approveApi.queryApproHis({ origin: this.origin, businessNum: keyName }).then(res => {
        this.sysIdRecords = {
          ...this.sysIdRecords,
          [keyName]: res.result || []
        }
      })
      this.sysIdRecords[keyName] = false
    },

    // 打开 审批弹框
    doOpen () {
      this.initOptions()

      // 审批人类型
      const autoTag = null
      const origin = this.origin

      this.labelApprove = '下个审批人:'
      // 设置 autoTag
      const record = this.selectedRows[0]
      console.log(record)
      this.selectTag = record.status
      this.changeCategory = record.changeCategory
      this.visible = true
    },
    // 关闭 审批弹窗
    doClose () {
      // 是否刷新
      if (this.refresh) {
        this.refresh = false
        this.dataChanged()
      } else {
        this.drawerClosed()
      }
      this.approveList = []
      this.sysIdRecords = {}
      this.recordSysId = ''
      this.approveParam = {
        showApprove: false,
        selectMode: '',
        selectSysIds: [],
        selectApprove: '',
        approveSuggest: '',
        action: '1'
      }
      this.approveAutoTag = null
      this.visible = false
    },
    // 审批调用
    doApprove () {
      const approveParam = this.approveParam
      if (!approveParam.selectMode) {
        this.$message.error('请选择审批模式！')
        return
      }
      const status = this.selectedRows[0].status
      // showApprove 为 true时，须指定审批人
      const pattern = 'approByNum'
      const needUserId = approveParam.showApprove && approveParam.action !== '9'
      const selectApproveArr = [].concat(approveParam.selectApprove)
      const userId = needUserId ? selectApproveArr.join(',') : ''
      const list = [this.selectedRows[0].uuid]
      const msg = approveParam.approveSuggest
      const flag = approveParam.action
      const selectApprove = approveParam.selectApprove

      const param = {
        op: USER_OPERATOR,
        pattern: pattern,
        userId: selectApprove,
        list: list,
        msg: msg,
        flag: flag,
        origin: this.origin
      }
      console.log(status)
      console.log(param)
      // this.loading = true
      this.$emit('toApprove', param)
    }
  }
}
</script>
