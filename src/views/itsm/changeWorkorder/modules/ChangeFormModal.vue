<template>
  <a-modal
    v-model="visible"
    :title="title"
    :width="1200"
    :style="{ height: 'calc(80vh -100px)' }"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-row :gutter="16">
      <!-- 左侧：表单内容 -->
      <a-col :span="16">
        <a-form :form="form" layout="vertical" ref="form">
          <a-row :gutter="16">
            <a-col :span="8" v-show="false">
              <a-form-item
                label="uuid"
              >
                <a-input
                  v-decorator="['uuid', { rules: [{ required: false, message: '' }] }]"
                  placeholder="请输入申请人"
                  disabled="disabled"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="变更分类">
                <a-select
                  v-decorator="['changeCategory', { rules: [{ required: true, message: '请输入变更分类' }] }]"
                  placeholder="请选择变更分类"
                  allowClear
                  style="width: 100%"
                  @change="changeCategoryChange"
                >
                  <a-select-option
                    v-for="(item, index) in changeCategorys"
                    :value="item.value"
                    :label="item.label"
                    :key="index"
                  >{{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                label="变更种类"
              >
                <a-tree-select
                  v-decorator="['changeType', { rules: [{ required: true, message: '请选择变更种类' }] }]"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="treeData"
                  placeholder="请选择变更种类"
                  @change="changeTypeChange"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                label="申请人"
              >
                <a-input
                  v-decorator="['applicant', { rules: [{ required: true, message: '请输入申请人' }] }]"
                  placeholder="请输入申请人"
                  disabled="disabled"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item
                label="联系电话"
              >
                <a-input
                  v-decorator="['phone', { rules: [{ required: true, message: '请输入联系电话' }] }]"
                  placeholder="请输入联系电话"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                label="申请部门"
              >
                <a-select
                  v-decorator="['department', { rules: [{ required: true, message: '请输入变更分类' }] }]"
                  placeholder="请选择部门"
                  allowClear
                  showSearch
                  optionFilterProp="label"
                  style="width: 100%"
                >
                  <a-select-option
                    v-for="(item, index) in deptList"
                    :value="item.value"
                    :label="item.label"
                    :key="index"
                  >{{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                label="申请日期"
              >
                <a-date-picker
                  v-decorator="['applyDate', { rules: [{ required: true, message: '请输入申请日期' }] }]"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                label="验收人"
                v-show="SHOW_YSR"
              >
                <a-select
                  v-decorator="['ysr', { rules: [{ required: false, message: '请输入变更分类' }] }]"
                  placeholder="请选择验收人"
                  allowClear
                  showSearch
                  optionFilterProp="label"
                  style="width: 100%"
                >
                  <a-select-option
                    v-for="(item, index) in ysrs"
                    :value="item.value"
                    :label="item.label"
                    :key="index"
                  >{{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="资源类型">
                <a-checkbox-group
                  v-decorator="['resourceTypes', { rules: [{ required: true, message: '至少选择一种类型' }] }]"
                >
                  <a-row>
                    <a-col :span="6">
                      <a-checkbox value="数据库">数据库</a-checkbox>
                    </a-col>
                    <a-col :span="6">
                      <a-checkbox value="网络设备">网络设备</a-checkbox>
                    </a-col>
                    <a-col :span="6">
                      <a-checkbox value="应用系统版本升级">
                        应用系统版本升级
                      </a-checkbox>
                    </a-col>
                    <a-col :span="6">
                      <a-checkbox value="中间件">中间件</a-checkbox>
                    </a-col>
                    <a-col :span="6">
                      <a-checkbox value="基础设备">基础设备</a-checkbox>
                    </a-col>
                    <a-col :span="6">
                      <a-checkbox value="应用系统部署">应用系统部署</a-checkbox>
                    </a-col>
                    <a-col :span="6">
                      <a-checkbox value="服务器">服务器</a-checkbox>
                    </a-col>
                    <a-col :span="6">
                      <a-checkbox value="账号变更">账号变更</a-checkbox>
                    </a-col>
                    <a-col :span="6">
                      <a-checkbox value="系统管理软件">系统管理软件</a-checkbox>
                    </a-col>
                    <a-col :span="6">
                      <a-checkbox value="数据变更">数据变更</a-checkbox>
                    </a-col>
                    <a-col :span="6">
                      <a-checkbox value="应用代码">应用代码</a-checkbox>
                    </a-col>
                    <a-col :span="6">
                      <a-checkbox value="其他">其他</a-checkbox>
                    </a-col>
                  </a-row>
                </a-checkbox-group>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item
                label="变更描述"
              >
                <a-textarea
                  v-decorator="['changeDescription']"
                  placeholder="请输入变更描述"
                  rows="6"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="预计开始">
                <a-date-picker
                  v-decorator="['estimatedStartTime', { rules: [{ required: true, message: '请输入预计开始时间' }] }]"
                  show-time
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="预计结束">
                <a-date-picker
                  v-decorator="['estimatedEndTime', { rules: [{ required: true, message: '请输入预计结束时间' }] }]"
                  show-time
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 堡垒机相关信息 - 当变更种类为11时显示 -->
          <div v-if="showBastionFields">
            <a-divider>堡垒机相关信息填写</a-divider>

            <!-- 申请类型 -->
            <a-row :gutter="16">
              <a-col :span="24">
                <a-form-item label="申请类型">
                  <a-radio-group
                    v-decorator="['bastionApplyType', { rules: [{ required: showBastionFields, message: '请选择申请类型' }] }]"
                    @change="handleBastionApplyTypeChange"
                  >
                    <a-radio value="account">开通账号</a-radio>
                    <a-radio value="resource">资源申请</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
            </a-row>

            <!-- 账号相关字段 - 无论哪种类型都显示 -->
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="登录名">
                  <a-tooltip placement="top" title="长度1-64个字符，不支持的字符:/\[]:;|=,+&quot;?<>@*">
                    <a-input
                      v-decorator="['loginName', {
                        rules: [
                          { required: true, message: '请输入登录名' },
                          { validator: validateLoginName }
                        ]
                      }]"
                      placeholder="请输入登录名"
                    />
                  </a-tooltip>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="邮箱">
                  <a-tooltip placement="top" title="请输入正确的邮箱格式，如：<EMAIL>">
                    <a-input
                      v-decorator="['email', {
                        rules: [
                          { required: bastionApplyType === 'account', message: '请输入邮箱' },
                          { type: 'email', message: '请输入正确的邮箱格式' }
                        ]
                      }]"
                      placeholder="请输入邮箱"
                    />
                  </a-tooltip>
                </a-form-item>
              </a-col>
            </a-row>

            <!-- 资源申请详情 - 无论哪种类型都显示 -->
            <div class="resource-section">
              <div class="resource-section-header">
                <div class="resource-section-title">资源详情</div>
                <div>
                  <a-button type="primary" style="margin-right: 8px;" @click="validateBastionInfo" :loading="validating">
                    <a-icon v-if="!validating" type="check-circle" /> 校验信息
                  </a-button>
                  <a-button type="dashed" @click="addResourceItem">
                    <a-icon type="plus" /> 添加资源
                  </a-button>
                </div>
              </div>

              <div v-if="resourceList.length === 0" style="text-align: center; color: #ff4d4f; margin: 16px 0;">
                {{ bastionApplyType === 'resource' ? '资源申请类型至少需要添加一条资源信息' : '请添加资源信息' }}
              </div>

              <div
                v-for="(item, index) in resourceList"
                :key="index"
                class="resource-item">
                <div class="resource-item-row">
                  <div class="resource-item-field" style="flex: 1;">
                    <span class="resource-item-label">主机IP:</span>
                    <a-tooltip placement="top" title="请输入正确的IP格式，如：***********">
                      <a-input
                        v-model="item.address"
                        placeholder="请输入主机IP"
                        @blur="validateIpFormat(item, $event)"
                      />
                    </a-tooltip>
                  </div>
                  <div class="resource-item-field" style="flex: 1; margin-left: 16px;">
                    <span class="resource-item-label">主机名称:</span>
                    <a-tooltip placement="top" title="请输入主机名称，长度不超过64个字符">
                      <a-input
                        class="pointNone"
                        v-model="item.relName"
                        placeholder="校验信息后自动读取"
                      />
                    </a-tooltip>
                  </div>
                  <div style="margin-left: 8px; width: 32px;">
                    <a-tooltip title="删除此项">
                      <a-button
                        type="danger"
                        shape="circle"
                        icon="delete"
                        size="small"
                        @click="removeResourceItem(index)"
                      />
                    </a-tooltip>
                  </div>
                </div>
                <div class="resource-item-row" style="margin-top: 16px;">
                  <div class="resource-item-field" style="flex: 1;">
                    <span class="resource-item-label">有效期:</span>
                    <a-tooltip placement="top" title="请选择资源的有效期起止时间">
                      <a-range-picker
                        v-model="item.validPeriod"
                        style="width: calc(100% - 40px)"
                        show-time
                        format="YYYY-MM-DD HH:mm:ss"
                      />
                    </a-tooltip>
                  </div>
                  <div style="width: 40px;"/>
                </div>
              </div>
            </div>

            <!-- 处理信息 - 当变更种类为11且handleResult有值时显示 -->
            <div v-if="showBastionFields && record && record.handleResult" class="handle-result-section">
              <a-divider>处理信息</a-divider>
              <div class="handle-result-content">
                <div class="handle-result-message">
                  <a-icon
                    :type="record.handleResult.includes('成功') ? 'check-circle' : 'info-circle'"
                    :style="{ color: record.handleResult.includes('成功') ? '#52c41a' : '#1890ff', fontSize: '24px' }"
                  />
                  <div class="handle-result-text">
                    <template >
                      <div v-for="(line, index) in record.handleResult.split('\n')" :key="index" class="handle-result-line">{{ line }}</div>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <a-row :gutter="16" v-if="showDetail">
            <a-divider>数据库相关信息填写</a-divider>
            <a-col :span="8">
              <a-form-item
                label="ip来源"
              >
                <a-input
                  v-decorator="['resourceIp', { rules: [{ required: showDetail, message: '请输入ip来源' }] }]"
                  placeholder="请输入ip来源"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                label="数据库"
              >
                <a-select
                  v-decorator="['databaseId', { rules: [{ required: showDetail, message: '请选择访问数据库' }] }]"
                  placeholder="请选择访问数据库"
                  allowClear
                  style="width: 100%"
                  @change="changeDatabase"
                >
                  <a-select-option
                    v-for="(item, index) in databaseList"
                    :value="item.value"
                    :label="item.label"
                    :key="index"
                  >{{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <!--            <a-col :span="8">-->
            <!--              <a-form-item-->
            <!--                label="操作"-->
            <!--              >-->
            <!--                <a-select-->
            <!--                  v-decorator="['action', { rules: [{ required: true, message: '请选择操作类型' }] }]"-->
            <!--                  placeholder="请选择操作类型"-->
            <!--                  allowClear-->
            <!--                  style="width: 100%"-->
            <!--                >-->
            <!--                  <a-select-option-->
            <!--                    v-for="(item, index) in actionList"-->
            <!--                    :value="item.value"-->
            <!--                    :label="item.label"-->
            <!--                    :key="index"-->
            <!--                  >{{ item.label }}-->
            <!--                  </a-select-option>-->
            <!--                </a-select>-->
            <!--              </a-form-item>-->
            <!--            </a-col>-->
          </a-row>
          <!-- <a-row v-if="action !== 'view'">
            <a-col>
              <a-form-item>
                <a-button type="primary" html-type="submit" @click="handleSubmit">
                  {{ action === 'add' ? '新增' : '保存' }}
                </a-button>
              </a-form-item>
            </a-col>
          </a-row> -->
          <!-- <a-card style="margin-top: 16px; background-color: #f0f2f5;">
            <a-steps :current="currentStep" direction="horizontal" size="small">
              <a-step
                v-for="(step, index) in steps"
                :key="index"
                :title="step.title"
              />
            </a-steps>
          </a-card> -->
          <a-card style="margin-top: 16px; background-color: #f0f2f5;">
            <a-tabs default-active-key="1">
              <a-tab-pane key="1" tab="附件">
                <appendix
                  ref="appendix"
                  text=""
                  :tableDisplay="true"
                  :businessName="businessName"
                  :keyId="recordId"
                />
              </a-tab-pane>
            </a-tabs>
          </a-card>
        </a-form>
      </a-col>
      <!-- 右侧：审批历史 -->
      <a-col :span="8">
        <a-card
          class="history-card"
          :tab-list="tabList"
          :active-tab-key="titleKey"
          @tabChange="key => this.titleKey = key"
        >
          <div v-if="titleKey === 'history'">
            <a-timeline v-if="approvalHistory.length > 0" class="approval-timeline">
              <a-timeline-item
                v-for="(history, index) in approvalHistory"
                :key="index"
                class="timeline-item"
              >
                <p><strong>审批节点:</strong> {{ history.statusName }}</p>
                <p><strong>审批人:</strong> {{ history.createBy }}</p>
                <p><strong>建议:</strong> {{ history.flag }}</p>
                <p><strong>备注:</strong> {{ history.msg }}</p>
                <p><strong>日期:</strong> {{ history.createDate }}</p>
              </a-timeline-item>
            </a-timeline>
            <div
              v-else
              style="padding: 8px 14px; font-size: 14px; color: #909399; border-radius: 8px;"
            >
              暂无审批记录
            </div>
          </div>
          <div v-if="titleKey === 'progress'" class="progress-container">
            <h3 class="progress-title">流程进度</h3>

            <a-steps
              direction="vertical"
              size="small"
              :current="getCurrentStepIndex()"
              class="workflow-steps"
            >
              <a-step
                v-for="(step, index) in finalSteps"
                :key="index"
                :title="step.title"
                :description="getStepDescription(step)"
                :status="getStepStatus(step)"
              />
            </a-steps>

            <div class="current-step-info">
              <div class="info-header">
                <a-icon :type="record && record.status ? 'info-circle' : 'exclamation-circle'" theme="filled" />
                <span>{{ record && record.status ? '当前节点信息' : '提示信息' }}</span>
              </div>
              <div class="info-content">
                <p>{{ getCurrentStepInfo() }}</p>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>
    <template slot="footer">
      <a-button type="primary" html-type="submit" @click="handleSubmit" v-if="action !== 'view'">
        {{ action === 'add' ? '新增' : '保存' }}
      </a-button>
      <a-button @click="handleCancel">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import Appendix from '@/views/system/components/AppendixNew'
import * as baseApi from '@/api/system/base'
import { creatNewITSM, updateITSM, validateITSMInfo } from '@/api/device/itsm'
import { requestBuilder, takeTreeByKey } from '@/utils/util'
import moment from 'moment'

import Vue from 'vue'

import { DEPT_ID, OPERATOR, ORG_ID, PERSON_ID } from '@/store/mutation-types'
import * as approveApi from '@/api/system/approve'

const USER_ORG_ID = Vue.ls.get(ORG_ID)
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)
const USER_OPERATOR = Vue.ls.get(OPERATOR)
const USER_DEPT_ID = Vue.ls.get(DEPT_ID)

const defaultForm = {
  changeCategory: undefined,
  changeType: undefined,
  applicant: undefined,
  applyDate: undefined,
  resourceTypes: undefined,
  changeDescription: undefined,
  estimatedStartTime: undefined,
  estimatedEndTime: undefined
}
export default {
  name: 'ChangeFormModal',
  components: {
    Appendix
  },
  props: {
    // visible: {
    //   type: Boolean,
    //   required: true
    // },
    // title: {
    //   type: String,
    //   default: '变更工单'
    // }
    databaseList: {
      type: Array,
      default: () => []
    },
    actionList: {
      type: Array,
      default: () => []
    }
    // showDetail: {
    //   type: Boolean,
    //   detail: false
    // }
  },
  data () {
    return {
      USER_ORG_ID,
      USER_PERSON_ID,
      USER_OPERATOR,
      USER_DEPT_ID,
      validating: false,
      SHOW_YSR: false,
      deptList: [],
      ysrs: [],
      personList: [],
      recordId: null,
      visible: false,
      currentStep: 0,
      modalVisible: false,
      form: null,
      title: '',
      showDetail: false,
      showBastionFields: false,
      bastionApplyType: null,
      resourceList: [],
      databaseObj: {
        databaseId: null,
        databaseName: null
      },
      steps: [
        { title: '申请人发起', check: true, status: 'approval1009' },
        { title: '部门内部审核', check: true, status: 'approval1218' },
        { title: 'SMG办公室流转', check: true, status: 'approval1219' },
        { title: '技术中心', status: 'approval1223' },
        { title: 'SMG办公室流转', status: 'approval1224' },
        { title: '相关技术部门负责人审核', check: true, status: 'approval1220' },
        { title: '变更方案', status: 'approval1221' },
        { title: '方案确认', status: 'approval1228' },
        { title: '工程师执行', check: true, status: 'approval1227' },
        { title: '申请人确认', check: true, status: 'approval1039' },
        { title: 'SMG办公室确认', check: true, status: 'approval1222' },
        { title: '审批完成', check: true, status: 'approval1003' },
        { title: '已取消', check: true, status: 'approval1011', isFinal: true, isCancelled: true }
      ],
      approvalHistory: [],
      changeCategorys: [],
      treeData: [],
      approHisRecord: [],
      action: 'edit',
      record: null,
      businessName: 'ITSM',
      titleKey: 'history',
      tabList: [
        {
          key: 'history',
          tab: '审批历史'
        },
        {
          key: 'progress',
          tab: '流程进度'
        }
      ]
    }
  },
  computed: {
    finalSteps () {
      const changeCategory = this.record?.changeCategory
      const currentStatus = this.record?.status

      // 获取基础步骤（不包括最终状态步骤）
      const baseSteps = this.steps.filter(item => !item.isFinal)
      let filteredSteps

      if (changeCategory === '2') {
        filteredSteps = baseSteps
      } else {
        filteredSteps = baseSteps.filter(item => item.check === (changeCategory === '1'))
      }

      // 根据当前状态添加最终步骤
      if (currentStatus === 'approval1003') {
        // 审批完成
        filteredSteps.push(this.steps.find(item => item.status === 'approval1003'))
      } else if (currentStatus === 'approval1011') {
        // 已取消
        filteredSteps.push(this.steps.find(item => item.status === 'approval1011'))
      }

      return filteredSteps
    }
  },
  created () {
    this.form = this.$form.createForm(this)
  },
  mounted () {
    this.initOptions()
  },
  methods: {
    handleOk () {
      this.$emit('submit', this.form)
      console.log('新增', this.form)
      this.showDetail = false
    },
    handleCancel () {
      console.log('cancel')
      this.visible = false
      // this.formData = JSON.parse(JSON.stringify(defaultForm))
      this.form.resetFields()
      this.recordId = null
      this.record = null
      this.showDetail = false
      this.showBastionFields = false
      this.bastionApplyType = null
      this.resourceList = []
      this.approvalHistory = []
    },
    showModal (action, record) {
      this.action = action
      this.title = action === 'add' ? '新增变更工单' : '编辑变更工单'
      this.initData(action, record)
      this.visible = true
      console.log('this.action', this.action)
    },
    getDisableStatus () {
      if (this.record.status === 'approval1219') {
        return false
      }
      return true
    },
    initOptions () {
      // 变更分类
      baseApi.getCommboxById({
        id: 'basCodeByClassId',
        sqlParams: { codeClassId: 'itsm_newChange_changeType' }
      }).then(res => {
        if (res.code === '0000') {
          this.changeCategorys = res.result
        }
      })
      // 部门
      baseApi.getCommboxById({ id: 'dept', sqlParams: { byOrgId: '1' } }).then(res => {
        if (res.code === '0000') {
          this.deptList = res.result
        }
      })
      //
      baseApi.getCommboxById({ id: 'applyPerson' }).then(res => {
        if (res.code === '0000') {
          this.personList = res.result
        }
      })
      baseApi.getCommboxById({ id: 'applyPerson' }).then(res => {
        if (res.code === '0000') {
          this.ysrs = res.result
        }
      })
      // 变更种类
      baseApi.getTreeById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'ITSM_change_type' } }).then(res => {
        if (res.code === '0000') {
          this.treeData = res.result
        }
      })
    },
    initData (action, record) {
      if (action !== 'add') {
        const changeType = record.changeType
        this.showDetail = changeType.startsWith('1-1')

        // 检查是否为堡垒机相关变更（值为11）
        if (changeType === '11') {
          this.showBastionFields = true
          this.bastionApplyType = record.bastionApplyType || 'account'
          // 如果是资源申请类型，初始化资源项列表
          if (record.resourceList) {
            try {
              this.resourceList = record.resourceList.map(item => {
                return {
                  address: item.address || '',
                  relName: item.relName || '',
                  itsmChangeUuid: item.itsmChangeUuid || record.uuid,
                  validPeriod: [
                    item.startDate ? moment(item.startDate, 'YYYY-MM-DD HH:mm:ss') : null,
                    item.endDate ? moment(item.endDate, 'YYYY-MM-DD HH:mm:ss') : null
                  ].filter(Boolean)
                }
              })
            } catch (e) {
              console.error('解析资源项数据失败', e)
              this.resourceList = []
            }
          }
        } else {
          this.showBastionFields = false
          this.bastionApplyType = null
          this.resourceList = []
        }
      }

      this.$nextTick(() => {
        this.record = record
        this.$refs.appendix.doCreateUpload('ITSM', record)
        this.action = action
        if (action === 'add') {
          this.form.setFieldsValue({
            changeCategory: '1',
            phone: USER_OPERATOR,
            department: USER_DEPT_ID,
            applyDate: moment(),
            applicant: this.takeSelectLabel(this.personList, USER_PERSON_ID)
          })
        } else {
          this.recordId = record.uuid
          const data = {
            ...record,
            applicant: this.takeSelectLabel(this.personList, record.applicant),
            estimatedStartTime: moment(record.estimatedStartTime),
            estimatedEndTime: moment(record.estimatedEndTime),
            applyDate: moment(record.applyDate),
            resourceTypes: record.resourceTypes ? record.resourceTypes.split(',') : [],
            // 堡垒机相关字段
            bastionApplyType: record.bastionApplyType,
            loginName: record.loginName,
            email: record.email
          }
          if (record.changeCategory === '2') {
            this.SHOW_YSR = true
          } else {
            this.SHOW_YSR = false
          }
          console.log('编辑的原数据', data)
          // 查询审批记录
          this.queryHistory(record.uuid)
          this.form.setFieldsValue(data)
        }
      })
    },
    async handleSubmit () {
      this.form.validateFields(async (errors, values) => {
        if (!errors) {
          // 如果是堡垒机相关变更，先验证信息
          if (this.showBastionFields) {
            // 验证资源申请项
            if (this.bastionApplyType === 'resource' && this.resourceList.length === 0) {
              this.$message.error('资源申请类型至少需要添加一条资源信息')
              return
            }

            // 验证堡垒机信息 - 使用await等待异步验证完成
            const isValid = await this.validateBastionInfo()
            if (!isValid) {
              console.log('堡垒机信息验证失败，终止提交')
              return
            }
          }

          // 处理资源申请项
          let resourceItemsData = []
          if (this.showBastionFields && this.resourceList.length > 0) {
            resourceItemsData = this.resourceList.map(item => {
              return {
                address: item.address,
                relName: item.relName,
                accountId: item.accountId,
                itsmChangeUuid: item.itsmChangeUuid || this.recordId || '',
                startDate: item.validPeriod && item.validPeriod[0] ? moment(item.validPeriod[0]).format('YYYY-MM-DD HH:mm:ss') : null,
                endDate: item.validPeriod && item.validPeriod[1] ? moment(item.validPeriod[1]).format('YYYY-MM-DD HH:mm:ss') : null
              }
            })
          }

          const params = {
            changeCategory: values.changeCategory,
            changeType: values.changeType,
            applicant: values.applicant,
            phone: values.phone,
            department: values.department,
            ysr: values.ysr,
            applyDate: values.applyDate ? moment(values.applyDate).format('YYYY-MM-DD HH:mm:ss') : null,
            resourceTypes: values.resourceTypes.join(','),
            estimatedStartTime: values.estimatedStartTime ? moment(values.estimatedStartTime).format('YYYY-MM-DD HH:mm:ss') : null,
            estimatedEndTime: values.estimatedEndTime ? moment(values.estimatedEndTime).format('YYYY-MM-DD HH:mm:ss') : null,
            changeDescription: values.changeDescription,
            status: this.record ? this.record.status : null,
            resourceIp: values.resourceIp,
            databaseId: this.databaseObj.databaseId,
            databaseName: this.databaseObj.databaseName
          }

          // 添加堡垒机相关字段
          if (this.showBastionFields) {
            params.bastionApplyType = values.bastionApplyType
            params.loginName = values.loginName
            params.email = values.email
            // 无论哪种申请类型，都包含资源项数据
            params.resourceList = resourceItemsData
          }
          if (this.action === 'edit') {
            console.log('更新')
            console.log(this.recordId)
            updateITSM(requestBuilder('', {
              ...params,
              uuid: this.recordId
            })).then(res => {
              console.log('更新结果', res)
              if (res.code === '0000') {
                this.$emit('refresh')
                this.$message.success(res.message)
                this.handleCancel()
              } else {
                this.$message.error(res.message)
              }
            })
          } else if (this.action === 'add') {
            console.log('新增')
            creatNewITSM(requestBuilder('', {
              ...params
            })).then(res => {
              if (res.code === '0000') {
                // 新增成功
                this.$emit('refresh')
                this.$notification.success({
                  message: '系统消息',
                  description: '变更工单新增成功！'
                })
                // this.$message.success('新增成功')
                // 新增完成后不关闭窗口，action改为edit
                this.action = 'edit'
                // 赋值uuid
                this.recordId = res.result
                // this.handleCancel()
              } else {
                this.$message.error(res.message)
              }
            })
          } else {
            this.$message.error('a参数有误')
          }
        } else {
          this.$message.error('请检查字段是否填写完整！')
          console.log('validate false')
          console.log(errors)
        }
      })
    },
    edit (record) {
      this.$nextTick(() => {
        // this.form.resetFields()
        const data = {
          ...record,
          estimatedStartTime: moment(record.estimatedStartTime),
          estimatedEndTime: moment(record.estimatedEndTime),
          applyDate: moment(record.applyDate),
          resourceTypes: record.resourceTypes.split(',')
        }
        console.log('编辑的原数据', data)
        this.form.setFieldsValue(data)
      }, 1000)
    },
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || key
    },
    queryHistory (uuid) {
      approveApi.queryApproHis({ origin: 'ITSMWorkOrder', businessNum: uuid }).then(res => {
        this.approvalHistory = res.result
      })
    },
    changeTypeChange (value, label, extra) {
      console.log(value, label, extra)
      if (value.startsWith('1-1')) {
        this.showDetail = true
      } else {
        this.showDetail = false
      }

      // 检查是否为堡垒机相关变更（值为11）
      if (value === '11') {
        this.showBastionFields = true
        // 默认选择开通账号
        this.$nextTick(() => {
          this.form.setFieldsValue({
            bastionApplyType: 'account'
          })
          this.bastionApplyType = 'account'
        })
      } else {
        this.showBastionFields = false
        this.bastionApplyType = null
      }
    },

    // 处理堡垒机申请类型变更
    handleBastionApplyTypeChange (e) {
      this.bastionApplyType = e.target.value
      // 不再清空资源项列表，因为无论哪种类型都需要资源信息
    },

    // 添加资源项
    addResourceItem () {
      this.resourceList.push({
        itsmChangeUuid: this.record?.uuid,
        address: '',
        relName: '',
        validPeriod: []
      })
    },

    // 移除资源项
    removeResourceItem (index) {
      this.$confirm({
        title: '提示',
        content: '确认删除此项 ?',
        onOk: () => {
          this.resourceList.splice(index, 1)
        }
      })
    },

    // 验证登录名
    validateLoginName (rule, value, callback) {
      if (!value) {
        callback(new Error('请输入登录名'))
        return
      }

      // 检查长度：1-64个字符
      if (value.length < 1 || value.length > 64) {
        callback(new Error('登录名长度必须在1-64个字符之间'))
        return
      }

      // 检查不支持的字符：/\[]:;|=,+"?<>@*
      // 注意：在正则表达式中需要转义特殊字符
      const invalidChars = /[/\\[\]:;|=,+"?<>@*]/
      if (invalidChars.test(value)) {
        // 找出具体的无效字符
        const foundInvalidChars = value.match(/[/\\[\]:;|=,+"?<>@*]/g)
        const invalidCharsList = foundInvalidChars ? [...new Set(foundInvalidChars)].join('') : ''
        callback(new Error(`登录名包含不支持的字符：${invalidCharsList}`))
        return
      }

      // 验证通过
      callback()
    },

    // 验证IP格式
    validateIpFormat (item, event) {
      const ipValue = event.target.value
      if (!ipValue) return

      // 检查是否符合IP格式 xxx.xxx.xxx.xxx
      const ipRegex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/
      const match = ipValue.match(ipRegex)

      if (match) {
        // 验证每个部分是否在0-255范围内
        const isValid = match.slice(1).every(part => {
          const num = parseInt(part, 10)
          return num >= 0 && num <= 255
        })

        if (isValid) {
          // IP格式正确
          this.$message.success('IP格式正确')
        } else {
          // IP格式不正确
          this.$message.error('IP地址的每个部分必须在0-255范围内')
        }
      } else {
        // 不符合IP格式
        this.$message.warning('请输入正确的IP格式，如：***********')
      }
    },

    // 获取当前步骤索引
    getCurrentStepIndex () {
      if (!this.record || !this.record.status) return -1 // 返回-1表示没有当前步骤

      const status = this.record.status

      // 处理最终状态
      if (status === 'approval1003' || status === 'approval1011') {
        return this.finalSteps.length - 1 // 最后一步
      }

      // 处理第一个节点可能是新增(approval1009)的情况
      if (status === 'approval1009') {
        return 0 // 新增状态，显示为第一步
      }

      const currentIndex = this.finalSteps.findIndex(step => step.status === status)
      return currentIndex !== -1 ? currentIndex : -1
    },

    // 获取步骤状态
    getStepStatus (step) {
      // 如果没有记录或状态为空，所有步骤都显示为等待状态
      if (!this.record || !this.record.status) {
        return 'wait'
      }

      const currentStatus = this.record.status

      // 处理已取消状态：只有取消步骤显示为错误，其他都是等待
      if (currentStatus === 'approval1011') {
        if (step.status === 'approval1011') {
          return 'error' // 已取消显示为错误状态
        }
        return 'wait' // 其他步骤都显示为等待
      }

      // 处理审批完成状态：前面所有步骤都完成，最后一步也完成
      if (currentStatus === 'approval1003') {
        if (step.status === 'approval1003') {
          return 'finish' // 审批完成显示为完成状态
        }
        // 如果不是最终步骤，检查是否在流程中
        if (!step.isFinal) {
          return 'finish' // 前面的步骤都完成
        }
        return 'wait'
      }

      // 正常流程状态判断
      if (this.isCurrentStep(step)) {
        return 'process'
      } else if (this.isCompletedStep(step)) {
        return 'finish'
      }
      return 'wait'
    },

    // 获取步骤描述
    getStepDescription (step) {
      const currentStatus = this.record?.status

      // 处理最终状态的描述
      if (step.isFinal) {
        if (step.status === 'approval1003' && currentStatus === 'approval1003') {
          return '流程已完成'
        } else if (step.status === 'approval1011' && currentStatus === 'approval1011') {
          return '流程已取消'
        }
        return ''
      }

      // 处理已取消状态：所有非最终步骤都显示为"未执行"
      if (currentStatus === 'approval1011') {
        return '未执行'
      }

      // 处理审批完成状态：所有非最终步骤都显示为"已完成"
      if (currentStatus === 'approval1003') {
        return '已完成'
      }

      // 正常流程状态描述
      if (this.isCurrentStep(step)) {
        return '当前处理中'
      } else if (this.isCompletedStep(step)) {
        return '已完成'
      }
      return '等待处理'
    },

    // 获取当前步骤信息
    getCurrentStepInfo () {
      if (!this.record || !this.record.status) {
        return '请先新增当前变更工单！'
      }

      const currentStatus = this.record.status

      // 处理最终状态
      if (currentStatus === 'approval1003') {
        return ' 变更工单审批流程已完成！所有审批节点均已通过。'
      }

      if (currentStatus === 'approval1011') {
        return ' 变更工单已被取消，流程终止。'
      }

      // 处理新增状态
      if (currentStatus === 'approval1009') {
        return '当前处于"新增"阶段，请发起审批流程。'
      }

      const currentStep = this.finalSteps.find(step => step.status === currentStatus)
      if (!currentStep) return '未知流程状态'

      // 获取当前步骤在流程中的位置
      const totalSteps = this.finalSteps.length
      const currentIndex = this.finalSteps.findIndex(step => step.status === currentStatus)
      const completedSteps = currentIndex

      // 计算流程完成百分比
      const progressPercent = Math.round((completedSteps / totalSteps) * 100)

      return `当前处于"${currentStep.title}"阶段，流程进度 ${progressPercent}%。`
    },

    // 判断是否是当前步骤
    isCurrentStep (step) {
      if (!this.record || !this.record.status) return false

      const currentStatus = this.record.status

      // 处理最终状态
      if (currentStatus === 'approval1003' || currentStatus === 'approval1011') {
        return step.status === currentStatus
      }

      // 处理新增状态
      if (currentStatus === 'approval1009' && step.status === 'approval1009') {
        return true // 新增状态时，显示为当前步骤
      }

      return step.status === currentStatus
    },

    // 判断是否是已完成步骤
    isCompletedStep (step) {
      if (!this.record || !this.record.status) return false

      const currentStatus = this.record.status

      // 处理已取消状态：没有已完成的步骤
      if (currentStatus === 'approval1011') {
        return false
      }

      // 处理审批完成状态：所有非最终步骤都完成
      if (currentStatus === 'approval1003') {
        return !step.isFinal
      }

      // 新增状态时没有已完成的步骤
      if (currentStatus === 'approval1009') {
        return false
      }

      // 获取当前步骤的索引
      const currentStepIndex = this.finalSteps.findIndex(s => s.status === currentStatus)
      if (currentStepIndex === -1) return false

      // 获取步骤的索引
      const stepIndex = this.finalSteps.findIndex(s => s.status === step.status)
      if (stepIndex === -1) return false

      // 如果步骤索引小于当前步骤索引，则表示该步骤已完成
      return stepIndex < currentStepIndex
    },

    // 校验堡垒机相关信息
    async validateBastionInfo () {
      // 设置加载状态
      this.validating = true

      // 类型
      const resource = this.bastionApplyType === 'resource'
      const account = this.bastionApplyType === 'account'

      // 验证登录名
      const loginName = this.form.getFieldValue('loginName')
      if (!loginName) {
        this.$message.error('登录名不能为空！')
        this.validating = false
        return false
      } else {
        if (loginName.length < 1 || loginName.length > 64) {
          this.validating = false
          return this.$message.error('登录名长度必须在1-64个字符之间！')
        }

        // 检查不支持的字符：/\[]:;|=,+"?<>@*
        // 注意：在正则表达式中需要转义特殊字符
        const invalidChars = /[/\\[\]:;|=,+"?<>@*]/
        if (invalidChars.test(loginName)) {
          this.validating = false
          // 找出具体的无效字符
          const foundInvalidChars = loginName.match(/[/\\[\]:;|=,+"?<>@*]/g)
          const invalidCharsList = foundInvalidChars ? [...new Set(foundInvalidChars)].join('') : ''
          return this.$message.error(`登录名包含不支持的字符：${invalidCharsList}`)
        }
      }

      // 验证邮箱
      const email = this.form.getFieldValue('email')
      if (account && !email) {
        this.$message.error('开通账号时邮箱不能为空！')
        this.validating = false
        return false
      } else if (email) {
        // 验证邮箱格式
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
        if (!emailRegex.test(email)) {
          this.$message.error('邮箱格式不正确')
          this.validating = false
          return false
        }
      }

      // 验证资源项
      if (resource && this.resourceList.length === 0) {
        this.$message.error('资源申请类型至少需要添加一条资源信息')
        this.validating = false
        return false
      }

      // 验证资源项内容
      if (this.resourceList.length > 0) {
        // 检查是否有空的主机IP
        if (this.resourceList.some(item => !item.address)) {
          this.$message.error('申请资源的主机IP不能为空')
          this.validating = false
          return false
        }

        // 检查是否有不完整的有效期
        if (this.resourceList.some(item => !item.validPeriod || item.validPeriod.length !== 2)) {
          this.$message.error('申请资源的有效期不完整')
          this.validating = false
          return false
        }
      }

      // 准备验证数据
      const tempData = {
        ...this.record,
        loginName,
        email,
        bastionApplyType: this.bastionApplyType
      }
      this.$set(tempData, 'resourceList', this.resourceList)
      const param = requestBuilder('', tempData)

      try {
        // 调用API验证
        const res = await validateITSMInfo(param)
        if (res.code !== '0000') {
          this.$message.error(res.message)
          return false
        }

        // 处理返回的资源列表
        if (res.result && res.result.resourceList) {
          // 将匹配到的主机名塞入
          // 1. 将返回结果转换为 Map { address => item }
          const bMap = new Map(res.result.resourceList.map(item => [item.address, item]))

          // 2. 遍历当前资源列表，更新名称和账号ID
          this.resourceList.forEach(itemA => {
            const itemB = bMap.get(itemA.address)
            if (itemB) {
              itemA.relName = itemB.relName
              itemA.accountId = itemB.accountId
            }
          })
        }

        this.$message.success('堡垒机相关信息验证通过')
        return true
      } catch (error) {
        console.error('验证堡垒机信息失败', error)
        this.$message.error('验证堡垒机信息失败')
        return false
      } finally {
        // 无论成功还是失败，都重置加载状态
        this.validating = false
      }
    },
    changeCategoryChange (value, label, extra) {
      console.log(value)
      console.log(label)
      console.log(extra)
      if (value === '2') {
        this.SHOW_YSR = true
      }
    },
    isShowDeleteAndUpload () {
      return !!(this.record && this.record.status === 'approval1009')
    },
    changeDatabase (value, label, extra) {
      const item = this.databaseList.filter(item => item.value === value)[0]
      this.databaseObj = {
        databaseId: value,
        databaseName: item.label
      }
    }
  }
}
</script>

<style scoped>
/* 添加自定义样式 */
.a-form-item {
  margin-bottom: 24px;
  transition: all 0.3s;
}

.a-form-item:hover {
  transform: translateX(5px);
}

.custom-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.help-icon {
  color: #999;
  cursor: help;
  font-size: 14px;
}

.a-card {
  margin-top: 16px;
  background-color: #f0f2f5;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s;
}

.a-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.a-card .ant-card-head-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.a-select,
.a-input,
.a-textarea {
  width: 100%;
  border-radius: 4px;
  transition: all 0.3s;
}

.a-select:hover,
.a-input:hover,
.a-textarea:hover {
  border-color: #40a9ff;
}

.a-checkbox-group .a-checkbox {
  margin-bottom: 8px;
  transition: all 0.3s;
}

.a-checkbox-group .a-checkbox:hover {
  color: #40a9ff;
}

.a-timeline-item p {
  margin: 0;
  line-height: 1.8;
}

.a-input:focus,
.a-select:focus,
.a-textarea:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.a-button {
  background-color: #1890ff;
  color: #fff;
  border: none;
  border-radius: 4px;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  transition: all 0.3s;
}

.a-button:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.approval-timeline {
  padding: 16px;
}

.timeline-item {
  background: #fff;
  padding: 16px;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.timeline-item p {
  margin: 10px 0;
  color: #333;
  font-size: 14px;
}

.timeline-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  background: #fafafa;
}

/* 资源项自定义样式 */
.resource-section {
  margin-bottom: 24px;
  width: 100%;
}

.resource-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}

.resource-section-title {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.resource-item {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #fafafa;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.resource-item-row {
  display: flex;
  align-items: center;
}

.resource-item-field {
  display: flex;
  align-items: center;
}

.resource-item-label {
  min-width: 70px;
  margin-right: 8px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
}

.pointNone {
  pointer-events: none;
}

/* 处理信息样式 */
.handle-result-section {
  margin-bottom: 24px;
  width: 100%;
}

.handle-result-content {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.handle-result-message {
  display: flex;
  align-items: flex-start;
}

.handle-result-text {
  margin-left: 12px;
  flex: 1;
}

.handle-result-line {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 8px;
  color: #333;
  font-weight: 500;
}

/* 审批历史卡片样式 */
.history-card {
  background-color: #f5f7fa;
  border-radius: 12px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.history-card :deep(.ant-card-body) {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* 确保左右两侧的卡片高度一致 */
.ant-row {
  display: flex;
  flex-wrap: wrap;
}

.ant-row > .ant-col {
  display: flex;
  flex-direction: column;
}

.ant-form {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.ant-card {
  margin-bottom: 16px;
}

/* 最后一个卡片占满剩余空间 */
.ant-form > .ant-card:last-child {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.ant-form > .ant-card:last-child > .ant-card-body {
  flex: 1;
  overflow-y: auto;
}

/* 流程进度样式 - Ant Design Steps */
.progress-container {
  padding: 16px 0;
}

.progress-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 20px;
}

.workflow-steps {
  margin: 0 8px 20px;
}

/* 自定义 Ant Design Steps 样式 */
.workflow-steps :deep(.ant-steps-item-title) {
  font-size: 14px;
  font-weight: 500;
}

.workflow-steps :deep(.ant-steps-item-description) {
  font-size: 12px;
}

.workflow-steps :deep(.ant-steps-item-finish .ant-steps-item-description) {
  color: rgba(0, 0, 0, 0.65); /* 已完成步骤的描述文字颜色 */
}

.workflow-steps :deep(.ant-steps-item-process .ant-steps-item-icon) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.workflow-steps :deep(.ant-steps-item-finish .ant-steps-item-icon) {
  background-color: #E6F4FF;
  border-color: #1890ff;
}

.workflow-steps :deep(.ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon) {
  color: #1890ff;
}

.workflow-steps :deep(.ant-steps-item-finish .ant-steps-item-tail::after) {
  background-color: #1890ff;
}

.workflow-steps :deep(.ant-steps-item-finish .ant-steps-item-title) {
  color: rgba(0, 0, 0, 0.85); /* 默认黑色 */
}

.workflow-steps :deep(.ant-steps-item-process .ant-steps-item-title) {
  color: #1890ff;
}

.workflow-steps :deep(.ant-steps-item-wait .ant-steps-item-title) {
  color: rgba(0, 0, 0, 0.45);
}

.current-step-info {
  margin-top: 24px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.current-step-info:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  color: #1890ff;
  font-weight: 500;
  font-size: 14px;
}

.info-header .anticon {
  margin-right: 8px;
  font-size: 16px;
}

.info-content {
  color: #595959;
  font-size: 13px;
  line-height: 1.6;
  letter-spacing: 0.3px;
}
</style>
