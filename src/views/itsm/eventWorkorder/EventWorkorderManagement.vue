<template>
  <a-card title="事件管理" :bordered="false">
    <a-form layout="inline" :form="formData" class="search-form">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item label="事件号">
            <a-input
              v-model="queryParam.eventId"
              placeholder="请输入事件号"
              allowClear
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="受理人">
            <a-input
              v-model="queryParam.handler"
              placeholder="请输入受理人"
              allowClear
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="联系人">
            <a-input
              v-model="queryParam.contactPerson"
              placeholder="请输入联系人"
              allowClear
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="事件描述">
            <a-input
              v-model="queryParam.eventDescription"
              placeholder="请输入事件描述"
              allowClear
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item label="事件关闭">
            <a-select
              v-model="queryParam.isClosed"
              placeholder="请选择事件关闭状态"
              allowClear
              style="width: 100%"
              @change="$refs.table.refresh(true)"
            >
              <a-select-option value="Y">是</a-select-option>
              <a-select-option value="N">否</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="事件状态">
            <a-select
              v-model="queryParam.eventStatus"
              placeholder="请选择事件状态"
              allowClear
              style="width: 100%"
              @change="$refs.table.refresh(true)"
            >
              <a-select-option v-for="(item,key) in eventStatusList" :value="item.value" :key="key">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
<!--        <a-col :span="6">-->
<!--          <a-form-item label="申告开始日期">-->
<!--            <a-date-picker-->
<!--              v-model="queryParam.startDate"-->
<!--              style="width: 100%"-->
<!--              allowClear-->
<!--            />-->
<!--          </a-form-item>-->
<!--        </a-col>-->
<!--        <a-col :span="6">-->
<!--          <a-form-item label="申告结束日期">-->
<!--            <a-date-picker-->
<!--              v-model="queryParam.endDate"-->
<!--              style="width: 100%"-->
<!--              allowClear-->
<!--            />-->
<!--          </a-form-item>-->
<!--        </a-col>-->
      </a-row>
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item>
            <a-button type="primary" icon="search" @click="$refs.table.refresh(true)">
              查询
            </a-button>
            <a-button style="margin-left: 8px" @click="handleReset">
              重置
            </a-button>
            <a-button
              type="primary"
              icon="plus"
              style="margin-left: 8px"
              @click="showModal('add')"
            >
              新增事件
            </a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <s-table
      ref="table"
      :rowKey="(record) => record.uuid"
      :columns="columns"
      :data="loadData"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      :scroll="scroll"
    >
      <span
        slot="eventStatus"
        slot-scope="key"
      >{{ takeSelectLabel(eventStatusList, key) }}
      </span>
      <span
        slot="person"
        slot-scope="key"
      >{{ takeSelectLabel(personList, key) }}
      </span>
      <span
        slot="serviceType"
        slot-scope="key"
      >{{ takeSelectLabel(serviceTypeList, key) }}
      </span>
      <span
        slot="isClosed"
        slot-scope="key"
      >{{ transYesOrNo(key)}}
      </span>
      <div
        slot="action"
        slot-scope="text, record"
      >
        <template>
          <a
            style="margin: 3px;"
            @click.stop="showModal('view',record)"
          >查看</a >
        </template>
        <template>
          <a
            style="margin: 3px;"
            @click.stop="showModal('edit',record)"
          >编辑</a >
        </template>
        <template style="margin-left: 5px">
          <a-popconfirm
            title="确认删除该条记录？"
            ok-text="删除"
            cancel-text="取消"
            @confirm="confirm(record)"
            @cancel="cancel"
            placement="topRight"
          >
            <a href="#">删除</a>
          </a-popconfirm>
        </template>
      </div>
    </s-table>

    <!-- 使用 EventFormModal 组件 -->
    <EventFormModal
      ref="EventFormModal"
      @refresh="$refs.table.refresh(true)"
    />
  </a-card>
</template>

<script>
import EventFormModal from './modules/EventFormModal.vue'
import { STable } from '@/components'
import moment from 'moment'
import { requestBuilder, takeTreeByKey } from '@/utils/util'
import { queryEventList } from '@/api/device/itsm'
import * as baseApi from '@/api/system/base'
import { APPROVE_OPTIONS_HIDES } from '@/store/variable/approve'

const defaultQueryParam = {
  eventId: undefined,
  handler: undefined,
  contactPerson: undefined,
  eventDescription: undefined,
  isClosed: undefined,
  eventStatus: undefined
}
export default {
  name: 'EventManagement',
  components: {
    STable,
    EventFormModal
  },
  data () {
    return {
      queryParam: JSON.parse(JSON.stringify(defaultQueryParam)),
      columns: [
        // { title: 'uuid', dataIndex: 'uuid', width: 150 },
        { title: '事件号', dataIndex: 'workOrderNum', width: 150 },
        { title: '事件状态', dataIndex: 'eventStatus', width: 150, scopedSlots: { customRender: 'eventStatus' } },
        { title: '受理人', dataIndex: 'applicant', width: 150, scopedSlots: { customRender: 'person' } },
        { title: '生成时间',
          dataIndex: 'createDate',
          width: 150,
          customRender: (text, row, index) => {
            return moment(text).format('YYYY-MM-DD HH:mm:ss')
          }
        },
        { title: '联系人', dataIndex: 'contactPerson', width: 150 },
        { title: '性别', dataIndex: 'gender', width: 100 },
        { title: '职务', dataIndex: 'position', width: 150 },
        { title: '单位ID', dataIndex: 'unitId', width: 150 },
        { title: '单位', dataIndex: 'company', width: 150 },
        { title: '联系电话', dataIndex: 'contactInfo', width: 150 },
        { title: '地址', dataIndex: 'address', width: 150 },
        { title: '事件种类', dataIndex: 'eventCategory', width: 150 },
        { title: '服务类别', dataIndex: 'serviceType', width: 150, scopedSlots: { customRender: 'serviceType' } },
        { title: '事件描述', dataIndex: 'eventDescription', width: 200 },
        { title: '事件结果', dataIndex: 'eventResult', width: 200 },
        // { title: '备注', dataIndex: 'remarks', width: 200 },
        // { title: '派单时间', dataIndex: 'dispatchTime', width: 150 },
        // { title: '关闭时间', dataIndex: 'closeTime', width: 150 },
        // { title: '受理人ID', dataIndex: 'handlerId', width: 150 },
        // { title: '回访', dataIndex: 'followUp', width: 150 },
        // { title: '满意状态', dataIndex: 'satisfactionStatus', width: 150 },
        // { title: '回访内容', dataIndex: 'followUpContent', width: 200 },
        // { title: '回访时间', dataIndex: 'followUpTime', width: 150 },
        // { title: '级别', dataIndex: 'level', width: 150 },
        { title: '客服中心发起', dataIndex: 'isCallCenterInitiated', width: 150 },
        { title: '关联热线ID', dataIndex: 'relatedHotlineId', width: 150 },
        { title: '关闭', dataIndex: 'isClosed', width: 150, scopedSlots: { customRender: 'isClosed' } },
        {
          title: '操作',
          scopedSlots: { customRender: 'action' },
          fixed: 'right',
          width: 120
        }
      ],
      dataSource: [
      ],
      scroll: {
        x: '100%',
        // x: 1500,
        y: 'calc(45vh)',
        scrollToFirstRowOnChange: false
      },
      pagination: { current: 1, pageSize: 10, total: 0 },
      modalVisible: false,
      modalTitle: '',
      formData: {},
      loadData: parameter => {
        console.log(this.queryParam)
        const params = this.queryParam
        const queryParam = params
        const param = requestBuilder(
          '',
          queryParam,
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder)
        return queryEventList(param).then(res => {
          console.log(res)
          this.dataSource = res.result.data
          return res.result
        })
      },
      selectedRowKeys: [],
      eventStatusList: [],
      statusList: [],
      personList: [],
      serviceTypeList: []

    }
  },
  methods: {
    initOptions () {
      // eslint-disable-next-line no-unused-expressions
      baseApi.getCommboxById({
        id: 'basCodeByClassId',
        sqlParams: { codeClassId: 'itsm_event_status' }
      }).then(res => {
        if (res.code === '0000') {
          this.eventStatusList = res.result
        }
        // eslint-disable-next-line no-sequences
      })
      // 审批状态
      baseApi.getTreeById({ id: 'appro' }).then(res => {
        if (res.code === '0000') {
          const list = []
          for (const item of res.result) {
            if (APPROVE_OPTIONS_HIDES.includes(item.value)) {
              item.disabled = true
            }
            list.push(item)
          }
          this.statusList = list
          console.log('status list', this.statusList)
        }
      })
      // 人员
      baseApi.getCommboxById({ id: 'applyPerson' }).then(res => {
        if (res.code === '0000') {
          this.personList = res.result
        }
      })
      // 服务类别
      baseApi.getCommboxById({
        id: 'basCodeByClassId',
        sqlParams: { codeClassId: 'ITSM_change_type' }
      }).then(res => {
        if (res.code === '0000') {
          this.serviceTypeList = res.result
        }
      })
    },
    handleSearch () {
      // 查询逻辑
    },
    handleReset () {
      this.queryParam = JSON.parse(JSON.stringify(defaultQueryParam))
      this.$refs.table.refresh(true)
    },
    handleTableChange (pagination) {
      this.pagination = pagination
      this.fetchData()
    },
    showModal (action, record = {}) {
      this.$refs.EventFormModal.showModal(action, record)
    },
    handleSubmit (data) {
      // 提交表单逻辑
      this.modalVisible = false
    },
    handleCancel () {
      this.modalVisible = false
    },
    deleteEvent (record) {
      // 删除逻辑
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      console.log(this.selectedRows)
    },
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || key
    },
    transYesOrNo (key) {
      switch (key) {
        case 'Y':
          return '是'
        case 'N':
          return '否'
      }
    },
    cancel () {
      console.log('cancel')
    }
  },
  mounted () {
    this.initOptions()
  }
}
</script>

<style lang="less" scoped>
.search-form {
  margin-bottom: 16px;
}

:deep(.table-striped) {
  background-color: #fafafa;
}
</style>
