<template>
  <a-modal
    v-model="visible"
    v-if="visible"
    :title="title"
    :width="1200"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-row v-if="action !== 'view'" style="display: flex;justify-content: right">
      <a-col>
        <a-button type="primary" html-type="submit" @click="handleSubmit">
          保存
        </a-button>
      </a-col>
      <a-col>
        <a-button html-type="submit" @click="handleDispatchSubmit">
          保存派工信息
        </a-button>
      </a-col>
    </a-row>
    <a-tabs @change="tabChange" defaultActiveKey="1">
      <a-tab-pane key="1" tab="基本信息">
        <a-row :gutter="16">
          <a-form layout="vertical" ref="form" :form="form">
            <a-col :span="12">
              <a-card title="用户信息" :bordered="false" class="card-with-icon">
                <template slot="title">
                  <a-icon type="user" />
                  <span style="margin-left: 8px">用户信息</span>
                </template>
                <a-form-item label="联系方式">
                  <a-input
                    v-decorator="['contactInfo', { rules: [{ required: true, message: '请输入联系方式' }] }]"
                    placeholder="请输入联系方式"
                  />
                </a-form-item>
                <a-form-item label="联系人">
                  <a-input
                    v-decorator="['contactPerson', { rules: [{ required: true, message: '请输入联系人' }] }]"
                    placeholder="请输入联系人"
                  />
                </a-form-item>
                <a-form-item label="性别">
                  <a-radio-group v-decorator="['gender', { rules: [{ required: true, message: '请选择性别' }] }]">
                    <a-radio value="男">男</a-radio>
                    <a-radio value="女">女</a-radio>
                  </a-radio-group>
                </a-form-item>
                <a-form-item label="职务">
                  <a-input v-decorator="['position']" placeholder="请输入职务" />
                </a-form-item>
                <a-form-item label="公司">
                  <a-input v-decorator="['company', { rules: [{ required: true, message: '请输入公司' }] }]" placeholder="请选择公司" />
                </a-form-item>
                <a-form-item label="联系地址">
                  <a-input
                    v-decorator="['address', { rules: [{ required: true, message: '请输入联系地址' }] }]"
                    placeholder="请输入联系地址"
                  />
                </a-form-item>
              </a-card>
            </a-col>
            <a-col :span="12">
              <a-card title="事件信息" :bordered="false" class="card-with-icon">
                <template slot="title">
                  <a-icon type="info-circle" />
                  <span style="margin-left: 8px">事件信息</span>
                </template>
                <a-form-item label="事件分类">
                  <a-radio-group
                    v-decorator="['eventCategory', { rules: [{ required: true, message: '请输入事件分类' }] }]"
                  >
                    <a-radio v-for="item in eventCategoryList" :value="item.value" :key="item.value">
                      {{ item.label }}
                    </a-radio>
                  </a-radio-group>
                </a-form-item>
                <a-form-item label="事件级别">
                  <a-select
                    v-decorator="['eventLevel', { rules: [{ required: true, message: '请选择事件级别' }] }]"
                    placeholder="请选择事件级别"
                  >
                    <a-select-option value="1">1</a-select-option>
                    <a-select-option value="2">2</a-select-option>
                    <a-select-option value="3">3</a-select-option>
                    <a-select-option value="4">4</a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item label="服务类别">
                  <a-tree-select
                    v-decorator="['serviceType', { rules: [{ required: true, message: '请选择服务类别' }] }]"
                    style="width: 100%"
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    :tree-data="treeData"
                    placeholder="请选择服务类别"
                  />
                </a-form-item>
                <a-form-item label="事件描述">
                  <a-textarea
                    v-decorator="['eventDescription']"
                    placeholder="请输入事件描述"
                  />
                </a-form-item>
              </a-card>
            </a-col>
          </a-form>
        </a-row>
      </a-tab-pane>
      <a-tab-pane key="2" tab="处理信息" :forceRender="true">
        <dispatch-form
          ref="dispatch"
          :dispatch-info="dispatchData"
        />
      </a-tab-pane>
    </a-tabs>
    <div class="attachment-section">
      <a-divider>附件</a-divider>
      <appendix
        ref="appendix"
        text=""
        :tableDisplay="true"
        :businessName="businessName"
        :keyId="recordId"
      />
    </div>
  </a-modal>
</template>

<script>
import Appendix from '@/views/system/components/AppendixNew'
import * as baseApi from '@/api/system/base'
import { creatEvent, updateEvent } from '@/api/device/itsm'
import { requestBuilder } from '@/utils/util'
import DispatchForm from '@/views/itsm/eventWorkorder/modules/dispatchForm.vue'

export default {
  name: 'EventFormModal',
  components: {
    DispatchForm,
    Appendix
  },
  data () {
    return {
      visible: false,
      form: null,
      form1: null,
      action: '',
      title: '',
      formData: {
        contactInfo: '',
        contactPerson: '',
        gender: '男',
        position: '',
        company: '',
        address: '',
        eventId: '',
        eventCategory: '',
        eventLevel: '',
        serviceType: '',
        eventDescription: '',
        processing: '',
        startProcessing: null,
        endProcessing: null,
        processingResult: ''
      },
      dispatchData: {},
      treeData: [],
      eventCategoryList: [
        {
          label: '咨询',
          value: '1'
        },
        {
          label: '需求/变更',
          value: '2'
        },
        {
          label: '故障',
          value: '3'
        },
        {
          label: '投诉/意见和建议',
          value: '4'
        }
      ],
      businessName: 'eventWorkOrder',
      recordId: '',
      type: 1
    }
  },
  created () {
    this.form = this.$form.createForm(this)
  },
  mounted () {
    this.initOptions()
  },
  methods: {
    initOptions () {
      // eslint-disable-next-line no-unused-expressions
      baseApi.getCommboxById({
        id: 'basCodeByClassId',
        sqlParams: { codeClassId: 'itsm_newChange_changeType' }
      }).then(res => {
        if (res.code === '0000') {
          this.changeCategorys = res.result
        }
        // eslint-disable-next-line no-sequences
      })
      // 变更种类
      baseApi.getTreeById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'ITSM_change_type' } }).then(res => {
        if (res.code === '0000') {
          this.treeData = res.result
        }
      })
    },
    initData (action, record) {
      console.log(action)
      this.$nextTick(() => {
        this.record = record
        this.$refs.appendix.doCreateUpload(this.businessName, record)
        this.action = action
        if (action === 'add') {
          this.form.setFieldsValue({

          })
        } else {
          this.recordId = record.uuid
          const data = {
            ...record
          }
          console.log('编辑的原数据', data)
          // 查询审批记录
          this.form.setFieldsValue(data)
        }
      })
    },
    showModal (action, record = {}) {
      this.visible = true
      this.title = action === 'add' ? '新增事件' : '编辑事件'
      if (action === 'edit') {
        this.formData = { ...record }
      }
      this.initData(action, record)
    },
    handleOk () {
      // 处理表单提交
      this.$emit('submit', this.formData)
      this.visible = false
    },
    handleCancel () {
      this.visible = false
      this.form.resetFields()
    },
    handleSubmit () {
      console.log(this.form.getFieldsValue())
      // 保存
      this.form.validateFields((errors, values) => {
        if (!errors) {
          console.log('校验value', values)
          const params = {
            ...values
          }
          console.log(params)
          if (this.action === 'edit') {
            console.log('更新')
            updateEvent(requestBuilder('', {
              ...params,
              uuid: this.recordId
            })).then(res => {
              console.log('更新结果', res)
              if (res.code === '0000') {
                this.$emit('refresh')
                this.$message.success(res.message)
                this.handleCancel()
              } else {
                this.$message.error(res.message)
              }
            })
          } else if (this.action === 'add') {
            console.log('新增')
            creatEvent(requestBuilder('', {
              ...params
            })).then(res => {
              if (res.code === '0000') {
                // 新增成功
                this.$emit('refresh')
                this.$message.success('新增成功')
                this.handleCancel()
              } else {
                this.$message.error(res.message)
              }
            })
          } else {
            this.$message.error('a参数有误')
          }
          this.$emit('refresh')
        } else {
          console.log('validate false')
        }
      })
    },
    tabChange (activeKey) {
      console.log(activeKey)
      if (activeKey === '2') {
        this.$nextTick(() => {
          this.$refs.dispatch.initData(this.record)
        })
      }
    },
    handleDispatchSubmit () {
      this.$refs.dispatch.updateData()
    }
  }
}
</script>

<style scoped>
.card-with-icon {
  margin-bottom: 16px;
  background-color: #fafafa;
}

.card-with-icon :deep(.ant-card-head) {
  background-color: #f0f2f5;
}

.attachment-section {
  margin-top: 24px;
}

.ant-tabs {
  margin-bottom: -24px;
}

.ant-form-item {
  margin-bottom: 16px;
}

.ant-radio-group {
  width: 100%;
}

.ant-card {
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
.disable {
  pointer-events: none;
}
</style>
