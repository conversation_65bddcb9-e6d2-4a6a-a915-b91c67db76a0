<template>
  <section>
    <a-row :gutter="16">
      <a-col :span="12">
        <a-card title="派工信息" :bordered="false" class="card-with-icon">
          <template slot="title">
            <a-icon type="team" />
            <span style="margin-left: 8px">派工信息</span>
          </template>
          <a-table
            :columns="dispatchColumns"
            :dataSource="engineerData"
            rowKey="engineerId"
          />
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card title="工单描述" :bordered="false" class="card-with-icon">
          <template slot="title">
            <a-icon type="file-text" />
            <span style="margin-left: 8px">工单描述</span>
          </template>
          <a-form layout="vertical" ref="form" :form="form">
            <a-form-item label="处理">
              <a-radio-group
                v-decorator="['status']"
              >
                <a-radio value="处理中">处理中</a-radio>
                <a-radio value="已处理">已处理</a-radio>
                <a-radio value="现场处理">现场处理</a-radio>
                <a-radio value="5*8外">5*8外</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="开始处理">
              <a-date-picker
                v-decorator="['startDate']"
                style="width: 100%"
              />
            </a-form-item>
            <a-form-item label="处理完成">
              <a-date-picker
                v-decorator="['endDate']"
                style="width: 100%"
              />
            </a-form-item>
            <a-form-item label="处理过程和结果">
              <a-textarea
                v-decorator="['processAndResult']"
                placeholder="请输入处理过程和结果"
              />
            </a-form-item>
          </a-form>
        </a-card>
      </a-col>
    </a-row>
  </section>
</template>

<style scoped lang='less'>

</style>
<script>
import { selectDispatchInfoByEventId, updateDispatchInfo } from '@/api/device/itsm'
import { requestBuilder } from '@/utils/util'
import moment from 'moment'

export default {
  data () {
    return {
      form: this.$form.createForm(this),
      engineerData: [],
      dispatchColumns: [
        { title: '工程师ID', dataIndex: 'engineerId' },
        { title: '工程师姓名', dataIndex: 'engineerName' },
        { title: '状态', dataIndex: 'status' }
      ],
      dispatchInfo: {}
    }
  },
  methods: {
    initData (record) {
      selectDispatchInfoByEventId(requestBuilder('', { eventUuid: record.uuid })).then(res => {
        console.log('获取派工信息', res)
        if (res.code === '0000' && res.result) {
          const info = res.result
          this.dispatchInfo = info
          // 派工信息
          this.engineerData = [
            {
              engineerId: info.engineerId ? info.engineerId : null,
              engineerName: info.engineerName ? info.engineerName : null
            }
          ]
          // 工单描述
          const descriprion = {
            status: info.status ? info.status : null,
            startDate: info.startDate ? moment(info.startDate) : null,
            endDate: info.endDate ? moment(info.endDate) : null,
            processAndResult: info.processAndResult ? info.processAndResult : null
          }
          this.form.setFieldsValue(descriprion)
        }
      })
    },
    updateData () {
      const fieldsValue = this.form.getFieldsValue()
      const param = {
        uuid: this.dispatchInfo.uuid,
        status: fieldsValue.status ? fieldsValue.status : null,
        startDate: fieldsValue.startDate ? moment(fieldsValue.startDate).format('YYYY-MM-DD HH:mm:ss') : null,
        endDate: fieldsValue.endDate ? moment(fieldsValue.endDate).format('YYYY-MM-DD HH:mm:ss') : null,
        processAndResult: fieldsValue.processAndResult ? fieldsValue.processAndResult : null
      }
      updateDispatchInfo(requestBuilder('', param)).then(res => {
        if (res.code === '0000') { this.$message.success('保存成功') }
      })
    }
  }
}
</script>
<style scoped>
.card-with-icon {
  margin-bottom: 16px;
  background-color: #fafafa;
}

.card-with-icon :deep(.ant-card-head) {
  background-color: #f0f2f5;
}

.attachment-section {
  margin-top: 24px;
}

.ant-tabs {
  margin-bottom: -24px;
}

.ant-form-item {
  margin-bottom: 16px;
}

.ant-radio-group {
  width: 100%;
}

.ant-card {
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
.disable {
  pointer-events: none;
}
</style>
