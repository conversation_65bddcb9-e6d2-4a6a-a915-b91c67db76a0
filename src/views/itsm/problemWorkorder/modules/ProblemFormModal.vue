<template>
  <a-modal
    :title="selectedRecord ? '编辑问题' : '新增问题'"
    :visible="visible"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :width="1200"
    :style="{ height: 'calc(80vh -100px)' }"
  >
    <a-row :gutter="16">
      <!-- 左侧：表单内容 -->
      <a-col>
        <a-form :form="form" layout="vertical">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="问题来源">
                <a-select
                  v-decorator="['resource', { rules: [{ required: true, message: '请选择问题来源' }] }]"
                  placeholder="请选择问题来源"
                >
                  <a-select-option value="事件升级">事件升级</a-select-option>
                  <a-select-option value="维护中提出">维护中提出</a-select-option>
                  <a-select-option value="趋势分析">趋势分析</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="优先级">
                <a-select
                  v-decorator="['priority', { rules: [{ required: true, message: '请选择优先级' }] }]"
                  placeholder="请选择优先级"
                >
                  <a-select-option value="关键">关键</a-select-option>
                  <a-select-option value="重要">重要</a-select-option>
                  <a-select-option value="普通">普通</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="问题分类">
                <a-select
                  v-decorator="['category', { rules: [{ required: true, message: '请选择问题分类' }] }]"
                  placeholder="请选择问题分类"
                >
                  <a-select-option value="核心系统">核心系统</a-select-option>
                  <a-select-option value="桌面终端">桌面终端</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="问题描述">
            <a-textarea
              v-decorator="['description', { rules: [{ required: true, message: '请输入问题描述' }] }]"
              :rows="4"
              placeholder="请输入问题描述"
            />
          </a-form-item>

          <!-- 措施及实施计划表格 -->
          <a-form-item label="措施及实施计划">
            <div style="display: flex;justify-content: space-between;">
              <a-button type="primary" :disabled="disabled" icon="plus" @click="handleAddMeasure" style="margin-bottom: 8px">
                新增措施
              </a-button>
              <a-button type="primary" icon="plus" @click="saveHandle" style="margin-bottom: 8px">
                保存
              </a-button>
            </div>
            <s-table
              ref="table"
              :columns="measureColumns"
              :data="loadData"
              :scroll="scroll"
              :showPagination="false"
              :bordered="false"
              :immediate="true"
              rowKey="uuid"
            >
              <template
                slot="editCellInput"
                slot-scope="text, record, index, column"
              >
                <edit-cell-input
                  :ref="`editCellInput_${column.key}`"
                  :text="text"
                  :check="false"
                  :opened="false"
                  @change="doCellChange(record, column.key, $event.value)"
                  @confirm="doCellChange(record, column.key, $event.value)"
                  @blur="blurHandle(column.key)"
                />
              </template>
              <template
                slot="datePicker"
                slot-scope="text, record, index, column"
              >
                <a-date-picker
                  :value="moment(text)"
                  @change="(dateValue) => datePickerChangeHandle(dateValue, record, column.key)"
                />
              </template>
              <template slot="operation" slot-scope="text, record, index">
                <a-button type="link" @click="saveMeasureHandle(index, record)" icon="check">保存</a-button>
                <a-button type="link" @click="handleDeleteMeasure(index, record)" icon="delete">删除</a-button>
              </template>
            </s-table>
          </a-form-item>

          <!-- 附件上传 -->
          <a-form-item label="附件">
            <Appendix
              ref="appendix"
              text=""
              :tableDisplay="true"
              :businessName="businessName"
              :keyId="recordId"
            />
          </a-form-item>
        </a-form>
      </a-col>

      <!-- 右侧：审批历史 -->
      <!-- <a-col :span="8">
        <div class="approval-history">
          <h3>审批历史</h3>
          <a-timeline>
            <a-timeline-item v-for="(item, index) in approvalHistory" :key="index">
              <div class="approval-item">
                <p class="approver">{{ item.approver }} - {{ item.action }}</p>
                <p class="time">{{ item.time }}</p>
                <p v-if="item.comment" class="comment">备注：{{ item.comment }}</p>
                <p class="status" :class="item.status">{{ item.status }}</p>
              </div>
            </a-timeline-item>
          </a-timeline>
        </div>
      </a-col> -->
    </a-row>
  </a-modal>
</template>

<script>
import Appendix from '@/views/system/components/AppendixNew'
import { EditCellInput, STable } from '@/components'
import moment from 'moment'
import {
  createMeasure,
  creatIssue,
  deleteMeasure,
  queryMeasureList,
  updateIssue,
  updateMeasure
} from '@/api/device/itsm'
import { requestBuilder } from '@/utils/util'
import { tableMixin } from '@/utils/tableMixin'

moment.locale('zh_CN')

export default {
  name: 'ProblemFormModal',
  mixins: [tableMixin],
  components: {
    Appendix,
    STable,
    EditCellInput
  },
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    selectedRecord: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      form: this.$form.createForm(this),
      businessName: 'problemWorkorder',
      scroll: {
        x: '100%',
        y: '35vh',
        scrollToFirstRowOnChange: false
      },
      tempList: [],
      loadData: parameter => {
        const param = {
          issueId: this.recordId
        }
        return queryMeasureList(requestBuilder('', param)).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '查询失败！'
            })
            return
          }
          const result = res.result.map(item => { return { ...item, completeLimit: moment(item.completeLimit) } }) || []
          const list = [...this.tempList, ...result]
          return Promise.resolve(list)
        })
      },
      fileList: [],
      approvalHistory: [
        {
          approver: '张三',
          action: '提交问题',
          time: '2024-03-20 09:00:00',
          status: 'submitted',
          comment: '紧急处理'
        },
        {
          approver: '李四',
          action: '审核通过',
          time: '2024-03-20 10:30:00',
          status: 'approved',
          comment: '同意处理'
        },
        {
          approver: '王五',
          action: '开始处理',
          time: '2024-03-20 11:00:00',
          status: 'processing'
        }
      ],
      measureColumns: [
        {
          title: '措施',
          dataIndex: 'measure',
          scopedSlots: { customRender: 'editCellInput' },
          key: 'measure',
          check: true,
          width: 200
        },
        {
          title: '责任人',
          dataIndex: 'responsible',
          scopedSlots: { customRender: 'editCellInput' },
          key: 'responsible',
          check: true,
          width: 200
        },
        {
          title: '完成时限',
          dataIndex: 'completeLimit',
          scopedSlots: { customRender: 'datePicker' },
          key: 'completeLimit',
          check: true,
          width: 200
        },
        {
          title: '检验人',
          dataIndex: 'checkPerson',
          scopedSlots: { customRender: 'editCellInput' },
          key: 'checkPerson',
          check: true,
          width: 200
        },
        {
          title: '操作',
          align: 'center',
          key: 'operation',
          scopedSlots: { customRender: 'operation' },
          check: true,
          width: 200
        }
      ],
      saveFlag: false,
      recordId: ''
    }
  },
  watch: {
    selectedRecord: {
      handler (val) {
        if (val) {
          this.recordId = val?.uuid || ''
          this.$nextTick(() => {
            this.form.setFieldsValue(val)
            this.fileList = val.attachments || []
            this.approvalHistory = val.approvalHistory || this.approvalHistory

            this.$refs.table.refresh()
            this.$refs.appendix.doCreateUpload(this.businessName, val)
          })
        }
      },
      immediate: true
    }
  },
  computed: {
    disabled () {
      const { recordId, saveFlag } = this.$data
      return !(recordId || saveFlag)
    }
  },
  methods: {
    moment,
    saveHandle () {
      this.form.validateFields(async (err, values) => {
        if (err) {
          return
        }
        const param = requestBuilder(
          '',
          {
            ...values,
            ...(this.recordId ? { uuid: this.recordId } : {})
          }
        )
        const res = this.recordId ? await updateIssue(param) : await creatIssue(param)
        const flag = res.code !== '0000'
        const status = flag ? 'error' : 'success'
        const action = this.recordId ? '措施更新' : '措施新增'
        const msg = `${action}${flag ? '失败' : '成功'}！`
        this.$notification[status]({
          message: '系统消息',
          description: res.message || msg
        })
        // 保存成功后设置saveFlag
        this.saveFlag = true
      })
    },
    doCellChange (record, key, value) {
      record[key] = value
    },
    datePickerChangeHandle (dateValue, record, key) {
      const value = moment(dateValue).format('YYYY-MM-DD HH:mm:ss')
      this.doCellChange(record, key, value)
    },
    async saveMeasureHandle (index, record) {
      const { uuid } = record
      const param = requestBuilder(
        '',
        {
          ...record,
          ...(uuid ? { uuid } : {})
        }
      )
      console.log('uuid', uuid, 'record', record, 'param', param)
      const res = uuid ? await updateMeasure(param) : await createMeasure(param)
      const flag = res.code !== '0000'
      const status = flag ? 'error' : 'success'
      const action = uuid ? '更新' : '新增'
      const msg = `${action}${flag ? '失败' : '成功'}！`
      this.$notification[status]({
        message: '系统消息',
        description: res.message || msg
      })

      if (!flag) {
        this.tempList.splice(index, 1)
        this.$refs.table.refresh()
      }
    },
    blurHandle (key) {
      this.$nextTick(() => {
        this.$refs[`editCellInput_${key}`].doConfirm()
      })
    },
    handleSubmit () {
      this.handleCancel()
      // this.form.validateFields((err, values) => {
      //   if (!err) {
      //     this.loading = true
      //     const formData = {
      //       ...values,
      //       measures: this.tempList,
      //       attachments: this.fileList
      //     }
      //     // TODO: 调用API保存数据
      //     setTimeout(() => {
      //       this.loading = false
      //       this.$emit('success')
      //     }, 1000)
      //   }
      // })
    },
    handleCancel () {
      this.form.resetFields()
      this.tempList = []
      this.fileList = []
      this.$emit('close')
    },
    handleAddMeasure () {
      this.tempList.unshift({
        key: Date.now(),
        measure: '',
        responsible: '',
        completeLimit: moment().format('YYYY-MM-DD HH:mm:ss'),
        checkPerson: '',
        issueId: this.recordId
      })
      this.$refs.table.refresh()
    },
    handleDeleteMeasure (index, record) {
      const { uuid } = record
      this.$confirm({
        title: '确认删除',
        content: '确定要删除这条记录吗？',
        onOk: () => {
          if (uuid) {
            deleteMeasure(requestBuilder('', { uuid })).then(res => {
              const flag = res.code !== '0000'
              const status = flag ? 'error' : 'success'
              const msg = `删除${flag ? '失败' : '成功'}！`
              this.$notification[status]({
                message: '系统消息',
                description: res.message || '删除！'
              })
              this.$refs.table.refresh()
            })
          } else {
            this.tempList.splice(index, 1)
            this.$refs.table.refresh()
          }
        }
      })
    },
    beforeUpload (file) {
      this.fileList = [...this.fileList, file]
      return false
    },
    handleRemove (file) {
      const index = this.fileList.indexOf(file)
      const newFileList = this.fileList.slice()
      newFileList.splice(index, 1)
      this.fileList = newFileList
    }
  }
}
</script>

<style scoped>
.approval-history {
  padding: 0 16px;
  border-left: 1px solid #e8e8e8;
}

.approval-item {
  margin-bottom: 8px;
}

.approver {
  font-weight: 500;
  margin-bottom: 4px;
}

.time {
  color: #999;
  font-size: 12px;
  margin-bottom: 4px;
}

.comment {
  color: #666;
  font-size: 13px;
  margin-bottom: 4px;
}

.status {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status.submitted {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status.approved {
  background-color: #f6ffed;
  color: #52c41a;
}

.status.processing {
  background-color: #fff7e6;
  color: #fa8c16;
}
</style>
