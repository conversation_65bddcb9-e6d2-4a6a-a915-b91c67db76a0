<template>
  <a-card title="问题工单管理" :bordered="false">
    <a-form layout="inline" class="search-form">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item label="问题来源">
            <a-select v-model="filters.resource" placeholder="请选择问题来源" @change="handleSearch" allowClear style="width: 100%">
              <a-select-option value="事件升级">事件升级</a-select-option>
              <a-select-option value="维护中提出">维护中提出</a-select-option>
              <a-select-option value="趋势分析">趋势分析</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="优先级">
            <a-select v-model="filters.priority" placeholder="请选择优先级" @change="handleSearch" allowClear style="width: 100%">
              <a-select-option value="关键">关键</a-select-option>
              <a-select-option value="重要">重要</a-select-option>
              <a-select-option value="普通">普通</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="问题分类">
            <a-select v-model="filters.category" placeholder="请选择问题分类" @change="handleSearch" allowClear style="width: 100%">
              <a-select-option value="核心系统">核心系统</a-select-option>
              <a-select-option value="桌面终端">桌面终端</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="发起人">
            <a-input v-model="filters.initiator" placeholder="请输入发起人" @pressEnter="handleSearch" allowClear />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item label="问题描述">
            <a-input v-model="filters.description" placeholder="请输入问题描述" @pressEnter="handleSearch" allowClear />
          </a-form-item>
        </a-col>
        <a-col :span="18">
          <a-form-item>
            <a-button type="primary" icon="search" @click="handleSearch"> 查询 </a-button>
            <a-button style="margin-left: 8px" @click="handleReset"> 重置 </a-button>
            <a-button type="primary" style="margin-left: 8px" icon="plus" @click="handleAdd"> 新增问题 </a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <s-table
      ref="table"
      :columns="columns"
      :data="loadData"
      :scroll="scroll"
      :showPagination="true"
      :bordered="false"
      :immediate="true"
      :rowClassName="rowClassName"
      rowKey="uuid"
    >
      <template slot="operation" slot-scope="text, record">
        <a @click="handleEdit(record)">编辑</a>
        <a-divider type="vertical" />
        <a @click="handleDelete(record)">删除</a>
      </template>
    </s-table>

    <problem-form-modal
      :visible="modalVisible"
      :selectedRecord="currentRecord"
      @close="handleModalClose"
      @success="handleModalSuccess"
    />
  </a-card>
</template>

<script>
import { requestBuilder, deepUpdate } from '@/utils/util'
import { STable } from '@/components'
import { queryIssue, deleteIssue } from '@/api/device/itsm'
import { tableMixin } from '@/utils/tableMixin'

export default {
  name: 'ProblemWorkorderManagement',
  mixins: [tableMixin],
  components: {
    STable,
    ProblemFormModal: () => import('./modules/ProblemFormModal')
  },
  data () {
    return {
      filters: {
        resource: undefined,
        priority: undefined,
        category: undefined,
        initiator: '',
        description: ''
      },
      scroll: {
        x: '100%',
        scrollToFirstRowOnChange: false
      },
      rowClassName: (record, index) => (index % 2 === 1 ? 'table-striped' : ''),
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          key: 'index',
          check: true,
          width: 20,
          customRender: (text, record, index) => index + 1
        },
        {
          title: '问题来源',
          dataIndex: 'resource',
          key: 'resource',
          check: true,
          width: 50
        },
        {
          title: '优先级',
          dataIndex: 'priority',
          key: 'priority',
          check: true,
          width: 50
        },
        {
          title: '问题分类',
          dataIndex: 'category',
          key: 'category',
          check: true,
          width: 50
        },
        {
          title: '发起人',
          dataIndex: 'initiator',
          key: 'initiator',
          check: true,
          width: 50
        },
        {
          title: '问题描述',
          dataIndex: 'description',
          key: 'description',
          check: true,
          width: 100
        },
        {
          title: '操作',
          key: 'operation',
          scopedSlots: { customRender: 'operation' },
          check: true,
          width: 50
        }
      ],
      loadData: parameter => {
        const param = requestBuilder(
          '',
          this.filters,
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder
        )
        return queryIssue(param).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '查询失败！'
            })
            return Promise.reject(res)
          }
          return res.result
        })
      },
      loading: false,
      modalVisible: false,
      currentRecord: null
    }
  },
  mounted () {
  },
  methods: {
    handleSearch () {
      this.$refs.table.refresh()
    },
    handleReset () {
      this.filters = {
        resource: undefined,
        priority: undefined,
        category: undefined,
        initiator: '',
        description: ''
      }
      this.handleSearch()
    },
    handleAdd () {
      this.currentRecord = null
      this.modalVisible = true
    },
    handleEdit (record) {
      this.currentRecord = { ...record }
      this.modalVisible = true
    },
    handleDelete (record) {
      this.$confirm({
        title: '确认删除',
        content: '确定要删除这条记录吗？',
        onOk: () => {
          deleteIssue(requestBuilder('', record)).then(res => {
            const flag = res.code !== '0000'
            const status = flag ? 'error' : 'success'
            const msg = `删除${flag ? '失败' : '成功'}！`
            this.$notification[status]({
              message: '系统消息',
              description: res.message || '删除！'
            })
          }).then(() => {
            this.handleSearch()
          })
        }
      })
    },
    handleModalClose () {
      this.modalVisible = false
      this.currentRecord = null
      this.handleSearch()
    },
    handleModalSuccess () {
      this.handleModalClose()
      this.handleSearch()
    }
  }
}
</script>

<style lang="less" scoped>
.search-form {
  margin-bottom: 16px;
}

:deep(.table-striped) {
  background-color: #fafafa;
}
</style>
