import {
  generatorLayoutRouter,
  generatorViewsRouter
} from '@/router/generator-routers'

// 路由汇总
export const generatorComponents = {
  // 布局基础页面
  ...generatorLayoutRouter(
    require.context('@/layouts', false, /^((\.{0,2}\/(?!_\w*\/?))[^/]+)+\.vue$/i)
  ),

  // 业务相关页面
  ...generatorViewsRouter(
    require.context('@/views/material', true, /^((\.{0,2}\/(?!modules\/|_\w*\/?))[^/]+)+\.vue$/i),
    require.context('@/views/system', true, /^((\.{0,2}\/(?!modules\/|_\w*\/?))[^/]+)+\.vue$/i),
    require.context('@/views/itsm', true, /^((\.{0,2}\/(?!modules\/|_\w*\/?))[^/]+)+\.vue$/i)
  )
}
