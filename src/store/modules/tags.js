import Vue from 'vue'
import VueStorage from 'vue-ls'
import config from '@/config/defaultSettings'

// 配置 vue.ls
Vue.use(VueStorage, config.storageOptions)

// 数据类型判断
const itString = function (str) {
  return Object.prototype.toString.call(str)
}
const itType = function (val) {
  return itString(val).replace(/^\[[^\s\]]+\s*([^\s\]]+)]$/, '$1')
}
const isArray = function (arr, bool) {
  if (itType(arr) === 'Array') {
    return bool === true
      ? arr.length > 0
      : true
  }
  return false
}
const isObject = function (obj, bool) {
  if (itType(obj) === 'Object') {
    return bool === true
      ? Object.keys(obj).length > 0
      : true
  }
  return false
}
const isFunction = function (obj) {
  return itType(obj) === 'Function'
}

// vue.ls 缓存
const _stackTags = []
const _cachedTags = []
const _visitedTags = Vue.ls.get('visitedTags') || []
const _currentTag = {}
const _component = {}

// 标签页
const tags = {
  state: {
    stackTags: _stackTags,
    cachedTags: _cachedTags,
    visitedTags: _visitedTags,
    currentTag: _currentTag,
    component: _component
  },

  getters: {
    stackTags: state => state.stackTags,
    cachedTags: state => state.cachedTags,
    visitedTags: state => state.visitedTags,
    currentTag: state => state.currentTag
  },

  mutations: {
    ADD_CACHED_TAGS: (state, tags) => {
      let cachedTags = []
      if (isArray(state.cachedTags)) {
        cachedTags = Object.assign([], state.cachedTags)
      }
      if (isObject(tags, true)) {
        tags = [tags]
      }
      if (isArray(tags, true)) {
        for (const tag of tags) {
          const meta = tag.meta || {}
          const name = meta.componentName
          if (cachedTags.includes(name)) {
            continue
          }
          if (meta.noCache !== true) {
            cachedTags.push(name)
          }
        }
        state.cachedTags = cachedTags
      }
    },
    ADD_VISITED_TAGS: (state, tags) => {
      // 访问记录
      let visitedTags = []
      if (isArray(state.visitedTags)) {
        visitedTags = Object.assign([], state.visitedTags)
      }
      if (isObject(tags, true)) {
        tags = [tags]
      }
      if (isArray(tags, true)) {
        for (const tag of tags) {
          const meta = tag.meta || {}
          const match = meta.match || 'path'
          const isExternal = match === 'external'
          if (
            visitedTags.some(v => (!isExternal ? v[match] === tag[match] : v.meta[match] === tag.meta[match]))
          ) {
            state.currentTag = tag
            continue
          }
          const cloneTag = Object.assign(
            {},
            {
              fullPath: tag.fullPath,
              alias: tag.alias,
              path: tag.path,
              hash: tag.hash,
              meta: tag.meta,
              name: tag.name,
              query: tag.query,
              params: tag.params,
              title: tag.meta.title || '未定义',
              matched: tag.matched.map(tag => ({
                path: tag.path,
                name: tag.name,
                meta: tag.meta
              }))
            }
          )
          state.currentTag = cloneTag
          visitedTags.push(cloneTag)
        }
        state.visitedTags = visitedTags
        Vue.ls.set('visitedTags', visitedTags)
      }
      // 历史记录
      let stackTags = []
      if (isArray(state.stackTags)) {
        stackTags = Object.assign([], state.stackTags)
      }
      if (isObject(tags, true)) {
        tags = [tags]
      }
      if (isArray(tags, true)) {
        for (const tag of tags) {
          const meta = tag.meta || {}
          const match = meta.match || 'path'
          const isExternal = match === 'external'
          if (
            stackTags.some(v => (!isExternal ? v[match] === tag[match] : v.meta[match] === tag.meta[match]))
          ) {
            const key = stackTags.findIndex(v => (!isExternal ? v[match] === tag[match] : v.meta[match] === tag.meta[match]))
            const ref = stackTags.find(v => (!isExternal ? v[match] === tag[match] : v.meta[match] === tag.meta[match]))
            stackTags.splice(key, 1)
            stackTags.unshift(ref)
            continue
          }
          const cloneTag = Object.assign(
            {},
            {
              fullPath: tag.fullPath,
              alias: tag.alias,
              path: tag.path,
              hash: tag.hash,
              meta: tag.meta,
              name: tag.name,
              query: tag.query,
              params: tag.params,
              title: tag.meta.title || '未定义',
              matched: tag.matched.map(tag => ({
                path: tag.path,
                name: tag.name,
                meta: tag.meta
              }))
            }
          )
          stackTags.unshift(cloneTag)
        }
        state.stackTags = stackTags
      }
    },
    DEL_CACHED_TAGS: (state, tags) => {
      let cachedTags = []
      if (isArray(state.cachedTags)) {
        cachedTags = Object.assign([], state.cachedTags)
      }
      if (isObject(tags, true)) {
        tags = [tags]
      }
      if (isArray(tags, true)) {
        for (const tag of tags) {
          const meta = tag.meta || {}
          const name = meta.componentName
          for (const n of cachedTags) {
            if (n === name) {
              const index = cachedTags.indexOf(n)
              cachedTags.splice(index, 1)
              break
            }
          }
        }
        state.cachedTags = cachedTags
      }
    },
    DEL_VISITED_TAGS: (state, tags) => {
      // 访问记录
      let visitedTags = []
      if (isArray(state.visitedTags)) {
        visitedTags = Object.assign([], state.visitedTags)
      }
      if (isObject(tags, true)) {
        tags = [tags]
      }
      if (isArray(tags, true)) {
        for (const tag of tags) {
          const meta = tag.meta || {}
          const match = meta.match || 'path'
          const isExternal = match === 'external'
          for (const [k, v] of visitedTags.entries()) {
            if (!isExternal ? v[match] === tag[match] : v.meta[match] === tag.meta[match]) {
              visitedTags.splice(k, 1)
              break
            }
          }
        }
        state.visitedTags = visitedTags
        Vue.ls.set('visitedTags', visitedTags)
      }
      // 历史记录
      let stackTags = []
      if (isArray(state.stackTags)) {
        stackTags = Object.assign([], state.stackTags)
      }
      if (isObject(tags, true)) {
        tags = [tags]
      }
      if (isArray(tags, true)) {
        for (const tag of tags) {
          const meta = tag.meta || {}
          const match = meta.match || 'path'
          const isExternal = match === 'external'
          for (const [k, v] of stackTags.entries()) {
            if (!isExternal ? v[match] === tag[match] : v.meta[match] === tag.meta[match]) {
              stackTags.splice(k, 1)
              break
            }
          }
        }
        state.stackTags = stackTags
      }
    },
    DEL_OTHERS_CACHED_TAGS: (state, tags) => {
      const cachedTags = []
      let lastCachedTags = []
      if (isArray(state.cachedTags)) {
        lastCachedTags = Object.assign([], state.cachedTags)
      }
      if (isObject(tags, true)) {
        tags = [tags]
      }
      if (isArray(tags, true)) {
        for (const tag of tags) {
          const meta = tag.meta || {}
          const name = meta.componentName
          for (const n of lastCachedTags) {
            if (n === name) {
              const index = lastCachedTags.indexOf(n)
              cachedTags.push(lastCachedTags[index])
              break
            }
          }
        }
        state.cachedTags = cachedTags
      }
    },
    DEL_OTHERS_VISITED_TAGS: (state, tags) => {
      // 访问记录
      const visitedTags = []
      let lastVisitedTags = []
      if (isArray(state.visitedTags)) {
        lastVisitedTags = Object.assign([], state.visitedTags)
      }
      if (isObject(tags, true)) {
        tags = [tags]
      }
      if (isArray(tags, true)) {
        for (const tag of tags) {
          const meta = tag.meta || {}
          const match = meta.match || 'path'
          const isExternal = match === 'external'
          for (const [k, v] of lastVisitedTags.entries()) {
            if (!isExternal ? v[match] === tag[match] : v.meta[match] === tag.meta[match]) {
              visitedTags.push(lastVisitedTags[k])
              break
            }
          }
        }
        state.visitedTags = visitedTags
        Vue.ls.set('visitedTags', visitedTags)
      }
      // 历史记录
      const stackTags = []
      let lastStackTags = []
      if (isArray(state.stackTags)) {
        lastStackTags = Object.assign([], state.stackTags)
      }
      if (isObject(tags, true)) {
        tags = [tags]
      }
      if (isArray(tags, true)) {
        for (const tag of tags) {
          const meta = tag.meta || {}
          const match = meta.match || 'path'
          const isExternal = match === 'external'
          for (const [k, v] of lastStackTags.entries()) {
            if (!isExternal ? v[match] === tag[match] : v.meta[match] === tag.meta[match]) {
              stackTags.push(lastStackTags[k])
              break
            }
          }
        }
        state.stackTags = stackTags
      }
    },
    DEL_ALL_CACHED_TAGS: (state, tags) => {
      state.cachedTags = []
    },
    DEL_ALL_VISITED_TAGS: (state, tags) => {
      state.stackTags = []
      state.visitedTags = []
      Vue.ls.set('visitedTags', [])
    },
    UPDATE_VISITED_TAGS: (state, tags) => {
      // 访问记录
      let visitedTags = []
      if (isArray(state.visitedTags)) {
        visitedTags = Object.assign([], state.visitedTags)
      }
      if (isObject(tags, true)) {
        tags = [tags]
      }
      if (isArray(tags, true)) {
        for (const tag of tags) {
          const meta = tag.meta || {}
          const match = meta.match || 'path'
          const isExternal = match === 'external'
          for (const v of visitedTags) {
            if (!isExternal ? v[match] === tag[match] : v.meta[match] === tag.meta[match]) {
              Object.assign(v, {
                fullPath: tag.fullPath,
                alias: tag.alias,
                path: tag.path,
                hash: tag.hash,
                meta: tag.meta,
                name: tag.name,
                query: tag.query,
                params: tag.params,
                title: tag.meta.title || '未定义',
                matched: tag.matched.map(tag => ({
                  path: tag.path,
                  name: tag.name,
                  meta: tag.meta
                }))
              })
              break
            }
          }
        }
        state.visitedTags = visitedTags
        Vue.ls.set('visitedTags', visitedTags)
      }
      // 历史记录
      let stackTags = []
      if (isArray(state.stackTags)) {
        stackTags = Object.assign([], state.stackTags)
      }
      if (isObject(tags, true)) {
        tags = [tags]
      }
      if (isArray(tags, true)) {
        for (const tag of tags) {
          const meta = tag.meta || {}
          const match = meta.match || 'path'
          const isExternal = match === 'external'
          for (const v of stackTags) {
            if (!isExternal ? v[match] === tag[match] : v.meta[match] === tag.meta[match]) {
              Object.assign(v, {
                fullPath: tag.fullPath,
                alias: tag.alias,
                path: tag.path,
                hash: tag.hash,
                meta: tag.meta,
                name: tag.name,
                query: tag.query,
                params: tag.params,
                title: tag.meta.title || '未定义',
                matched: tag.matched.map(tag => ({
                  path: tag.path,
                  name: tag.name,
                  meta: tag.meta
                }))
              })
              break
            }
          }
        }
        state.stackTags = stackTags
      }
    },
    INJECT_MULTIPLE_TABS: (state, node) => {
      state.component = node
    },
    CALL_MULTIPLE_TABS_CLOSE: (state, tags) => {
      if (!tags) {
        tags = [state.currentTag]
      }
      if (isFunction(state.component.closeArray)) {
        state.component.closeArray(tags)
      }
    }
  },

  actions: {
    AddTags ({ dispatch, commit, state }, tags) {
      return Promise.all([
        dispatch('AddCachedTags', tags),
        dispatch('AddVisitedTags', tags)
      ]).then(() => ({
        stackTags: [...state.stackTags],
        cachedTags: [...state.cachedTags],
        visitedTags: [...state.visitedTags]
      }))
    },
    AddCachedTags ({ dispatch, commit, state }, tags) {
      return Promise.resolve(commit('ADD_CACHED_TAGS', tags))
        .then(() => ({
          stackTags: [...state.stackTags],
          cachedTags: [...state.cachedTags],
          visitedTags: [...state.visitedTags]
        }))
    },
    AddVisitedTags ({ dispatch, commit, state }, tags) {
      return Promise.resolve(commit('ADD_VISITED_TAGS', tags))
        .then(() => ({
          stackTags: [...state.stackTags],
          cachedTags: [...state.cachedTags],
          visitedTags: [...state.visitedTags]
        }))
    },
    DelTags ({ dispatch, commit, state }, tags) {
      return Promise.all([
        dispatch('DelCachedTags', tags),
        dispatch('DelVisitedTags', tags)
      ]).then(() => ({
        stackTags: [...state.stackTags],
        cachedTags: [...state.cachedTags],
        visitedTags: [...state.visitedTags]
      }))
    },
    DelCachedTags ({ dispatch, commit, state }, tags) {
      return Promise.resolve(commit('DEL_CACHED_TAGS', tags))
        .then(() => ({
          stackTags: [...state.stackTags],
          cachedTags: [...state.cachedTags],
          visitedTags: [...state.visitedTags]
        }))
    },
    DelVisitedTags ({ dispatch, commit, state }, tags) {
      return Promise.resolve(commit('DEL_VISITED_TAGS', tags))
        .then(() => ({
          stackTags: [...state.stackTags],
          cachedTags: [...state.cachedTags],
          visitedTags: [...state.visitedTags]
        }))
    },
    DelOthersTags ({ dispatch, commit, state }, tags) {
      return Promise.all([
        dispatch('DelOthersCachedTags', tags),
        dispatch('DelOthersVisitedTags', tags)
      ]).then(() => ({
        stackTags: [...state.stackTags],
        cachedTags: [...state.cachedTags],
        visitedTags: [...state.visitedTags]
      }))
    },
    DelOthersCachedTags ({ dispatch, commit, state }, tags) {
      return Promise.resolve(commit('DEL_OTHERS_CACHED_TAGS', tags))
        .then(() => ({
          stackTags: [...state.stackTags],
          cachedTags: [...state.cachedTags],
          visitedTags: [...state.visitedTags]
        }))
    },
    DelOthersVisitedTags ({ dispatch, commit, state }, tags) {
      return Promise.resolve(commit('DEL_OTHERS_VISITED_TAGS', tags))
        .then(() => ({
          stackTags: [...state.stackTags],
          cachedTags: [...state.cachedTags],
          visitedTags: [...state.visitedTags]
        }))
    },
    DelAllTags ({ dispatch, commit, state }, tags) {
      return Promise.all([
        dispatch('DelAllCachedTags', tags),
        dispatch('DelAllVisitedTags', tags)
      ]).then(() => ({
        stackTags: [...state.stackTags],
        cachedTags: [...state.cachedTags],
        visitedTags: [...state.visitedTags]
      }))
    },
    DelAllCachedTags ({ dispatch, commit, state }, tags) {
      return Promise.resolve(commit('DEL_ALL_CACHED_TAGS', tags))
        .then(() => ({
          stackTags: [...state.stackTags],
          cachedTags: [...state.cachedTags],
          visitedTags: [...state.visitedTags]
        }))
    },
    DelAllVisitedTags ({ dispatch, commit, state }, tags) {
      return Promise.resolve(commit('DEL_ALL_VISITED_TAGS', tags))
        .then(() => ({
          stackTags: [...state.stackTags],
          cachedTags: [...state.cachedTags],
          visitedTags: [...state.visitedTags]
        }))
    },
    RefreshAllTags ({ dispatch, commit, state }, tags) {
      return Promise.resolve(dispatch('DelAllCachedTags'))
        .then(() => ({
          stackTags: [...state.stackTags],
          cachedTags: [...state.cachedTags],
          visitedTags: [...state.visitedTags]
        }))
    },
    UpdateVisitedTags ({ dispatch, commit, state }, tags) {
      return Promise.resolve(commit('UPDATE_VISITED_TAGS', tags))
        .then(() => ({
          stackTags: [...state.stackTags],
          cachedTags: [...state.cachedTags],
          visitedTags: [...state.visitedTags]
        }))
    },
    DelCurrentTag ({ dispatch, commit, state }, tag) {
      return Promise.all([
        dispatch('DelCachedTags', state.currentTag),
        dispatch('DelVisitedTags', state.currentTag)
      ]).then(() => ({
        stackTags: [...state.stackTags],
        cachedTags: [...state.cachedTags],
        visitedTags: [...state.visitedTags]
      }))
    },
    InjectMultipleTabs ({ dispatch, commit, state }, node) {
      return Promise.resolve(commit('INJECT_MULTIPLE_TABS', node))
        .then(() => ({
          stackTags: [...state.stackTags],
          cachedTags: [...state.cachedTags],
          visitedTags: [...state.visitedTags]
        }))
    },
    CallMultipleTabsClose ({ dispatch, commit, state }, tags) {
      return Promise.resolve(commit('CALL_MULTIPLE_TABS_CLOSE', tags))
        .then(() => ({
          stackTags: [...state.stackTags],
          cachedTags: [...state.cachedTags],
          visitedTags: [...state.visitedTags]
        }))
    }
  }
}

export default tags
