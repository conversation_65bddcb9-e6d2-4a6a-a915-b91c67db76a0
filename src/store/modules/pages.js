import { queryPersonalTableRecord, queryPersonalCockpitRecord } from '@/api/material/table'
import { requestBuilder } from '@/utils/util'
import * as flowApi from '@/api/system/flow'
import Vue from 'vue'
import { ORG_ID } from '@/store/mutation-types'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_MD_ENV = USER_ORG_ID === '1.100.104'

const MARK_TODO_LIST = 'TodoList'
const REGEX_TODO_CARD = /^TodoCard\w/
const REGEX_TODO_REPORT = /^TodoReport\w/
const REGEX_TODO_KPI_VIEW = /^TodoKpiView\w/

const pages = {
  state: {
    todoNum: 0,
    todoCardGroup: [],
    todoReportGroup: [],
    todoKpiViewGroup: [],
    todoTableGroup: [],
    todoCockpitGroup: []
  },

  getters: {
    todoNum: state => state.todoNum,
    todoCardGroup: state => state.todoCardGroup,
    todoReportGroup: state => state.todoReportGroup,
    todoKpiViewGroup: state => state.todoKpiViewGroup,
    todoTableGroup: state => state.todoTableGroup,
    todoCockpitGroup: state => state.todoCockpitGroup
  },

  mutations: {
    SET_TODONUM: (state, nums) => {
      state.todoNum = nums
    },
    SET_TODO_CARD_GROUP: (state, cards) => {
      state.todoCardGroup = [...cards]
    },
    SET_TODO_REPORT_GROUP: (state, reports) => {
      state.todoReportGroup = [...reports]
    },
    SET_TODO_KPI_VIEW_GROUP: (state, kpiViews) => {
      state.todoKpiViewGroup = [...kpiViews]
    },
    SET_TODO_TABLE_GROUP: (state, tables) => {
      state.todoTableGroup = [...tables]
    },
    SET_TODO_COCKPIT_GROUP: (state, group) => {
      state.todoCockpitGroup = [...group]
    }
  },

  actions: {
    SettodoNum ({ commit }, role) {
      const param = requestBuilder(
        '',
        {
          isSummary: IS_MD_ENV ? 'Y' : ''
        }
      )
      return flowApi.findTodoCount(param).then(res => {
        if (res.code === '0000') {
          let todoTotalCount = 0
          for (const item of res.result) {
            todoTotalCount += item.count || 0
          }
          commit('SET_TODONUM', todoTotalCount)
        }
      })
    },
    SettodoCockpitGroup ({ commit }, role) {
      queryPersonalCockpitRecord(requestBuilder('', {})).then(res => {
        commit('SET_TODO_COCKPIT_GROUP', res.result)
      })
    },
    SetTodoTableGroup ({ commit }, role) {
      queryPersonalTableRecord(requestBuilder('', {})).then(res => {
        commit('SET_TODO_TABLE_GROUP', res.result)
      })
    },
    SetTodoGroup ({ commit }, role) {
      const todoCardGroup = []
      const todoReportGroup = []
      const todoKpiViewGroup = []
      const { permissions = [] } = role
      for (const item of permissions) {
        if (MARK_TODO_LIST === item.permissionId && item.actionEntitySet) {
          for (const entitySet of item.actionEntitySet) {
            if (REGEX_TODO_CARD.test(entitySet.action)) {
              todoCardGroup.push({
                name: entitySet.action,
                describe: entitySet.describe
              })
            }
            if (REGEX_TODO_REPORT.test(entitySet.action)) {
              todoReportGroup.push({
                name: entitySet.action,
                describe: entitySet.describe
              })
            }
            if (REGEX_TODO_KPI_VIEW.test(entitySet.action)) {
              todoKpiViewGroup.push({
                name: entitySet.action,
                describe: entitySet.describe
              })
            }
          }
        }
      }
      commit('SET_TODO_CARD_GROUP', todoCardGroup)
      commit('SET_TODO_REPORT_GROUP', todoReportGroup)
      commit('SET_TODO_KPI_VIEW_GROUP', todoKpiViewGroup)
    }
  }
}

export default pages
