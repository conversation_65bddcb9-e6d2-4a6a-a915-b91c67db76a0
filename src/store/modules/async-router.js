import { constantRouterMap } from '@/config/router.config'
import { generatorDynamicRouter } from '@/router/generator-routers'

const permission = {
  state: {
    routers: constantRouterMap,
    addRouters: []
  },

  getters: {
    addRouters: state => state.addRouters
  },

  mutations: {
    SET_ROUTERS: (state, routers) => {
      state.addRouters = routers
      state.routers = constantRouterMap.concat(routers)
    }
  },

  actions: {
    GenerateRoutes ({ commit }, config) {
      return generatorDynamicRouter(config).then(routers => {
        commit('SET_ROUTERS', routers)
      })
    }
  }
}

export default permission
