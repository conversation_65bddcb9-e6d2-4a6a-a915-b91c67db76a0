import Vue from 'vue'
import { welcome, requestBuilder } from '@/utils/util'
import { login, logout, txLogin, loginSys, getUserInfo } from '@/api/system/login'

import {
  ORG_ID,
  ORG_NAME,
  DEPT_ID,
  DEPT_NAME,
  DATA_FLAG,
  PERSON_ID,
  CC_TOKEN,
  PERSON_NAME,
  ACCESS_TOKEN,
  ROLE_NAME,
  CHECK_GROUP_NUM,
  OPERATOR,
  DATA_FLAG_DEVICE,
  DATA_FLAG_GOODS,
  MOBILE_PHONE,
  TENANT
} from '@/store/mutation-types'

const user = {
  state: {
    token: '',
    personName: '',
    welcome: '',
    avatar: '',
    roles: [],
    info: {}
  },

  getters: {
    token: state => state.token,
    avatar: state => state.avatar,
    nickname: state => state.personName,
    welcome: state => state.welcome,
    roles: state => state.roles,
    userInfo: state => state.info
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, { name, welcome }) => {
      state.personName = name
      state.welcome = welcome
    },
    SET_AVATAR: (state, avatar) => {
      // 暂时先写死
      state.avatar = 'https://cdn2.ettoday.net/images/2194/d2194378.jpg'
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_INFO: (state, info) => {
      state.info = info
    }
  },

  actions: {
    // 门户认证登录
    Login ({ dispatch, commit }, userInfo) {
      // 清空缓存标签页
      dispatch('DelAllTags', [])
      // 调用登录接口
      return login(requestBuilder('login', userInfo)).then(response => {
        if (response.code === '0000') {
          const expires = 7 * 24 * 60 * 60 * 1000
          const result = response.result || {}
          const data = result.data || {}
          const orgId = data.orgId || ''
          const userNo = data.userNo || ''
          const personSysId = data.personSysId || ''
          const ccToken = data.ccToken || ''
          const token = result.token || ''
          Vue.ls.set(ACCESS_TOKEN, token, expires)
          Vue.ls.set(PERSON_ID, personSysId, expires)
          Vue.ls.set(OPERATOR, userNo, expires)
          Vue.ls.set(CC_TOKEN, ccToken, expires)
          Vue.ls.set(ORG_ID, orgId, expires)
          commit('SET_TOKEN', token)
        } else {
          return Promise.reject(new Error(response.message || '登录失败！'))
        }
      })
    },
    LoginTX ({ dispatch, commit }, userInfo = {}) {
      // 清空缓存标签页
      dispatch('DelAllTags', [])
      // 调用登录接口
      console.log(11111)
      return txLogin(requestBuilder('login', userInfo)).then(response => {
        if (response.code === '0000') {
          const expires = 7 * 24 * 60 * 60 * 1000
          const result = response.result || {}
          const data = result.data || {}
          const orgId = data.orgId || ''
          const userNo = data.userNo || ''
          const personSysId = data.personSysId || ''
          const ccToken = data.ccToken || ''
          const token = result.token || ''
          Vue.ls.set(ACCESS_TOKEN, token, expires)
          Vue.ls.set(PERSON_ID, personSysId, expires)
          Vue.ls.set(OPERATOR, userNo, expires)
          Vue.ls.set(CC_TOKEN, ccToken, expires)
          Vue.ls.set(ORG_ID, orgId, expires)
          commit('SET_TOKEN', token)
        } else {
          return Promise.reject(new Error(response.message || '登录失败！'))
        }
      })
    },

    // 系统常规登录
    LoginSys ({ dispatch, commit }, userInfo) {
      // 清空缓存标签页
      dispatch('DelAllTags', [])
      // 调用登录接口
      return loginSys(requestBuilder('login', userInfo)).then(response => {
        if (response.code === '0000') {
          const expires = 7 * 24 * 60 * 60 * 1000
          const result = response.result || {}
          const data = result.data || {}
          const orgId = data.orgId || ''
          const userNo = data.userNo || ''
          const personSysId = data.personSysId || ''
          const checkGroupNum = data.checkGroupNum || ''
          const token = result.token || ''
          const ccToken = data.ccToken || ''
          const tenant = data.tenant || ''
          Vue.ls.set(ACCESS_TOKEN, token, expires)
          Vue.ls.set(PERSON_ID, personSysId, expires)
          Vue.ls.set(OPERATOR, userNo, expires)
          Vue.ls.set(CC_TOKEN, ccToken, expires)
          Vue.ls.set(ORG_ID, orgId, expires)
          Vue.ls.set(TENANT, tenant, expires)
          Vue.ls.set(CHECK_GROUP_NUM, checkGroupNum, expires)
          commit('SET_TOKEN', token)
        } else {
          return Promise.reject(new Error(response.message || '登录失败！'))
        }
      })
    },

    // 获取用户信息
    GetUserInfo ({ commit }, userInfo) {
      return getUserInfo(requestBuilder('getInfo', userInfo))
        .then(response => {
          const result = response.result || {}
          const role = result.role || {}
          const avatar = result.avatar || ''
          const orgName = result.orgName || ''
          const dataFlag = result.dataFlag || ''
          const dataFlagGoods = result.dataFlagGoods || ''
          const dataFlagDevice = result.dataFlagDevice || ''
          const personName = result.personName || ''
          const departmentName = result.departmentName || ''
          const departmentSysId = result.departmentSysId || ''
          const roleName = result.roleName || ''
          const checkGroupNum = result.checkGroupNum || ''
          const permissions = role.permissions || []
          const mobilePhone = result.mobilePhone || ''
          // 角色及权限校验
          if (role && permissions.length > 0) {
            permissions.map(per => {
              if (per.actionEntitySet && per.actionEntitySet.length > 0) {
                per.actionList = per.actionEntitySet.map(action => { return action.action })
              }
            })
            role.permissionList = permissions.map(permission => { return permission.permissionId })
            commit('SET_NAME', { name: personName, welcome: welcome() })
            commit('SET_AVATAR', avatar)
            commit('SET_ROLES', role)
            commit('SET_INFO', result)
            Vue.ls.set(ORG_NAME, orgName)
            Vue.ls.set(DEPT_ID, departmentSysId)
            Vue.ls.set(DEPT_NAME, departmentName)
            Vue.ls.set(ROLE_NAME, roleName)
            Vue.ls.set(CHECK_GROUP_NUM, checkGroupNum)
            Vue.ls.set(DATA_FLAG, dataFlag)
            Vue.ls.set(PERSON_NAME, personName)
            Vue.ls.set(MOBILE_PHONE, mobilePhone)
            Vue.ls.set(DATA_FLAG_GOODS, dataFlagGoods)
            Vue.ls.set(DATA_FLAG_DEVICE, dataFlagDevice)
            return Promise.resolve(response)
          } else {
            return Promise.reject(new Error(response.message || '获取用户失败！'))
          }
        })
    },

    // 系统常规登出
    Logout ({ dispatch, commit, state }) {
      return logout(requestBuilder('loginout', {}))
        .catch(() => {})
        .finally(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          dispatch('DelAllTags', [])
          Vue.ls.remove(ACCESS_TOKEN)
          Vue.ls.remove(PERSON_ID)
          Vue.ls.remove(OPERATOR)
          Vue.ls.remove(ORG_ID)
          Vue.ls.remove(ORG_NAME)
          Vue.ls.remove(DEPT_ID)
          Vue.ls.remove(DEPT_NAME)
          Vue.ls.remove(ROLE_NAME)
          Vue.ls.remove(CC_TOKEN)
          Vue.ls.remove(CHECK_GROUP_NUM)
          Vue.ls.remove(DATA_FLAG)
          Vue.ls.remove(PERSON_NAME)
          Vue.ls.remove(DATA_FLAG_DEVICE)
          Vue.ls.remove(DATA_FLAG_GOODS)
          Vue.ls.remove(MOBILE_PHONE)
          Vue.ls.remove(TENANT)
        })
    }

  }
}

export default user
