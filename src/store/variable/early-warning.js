// 预警信息

export const EARLY_WARNING_PURCHASE_KEY = '物资采购'
export const EARLY_WARNING_SERVICE_KEY = '综合服务'

// 预警场景:集团OA
export const EARLY_WARNING_JTOA_SCENE = '集团OA'
export const EARLY_WARNING_JTOA_PURCHASE_VALUE = `当前项目类别为:${EARLY_WARNING_PURCHASE_KEY},金额应不小于100万,`
export const EARLY_WARNING_JTOA_SERVICE_VALUE = `当前项目类别为:${EARLY_WARNING_SERVICE_KEY},金额应不小于200万,`

// 预警场景:电子招采
export const EARLY_WARNING_TENDER_SCENE = '电子招采'
export const EARLY_WARNING_TENDER_PURCHASE_VALUE = `当前项目类别为:${EARLY_WARNING_PURCHASE_KEY},金额应不小于100万,`
export const EARLY_WARNING_TENDER_PURCHASE_VALUE2 = `当前项目类别为:${EARLY_WARNING_PURCHASE_KEY},满足金额不小于100万,但未走集团OA`

export const EARLY_WARNING_TENDER_SERVICE_VALUE = `当前项目类别为:${EARLY_WARNING_SERVICE_KEY},金额应不小于200万,`
export const EARLY_WARNING_TENDER_SERVICE_VALUE2 = `当前项目类别为:${EARLY_WARNING_SERVICE_KEY},满足金额不小于200万,但未走集团OA`

// 预警场景:简单询价
export const EARLY_WARNING_RFQLINE_SCENE = '简单询价'
export const EARLY_WARNING_RFQLINE_VALUE = `计划金额应小于20万,`

export const EARLY_WARNING_CONTRACT_SCENE = '合同付款'

// 支付方式
export const CONTRACT_PAYMENT_MODE_BATCH_CODE = 'batch'
export const CONTRACT_PAYMENT_MODE_BATCH_TEXT = '分批次付款'

export const CONTRACT_PAYMENT_MODE_DIS_CODE = 'disposable'
export const CONTRACT_PAYMENT_MODE_DIS_TEXT = '一次性付款'
