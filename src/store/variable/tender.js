// 招标类型 招标
export const TENDER = '00'
// 招标类型 非招
export const NON_TENDER = '01'

// 非招采购方式
export const TENDER_METHOD_ONE = '11' // 简单询价
export const TENDER_METHOD_TWO = '12' // 单一来源
export const TENDER_METHOD_THREE = '13' // 竞争性谈判
export const TENDER_METHOD_FOUR = '14' // 竞价

// 招标节点
export const TENDER_NODE_ONE = 'A001' // 招标公告发布（含重新招标)
export const TENDER_NODE_TWO = 'A002' // 报名截止时间
export const TENDER_NODE_THREE = 'A003' // 开标
export const TENDER_NODE_FOUR = 'A004' // 评标（评标结果公示)
export const TENDER_NODE_FIVE = 'A005' // 定标（中标结果公告)
export const TENDER_NODE_SIX = 'A006' // 流标
export const TENDER_NODE_SEVEN = 'A007' // 采购方式变更
export const TENDER_NODE_EIGHT = 'A008' // 终止招标

// 非招节点
export const NON_TENDER_NODE_ONE = 'B001' // 采购单发布
export const NON_TENDER_NODE_TWO = 'B002' // 供应商响应截止
export const NON_TENDER_NODE_THREE = 'B003' // 报价开启
export const NON_TENDER_NODE_FOUR = 'B004' // 采购结果提交
export const NON_TENDER_NODE_FIVE = 'B005' // 结果公告发布
export const NON_TENDER_NODE_SIX = 'B006' // 流标
export const NON_TENDER_NODE_SEVEN = 'B007' // 采购方式变更
export const NON_TENDER_NODE_EIGHT = 'B008' // 整单释放

export const TENDER_PUBLIC = '00'
export const TENDER_INVITE = '01'
