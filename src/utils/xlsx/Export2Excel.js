/* eslint-disable */
require('script-loader!file-saver');
require('script-loader!./Blob');
require('script-loader!xlsx/dist/xlsx.core.min');
import XLSXStyle from 'yxg-xlsx-style'
import { ORG_ID } from '@/store/mutation-types'
import Vue from 'vue'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_SLH_EVN = USER_ORG_ID === '1.100.111'
function datenum(v, date1904) {
    if (date1904) v += 1462;
    var epoch = Date.parse(v);
    return (epoch - new Date(Date.UTC(1899, 11, 30))) / (24 * 60 * 60 * 1000);
}

function sheet_from_array_of_arrays(data, opts) {
    var ws = {};
    var range = {s: {c: 10000000, r: 10000000}, e: {c: 0, r: 0}};
    console.log(data.length)
    for (var R = 0; R != data.length; ++R) {
        for (var C = 0; C != data[R].length; ++C) {
            if (range.s.r > R) range.s.r = R;
            if (range.s.c > C) range.s.c = C;
            if (range.e.r < R) range.e.r = R;
            if (range.e.c < C) range.e.c = C;
            var cell = {v: data[R][C]};
            if (cell.v == null) continue;
            var cell_ref = XLSX.utils.encode_cell({c: C, r: R});

            if (typeof cell.v === 'number') cell.t = 'n';
            else if (typeof cell.v === 'boolean') cell.t = 'b';
            else if (cell.v instanceof Date) {
                cell.t = 'n';
                cell.z = XLSX.SSF._table[14];
                cell.v = datenum(cell.v);
            }
            else cell.t = 's';

            ws[cell_ref] = cell;
        }
    }
    if (range.s.c < 10000000) ws['!ref'] = XLSX.utils.encode_range(range);
    return ws;
}

function Workbook() {
    if (!(this instanceof Workbook)) return new Workbook();
    this.SheetNames = [];
    this.Sheets = {};
}

function s2ab(s) {
    var buf = new ArrayBuffer(s.length);
    var view = new Uint8Array(buf);
    for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
    return buf;
}

export function exportJsontoexcel( {
   title,
   multiHeader = [],
   remark,
   header,
   data,
   filename,
   merges = [],
   autoWidth = true,
   bookType = 'xlsx',
   footer,
   needBold = []
  } = {}) {
  filename = filename || 'excel-list'
  data = [...data]
  data.unshift(header);
   for (let i = multiHeader.length - 1; i > -1; i--) {
    data.unshift(multiHeader[i])
  }
  data.unshift(remark);
  data.unshift(title);
  data.push(footer);
  var ws_name = "SheetJS";
  var wb = new Workbook(),
    ws = sheet_from_array_of_arrays(data);

  if (merges.length > 0) {
    if (!ws['!merges']) ws['!merges'] = [];
    merges.forEach(item => {
      console.log(item)
      ws['!merges'].push(XLSX.utils.decode_range(item))
    })
  }
  if (autoWidth) {
    /*设置worksheet每列的最大宽度*/
    const colWidth = data.map(row => row.map(val => {
      return {
        'wch':15
      }
    }))
    /*以第一行为初始值*/
    let result = colWidth[0];
    for (let i = 1; i < colWidth.length; i++) {
      // 避免超出数组索引范围
      const a = colWidth[i].length - result.length
      if (a > 0) {
        for(let s = 0; s < a;s++) {
          result.push({wch : 15})
        }
      }
      for (let j = 0; j < colWidth[i].length; j++) {
        if (result[j]['wch'] < colWidth[i][j]['wch']) {
          result[j]['wch'] = colWidth[i][j]['wch'];
        }
      }
    }
    ws['!cols'] = result;
  }

  wb.SheetNames.push(ws_name);
  wb.Sheets[ws_name] = ws;
  var dataInfo = wb.Sheets[wb.SheetNames[0]];
  console.log(dataInfo)
  const borderAll = {  //单元格外侧框线
    top: {
      style: 'thin'
    },
    bottom: {
      style: 'thin'
    },
    left: {
      style: 'thin'
    },
    right: {
      style: 'thin'
    }
  };
  const alignment= {
          wrapText: 1,
          horizontal: 'center',
          vertical: 'center',
          indent: 0
        }
  for (var i in dataInfo) {
    if (i == '!ref' || i == '!merges' || i == '!cols' || i == 'A1') {

    } else {
      dataInfo[i + ''].s = {
        border: borderAll,
        alignment:alignment
      }
    }
  }
  
  //设置主标题样式
  dataInfo["A1"].s = {
    font: {
      name: '宋体',
      sz: 16,
      color: {rgb: "262626"},
      bold: true,
      italic: false,
      underline: false
    },
    alignment: {
      horizontal: "center",
      vertical: "center"
    },
  };
  // console.log(dataInfo, 'dataInfo')
  if (IS_SLH_EVN && dataInfo[`B${data.length}`]) {
    dataInfo[`B${data.length}`].s.alignment = {
      horizontal: "left",
      vertical: "center"
  };
  }
  for (var i = 0; i < needBold.length; i++) {
    //设置加粗样式
    dataInfo[needBold[i]].s = {
      font: {
        bold: true
      },
      border: borderAll,
      alignment: {
        wrapText: 1,
        horizontal: 'center',
        vertical: 'center',
        indent: 0
      },
    };
  }

  var wbout = XLSXStyle.write(wb, {
    bookType: bookType,
    bookSST: false,
    type: 'binary',
    cellStyles:true
  });
  saveAs(new Blob([s2ab(wbout)], {
    type: "application/octet-stream"
  }), `${filename}.${bookType}`);
}