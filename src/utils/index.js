export function getFirstWeekDay (date) {
  const day = date.getDay() || 7
  return new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1 - (day === 7 ? 1 : day))
}

export function getLastWeekDay (date) {
  const day = date.getDay() || 7
  return new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1 - (day === 7 ? -6 : day - 6))
}

export function isExist (array, target) {
  let exist = false
  array.forEach(item => {
    if (item === target) {
      exist = true
    }
  })
  return exist
}
