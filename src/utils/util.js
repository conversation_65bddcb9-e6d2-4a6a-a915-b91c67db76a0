import Vue from 'vue'
import store from '@/store'
import { ORG_ID, OPERATOR, PERSON_ID, DATA_FLAG, DATA_FLAG_GOODS, DATA_FLAG_DEVICE, CC_TOKEN } from '@/store/mutation-types'

/** *
 *
 * 获取请求的UUID，指定长度和进制,如
 * getUUID(8, 2)   //"01001010" 8 character (base=2)
 * getUUID(8, 10) // "47473046" 8 character ID (base=10)
 * getUUID(8, 16) // "098F4D35"。 8 character ID (base=16)
 *
 */
export function getUUID (len, radix) {
  var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
  var uuid = []
  var i
  radix = radix || chars.length
  if (len) {
    for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix]
  } else {
    var r
    // uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
    uuid[14] = '4'
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = 0 | Math.random() * 16
        uuid[i] = chars[(i === 19) ? (r & 0x3) | 0x8 : r]
      }
    }
  }
  return uuid.join('')
}

/**
 * 首页显示时间段
 */
export function timeFix () {
  const time = new Date()
  const hour = time.getHours()
  return hour < 9 ? '早上好' : hour <= 11 ? '上午好' : hour <= 13 ? '中午好' : hour < 20 ? '下午好' : '晚上好'
}

/**
 * 首页显示欢迎语句
 */
export function welcome () {
  const arr = ['天生我材必有用', '准备吃什么呢?', '要不要喝一瓶肥佬水', '我猜你可能累了']
  const index = Math.floor(Math.random() * arr.length)
  return arr[index]
}

/**
 * 判断宿主是否为IE环境
 */
export function isIE () {
  const bw = window.navigator.userAgent
  const compare = s => bw.indexOf(s) >= 0
  const ie11 = (() => 'ActiveXObject' in window)()
  return compare('MSIE') || ie11
}

/**
 * 定时移除页面中加载动画
 */
export function removeLoadingAnimate (id = '', timeout = 1500) {
  if (id) {
    setTimeout(() => {
      document.body.removeChild(document.getElementById(id))
    }, timeout)
  }
}

/**
 * 触发window窗口大小更改
 */
export function triggerWindowResizeEvent () {
  const event = document.createEvent('HTMLEvents')
  event.initEvent('resize', true, true)
  event.eventType = 'message'
  window.dispatchEvent(event)
}

/**
 * 触发滚动事件、执行回调函数(参数-滚动方向)
 */
export function handleScrollHeader (callback) {
  let timer = 0
  let beforeScrollTop = window.pageYOffset
  window.addEventListener(
    'scroll',
    () => {
      clearTimeout(timer)
      timer = setTimeout(() => {
        let direction = 'up'
        const afterScrollTop = window.pageYOffset
        const delta = afterScrollTop - beforeScrollTop
        if (delta === 0) {
          return false
        }
        direction = delta > 0 ? 'down' : 'up'
        callback && callback(direction)
        beforeScrollTop = afterScrollTop
      }, 50)
    },
    false
  )
}

/**
 * 封装接口传参统一格式
 * zhouji 2020-3-20
 */
export const requestBuilder = (action = '', param = {}, pageNo = 0, pageSize = 10, sortField = '', sortOrder = '', dataFlag = '') => {
  const toSortField = (field, order) => {
    const sortField = field && field.replace(/(^|\B)([A-Z])/g, '_$2').toLowerCase()
    const sortOrder = order && order.replace(/end$/i, '')
    return (sortOrder && sortField) || undefined
  }
  const toSortOrder = (field, order) => {
    const sortField = field && field.replace(/(^|\B)([A-Z])/g, '_$2').toLowerCase()
    const sortOrder = order && order.replace(/end$/i, '')
    return (sortField && sortOrder) || undefined
  }

  const sortTopField = param.sortTopField
  const sortTopOrder = param.sortTopOrder
  const roleType = param.roleType

  delete param.sortTopField
  delete param.sortTopOrder
  delete param.roleType

  return {
    param: param,
    action: action,
    orgId: Vue.ls.get(ORG_ID),
    operator: Vue.ls.get(OPERATOR),
    personId: Vue.ls.get(PERSON_ID),
    ccToken: Vue.ls.get(CC_TOKEN),
    // 默认取物资角色，若存在设备角色，则通过是否填写角色类型判断(不填角色类型默认为物资角色，反之为设备角色)，
    dataFlag: (dataFlag) || (Vue.ls.get(DATA_FLAG_DEVICE) === '' ? Vue.ls.get(DATA_FLAG) : !roleType ? Vue.ls.get(DATA_FLAG_GOODS) : Vue.ls.get(DATA_FLAG_DEVICE)),
    dataFlagGoods: Vue.ls.get(DATA_FLAG_GOODS),
    dataFlagDevice: Vue.ls.get(DATA_FLAG_DEVICE),
    sortField: toSortField(sortField, sortOrder),
    sortOrder: toSortOrder(sortField, sortOrder),
    sortTopField: toSortField(sortTopField, sortTopOrder),
    sortTopOrder: toSortOrder(sortTopField, sortTopOrder),
    pageSize: pageSize,
    pageNo: pageNo,
    isCementModule: Vue.ls.get(ORG_ID) === '1.100.131' ? 'N' : null
  }
}
export const requestBuilderCement = (action = '', param = {}, pageNo = 0, pageSize = 10, sortField = '', sortOrder = '', dataFlag = '') => {
  const toSortField = (field, order) => {
    const sortField = field && field.replace(/(^|\B)([A-Z])/g, '_$2').toLowerCase()
    const sortOrder = order && order.replace(/end$/i, '')
    return (sortOrder && sortField) || undefined
  }
  const toSortOrder = (field, order) => {
    const sortField = field && field.replace(/(^|\B)([A-Z])/g, '_$2').toLowerCase()
    const sortOrder = order && order.replace(/end$/i, '')
    return (sortField && sortOrder) || undefined
  }

  const sortTopField = param.sortTopField
  const sortTopOrder = param.sortTopOrder
  const roleType = param.roleType

  delete param.sortTopField
  delete param.sortTopOrder
  delete param.roleType
  param.isCement = 'Y'
  param.isCementModule = 'Y'
  return {
    param: param,
    action: action,
    orgId: Vue.ls.get(ORG_ID),
    operator: Vue.ls.get(OPERATOR),
    personId: Vue.ls.get(PERSON_ID),
    // 默认取物资角色，若存在设备角色，则通过是否填写角色类型判断(不填角色类型默认为物资角色，反之为设备角色)，
    dataFlag: (dataFlag) || (Vue.ls.get(DATA_FLAG_DEVICE) === '' ? Vue.ls.get(DATA_FLAG) : !roleType ? Vue.ls.get(DATA_FLAG_GOODS) : Vue.ls.get(DATA_FLAG_DEVICE)),
    dataFlagGoods: Vue.ls.get(DATA_FLAG_GOODS),
    dataFlagDevice: Vue.ls.get(DATA_FLAG_DEVICE),
    sortField: toSortField(sortField, sortOrder),
    sortOrder: toSortOrder(sortField, sortOrder),
    sortTopField: toSortField(sortTopField, sortTopOrder),
    sortTopOrder: toSortOrder(sortTopField, sortTopOrder),
    pageSize: pageSize,
    pageNo: pageNo,
    isCementModule: 'Y'
  }
}

/**
 * 根据 actions 判断当前账号权限
 * lin pengteng 2020-3-31
 */
export const getPermission = (context, actions, every = false) => {
  const roles = store.getters.roles
  const permission = context.$route.meta.permission
  const permissionIds = (permission instanceof String && [permission]) || permission
  for (const permission of roles.permissions) {
    if (!permissionIds.includes(permission.permissionId)) {
      continue
    }
    for (const action of actions) {
      if (!permission.actionList) {
        // 强校验
        return false
      }
      if (every === false && permission.actionList.includes(action)) {
        return true
      }
      if (every !== false && !permission.actionList.includes(action)) {
        return false
      }
    }
    return every !== false
  }
  // 强校验
  return false
}

/**
 * 递归更新 Object/Array 属性值
 * lin pengteng 2020-3-26
 */
export const deepUpdate = (...array) => {
  function isArray (arr) {
    return itType(arr) === 'Array'
  }
  function isObject (obj) {
    return itType(obj) === 'Object'
  }
  function isOwnProperty (context, property) {
    return Object.prototype.hasOwnProperty.call(context, property)
  }
  function itType (val) {
    return Object.prototype.toString.call(val).replace(/^\[[^\s\]]+\s*([^\s\]]+)]$/, '$1')
  }
  function isAllArray (oldArr, newArr) {
    return isArray(oldArr) && isArray(newArr)
  }
  function isAllObject (oldObj, newObj) {
    return isObject(oldObj) && isObject(newObj)
  }
  if (array.length > 1) {
    const oldData = array[0]
    const newArrs = array.slice(1)
    for (const newData of newArrs) {
      if (isAllObject(oldData, newData)) {
        for (const key in oldData) {
          if (isOwnProperty(oldData, key) && isOwnProperty(newData, key)) {
            if (isAllArray(oldData[key], newData[key]) || isAllObject(oldData[key], newData[key])) {
              deepUpdate(oldData[key], newData[key])
              continue
            }
            oldData[key] = newData[key]
          }
        }
      }
      if (isAllArray(oldData, newData)) {
        for (const key of newData.keys()) {
          if (isAllArray(oldData[key], newData[key]) || isAllObject(oldData[key], newData[key])) {
            deepUpdate(oldData[key], newData[key])
            continue
          }
          oldData[key] = newData[key]
        }
      }
    }
  }
  return array[0]
}

/**
 * 根据 key 取出 tree 中节点数据
 * lin pengteng 2020-3-27
 */
export const takeTreeByKey = (tree, key, value = 'value', children = 'children') => {
  function isArray (arr) {
    return itType(arr) === 'Array'
  }
  function isString (obj) {
    return itType(obj) === 'String'
  }
  function isNumber (obj) {
    return itType(obj) === 'Number'
  }
  function itType (val) {
    return Object.prototype.toString.call(val).replace(/^\[[^\s\]]+\s*([^\s\]]+)]$/, '$1')
  }
  function isOwnProperty (context, property) {
    return Object.prototype.hasOwnProperty.call(context, property)
  }
  if (isArray(key)) {
    const result = []
    for (const k of key) {
      result.push(takeTreeByKey(tree, k, value, children))
    }
    return result
  }
  if (isArray(tree) && (isString(key) || isNumber(key))) {
    for (let i = 0, l = tree.length; i < l; i++) {
      if (isOwnProperty(tree[i], value) && key === tree[i][value]) {
        return tree[i]
      }
      if (isArray(tree[i][children]) && tree[i][children].length > 0) {
        const result = takeTreeByKey(tree[i][children], key, value, children)
        if (result !== null) {
          return result
        }
      }
    }
    return null
  }
}

// 将当前时间转化为yyyy--mm--dd
export function getNowDate () {
  let date = new Date()
  const f1 = '/'
  const year = date.getFullYear()
  let month = date.getMonth() + 1
  const day = date.getDate()
  if (month >= 1 && month <= 9) {
    month = '0' + month
  }
  if (date >= 0 && date <= 9) {
    date = '0' + date
  }
  var currentdate = year + f1 + month + f1 + day
  return currentdate
}

// 处理数值精确度
export function handleFixedDigit (num, digit = 2) {
  if (!isFinite(+num)) {
    return '0.' + ''.padEnd(digit, 0)
  }

  let string = ''

  num = +num || 0
  digit = isFinite(digit) ? +digit : 2

  string = Math.round(Math.pow(10, digit) * num) / Math.pow(10, digit)
  string = String(string)

  if (~string.indexOf('.')) {
    const arr = string.split('.')
    string = arr[0] + '.' + arr[1].padEnd(digit, 0)
  } else {
    if (digit !== 0) {
      string += '.' + ''.padEnd(digit, '0')
    }
  }

  return string
}

// 数值处理
export function MathService () {
  // 加法
  this.add = function (a, b) {
    var c, d, e
    try {
      c = a.toString().split('.')[1].length // 获取a的小数位长度
    } catch (f) {
      c = 0
    }
    try {
      d = b.toString().split('.')[1].length // 获取b的小数位长度
    } catch (f) {
      d = 0
    }
    // 先求e，把a、b 同时乘以e转换成整数相加，再除以e还原
    e = Math.pow(10, Math.max(c, d))
    return (this.mul(a, e) + this.mul(b, e)) / e
  }

  // 乘法
  this.mul = function (a, b) {
    var c = 0
    var d = a.toString() // 转换为字符串
    var e = b.toString() // ...
    try {
      c += d.split('.')[1].length // c 累加a的小数位长度
    } catch (f) {}
    try {
      c += e.split('.')[1].length // c 累加b的小数位长度
    } catch (f) {}
    // 转换为整数相乘，再除以10^c ,移动小数点，还原，利用整数相乘不会丢失精度
    return Number(d.replace('.', '')) * Number(e.replace('.', '')) / Math.pow(10, c)
  }

  // 减法
  this.sub = function (a, b) {
    var c, d, e
    try {
      c = a.toString().split('.')[1].length // 获取a的小数位长度
    } catch (f) {
      c = 0
    }
    try {
      d = b.toString().split('.')[1].length // 获取b的小数位长度
    } catch (f) {
      d = 0
    }
    // 和加法同理
    e = Math.pow(10, Math.max(c, d))
    return (this.mul(a, e) - this.mul(b, e)) / e
  }

  // 除法
  this.div = function (a, b) {
    var c; var d; var e = 0
    var f = 0
    try {
      e = a.toString().split('.')[1].length
    } catch (g) {}
    try {
      f = b.toString().split('.')[1].length
    } catch (g) {}
    // 同理，转换为整数，运算后，还原
    c = Number(a.toString().replace('.', ''))
    d = Number(b.toString().replace('.', ''))
    return this.mul(c / d, Math.pow(10, f - e))
  }
}
