import qs from 'qs'
import Vue from 'vue'
import axios from 'axios'
import store from '@/store'
import Notification from 'ant-design-vue/es/notification'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { VueAxios } from './axios'

// Vue installer
const installer = {
  vm: {},
  install (Vue) {
    Vue.use(VueAxios, service)
  }
}

// 创建 axios 实例
const service = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL,
  timeout: 300000
})

// error 处理
const err = error => {
  let code = 0
  let message = ''
  const token = Vue.ls.get(ACCESS_TOKEN)
  try {
    code = error.response.data.status
    message = error.response.data.message
  } catch (e) {
    if (error.toString().indexOf('timeout') > -1) {
      Notification.error({
        message: '系统消息',
        description: '请求超时'
      })
      return Promise.reject(error)
    }
  }
  if (code === 403) {
    Notification.error({
      message: '系统消息',
      description: message || '禁止访问'
    })
    return Promise.reject(error)
  }
  if (code === 401) {
    Notification.error({
      message: '系统消息',
      description: message || (token ? 'token已过期' : '暂无权限')
    })
    if (token) {
      store.dispatch('Logout').then(() => {
        setTimeout(() => {
          window.location.reload()
        }, 1500)
      })
    }
    return Promise.reject(error)
  }
  if (message) {
    Notification.error({
      message: '系统消息',
      description: message
    })
  }
  return Promise.reject(error)
}

// 统一请求拦截
service.interceptors.request.use(config => {
  // 过滤单点请求
  if (config.url.startsWith('/sso')) {
    return config
  }
  const token = Vue.ls.get(ACCESS_TOKEN)
  if (token) {
    config.headers['token'] = token
  }
  return config
}, err)

// 统一返回拦截
service.interceptors.response.use(response => {
  const code = response.status
  const types = ['blob', 'raw']
  const config = response.config
  // 过滤单点响应
  if (config.url.startsWith('/sso')) {
    return response.data
  }
  const token = Vue.ls.get(ACCESS_TOKEN)
  if (code < 200 || code > 300) {
    if (response.message) {
      Notification.error({
        message: '系统消息',
        description: response.message
      })
    }
    return Promise.reject(response)
  } else {
    if (types.includes(config.responseType)) {
      return response
    }
    const code = (response.data || {}).code
    const message = (response.data || {}).message
    if ([403, '403'].includes(code)) {
      Notification.error({
        message: '系统消息',
        description: message || (token ? 'token已过期' : '禁止访问')
      })
      if (token) {
        store.dispatch('Logout').then(() => {
          setTimeout(() => {
            window.location.reload()
          }, 1500)
        })
      }
      return Promise.reject(response)
    }
    if ([401, '401'].includes(code)) {
      Notification.error({
        message: '系统消息',
        description: message || (token ? 'token已过期' : '暂无权限')
      })
      if (token) {
        store.dispatch('Logout').then(() => {
          setTimeout(() => {
            window.location.reload()
          }, 1500)
        })
      }
      return Promise.reject(response)
    }
    return response.data
  }
}, err)

export {
  installer as VueAxios,
  service as axios,
  qs
}
