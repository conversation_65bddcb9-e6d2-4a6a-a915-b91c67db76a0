
export const createImgBase = options => {
  const canvas = document.createElement('canvas')
  const text = options.content
  canvas.width = options.width
  canvas.height = options.height
  const ctx = canvas.getContext('2d')
  if (ctx) {
    ctx.shadowOffsetX = 2
    ctx.shadowOffsetY = 2
    ctx.shadowBlur = 2
    ctx.font = options.font
    ctx.fillStyle = options.color
    ctx.translate(options.width / 2, options.height / 2)
    ctx.rotate(options.rotateDegree)
    ctx.textAlign = 'center'
    ctx.fillText(text, options.x, options.y)
  }
  console.log(canvas.toDataURL('image/png'))
  return canvas.toDataURL('image/png')
}
/**
 * 生成水印
 * @param {String} className 水印类名
 * @param {String} content 内容
 * @param {String} font 字体
 * @param {String} color 自定义样式: 如字体颜色(使用RGBA)
 * @param {Number} rotate 翻转角度
 * @param {String} position 水印定位方式
 * @param {Number} top 距离顶部位置
 * @param {Number} left 距离左部位置
 * @param {Number} zIndex 水印层级
 */
export const genWaterMark = ({
  className = 'watermarked',
  content = '测试水印',
  font = '14px PingFang SC, sans-serif',
  color = 'rgba(156, 162, 169, 0.3)',
  rotate = -45,
  position = 'absolute',
  top = 0,
  left = 0,
  zIndex = 1000
}) => {
  const contentDom = document.createElement('span')
  contentDom.innerHTML = content
  contentDom.style.font = font
  contentDom.style.opacity = 0
  contentDom.style.position = 'absolute'
  const body = document.getElementsByTagName('body')
  body[0].appendChild(contentDom)
  const textWidth = contentDom.offsetWidth
  body[0].removeChild(contentDom)
  const option = {
    width: textWidth + 30,
    height: textWidth + 30,
    content,
    font,
    color,
    rotateDegree: (rotate * Math.PI) / 180,
    x: 0,
    y: 0
  }
  const dataUri1 = createImgBase({ ...option })
  const defaultStyle = document.createElement('style')
  defaultStyle.innerHTML = `.${className} {
    content: '';
    display: block;
    width: 100%;
    height: 98%;
    ${position ? `position: ${position}` : ''};
    ${zIndex ? `z-index:${zIndex}` : ''};
    ${top || top === 0 ? `top: ${top}px;` : ''}
    ${left || left === 0 ? `left: ${left}px;` : ''}
    background-image: url(${dataUri1});
    background-size: ${option.width}px ${option.height}px;
    background-repeat: repeat;
    pointer-events: none;
  }`
  document.head.appendChild(defaultStyle)
}
