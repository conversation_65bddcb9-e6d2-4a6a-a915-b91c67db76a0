import { mapGetters } from 'vuex'

const tableMixin = {
  computed: {
    resizeComponents () {
      return columns => {
        return {
          header: {
            cell: (h, props, children) => {
              const { key, ...restProps } = props
              const col = columns.find(col => {
                const k = col.dataIndex || col.key
                return k === key
              })

              if (!col || !col.width) {
                return h('th', { ...restProps }, [...children])
              }
              let isDrag = false
              const dragProps = {
                key: col.dataIndex || col.key,
                class: 'table-draggable-handle',
                attrs: {
                  w: 10,
                  x: col.width,
                  z: 1,
                  axis: 'x',
                  draggable: true,
                  resizable: false
                },
                on: {
                  // 拖动时把isdrag参数设置为true
                  dragging: (x, y) => {
                    isDrag = true
                    col.width = Math.max(x, 40)
                  },
                  // 拖动结束后把isdrag参数设置为false
                  dragstop: () => {
                    isDrag = true
                    // eslint-disable-next-line vue/no-async-in-computed-properties
                    setTimeout(() => {
                      isDrag = false
                    }, 300)
                  }
                }
              }
              if (restProps.on && restProps.on.click) {
                const clickFunc = restProps.on.click
                restProps.on.click = event => {
                  if (isDrag) {
                    return
                  }
                  clickFunc(event)
                }
              }
              const drag = h('vue-draggable-resizable', { ...dragProps })
              return h('th', { ...restProps, class: 'resize-table-th' }, [...children, drag])
            }
          }
        }
      }
    },
    ...mapGetters(['todoTableGroup'])
  },
  methods: {
    initTable (columns, tableId) {
      const objectTable = this.todoTableGroup.find(item => item.tableId === tableId)
      const tableList = objectTable ? objectTable.tableRecordList : []
      this[columns].forEach((item, indexco) => {
        const obj = tableList.find(info => info.dataIndex === item.dataIndex)
        if (obj) {
          item.check = !!obj.check
          item.fixed = obj.fixed ? obj.fixed : false
          item.index = obj.index
          item.sorter = !!obj.sorter
          item.ellipsis = !!obj.ellipsis
          item.title = obj.title || item.title
          item.width = obj.width || item.width || 120
          item.disabled = false
          // item.disabled = item.dataIndex === 'action' || item.key === 'action'
        } else {
          item.check = true
          item.edit = false
          item.ellipsis = !!item.ellipsis
          item.sorter = !!item.sorter
          item.index = indexco
          item.disabled = false
          // item.disabled = item.dataIndex === 'action' || item.key === 'action'
        }
      })
      this[columns].sort((a, b) => a.index - b.index)
    }
  }
}
export { tableMixin }
