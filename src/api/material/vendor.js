// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { qs, axios } from '@/utils/request'

/**
 *  queryVendorInfo: 获取供应商信息
 *  modifyVendorInfo: 增删改供应商信息
 *  queryVendorContact: 获取供应商联系人信息
 *  modifyVendorContact: 修改供应商联系人
 *  queryVendorScore: 查询供应商评分
 *  modifyVendorScore: 修改供应商评分
 *  approVendor: 供应商审批
 *  getApproVendor: 根据操作人返回需要审批的供应商
 *  queryApproHis: 根据vendorSysId查审批历史
 *  reportOfVendor: '向ETMS上报供应商代码'
 *  trueDeleteVendor: 真实删除供应商（慎用）
 */
const api = {
  queryVendorInfo: '/vendor/queryVendorInfo',
  modifyVendorInfo: '/vendor/modifyVendorInfo',
  queryVendorContact: '/vendor/queryVendorContact',
  modifyVendorContact: '/vendor/modifyVendorContact',
  queryVendorScore: '/vendor/queryVendorScore',
  modifyVendorScore: '/vendor/modifyVendorScore',
  approVendor: '/vendor/approVendor',
  approBackVendor: '/vendor/approBackVendor',
  getApproVendor: '/vendor/getApproVendor',
  queryApproHis: '/vendor/queryApproHis',
  trueDeleteVendor: '/vendor/trueDeleteVendor',
  reportOfVendor: '/vendor/reportOfVendor',
  queryMachineSelectVendor: '/vendor/queryMachineSelectVendor',
  getVendorsByInvoiceNum: '/vendor/getVendorsByInvoiceNum',
  doExport: 'vendor/doExport',
  queryAppraiseVendorInfo: '/vendorAppraise/queryAppraiseVendorInfo',
  getVendorScore: '/vendorAppraise/getVendorScore',
  appraiseVendors: '/vendorAppraise/appraiseVendors',
  offAppraise: '/vendorAppraise/offAppraise',
  modifyVendorScorePerson: '/vendorAppraise/modifyVendorScorePerson',
  getMatuByVendor: '/vendorAppraise/getMatuByVendor',
  doExportMultiScoreHistory: '/vendorAppraise/doExportMultiScoreHistory',
  getUnAppraiseVendorsNum: '/vendorAppraise/getUnAppraiseVendorsNum',
  doExportScoreAverage: '/vendorAppraise/doExportScoreAverage',
  checkAllUnScored: '/vendorAppraise/checkAllUnScored',
  insertSelfVendorScore: '/vendorAppraise/insertSelfVendorScore'
}

export function insertSelfVendorScore (parameter) {
  return axios({
    url: api.insertSelfVendorScore,
    method: 'post',
    data: parameter
  })
}

export function queryAppraiseVendorInfo (parameter) {
  return axios({
    url: api.queryAppraiseVendorInfo,
    method: 'post',
    data: parameter
  })
}

export function checkAllUnScored (parameter) {
  return axios({
    url: api.checkAllUnScored,
    method: 'post',
    data: parameter
  })
}

export function getUnAppraiseVendorsNum (parameter) {
  return axios({
    url: api.getUnAppraiseVendorsNum,
    method: 'post',
    data: parameter
  })
}

export function doExportScoreAverage (data) {
  return axios({
    url: api.doExportScoreAverage,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportMultiScoreHistory (data) {
  return axios({
    url: api.doExportMultiScoreHistory,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function getMatuByVendor (parameter) {
  return axios({
    url: api.getMatuByVendor,
    method: 'post',
    data: parameter
  })
}

export function offAppraise (parameter) {
  return axios({
    url: api.offAppraise,
    method: 'post',
    data: parameter
  })
}

export function modifyVendorScorePerson (parameter) {
  return axios({
    url: api.modifyVendorScorePerson,
    method: 'post',
    data: parameter
  })
}

export function appraiseVendors (parameter) {
  return axios({
    url: api.appraiseVendors,
    method: 'post',
    data: parameter
  })
}

export function getVendorScore (parameter) {
  return axios({
    url: api.getVendorScore,
    method: 'post',
    data: parameter
  })
}

export function doExport (data) {
  return axios({
    url: api.doExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function getVendorsByInvoiceNum (parameter) {
  return axios({
    url: api.getVendorsByInvoiceNum,
    method: 'post',
    data: parameter
  })
}

export function queryMachineSelectVendor (parameter) {
  return axios({
    url: api.queryMachineSelectVendor,
    method: 'post',
    data: parameter
  })
}

export function queryVendorInfo (parameter) {
  return axios({
    url: api.queryVendorInfo,
    method: 'post',
    data: parameter
  })
}

export function modifyVendorInfo (parameter) {
  return axios({
    url: api.modifyVendorInfo,
    method: 'post',
    data: parameter
  })
}

export function queryVendorContact (parameter) {
  return axios({
    url: api.queryVendorContact,
    method: 'post',
    data: parameter
  })
}

export function modifyVendorContact (parameter) {
  return axios({
    url: api.modifyVendorContact,
    method: 'post',
    data: parameter
  })
}

export function queryVendorScore (parameter) {
  return axios({
    url: api.queryVendorScore,
    method: 'post',
    data: parameter
  })
}

export function modifyVendorScore (parameter) {
  return axios({
    url: api.modifyVendorScore,
    method: 'post',
    data: parameter
  })
}

//  判断供应商评分是否已存在
export function isVendorScoreExist (parameter) {
  return axios({
    url: 'vendor/isVendorScoreExist',
    method: 'post',
    data: parameter
  })
}

export function approVendor (parameter) {
  return axios({
    url: api.approVendor,
    method: 'post',
    data: parameter
  })
}

export function approBackVendor (parameter) {
  return axios({
    url: api.approBackVendor,
    method: 'post',
    data: parameter
  })
}

export function getApproVendor (parameter) {
  return axios({
    url: api.getApproVendor,
    method: 'get',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function queryApproHis (parameter) {
  return axios({
    url: api.queryApproHis,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function reportOfVendor (parameter) {
  return axios({
    url: api.reportOfVendor,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function trueDeleteVendor (parameter) {
  return axios({
    url: api.trueDeleteVendor,
    method: 'post',
    data: parameter
  })
}
