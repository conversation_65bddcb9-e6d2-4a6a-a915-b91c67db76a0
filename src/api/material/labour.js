// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { axios } from '@/utils/request'

/**
 *  getLabourConfig: 获取劳保工具方案信息
 *  getLabourConfigDetail: 获取劳保工具明细信息
 *  modifyLabourConfig: 新增修改劳保工具方案
 */
const api = {
  getLabourConfig: '/labour/queryLabourConfig',
  getLabourConfigDetail: '/labour/queryLabourConfigDetail',
  modifyLabourConfig: 'labour/modifyLabourConfig',
  modifyLabourConfigDetail: 'labour/modifyLabourConfigDetail'
}

export function getLabourConfig (data) {
  return axios({
    url: api.getLabourConfig,
    method: 'post',
    data: data
  })
}

export function getLabourConfigDetail (data) {
  return axios({
    url: api.getLabourConfigDetail,
    method: 'post',
    data: data
  })
}

export function modifyLabourConfig (data) {
  return axios({
    url: api.modifyLabourConfig,
    method: 'post',
    data: data
  })
}

export function modifyLabourConfigDetail (data) {
  return axios({
    url: api.modifyLabourConfigDetail,
    method: 'post',
    data: data
  })
}

export default api
