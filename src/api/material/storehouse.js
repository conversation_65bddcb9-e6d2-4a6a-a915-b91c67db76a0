// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios, qs } from '@/utils/request'

/**
 *  queryStorehouse: 仓库查询
 *  querySecondStorehouse: 二级仓查询
 *  queryStorage: 收发存查询
 *  querySRLG: 仑港收发存查询,
 *  querySRTMG: 头门港收发存查询,
 *  doExportLG: 仑港收发存导出
 *  querySRZL: 众联收发存查询
 *  doExportZL: 众联收发存导出
 *  doExportTMG: 头门港收发存导出
 *  querySR26: 温州港26大类收发存
 * doExportDepartment:导出嘉兴部门出库金额汇总
 */
const api = {
  queryStorehouse: '/storehouse/queryStorehouse',
  queryStorage: '/SendReceive/querySRInfo',
  querySRSum: '/SendReceive/querySRSum',
  doExport: '/SendReceive/doExport',
  querySRLG: '/SendReceive/querySRLG',
  doExportLG: '/SendReceive/doExportLG',
  querySecondStorehouse: '/storehouse/querySecondStorehouse',
  querySRZL: '/SendReceive/querySRZL',
  querySRTMG: '/SendReceive/querySRTMG',
  querySR26: '/SendReceive/querySR26',
  queryWGSYBM: '/SendReceive/queryWGSYBM',
  doExportZL: '/SendReceive/doExportZL',
  doPrintZL: '/SendReceive/doPrintZL',
  doExportTMG: '/SendReceive/doExportTMG',
  doExportDZ: '/SendReceive/doExportDZ',
  queryStorageJx: '/SendReceive/querySRInfoJx',
  querySRSumJx: '/SendReceive/querySRSumJx',
  doExportJX: '/SendReceive/doExportJX',
  doExportMD: '/SendReceive/doExportMD',
  doTcwfStatistics: '/SendReceive/doTcwfStatistics',
  doExportTS: '/SendReceive/doExportTS',
  doPrintTS: '/SendReceive/doPrintTS',
  doExportTSSC: '/SendReceive/doExportTSSC',
  doExportTSRL: '/SendReceive/doExportTSRL',
  doExportDepartment: '/SendReceive/selectDepartmentJx',
  doExportNBCTSFC: '/SendReceive/doExportNBCTSFC',
  doExportType: '/SendReceive/doExportType',
  doExportLocationNum: '/SendReceive/doExportLocationNum',
  doExportStorehouse: '/SendReceive/doExportStorehouse',
  doExportMaterial: '/SendReceive/doExportMaterial',
  // 集运司属地收发存信息
  querySiteSfcInfo: '/SendReceive/querySiteSfcInfo',
  doPrint: '/SendReceive/doPrint',
  doPrintDetail: 'SendReceive/doPrintDetail',
  doPrintTMG: 'SendReceive/doPrintTMG',
  doPrintLG: 'SendReceive/doPrintLG',
  queryTMGAndDMYSR26: '/SendReceive/queryTMGAndDMYSR26',
  doExportMaterialTMGAndDMY: '/SendReceive/doExportMaterialTMGAndDMY'
}

export function doExportMaterialTMGAndDMY (data) {
  return axios({
    url: api.doExportMaterialTMGAndDMY,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryTMGAndDMYSR26 (data) {
  return axios({
    url: api.queryTMGAndDMYSR26,
    method: 'post',
    data: data
  })
}

export function doPrintTS (data) {
  return axios({
    url: api.doPrintTS,
    method: 'post',
    data: data
  })
}

export function doPrintZL (data) {
  return axios({
    url: api.doPrintZL,
    method: 'post',
    data: data
  })
}

export function doPrintLG (data) {
  return axios({
    url: api.doPrintLG,
    method: 'post',
    data: data
  })
}

export function doPrintTMG (data) {
  return axios({
    url: api.doPrintTMG,
    method: 'post',
    data: data
  })
}

export function doPrintDetail (data) {
  return axios({
    url: api.doPrintDetail,
    method: 'post',
    data: data
  })
}

export function doPrint (data) {
  return axios({
    url: api.doPrint,
    method: 'post',
    data: data
  })
}

export function doExportLocationNum (data) {
  return axios({
    url: api.doExportLocationNum,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportStorehouse (data) {
  return axios({
    url: api.doExportStorehouse,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExport (data) {
  return axios({
    url: api.doExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportJX (data) {
  return axios({
    url: api.doExportJX,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportMD (data) {
  return axios({
    url: api.doExportMD,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doTcwfStatistics (data) {
  return axios({
    url: api.doTcwfStatistics,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportTS (data) {
  return axios({
    url: api.doExportTS,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function doExportTSSC (data) {
  return axios({
    url: api.doExportTSSC,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function doExportTSRL (data) {
  return axios({
    url: api.doExportTSRL,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function queryStorehouse (data) {
  return axios({
    url: api.queryStorehouse,
    method: 'post',
    data: data
  })
}

export function queryStorage (data) {
  return axios({
    url: api.queryStorage,
    method: 'post',
    data: data
  })
}

export function querySiteSfcInfo (data) {
  return axios({
    url: api.querySiteSfcInfo,
    method: 'post',
    data: data
  })
}

export function querySRSum (data) {
  return axios({
    url: api.querySRSum,
    method: 'post',
    data: data
  })
}

export function querySRLG (data) {
  return axios({
    url: api.querySRLG,
    method: 'post',
    data: data
  })
}

export function doExportLG (data) {
  return axios({
    url: api.doExportLG,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function querySecondStorehouse (data) {
  return axios({
    url: api.querySecondStorehouse,
    method: 'post',
    data: data
  })
}

export function querySRZL (data) {
  return axios({
    url: api.querySRZL,
    method: 'post',
    data: data
  })
}

export function querySRTMG (data) {
  return axios({
    url: api.querySRTMG,
    method: 'post',
    data: data
  })
}

export function querySR26 (data) {
  return axios({
    url: api.querySR26,
    method: 'post',
    data: data
  })
}

export function queryWGSYBM (data) {
  return axios({
    url: api.queryWGSYBM,
    method: 'post',
    data: data
  })
}

export function doExportDepartment (data) {
  return axios({
    url: api.doExportDepartment,
    method: 'post',
    data: data
  })
}
export function doExportZL (data) {
  return axios({
    url: api.doExportZL,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportTMG (data) {
  return axios({
    url: api.doExportTMG,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function doExportDZ (data) {
  return axios({
    url: api.doExportDZ,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryStorageJx (data) {
  return axios({
    url: api.queryStorageJx,
    method: 'post',
    data: data
  })
}

export function querySRSumJx (data) {
  return axios({
    url: api.querySRSumJx,
    method: 'post',
    data: data
  })
}

export function doExportNBCTSFC (data) {
  return axios({
    url: api.doExportNBCTSFC,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportType (data) {
  return axios({
    url: api.doExportType,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportMaterial (parameter) {
  return axios({
    url: api.doExportMaterial,
    method: 'post',
    params: parameter,
    responseType: 'blob',
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export default api
