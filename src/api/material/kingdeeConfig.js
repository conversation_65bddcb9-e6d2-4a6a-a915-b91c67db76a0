import { axios } from '@/utils/request'

/**
 *  queryProject: 项目查询
 *  modifyProject: 项目增删改
 */

const api = {
  // 查询部门关联
  queryKingdeeDeptMappingInfo: '/kingdeeMapping/queryKingdeeDeptMappingInfo',
  // 增删改查
  modifyKingdeeDeptMapping: '/kingdeeMapping/modifyKingdeeDeptMapping',
  // 查询费用类型关联
  queryKingdeeFylxMappingInfo: '/kingdeeMapping/queryKingdeeFylxMappingInfo',
  // 增删改查
  modifyKingdeeFylxMapping: '/kingdeeMapping/modifyKingdeeFylxMapping'
}
export function modifyKingdeeDeptMapping (data) {
  return axios({
    url: api.modifyKingdeeDeptMapping,
    method: 'post',
    data: data
  })
}

export function modifyKingdeeFylxMapping (data) {
  return axios({
    url: api.modifyKingdeeFylxMapping,
    method: 'post',
    data: data
  })
}

export function queryKingdeeDeptMappingInfo (data) {
  return axios({
    url: api.queryKingdeeDeptMappingInfo,
    method: 'post',
    data: data
  })
}

export function queryKingdeeFylxMappingInfo (data) {
  return axios({
    url: api.queryKingdeeFylxMappingInfo,
    method: 'post',
    data: data
  })
}

export default api
