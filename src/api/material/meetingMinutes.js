import { axios } from '@/utils/request'

/**
 *  queryMeetingMinutes: 查询会议记录
 *  modifyMeetingMinutes: 会议记录增删改
 *  doExport:导出报表
 */

const api = {
  queryMeetingMinutes: '/meetingMinutes/queryMeetingMinutes',
  modifyMeetingMinutes: '/meetingMinutes/modifyMeetingMinutes',
  doExport: '/meetingMinutes/doExport'
}

export function queryMeetingMinutes (data) {
  return axios({
    url: api.queryMeetingMinutes,
    method: 'post',
    data: data
  })
}

export function modifyMeetingMinutes (data) {
  return axios({
    url: api.modifyMeetingMinutes,
    method: 'post',
    data: data
  })
}

export function doExport (data) {
  return axios({
    url: api.doExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export default api
