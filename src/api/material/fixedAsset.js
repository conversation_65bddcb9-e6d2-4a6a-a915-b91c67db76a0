import { axios } from '@/utils/request'

/**
 * queryGroupCodeInfo: 查询组别代码信息
 */
const api = {
  queryFixedAsset: '/fixedAsset/queryFixedAssetType',
  modifyFixedAsset: '/fixedAsset/modifyFixedAssetType',
  querytypeIdForPlan: '/fixedAsset/querytypeIdForPlan',

  modifyFixedAssetPlan: '/fixedAsset/modifyFixedAssetPlan',
  queryFixedAssetPlan: '/fixedAsset/queryFixedAssetPlan',
  downloadFixedAssetPlanSample: '/fixedAsset/downloadFixedAssetPlanSample',
  importFixedAssetPlan: '/fixedAsset/importFixedAssetPlan',
  queryFixedAssetPlanForItem: '/fixedAsset/queryFixedAssetPlanForItem',
  queryFixedAssetTypeForInsert: '/fixedAsset/queryFixedAssetTypeForInsert',
  sendToFixedAssetCheck: '/item/sendToFixedAssetCheck',
  queryFixedAssetCheck: '/fixedAsset/queryFixedAssetCheck',
  modifyFixedAssetCheck: '/fixedAsset/modifyFixedAssetCheck',
  cancelFinalAccount: '/fixedAsset/cancelFinalAccount',
  selectItemForConnect: '/fixedAsset/selectFinalAccountForConnect',
  connectFinalAccount: '/fixedAsset/connectFinalAccount',
  approFixedAsset: '/fixedAsset/approFixedAsset',
  queryItemSubmitByFixedPlan: '/fixedAsset/queryItemSubmitByFixedPlan',
  queryAllFinishedAmount: '/fixedAsset/queryAllFinishedAmount',
  modifyAllFinishedAmount: '/fixedAsset/modifyAllFinishedAmount'
}

export function modifyAllFinishedAmount (data) {
  return axios({
    url: api.modifyAllFinishedAmount,
    method: 'post',
    data: data
  })
}

export function queryAllFinishedAmount (data) {
  return axios({
    url: api.queryAllFinishedAmount,
    method: 'post',
    data: data
  })
}

export function queryItemSubmitByFixedPlan (data) {
  return axios({
    url: api.queryItemSubmitByFixedPlan,
    method: 'post',
    data: data
  })
}

export function approFixedAsset (data) {
  return axios({
    url: api.approFixedAsset,
    method: 'post',
    data: data
  })
}

export function connectFinalAccount (data) {
  return axios({
    url: api.connectFinalAccount,
    method: 'post',
    data: data
  })
}

export function selectItemForConnect (data) {
  return axios({
    url: api.selectItemForConnect,
    method: 'post',
    data: data
  })
}

export function cancelFinalAccount (data) {
  return axios({
    url: api.cancelFinalAccount,
    method: 'post',
    data: data
  })
}

export function modifyFixedAssetCheck (data) {
  return axios({
    url: api.modifyFixedAssetCheck,
    method: 'post',
    data: data
  })
}

export function queryFixedAssetCheck (data) {
  return axios({
    url: api.queryFixedAssetCheck,
    method: 'post',
    data: data
  })
}

export function sendToFixedAssetCheck (data) {
  return axios({
    url: api.sendToFixedAssetCheck,
    method: 'post',
    data: data
  })
}

export function queryFixedAssetTypeForInsert (data) {
  return axios({
    url: api.queryFixedAssetTypeForInsert,
    method: 'post',
    data: data
  })
}

export function queryFixedAssetPlanForItem (data) {
  return axios({
    url: api.queryFixedAssetPlanForItem,
    method: 'post',
    data: data
  })
}

export function importFixedAssetPlan (data) {
  return axios({
    url: api.importFixedAssetPlan,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function downloadFixedAssetPlanSample (data) {
  return axios({
    url: api.downloadFixedAssetPlanSample,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}

export function modifyFixedAssetPlan (data) {
  return axios({
    url: api.modifyFixedAssetPlan,
    method: 'post',
    data: data
  })
}
export function queryFixedAssetPlan (data) {
  return axios({
    url: api.queryFixedAssetPlan,
    method: 'post',
    data: data
  })
}

export function queryFixedAsset (data) {
  return axios({
    url: api.queryFixedAsset,
    method: 'post',
    data: data
  })
}

export function modifyFixedAsset (data) {
  return axios({
    url: api.modifyFixedAsset,
    method: 'post',
    data: data
  })
}

export function querytypeIdForPlan (data) {
  return axios({
    url: api.querytypeIdForPlan,
    method: 'post',
    data: data
  })
}

export default api
