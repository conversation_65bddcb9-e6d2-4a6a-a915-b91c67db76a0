// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 * getUsedforInfo: 查询使用方向
 * modifyUsedforInfo: 修改使用方向
 */
const api = {
  getUsedforInfo: '/usedfor/getUsedforInfo',
  modifyUsedforInfo: '/usedfor/modifyUsedforInfo'
}

export function getUsedforInfo (data) {
  return axios({
    url: api.getUsedforInfo,
    method: 'post',
    data: data
  })
}

export function modifyUsedforInfo (data) {
  return axios({
    url: api.modifyUsedforInfo,
    method: 'post',
    data: data
  })
}

export default api
