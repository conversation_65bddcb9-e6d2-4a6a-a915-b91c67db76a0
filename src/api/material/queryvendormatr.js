
// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 *  queryLabourRecord: 查询
 *  doPurchase: 采购
 * exportVendorMatr： 供应商查询入库汇总金额导出（打印）
 *  exportVendorMatrLine: 供应商查询入库订单导出（打印）
 * queryVendorJx:嘉兴供应商结算
 */
const api = {
  queryVendorMatr: '/matr/queryVendorMatr',
  queryVendorMatrLine: '/matr/queryVendorMatrLine',
  exportVendorMatr: '/matr/ExportVendorMatr',
  exportVendorMatrLine: '/matr/ExportVendorMatrLine',
  queryVendorJx: '/matr/queryVendorJx'
}

export function queryVendorMatr (data) {
  return axios({
    url: api.queryVendorMatr,
    method: 'post',
    data: data
  })
}

export function queryVendorMatrLine (data) {
  return axios({
    url: api.queryVendorMatrLine,
    method: 'post',
    data: data
  })
}

export function exportVendorMatr (data) {
  return axios({
    url: api.exportVendorMatr,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function exportVendorMatrLine (data) {
  return axios({
    url: api.exportVendorMatrLine,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryVendorJx (data) {
  return axios({
    url: api.queryVendorJx,
    method: 'post',
    data: data
  })
}

export default api
