// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { axios } from '@/utils/request'
// import { axios } from '@/utils/request'

/**
 *  earlyWarningSplitPurchase: 拆分采购预警
 *  earlyWarningPriceDeviation: 计算金额
 *  earlyWarningSinglePurchase: 单一采购预警
 *  earlyWarningHighPrice: 价格偏高预警
 *  exportEarlyWarningHighPrice: 导出价格偏高预警
 *  earlyWarningAbnormalSett: 异常结算预警(合同超额预警)
 *  exportEarlyWarningAbnormalSett: 导出异常结算预警(合同超额预警)
 *  earlyWarningNonUnified: 非统购物资提示
 * exportEarlyWarningNonUnified: 导出非统购物资提示
 *  earlyWarningTemporaryPurchase: 临时采购预警
 *  exportEarlyWarningTemporaryPurchase: 导出临时采购预警
 *  modifyEarlyWarningRecord: 采购方式预警
 *  earlyWarningVendorSensitive: 供应商敏感性预警
 *  exportEarlyWarningVendorSensitive: 导出供应商敏感性预警
 *  queryBidWinningDetail:查询供应商组合详情
 *  earlyWarningPurchaseMethod: 查询采购方式预警信息
 *  exportEarlyWarningPurchaseMethod: 导出采购方式预警信息
 *  earlyWarningRetentionMoney: 合同质保金提示
 *  earlyWarningHighPriceScale: 价格偏高预警new
 *  earlyWarningSplitPurchaseDetail: 拆分采购预警new
 *  exportEarlyWarningSplitPurchaseDetail: 导出拆分采购预警new
 */
const api = {
  earlyWarningSplitPurchase: '/earlyWarning/earlyWarningSplitPurchase',
  earlyWarningPriceDeviation: '/earlyWarning/earlyWarningPriceDeviation',
  earlyWarningSinglePurchase: '/earlyWarning/earlyWarningSinglePurchase',
  earlyWarningHighPrice: '/earlyWarning/earlyWarningHighPrice',
  exportEarlyWarningHighPrice: '/earlyWarning/exportEarlyWarningHighPrice',
  earlyWarningAbnormalSett: '/earlyWarning/earlyWarningAbnormalSett',
  exportEarlyWarningAbnormalSett: '/earlyWarning/exportEarlyWarningAbnormalSett',
  earlyWarningNonUnified: '/earlyWarning/earlyWarningNonUnified',
  exportEarlyWarningNonUnified: '/earlyWarning/exportEarlyWarningNonUnified',
  earlyWarningTemporaryPurchase: '/earlyWarning/earlyWarningTemporaryPurchase',
  exportEarlyWarningTemporaryPurchase: '/earlyWarning/exportEarlyWarningTemporaryPurchase',
  modifyEarlyWarningRecord: '/earlyWarning/modifyEarlyWarningRecord',
  earlyWarningVendorSensitive: '/earlyWarning/earlyWarningVendorSensitive',
  exportEarlyWarningVendorSensitive: '/earlyWarning/exportEarlyWarningVendorSensitive',
  queryBidWinningDetail: '/earlyWarning/queryBidWinningDetail',
  earlyWarningPurchaseMethod: '/earlyWarning/earlyWarningPurchaseMethod',
  exportEarlyWarningPurchaseMethod: '/earlyWarning/exportEarlyWarningPurchaseMethod',
  earlyWarningRetentionMoney: '/earlyWarning/earlyWarningRetentionMoney',
  earlyWarningHighPriceScale: '/earlyWarning/earlyWarningHighPriceScale',
  earlyWarningSplitPurchaseDetail: '/earlyWarning/earlyWarningSplitPurchaseDetail',
  exportEarlyWarningSplitPurchaseDetail: '/earlyWarning/exportEarlyWarningSplitPurchaseDetail',
  earlyWarningSinglePurchaseDetail: '/earlyWarning/earlyWarningSinglePurchaseDetail',
  exportEarlyWarningSinglePurchaseDetail: '/earlyWarning/exportEarlyWarningSinglePurchaseDetail',
  earlyWarningSinglePurchaseMs: '/earlyWarning/earlyWarningSinglePurchaseMs',
  earlyWarningSinglePurchaseMsDetail: '/earlyWarning/earlyWarningSinglePurchaseMsDetail',
  exportEarlyWarningSinglePurchaseMs: '/earlyWarning/exportEarlyWarningSinglePurchaseMs',
  earlyWarningImportantMaterial: '/earlyWarning/earlyWarningImportantMaterial',
  earlyWarningImportantMaterialDetail: '/earlyWarning/earlyWarningImportantMaterialDetail',
  earlyWarningOverstockInfo: '/earlyWarning/earlyWarningOverstockInfo',
  earlyWarningInventoryAmount: '/earlyWarning/earlyWarningInventoryAmount'

}

export function earlyWarningInventoryAmount (data) {
  return axios({
    url: api.earlyWarningInventoryAmount,
    method: 'post',
    data: data
  })
}

export function earlyWarningOverstockInfo (data) {
  return axios({
    url: api.earlyWarningOverstockInfo,
    method: 'post',
    data: data
  })
}

export function earlyWarningImportantMaterial (data) {
  return axios({
    url: api.earlyWarningImportantMaterial,
    method: 'post',
    data: data
  })
}
export function earlyWarningImportantMaterialDetail (data) {
  return axios({
    url: api.earlyWarningImportantMaterialDetail,
    method: 'post',
    data: data
  })
}
export function exportEarlyWarningSinglePurchaseMs (data) {
  return axios({
    url: api.exportEarlyWarningSinglePurchaseMs,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function earlyWarningSinglePurchaseMs (data) {
  return axios({
    url: api.earlyWarningSinglePurchaseMs,
    method: 'post',
    data: data
  })
}
export function earlyWarningSinglePurchaseMsDetail (data) {
  return axios({
    url: api.earlyWarningSinglePurchaseMsDetail,
    method: 'post',
    data: data
  })
}
export function earlyWarningSinglePurchaseDetail (data) {
  return axios({
    url: api.earlyWarningSinglePurchaseDetail,
    method: 'post',
    data: data
  })
}

export function exportEarlyWarningSinglePurchaseDetail (data) {
  return axios({
    url: api.exportEarlyWarningSinglePurchaseDetail,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function earlyWarningSplitPurchaseDetail (data) {
  return axios({
    url: api.earlyWarningSplitPurchaseDetail,
    method: 'post',
    data: data
  })
}

export function exportEarlyWarningSplitPurchaseDetail (data) {
  return axios({
    url: api.exportEarlyWarningSplitPurchaseDetail,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function earlyWarningHighPriceScale (data) {
  return axios({
    url: api.earlyWarningHighPriceScale,
    method: 'post',
    data: data
  })
}

export function earlyWarningRetentionMoney (data) {
  return axios({
    url: api.earlyWarningRetentionMoney,
    method: 'post',
    data: data
  })
}

export function earlyWarningPurchaseMethod (data) {
  return axios({
    url: api.earlyWarningPurchaseMethod,
    method: 'post',
    data: data
  })
}

export function exportEarlyWarningPurchaseMethod (data) {
  return axios({
    url: api.exportEarlyWarningPurchaseMethod,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryBidWinningDetail (data) {
  return axios({
    url: api.queryBidWinningDetail,
    method: 'post',
    data: data
  })
}

export function earlyWarningVendorSensitive (data) {
  return axios({
    url: api.earlyWarningVendorSensitive,
    method: 'post',
    data: data
  })
}

export function exportEarlyWarningVendorSensitive (data) {
  return axios({
    url: api.exportEarlyWarningVendorSensitive,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function modifyEarlyWarningRecord (data) {
  return axios({
    url: api.modifyEarlyWarningRecord,
    method: 'post',
    data: data
  })
}

export function earlyWarningTemporaryPurchase (data) {
  return axios({
    url: api.earlyWarningTemporaryPurchase,
    method: 'post',
    data: data
  })
}

export function exportEarlyWarningTemporaryPurchase (data) {
  return axios({
    url: api.exportEarlyWarningTemporaryPurchase,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function earlyWarningNonUnified (data) {
  return axios({
    url: api.earlyWarningNonUnified,
    method: 'post',
    data: data
  })
}

export function exportEarlyWarningNonUnified (data) {
  return axios({
    url: api.exportEarlyWarningNonUnified,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function earlyWarningAbnormalSett (data) {
  return axios({
    url: api.earlyWarningAbnormalSett,
    method: 'post',
    data: data
  })
}

export function exportEarlyWarningAbnormalSett (data) {
  return axios({
    url: api.exportEarlyWarningAbnormalSett,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function earlyWarningHighPrice (data) {
  return axios({
    url: api.earlyWarningHighPrice,
    method: 'post',
    data: data
  })
}

export function exportEarlyWarningHighPrice (data) {
  return axios({
    url: api.exportEarlyWarningHighPrice,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function earlyWarningSplitPurchase (data) {
  return axios({
    url: api.earlyWarningSplitPurchase,
    method: 'post',
    data: data
  })
}

export function earlyWarningPriceDeviation (data) {
  return axios({
    url: api.earlyWarningPriceDeviation,
    method: 'post',
    data: data
  })
}

export function earlyWarningSinglePurchase (data) {
  return axios({
    url: api.earlyWarningSinglePurchase,
    method: 'post',
    data: data
  })
}
export default api
