// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { qs, axios } from '@/utils/request'

/**
 * deleteFile: 删除附件
 * listFiles: 查看附件
 */
const api = {
  deleteFile: '/file/deleteFile',
  listFiles: '/file/listFiles',
  deleteJtoaFile: '/rfqline/deleteJtoaFile',
  cleanJtoaForm: '/rfqline/cleanJtoaForm',
  deleteContractAttach: '/contract/deleteContractAttach',
  fileDownload: '/file/fileDownload'
}

export function fileDownload (data) {
  return axios({
    url: api.fileDownload,
    method: 'get',
    responseType: 'blob',
    data
  })
}

export function deleteContractAttach (data) {
  return axios({
    url: api.deleteContractAttach,
    method: 'post',
    data
  })
}

export function deleteJtoaFile (data) {
  return axios({
    url: api.deleteJtoaFile,
    method: 'post',
    data
  })
}

export function cleanJtoaForm (data) {
  return axios({
    url: api.cleanJtoaForm,
    method: 'post',
    data
  })
}

export function deleteFile (parameter) {
  return axios({
    url: api.deleteFile,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function listFiles (parameter) {
  return axios({
    url: api.listFiles,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export default api
