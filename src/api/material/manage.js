// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { qs, axios } from '@/utils/request'

/**
 *  getUserList:
 *  getRoleList:
 *  saveService:
 *  getServiceList:
 *  getServiceListDemo:
 *  getPermissions:
 *  getOrgTree:
 */
const api = {
  getUserList: '/user',
  getRoleList: '/role',
  saveService: '/service',
  getServiceList: '/service',
  getServiceListDemo: '/servicedemo',
  getPermissions: '/permission/no-pager',
  getOrgTree: '/org/tree'
}

export function getUserList (parameter) {
  return axios({
    url: api.getUserList,
    method: 'get',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function getRoleList (parameter) {
  return axios({
    url: api.getRoleList,
    method: 'get',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function saveService (parameter) {
  // id == 0 add     post
  // id != 0 update  put
  return axios({
    url: api.saveService,
    method: parameter.id === 0 ? 'post' : 'put',
    data: parameter
  })
}

export function getServiceList (parameter) {
  return axios({
    url: api.getServiceList,
    method: 'get',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function getServiceListDemo (parameter) {
  return axios({
    url: api.getServiceListDemo,
    method: 'get',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function getPermissions (parameter) {
  return axios({
    url: api.getPermissions,
    method: 'get',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function getOrgTree (parameter) {
  return axios({
    url: api.getOrgTree,
    method: 'get',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export default api
