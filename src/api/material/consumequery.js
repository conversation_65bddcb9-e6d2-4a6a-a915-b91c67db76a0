// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
import { axios } from '@/utils/request'

/**
 *  getConsumeInfo: 获取各部门物资消耗详细信息
 *  getConsumeTotal: 获取当前所在部门的物资消耗情况
 */
const api = {
  getConsumeInfo: '/consume/queryConsumeInfo',
  getConsumeTotal: '/consume/getConsumeTotal'
}

export function getConsumeInfo (parameter) {
  return axios({
    url: api.getConsumeInfo,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function getConsumeTotal (parameter) {
  return axios({
    url: api.getConsumeTotal,
    method: 'post',
    data: parameter
  })
}

export default api
