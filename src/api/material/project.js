import { axios } from '@/utils/request'

/**
 *  queryProject: 项目查询
 *  modifyProject: 项目增删改
 */

const api = {
  queryProject: '/project/queryProject',
  modifyProject: '/project/modifyProject',
  // 项目对应的每月计划的增删改查
  queryMonthlyPlan: '/project/queryMonthlyPlan',
  modifyMonthlyPlan: '/project/modifyMonthlyPlan'
}

export function queryProject (data) {
  return axios({
    url: api.queryProject,
    method: 'post',
    data: data
  })
}

export function modifyProject (data) {
  return axios({
    url: api.modifyProject,
    method: 'post',
    data: data
  })
}

export function queryMonthlyPlan (data) {
  return axios({
    url: api.queryMonthlyPlan,
    method: 'post',
    data: data
  })
}

export function modifyMonthlyPlan (data) {
  return axios({
    url: api.modifyMonthlyPlan,
    method: 'post',
    data: data
  })
}
export default api
