import { axios, qs } from '@/utils/request'

const api = {
  queryMaterial: '/material/queryMaterialInfo',
  generateScrapPlan: '/scrap/generateScrapPlan',
  queryScrapPlan: '/scrap/queryScrapPlan',
  queryScrapSendReceive: '/scrap/queryScrapSendReceive',
  modifyScrapSendReceive: '/scrap/modifyScrapSendReceive',
  generateScrapSend: '/scrap/generateScrapSend',
  queryScrapSendMaterial: '/scrap/queryScrapSendMaterial',
  queryScrapBidder: '/scrap/queryScrapBidder',
  doExportOfScrapSend: '/scrap/doExportOfScrapSend',
  doExportOfScrapPlan: '/scrap/doExportOfScrapPlan'
}

export function queryMaterialForScrap (data) {
  return axios({
    url: api.queryMaterial,
    method: 'post',
    data: data
  })
}

export function generateScrapPlan (data) {
  return axios({
    url: api.generateScrapPlan,
    method: 'post',
    data: data
  })
}

export function queryScrapSendReceive (data) {
  return axios({
    url: api.queryScrapSendReceive,
    method: 'post',
    data: data
  })
}

export function queryScrapPlan (data) {
  return axios({
    url: api.queryScrapPlan,
    method: 'post',
    data: data
  })
}

export function modifyScrapSendReceive (data) {
  return axios({
    url: api.modifyScrapSendReceive,
    method: 'post',
    data: data
  })
}

export function generateScrapSend (data) {
  return axios({
    url: api.generateScrapSend,
    method: 'post',
    data: data
  })
}

export function queryScrapSendMaterial (data) {
  return axios({
    url: api.queryScrapSendMaterial,
    method: 'post',
    data: data
  })
}

export function queryScrapBidder (data) {
  return axios({
    url: api.queryScrapBidder,
    method: 'post',
    data: data
  })
}
// 评估表
export function doExportOfScrapSend (parameter) {
  return axios({
    url: api.doExportOfScrapSend,
    method: 'post',
    responseType: 'blob',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

// 申请表
export function doExportOfScrapPlan (parameter) {
  return axios({
    url: api.doExportOfScrapPlan,
    method: 'post',
    responseType: 'blob',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}
