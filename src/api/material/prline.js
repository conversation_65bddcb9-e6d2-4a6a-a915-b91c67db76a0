// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { qs, axios } from '@/utils/request'

/**
 *  queryPrlineAssigee: 采购计划审批人
 *  queryPrlineRequest: 获取领料计划
 *  getProcurePlanByPageNo: 获取采购计划
 *  getApproveRecordByNum: 获取审批记录
 *  approveProcurePlan: 审批采购计划
 *  forcedTerminate: 强制审批采购计划
 *  deleteProcurePlan: 删除采购计划
 *  modifyProcurePlan: 修改采购计划
 *  doExportProcurePlan:导出申报计划报表
 *  exportProcurePlan: 下载采购单
 *  exportProcurePlanDMY:下载采购申请单大麦屿专用
 *  exportBYJ: 导出需用计划
 *  markRequiredPlan: 标记需用计划
 * generatePlanOfOffice:办公用品补库生成
 *  splitProcurePlan: 采购单拆分
 *  transToRfqLine: 采购单询比价
 *  transToPoLine: 采购单采购
 *  cancelPrline: 采购单核销
 *  getPrlineByDate: 根据日期获取本月计划采购数
 *  querySecondStoreHousePrline: 二级库申报采购计划查询
 *  modifyPrlineCostType:修改出库管理界面的费用类型
 *  getPrlineSysIdByRfqlineSysId:获取采购计划id通过询价id
 *  getPrlineSysIdByPolineSysId:获取采购计划id通过订单id
 *  getRfqlineSysIdByPolineSysId:获取询价id通过订单id
 *  getProcureCount:获取年度已申报记录数
 *  getIndividualPrlineInfo:获取个人物资申报总表
 *  doExportIndividualPrline:下载个人物资申报总表
 *  doReportOfUniOrder: 发送统购物资到ETMS平台
 *  getUnpurchaseCount: 获取未采购记录数
 *  getApprovedAndFinishedPlan: 申报员状态为审核完成与结束的计划
 *  turnMatu: 采购计划转到出库管理界面
 *  turnPurchase: 计划由出库管理页面转到采购计划
 *  queryModifyJwspHis: 计划审核数修改记录（鼠浪湖）
 *  createHJPMCPlan: 海建甲供计划生成
 *  addDMYPrline: 大麦屿代办计划新增
 *  doExportTransfer：导出信通移交单
 */
const api = {
  queryPrlineAssigee: '/prline/queryPrlineAssigee',
  queryPrlineRequest: '/prline/queryPrlineRequest',
  getProcurePlanByPageNo: '/prline/queryPrline',
  getPrlineApprByPageNo: '/prline/queryPrlineAppr',
  getApproveRecordByNum: '/prline/queryApproHis',
  approveProcurePlan: '/prline/approPrline',
  forcedTerminate: '/prline/forcedTerminate',
  deleteProcurePlan: '/prline/modifyPrline',
  modifyProcurePlan: '/prline/modifyPrline',
  doExportProcurePlan: '/prline/exportPrline',
  exportPurchaseSummary: '/prline/exportPurchaseSummary',
  exportTemplate: '/file/modelDownload?fileName=计划导入模板',
  exportProcurePlan: '/prline/doExport',
  exportProcurePlanDMY: 'prline/doExportDMY',
  exportBYJ: 'prline/doExportBYJ',
  markRequiredPlan: 'prline/markRequiredPlan',
  splitProcurePlan: '/prline/splitPrline',
  transToRfqLine: '/prline/transToRfqLine',
  transToPoLine: '/prline/transToPoline',
  cancelPrline: '/prline/cancelPrline',
  getPrlineByDate: '/prline/getPrlineByDate',
  querySecondStoreHousePrline: '/prline/querySecondStoreHousePrline',
  modifyPrlineCostType: '/prline/modifyPrline',
  getPrlineSysIdByRfqlineSysId: '/prline/getPrlineSysId',
  getPrlineSysIdByPolineSysId: '/prline/getPrlineSysIdByPolineSysId',
  getRfqlineSysIdByPolineSysId: '/prline/getRfqlineSysIdByPolineSysId',
  getProcureCount: '/prline/getProcureCount',
  getIndividualPrlineInfo: '/prline/getIndividualPrlineInfo',
  doExportIndividualPrline: 'prline/doIndividualPrlineExport',
  doReportOfUniOrder: '/prline/doReportOfUniOrder',
  getUnpurchasedCount: '/prline/getUnpurchasedCount',
  getApprovedAndFinishedPlan: '/prline/getApprovedAndFinishedPlan',
  turnMatu: 'prline/turnMatu',
  turnPurchase: 'prline/turnPurchase',
  queryModifyJwspHis: '/prline/queryModifyJwspHis',
  creatPolineBy2Contracts: '/contract/creatPolineByContracts',
  generatePlanOfOffice: '/prline/generatePlanOfOffice',
  findContractByPrline: '/contract/findContractByPrline',
  createHJPMCPlan: '/prline/createHJPMCPlan',
  queryHJPMCInfoDetail: '/prline/queryHJPMCInfoDetail',
  modifyPrline: '/prline/modifyPrline',
  queryCarNumber: '/prline/queryCarNumber',
  findRequsetBy: '/prline/findRequestedBy',
  getPhyentityPrintData: '/prline/getPhyentityPrintData',
  directOutStorehouse: '/prline/directOutStorehouse',
  getSelectYearPrline: '/prline/getSelectYearPrline',
  checkMonthPrline: '/prline/checkMonthPrline',
  getDiscardPrintData: '/prline/getDiscardPrintData',
  approDiscardMatr: '/prline/approDiscardMatr',
  approDiscardMatu: '/discard/approDiscardMatu',
  enterStoreDiscard: '/discard/enterStoreDiscard',
  addDMYPrline: '/prline/addDMYPrline',
  doExport10: '/prline/doExport10',
  doExportTransfer: '/prline/doExportTransfer',
  transGKPrline: '/prline/transGKPrline',
  setConfig: '/config/setConfig',
  getCompanyConfig: '/config/getCompanyConfig',
  approve: '/appro/approve'
}

export function approve (data) {
  return axios({
    url: api.approve,
    method: 'post',
    data: data
  })
}

export function getCompanyConfig (data) {
  return axios({
    url: api.getCompanyConfig,
    method: 'post',
    data: data
  })
}

export function setConfig (data) {
  return axios({
    url: api.setConfig,
    method: 'post',
    data: data
  })
}

export function transGKPrline (data) {
  return axios({
    url: api.transGKPrline,
    method: 'post',
    data: data
  })
}

export function approDiscardMatu (data) {
  return axios({
    url: api.approDiscardMatu,
    method: 'post',
    data: data
  })
}

export function doExportTransfer (data) {
  return axios({
    url: api.doExportTransfer,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}

export function doExport10 (data) {
  return axios({
    url: api.doExport10,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}

export function addDMYPrline (data) {
  return axios({
    url: api.addDMYPrline,
    method: 'post',
    data: data
  })
}

export function enterStoreDiscard (data) {
  return axios({
    url: api.enterStoreDiscard,
    method: 'post',
    data: data
  })
}

export function approDiscardMatr (data) {
  return axios({
    url: api.approDiscardMatr,
    method: 'post',
    data: data
  })
}

export function getDiscardPrintData (data) {
  return axios({
    url: api.getDiscardPrintData,
    method: 'post',
    data: data
  })
}

export function checkMonthPrline (data) {
  return axios({
    url: api.checkMonthPrline,
    method: 'post',
    data: data
  })
}

export function getSelectYearPrline (data) {
  return axios({
    url: api.getSelectYearPrline,
    method: 'post',
    data: data
  })
}

export function directOutStorehouse (data) {
  return axios({
    url: api.directOutStorehouse,
    method: 'post',
    data: data
  })
}

export function getPhyentityPrintData (data) {
  return axios({
    url: api.getPhyentityPrintData,
    method: 'post',
    data: data
  })
}

export function findRequsetBy (data) {
  return axios({
    url: api.findRequsetBy,
    method: 'post',
    data: data
  })
}

export function queryCarNumber (data) {
  return axios({
    url: api.queryCarNumber,
    method: 'post',
    data: data
  })
}

export function modifyPrline (data) {
  return axios({
    url: api.modifyPrline,
    method: 'post',
    data: data
  })
}

export function queryHJPMCInfoDetail (data) {
  return axios({
    url: api.queryHJPMCInfoDetail,
    method: 'post',
    data: data
  })
}

export function queryPrlineAssigee (data) {
  return axios({
    url: api.queryPrlineAssigee,
    method: 'post',
    data: data
  })
}

export function findContractByPrline (data) {
  return axios({
    url: api.findContractByPrline,
    method: 'post',
    data: data
  })
}

export function creatPolineBy2Contracts (data) {
  return axios({
    url: api.creatPolineBy2Contracts,
    method: 'post',
    data: data
  })
}

export function queryPrlineRequest (data) {
  return axios({
    url: api.queryPrlineRequest,
    method: 'post',
    data: data
  })
}

export function doReportOfUniOrder (parameter) {
  return axios({
    url: api.doReportOfUniOrder,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function getProcurePlanByPageNo (data) {
  return axios({
    url: api.getProcurePlanByPageNo,
    method: 'post',
    data: data
  })
}

export function getPrlineApprByPageNo (data) {
  return axios({
    url: api.getPrlineApprByPageNo,
    method: 'post',
    data: data
  })
}

export function getApproveRecordByNum (parameter) {
  return axios({
    url: api.getApproveRecordByNum,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function approveProcurePlan (data) {
  return axios({
    url: api.approveProcurePlan,
    method: 'post',
    data: data
  })
}

export function forcedTerminate (data) {
  return axios({
    url: api.forcedTerminate,
    method: 'post',
    data: data
  })
}

export function deleteProcurePlan (data) {
  return axios({
    url: api.deleteProcurePlan,
    method: 'post',
    data: data
  })
}

export function modifyProcurePlan (data) {
  return axios({
    url: api.modifyProcurePlan,
    method: 'post',
    data: data
  })
}

export function exportProcurePlan (data) {
  return axios({
    url: api.exportProcurePlan,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportProcurePlan (data) {
  return axios({
    url: api.doExportProcurePlan,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function exportPurchaseSummary (data) {
  return axios({
    url: api.exportPurchaseSummary,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function exportTemplate (data) {
  return axios({
    url: api.exportTemplate,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export function exportProcurePlanDMY (data) {
  return axios({
    url: api.exportProcurePlanDMY,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function exportBYJ (data) {
  return axios({
    url: api.exportBYJ,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function markRequiredPlan (parameter) {
  return axios({
    url: api.markRequiredPlan,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}
export function splitProcurePlan (data) {
  return axios({
    url: api.splitProcurePlan,
    method: 'post',
    data: data
  })
}

export function transToRfqLine (data) {
  return axios({
    url: api.transToRfqLine,
    method: 'post',
    data: data
  })
}

export function transToPoLine (data) {
  return axios({
    url: api.transToPoLine,
    method: 'post',
    data: data
  })
}

export function cancelPrline (data) {
  return axios({
    url: api.cancelPrline,
    method: 'post',
    data: data
  })
}

export function getPrlineByDate (data) {
  return axios({
    url: api.getPrlineByDate,
    method: 'post',
    data: data
  })
}

export function querySecondStoreHousePrline (data) {
  return axios({
    url: api.querySecondStoreHousePrline,
    method: 'post',
    data: data
  })
}

export function modifyPrlineCostType (data) {
  return axios({
    url: api.modifyPrlineCostType,
    method: 'post',
    data: data
  })
}

export function getPrlineSysIdByRfqlineSysId (rfqlineSysId) {
  return axios({
    url: api.getPrlineSysIdByRfqlineSysId + `/${rfqlineSysId}`,
    method: 'post'
  })
}

export function getPrlineSysIdByPolineSysId (polineSysId) {
  return axios({
    url: api.getPrlineSysIdByPolineSysId + `/${polineSysId}`,
    method: 'post'
  })
}

export function getRfqlineSysIdByPolineSysId (polineSysId) {
  return axios({
    url: api.getRfqlineSysIdByPolineSysId + `/${polineSysId}`,
    method: 'post'
  })
}

export function getProcureCount (data) {
  return axios({
    url: api.getProcureCount,
    method: 'post',
    data: data
  })
}

export function getIndividualPrlineInfo (data) {
  return axios({
    url: api.getIndividualPrlineInfo,
    method: 'post',
    data: data
  })
}

export function doExportIndividualPrline (data) {
  return axios({
    url: api.doExportIndividualPrline,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function getUnpurchasedCount (data) {
  return axios({
    url: api.getUnpurchasedCount,
    method: 'post',
    data: data
  })
}

export function getApprovedAndFinishedPlan (data) {
  return axios({
    url: api.getApprovedAndFinishedPlan,
    method: 'post',
    data: data
  })
}

export function turnMatu (data) {
  return axios({
    url: api.turnMatu,
    method: 'post',
    data: data
  })
}

export function turnPurchase (data) {
  return axios({
    url: api.turnPurchase,
    method: 'post',
    data: data
  })
}

export function queryModifyJwspHis (data) {
  return axios({
    url: api.queryModifyJwspHis,
    method: 'post',
    data: data
  })
}

export function generatePlanOfOffice (parameter) {
  return axios({
    url: api.generatePlanOfOffice,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function createHJPMCPlan (data) {
  return axios({
    url: api.createHJPMCPlan,
    method: 'post',
    data: data
  })
}

export default api
