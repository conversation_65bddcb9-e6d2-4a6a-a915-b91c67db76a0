import { axios } from '@/utils/request'

const api = {
  queryDisplayOrder: '/homePage/queryDisplayOrder',
  queryDisplayDefect: '/homePage/queryDisplayDefect',
  queryAllOrder: '/homePage/queryAllOrder'
}

export function queryDisplayOrder (data) {
  return axios({
    url: api.queryDisplayOrder,
    method: 'post',
    data: data
  })
}

export function queryDisplayDefect (data) {
  return axios({
    url: api.queryDisplayDefect,
    method: 'post',
    data: data
  })
}

export function queryAllOrder (data) {
  return axios({
    url: api.queryAllOrder,
    method: 'post',
    data: data
  })
}

export default api
