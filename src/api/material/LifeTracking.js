
import { axios } from '@/utils/request'

const api = {
  queryAccessoryLife: '/workorder/queryAccessoryLife',
  exportAccessoryLife: '/workorder/exportAccessoryLife',
  queryAccessoryConsumption: '/workorder/queryAccessoryConsumption',
  exportAccessoryConsumption: '/workorder/exportAccessoryConsumption'
}
export function modifyBit (parameter) {
  return axios({
    url: api.modifyBit,
    method: 'post',
    data: parameter
  })
}
export function queryAccessoryLife (data) {
  return axios({
    url: api.queryAccessoryLife,
    method: 'post',
    data: data
  })
}
// 生成excel
export function exportAccessoryLife (data) {
  return axios({
    url: api.exportAccessoryLife,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}

export function queryAccessoryConsumption (data) {
  return axios({
    url: api.queryAccessoryConsumption,
    method: 'post',
    data: data
  })
}
// 生成excel
export function exportAccessoryConsumption (data) {
  return axios({
    url: api.exportAccessoryConsumption,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}
export default api
