// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
import { axios } from '@/utils/request'

/**
 *  queryBusiness: 查询业务总表
 */
const api = {
  queryBusiness: '/business/queryBusiness',
  doMatrDetailExports: '/business/doMatrDetailExports'
}

export function queryBusiness (data) {
  return axios({
    url: api.queryBusiness,
    method: 'post',
    data: data
  })
}
export function doMatrDetailExports (data) {
  return axios({
    url: api.doMatrDetailExports,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export default api
