import { axios } from '@/utils/request'

const api = {
  modifyPersonalTableRecord: '/personalTable/modifyPersonalTableRecord',
  queryPersonalTableRecord: '/personalTable/queryPersonalTableRecord',
  queryPersonalCockpitRecord: '/personalTable/queryPersonalCockpitRecord',
  modifyPersonalCockpitRecord: '/personalTable/modifyPersonalCockpitRecord'
}

export function queryPersonalCockpitRecord (data) {
  return axios({
    url: api.queryPersonalCockpitRecord,
    method: 'post',
    data: data
  })
}

export function modifyPersonalCockpitRecord (data) {
  return axios({
    url: api.modifyPersonalCockpitRecord,
    method: 'post',
    data: data
  })
}

export function modifyPersonalTableRecord (data) {
  return axios({
    url: api.modifyPersonalTableRecord,
    method: 'post',
    data: data
  })
}

export function queryPersonalTableRecord (data) {
  return axios({
    url: api.queryPersonalTableRecord,
    method: 'post',
    data: data
  })
}

export default api
