import { axios } from '@/utils/request'

const api = {
  getMaterialsWasteManageByPages: '/wzport/materialswaste/getMaterialsWasteManageByPages',
  modifyMaterialsWasteManageInfo: '/wzport/materialswaste/modifyMaterialsWasteManageInfo',
  getMaterialsWasteManageExport: '/wzport/materialswaste/getMaterialsWasteManageExport',
  getMaterialsWasteDisposalByPages: '/wzport/materialswaste/getMaterialsWasteDisposalByPages',
  modifyMaterialsWasteDisposalInfo: '/wzport/materialswaste/modifyMaterialsWasteDisposalInfo',
  getMaterialsWasteDisposalExport: '/wzport/materialswaste/getMaterialsWasteDisposalExport'
}

export function getMaterialsWasteManageByPages (parameter) {
  return axios({
    url: api.getMaterialsWasteManageByPages,
    method: 'post',
    data: parameter
  })
}

export function modifyMaterialsWasteManageInfo (parameter) {
  return axios({
    url: api.modifyMaterialsWasteManageInfo,
    method: 'post',
    data: parameter
  })
}
export function getMaterialsWasteManageExport (parameter) {
  return axios({
    url: api.getMaterialsWasteManageExport,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}

export function getMaterialsWasteDisposalByPages (parameter) {
  return axios({
    url: api.getMaterialsWasteDisposalByPages,
    method: 'post',
    data: parameter
  })
}

export function modifyMaterialsWasteDisposalInfo (parameter) {
  return axios({
    url: api.modifyMaterialsWasteDisposalInfo,
    method: 'post',
    data: parameter
  })
}
export function getMaterialsWasteDisposalExport (parameter) {
  return axios({
    url: api.getMaterialsWasteDisposalExport,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}
