import { axios } from '@/utils/request'

/**
 *  queryAccountAge: 账龄计算
 *  doAgeExports: 账龄下载
 */
const api = {
  queryAccountAge: '/accountage/countAccountAge',
  doAgeExports: '/accountage/ageExports'
}

export function queryAccountAge (data) {
  return axios({
    url: api.queryAccountAge,
    method: 'post',
    data: data
  })
}

export function doAgeExports (data) {
  return axios({
    url: api.doAgeExports,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export default api
