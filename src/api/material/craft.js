// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { axios } from '@/utils/request'

/**
 *  queryCraft:
 *  modifyCraft:
 *  queryCraftDetail
 *  saveCraftPerson
 *  delCraftPerson
 *  queryLabourConfig
 *  saveCraftLC
 *  delCraftLC
 */
const api = {
  queryCraft: '/craft/queryCraft',
  modifyCraft: '/craft/modifyCraft',
  queryCraftDetail: '/craft/queryCraftDetail',
  saveCraftPerson: '/craft/saveCraftPerson',
  delCraftPerson: '/craft/delCraftPerson',
  queryLabourConfig: '/craft/queryLabourConfig',
  saveCraftLC: '/craft/saveCraftLC',
  delCraftLC: '/craft/delCraftLC'
}

export function queryCraft (parameter) {
  return axios({
    url: api.queryCraft,
    method: 'post',
    data: parameter
  })
}

export function modifyCraft (parameter) {
  return axios({
    url: api.modifyCraft,
    method: 'post',
    data: parameter
  })
}

export function queryCraftDetail (parameter) {
  return axios({
    url: api.queryCraftDetail,
    method: 'post',
    data: parameter
  })
}

export function saveCraftPerson (parameter) {
  return axios({
    url: api.saveCraftPerson,
    method: 'post',
    data: parameter
  })
}

export function delCraftPerson (parameter) {
  return axios({
    url: api.delCraftPerson,
    method: 'post',
    data: parameter
  })
}

export function queryLabourConfig (parameter) {
  return axios({
    url: api.queryLabourConfig,
    method: 'post',
    data: parameter
  })
}

export function saveCraftLC (parameter) {
  return axios({
    url: api.saveCraftLC,
    method: 'post',
    data: parameter
  })
}

export function delCraftLC (parameter) {
  return axios({
    url: api.delCraftLC,
    method: 'post',
    data: parameter
  })
}

export default api
