import { axios } from '@/utils/request'

const api = {
  getEnergyConsumptionBaseByPages: '/wzport/energy/getEnergyConsumptionBaseByPages',
  modifyEnergyConsumptionBaseInfo: '/wzport/energy/modifyEnergyConsumptionBaseInfo',
  getEnergyConsumptionBaseApprovalByPages: '/wzport/energy/getEnergyConsumptionBaseApprovalByPages',
  getEnergyConsumptionDetails: '/wzport/energy/getEnergyConsumptionDetails',
  getEnergyConsumptionDetailsOfLastMonth: '/wzport/energy/getEnergyConsumptionDetailsOfLastMonth',
  getEnergyConsumptionReportForZHNHB: '/wzport/energyreport/getEnergyConsumptionReportForZHNHB',
  doExportZHNHB: '/wzport/energyreport/doExportZHNHB',
  getEnergyConsumptionReportForNYXHTZ: '/wzport/energyreport/getEnergyConsumptionReportForNYXHTZ',
  doExportNYXHTZ: '/wzport/energyreport/doExportNYXHTZ',
  getEnergyConsumptionReportForSCNYDH: '/wzport/energyreport/getEnergyConsumptionReportForSCNYDH',
  doExportSCNYDH: '/wzport/energyreport/doExportSCNYDH',
  getEnergyConsumptionReportForSHZX: '/wzport/energyreport/getEnergyConsumptionReportForSHZX',
  doExportSHZX: '/wzport/energyreport/doExportSHZX',
  getEnergyConsumptionReportForDHZZX: '/wzport/energyreport/getEnergyConsumptionReportForDHZZX',
  doExportDHZZX: '/wzport/energyreport/doExportDHZZX',
  getEnergyConsumptionReportForNYXHYB: '/wzport/energyreport/getEnergyConsumptionReportForNYXHYB',
  doExportNYXHYB: '/wzport/energyreport/doExportNYXHYB',
  queryEnergyLocation: '/energyLocation/queryEnergyLocation',
  uploadEnergyLocation: '/energyLocation/uploadEnergyLocation',
  modifyEnergyLocationForCard: '/energyLocation/modifyEnergyLocation',
  queryEnergyLocationForCard: '/energyLocation/queryEnergyLocationForCard',
  getEnergyLocationPie: '/energyLocation/getEnergyLocationPie',
  getEnergyLocationHistogram: '/energyLocation/getEnergyLocationHistogram',
  queryWenwuzn: '/wenwuzn/queryWenwuzn',
  queryEquipmentKpi: '/kpiManage/queryEquipmentKpi'
}

export function queryWenwuzn (parameter) {
  return axios({
    url: api.queryWenwuzn,
    method: 'post',
    data: parameter
  })
}

export function queryEquipmentKpi (parameter) {
  return axios({
    url: api.queryEquipmentKpi,
    method: 'post',
    data: parameter
  })
}

export function getEnergyLocationPie (parameter) {
  return axios({
    url: api.getEnergyLocationPie,
    method: 'post',
    data: parameter
  })
}

export function getEnergyLocationHistogram (parameter) {
  return axios({
    url: api.getEnergyLocationHistogram,
    method: 'post',
    data: parameter
  })
}

export function queryEnergyLocationForCard (parameter) {
  return axios({
    url: api.queryEnergyLocationForCard,
    method: 'post',
    data: parameter
  })
}

export function modifyEnergyLocationForCard (parameter) {
  return axios({
    url: api.modifyEnergyLocationForCard,
    method: 'post',
    data: parameter
  })
}

export function uploadEnergyLocation (parameter) {
  return axios({
    url: api.uploadEnergyLocation,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}

export function queryEnergyLocation (parameter) {
  return axios({
    url: api.queryEnergyLocation,
    // responseType: 'blob',
    method: 'post',
    data: parameter
  })
}

export function getEnergyConsumptionBaseByPages (parameter) {
  return axios({
    url: api.getEnergyConsumptionBaseByPages,
    method: 'post',
    data: parameter
  })
}

export function getEnergyConsumptionDetails (parameter) {
  return axios({
    url: api.getEnergyConsumptionDetails,
    method: 'post',
    data: parameter
  })
}

export function getEnergyConsumptionDetailsOfLastMonth (parameter) {
  return axios({
    url: api.getEnergyConsumptionDetailsOfLastMonth,
    method: 'post',
    data: parameter
  })
}

export function modifyEnergyConsumptionBaseInfo (parameter) {
  return axios({
    url: api.modifyEnergyConsumptionBaseInfo,
    method: 'post',
    data: parameter
  })
}

export function getEnergyConsumptionBaseApprovalByPages (parameter) {
  return axios({
    url: api.getEnergyConsumptionBaseApprovalByPages,
    method: 'post',
    data: parameter
  })
}

export function getEnergyConsumptionReportForZHNHB (parameter) {
  return axios({
    url: api.getEnergyConsumptionReportForZHNHB,
    method: 'post',
    data: parameter
  })
}
export function doExportZHNHB (parameter) {
  return axios({
    url: api.doExportZHNHB,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}

export function getEnergyConsumptionReportForNYXHTZ (parameter) {
  return axios({
    url: api.getEnergyConsumptionReportForNYXHTZ,
    method: 'post',
    data: parameter
  })
}

export function doExportNYXHTZ (parameter) {
  return axios({
    url: api.doExportNYXHTZ,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}

export function getEnergyConsumptionReportForSCNYDH (parameter) {
  return axios({
    url: api.getEnergyConsumptionReportForSCNYDH,
    method: 'post',
    data: parameter
  })
}

export function doExportSCNYDH (parameter) {
  return axios({
    url: api.doExportSCNYDH,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}
export function getEnergyConsumptionReportForSHZX (parameter) {
  return axios({
    url: api.getEnergyConsumptionReportForSHZX,
    method: 'post',
    data: parameter
  })
}
export function doExportSHZX (parameter) {
  return axios({
    url: api.doExportSHZX,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}

export function getEnergyConsumptionReportForDHZZX (parameter) {
  return axios({
    url: api.getEnergyConsumptionReportForDHZZX,
    method: 'post',
    data: parameter
  })
}
export function doExportDHZZX (parameter) {
  return axios({
    url: api.doExportDHZZX,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}

export function getEnergyConsumptionReportForNYXHYB (parameter) {
  return axios({
    url: api.getEnergyConsumptionReportForNYXHYB,
    method: 'post',
    data: parameter
  })
}
export function doExportNYXHYB (parameter) {
  return axios({
    url: api.doExportNYXHYB,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}
