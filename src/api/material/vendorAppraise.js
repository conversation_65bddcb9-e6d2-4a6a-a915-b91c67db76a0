// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { qs, axios } from '@/utils/request'

/**
 *  queryVendorInfo: 获取供应商信息
 */
const api = {
  approveVendorAppraise: '/vendor/approVendorScore'
}

export function approveVendorAppraise (parameter) {
  return axios({
    url: api.approveVendorAppraise,
    method: 'post',
    data: parameter
  })
}
