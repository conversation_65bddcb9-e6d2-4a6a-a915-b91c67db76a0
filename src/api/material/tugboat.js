import { axios } from '@/utils/request'

const api = {
  getTugboatInfoByPages: '/wzport/tugboatinfo/getTugboatInfoByPages',
  modifyTugboatInfo: '/wzport/tugboatinfo/modifyTugboatInfo',
  getTugboatInfos: '/wzport/tugboatinfo/getTugboatInfos',
  getTugboatFuelConsumptionByPages: '/wzport/tugboatfuelconsumption/getTugboatFuelConsumptionByPages',
  modifyTugboatFuelConsumption: '/wzport/tugboatfuelconsumption/modifyTugboatFuelConsumption',
  getTugboatFuelConsumptionApprovalByPages: '/wzport/tugboatfuelconsumption/getTugboatFuelConsumptionApprovalByPages',
  getTugboatShipNumberReport: '/wzport/tugboatreport/getTugboatShipNumberReport',
  exportTugboatShipNumberReport: '/wzport/tugboatreport/exportTugboatShipNumberReport',
  getTugboatFuelConsumptoinReport: '/wzport/tugboatreport/getTugboatFuelConsumptoinReport',
  exportTugboatFuelConsumptoinReport: '/wzport/tugboatreport/exportTugboatFuelConsumptoinReport'
}

export function getTugboatInfoByPages (parameter) {
  return axios({
    url: api.getTugboatInfoByPages,
    method: 'post',
    data: parameter
  })
}

export function modifyTugboatInfo (parameter) {
  return axios({
    url: api.modifyTugboatInfo,
    method: 'post',
    data: parameter
  })
}

export function getTugboatInfos (parameter) {
  return axios({
    url: api.getTugboatInfos,
    method: 'post',
    data: parameter
  })
}

export function getTugboatFuelConsumptionByPages (parameter) {
  return axios({
    url: api.getTugboatFuelConsumptionByPages,
    method: 'post',
    data: parameter
  })
}

export function modifyTugboatFuelConsumption (parameter) {
  return axios({
    url: api.modifyTugboatFuelConsumption,
    method: 'post',
    data: parameter
  })
}

export function getTugboatFuelConsumptionApprovalByPages (parameter) {
  return axios({
    url: api.getTugboatFuelConsumptionApprovalByPages,
    method: 'post',
    data: parameter
  })
}

export function getTugboatShipNumberReport (parameter) {
  return axios({
    url: api.getTugboatShipNumberReport,
    method: 'post',
    data: parameter
  })
}

export function exportTugboatShipNumberReport (parameter) {
  return axios({
    url: api.exportTugboatShipNumberReport,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}

export function getTugboatFuelConsumptoinReport (parameter) {
  return axios({
    url: api.getTugboatFuelConsumptoinReport,
    method: 'post',
    data: parameter
  })
}

export function exportTugboatFuelConsumptoinReport (parameter) {
  return axios({
    url: api.exportTugboatFuelConsumptoinReport,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}
