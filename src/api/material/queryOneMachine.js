
// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { axios } from '@/utils/request'

/**
 *  queryMatrDetail: 查询单机消耗
 *  queryMatuDetail: 导出单机消耗
 */
const api = {
  queryOneMachine: '/matu/queryMatuOneMachine',
  exportOneMachine: '/matu/exportMatuOneMachine'
}

export function queryOneMachine (data) {
  return axios({
    url: api.queryOneMachine,
    method: 'post',
    data: data
  })
}

export function exportOneMachine (data) {
  return axios({
    url: api.exportOneMachine,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export default api
