// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
import { axios } from '@/utils/request'

/**
 *  getCapitalOccupancyInfo: 获取各物资大类资金占用率详细信息
 */
const api = {
  getCapitalOccupancyInfo: '/capitaloccupancy/queryCapitalOccupancyInfo'
}

export function getCapitalOccupancyInfo (parameter) {
  return axios({
    url: api.getCapitalOccupancyInfo,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export default api
