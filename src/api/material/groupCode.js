import { axios } from '@/utils/request'

/**
 * queryGroupCodeInfo: 查询组别代码信息
 */
const api = {
  queryGroupCodeInfo: 'groupCode/queryGroupCodeInfo',
  queryGroupCodeInfoAll: 'groupCode/queryGroupCodeInfoAll',
  modifyGroupCode: 'groupCode/modifyGroupCode'
}

export function queryGroupCodeInfo (data) {
  return axios({
    url: api.queryGroupCodeInfo,
    method: 'post',
    data: data
  })
}

export function queryGroupCodeInfoAll (data) {
  return axios({
    url: api.queryGroupCodeInfoAll,
    method: 'post',
    data: data
  })
}

export function modifyGroupCode (data) {
  return axios({
    url: api.modifyGroupCode,
    method: 'post',
    data: data
  })
}

export default api
