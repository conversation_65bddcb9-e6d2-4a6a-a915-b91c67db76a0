// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { axios } from '@/utils/request'

/**
 *  queryDiscardMatr:
 */
const api = {
  queryDiscardMatr: '/discard/queryDiscardMatr',
  modifyDiscardAfterMatr: '/discard/modifyDiscardAfterMatr',
  sendToTender: '/discard/sendToTender',
  getDiscardPrintData: '/discard/getDiscardPrintData',
  sendToContract: '/discard/sendToContract',
  // 出库
  queryDiscardMatu: '/discard/queryDiscardMatu',
  modifyDiscardMatu: '/discard/modifyDiscardMatu',
  getDiscardMatuPrintData: '/discard/getDiscardMatuPrintData',
  exitDiscardStore: '/discard/exitDiscardStore',
  queryMatuMaterial: '/discard/queryMatuMaterial',

  queryDiscardSettleAccounts: '/discard/queryDiscardSettleAccounts',
  modifyDiscardSettleAccounts: '/discard/modifyDiscardSettleAccounts',
  approDiscardSettle: '/discard/approDiscardSettle',
  pushEasDiscard: '/discard/pushEasDiscard',
  getPrintSettleData: '/discard/getPrintSettleData',
  queryDiscardLedger: '/discard/queryDiscardLedger',
  exportDiscardLedger: '/discard/exportDiscardLedger',
  getDiscardMaterialInfo: '/discard/getDiscardMaterialInfo',
  sendToSettle: '/discard/sendToSettle',
  returnCurbal: '/discard/returnCurbal',
  returnDiscardMatu: '/discard/returnDiscardMatu',
  queryMaterialNumCombine: '/discard/queryMaterialNumCombine',
  modifyMaterialNumCombine: '/discard/modifyMaterialNumCombine',
  queryMaterialForCombine: '/discard/queryMaterialForCombine'
}

export function modifyMaterialNumCombine (parameter) {
  return axios({
    url: api.modifyMaterialNumCombine,
    method: 'post',
    data: parameter
  })
}

export function queryMaterialForCombine (parameter) {
  return axios({
    url: api.queryMaterialForCombine,
    method: 'post',
    data: parameter
  })
}

export function queryMaterialNumCombine (parameter) {
  return axios({
    url: api.queryMaterialNumCombine,
    method: 'post',
    data: parameter
  })
}

export function returnDiscardMatu (parameter) {
  return axios({
    url: api.returnDiscardMatu,
    method: 'post',
    data: parameter
  })
}

export function returnCurbal (parameter) {
  return axios({
    url: api.returnCurbal,
    method: 'post',
    data: parameter
  })
}

export function sendToSettle (parameter) {
  return axios({
    url: api.sendToSettle,
    method: 'post',
    data: parameter
  })
}

export function getDiscardMaterialInfo (parameter) {
  return axios({
    url: api.getDiscardMaterialInfo,
    method: 'post',
    data: parameter
  })
}

export function exportDiscardLedger (parameter) {
  return axios({
    url: api.exportDiscardLedger,
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}

export function queryDiscardLedger (parameter) {
  return axios({
    url: api.queryDiscardLedger,
    method: 'post',
    data: parameter
  })
}

export function getPrintSettleData (parameter) {
  return axios({
    url: api.getPrintSettleData,
    method: 'post',
    data: parameter
  })
}

export function approDiscardSettle (parameter) {
  return axios({
    url: api.approDiscardSettle,
    method: 'post',
    data: parameter
  })
}

export function pushEasDiscard (parameter) {
  return axios({
    url: api.pushEasDiscard,
    method: 'post',
    data: parameter
  })
}

export function queryDiscardSettleAccounts (parameter) {
  return axios({
    url: api.queryDiscardSettleAccounts,
    method: 'post',
    data: parameter
  })
}

export function modifyDiscardSettleAccounts (parameter) {
  return axios({
    url: api.modifyDiscardSettleAccounts,
    method: 'post',
    data: parameter
  })
}

export function exitDiscardStore (parameter) {
  return axios({
    url: api.exitDiscardStore,
    method: 'post',
    data: parameter
  })
}

export function queryMatuMaterial (parameter) {
  return axios({
    url: api.queryMatuMaterial,
    method: 'post',
    data: parameter
  })
}

export function getDiscardMatuPrintData (parameter) {
  return axios({
    url: api.getDiscardMatuPrintData,
    method: 'post',
    data: parameter
  })
}

export function modifyDiscardMatu (parameter) {
  return axios({
    url: api.modifyDiscardMatu,
    method: 'post',
    data: parameter
  })
}

export function queryDiscardMatu (parameter) {
  return axios({
    url: api.queryDiscardMatu,
    method: 'post',
    data: parameter
  })
}

export function sendToContract (parameter) {
  return axios({
    url: api.sendToContract,
    method: 'post',
    data: parameter
  })
}

export function getDiscardPrintData (parameter) {
  return axios({
    url: api.getDiscardPrintData,
    method: 'post',
    data: parameter
  })
}

export function sendToTender (parameter) {
  return axios({
    url: api.sendToTender,
    method: 'post',
    data: parameter
  })
}

export function modifyDiscardAfterMatr (parameter) {
  return axios({
    url: api.modifyDiscardAfterMatr,
    method: 'post',
    data: parameter
  })
}

export function queryDiscardMatr (parameter) {
  return axios({
    url: api.queryDiscardMatr,
    method: 'post',
    data: parameter
  })
}

export default api
