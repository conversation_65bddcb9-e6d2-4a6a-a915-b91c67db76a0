import { axios } from '@/utils/request'

/**
 *  assetOutBudget.js
 *  queryBudget: 获取计划
 *  queryBudgetTotal 获取汇总
 *  queryYwlb 获取业务类型
 *  queryFylx 获取费用类型
 *  modifyBudget: 修改计划
 *  deleteBudget: 删除计划
 *  queryLastYearTotal: 获取上一年预算
 */

const api = {
  queryBudget: '/assetOutBudget/queryBudget',
  queryBudgetTotal: '/assetOutBudget/queryBudgetTotal',
  modifyBudget: '/assetOutBudget/modifyBudget',
  deleteBudget: '/assetOutBudget/modifyBudget',
  queryYwlb: '/assetOutBudget/queryYwlb',
  queryFylx: '/assetOutBudget/queryFylx',
  queryLastYearTotal: '/assetOutBudget/queryLastYearTotal',
  exportBudget: '/assetOutBudget/exportBudget',
  exportBudgetTotal: '/assetOutBudget/exportBudgetTotal',
  approve: '/assetOutBudget/approve',
  queryYearBudget: '/assetOutBudget/queryYearBudget',
  updatePolineFylx: '/poline/updatePolineFylx'
}

export function updatePolineFylx (data) {
  return axios({
    url: api.updatePolineFylx,
    method: 'post',
    data: data
  })
}

export function queryYearBudget (data) {
  return axios({
    url: api.queryYearBudget,
    method: 'post',
    data: data
  })
}
export function exportBudgetTotal (data) {
  return axios({
    url: api.exportBudgetTotal,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}
export function exportBudget (data) {
  return axios({
    url: api.exportBudget,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}
export function queryLastYearTotal (data) {
  return axios({
    url: api.queryLastYearTotal,
    method: 'post',
    data: data
  })
}
export function queryYwlb (data) {
  return axios({
    url: api.queryYwlb,
    method: 'post',
    data: data
  })
}
export function queryFylx (data) {
  return axios({
    url: api.queryFylx,
    method: 'post',
    data: data
  })
}
export function queryBudgetTotal (data) {
  return axios({
    url: api.queryBudgetTotal,
    method: 'post',
    data: data
  })
}
export function queryBudget (data) {
  return axios({
    url: api.queryBudget,
    method: 'post',
    data: data
  })
}

export function modifyBudget (data) {
  return axios({
    url: api.modifyBudget,
    method: 'post',
    data: data
  })
}

export function deleteBudget (data) {
  return axios({
    url: api.deleteBudget,
    method: 'post',
    data: data
  })
}

export function approve (data) {
  return axios({
    url: api.approve,
    method: 'post',
    data: data
  })
}

export default api
