// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { axios } from '@/utils/request'

/**
 *  login: 系统登入
 *  loginSys: 系统登入
 *  logout: 系统登出
 *  getInfo: 获取用户信息
 *  addUserInfo: 新增用户信息 -- 未启用
 *  updateUserInfo: 更新用户信息 -- 未启用
 *  getCurrentUserNav: 获取用户菜单信息
 */
const api = {
  login: '/auth/login',
  logout: '/auth/logout',
  loginSys: '/auth/loginSys',
  getInfo: 'auth/getUserInfo',
  // addUserInfo: '/user/saveUserInfo',
  // updateUserInfo: '/user/updateUserInfo',
  getCurrentUserNav: '/auth/userNav',
  changePassword: '/auth/changePassword'
}

export function login (data) {
  return axios({
    url: api.login,
    method: 'post',
    data: data
  })
}

export function logout (data) {
  return axios({
    url: api.logout,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function loginSys (data) {
  return axios({
    url: api.loginSys,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function getInfo (parameter) {
  return axios({
    url: api.getInfo,
    method: 'post',
    data: parameter
  })
}

export function getCurrentUserNav (data) {
  return axios({
    url: api.getCurrentUserNav,
    method: 'post',
    data: data
  })
}

export function changePassword (parameter) {
  return axios({
    url: api.changePassword,
    method: 'post',
    data: parameter
  })
}

export default api
