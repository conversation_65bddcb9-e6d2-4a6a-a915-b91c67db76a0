import { axios } from '@/utils/request'

const api = {
  queryReceivePrline: '/materialCockpit/queryReceivePrline',
  querySendPrline: '/materialCockpit/querySendPrline',
  queryMatr: '/materialCockpit/queryMatr',
  queryMatrDetail: '/materialCockpit/queryMatrDetail',
  queryMatu: '/materialCockpit/queryMatu',
  queryMatuDetail: '/materialCockpit/queryMatuDetail',
  queryContract: '/materialCockpit/queryContract',
  queryContractVendor: '/materialCockpit/queryContractVendor',
  queryContractPayVendor: '/materialCockpit/queryContractPayVendor',
  queryTender: '/materialCockpit/queryTender',
  querySendPrlineDetail: '/materialCockpit/querySendPrlineDetail',
  queryReceivePrlineDetail: '/materialCockpit/queryReceivePrlineDetail',
  queryMaterialGoodsTime: '/materialCockpit/queryMaterialGoodsTime'
}

export function queryMaterialGoodsTime (data) {
  return axios({
    url: api.queryMaterialGoodsTime,
    method: 'post',
    data: data
  })
}

export function queryContractPayVendor (data) {
  return axios({
    url: api.queryContractPayVendor,
    method: 'post',
    data: data
  })
}

export function querySendPrlineDetail (data) {
  return axios({
    url: api.querySendPrlineDetail,
    method: 'post',
    data: data
  })
}

export function queryReceivePrlineDetail (data) {
  return axios({
    url: api.queryReceivePrlineDetail,
    method: 'post',
    data: data
  })
}

export function queryTender (data) {
  return axios({
    url: api.queryTender,
    method: 'post',
    data: data
  })
}

export function queryContractVendor (data) {
  return axios({
    url: api.queryContractVendor,
    method: 'post',
    data: data
  })
}

export function queryContract (data) {
  return axios({
    url: api.queryContract,
    method: 'post',
    data: data
  })
}

export function queryMatu (data) {
  return axios({
    url: api.queryMatu,
    method: 'post',
    data: data
  })
}

export function queryMatuDetail (data) {
  return axios({
    url: api.queryMatuDetail,
    method: 'post',
    data: data
  })
}

export function queryMatrDetail (data) {
  return axios({
    url: api.queryMatrDetail,
    method: 'post',
    data: data
  })
}

export function queryMatr (data) {
  return axios({
    url: api.queryMatr,
    method: 'post',
    data: data
  })
}

export function querySendPrline (data) {
  return axios({
    url: api.querySendPrline,
    method: 'post',
    data: data
  })
}

export function queryReceivePrline (data) {
  return axios({
    url: api.queryReceivePrline,
    method: 'post',
    data: data
  })
}

export default api
