import { axios } from '@/utils/request'

/**
 * queryOfficeSuppliesQuota: 查询办公用品定额
 * modifyOfficeSuppliesQuota: 修改办公用品定额
 * checkQuotaByDept: 检测部门本月是否超出定额
 * updateOfficeSuppliesQuota: 更新办公用品定额(批量更新)
 */
const api = {
  queryOfficeSuppliesQuota: 'quota/queryOfficeSuppliesQuota',
  modifyOfficeSuppliesQuota: 'quota/modifyOfficeSuppliesQuota',
  checkQuotaByDept: 'quota/checkQuotaByDept',
  updateOfficeSuppliesQuota: 'quota/updateOfficeSuppliesQuota'
}

export function queryOfficeSuppliesQuota (data) {
  return axios({
    url: api.queryOfficeSuppliesQuota,
    method: 'post',
    data: data
  })
}

export function modifyOfficeSuppliesQuota (data) {
  return axios({
    url: api.modifyOfficeSuppliesQuota,
    method: 'post',
    data: data
  })
}

export function checkQuotaByDept (data) {
  return axios({
    url: api.checkQuotaByDept,
    method: 'post',
    data: data
  })
}

export function updateOfficeSuppliesQuota (data) {
  return axios({
    url: api.updateOfficeSuppliesQuota,
    method: 'post',
    data: data
  })
}

export default api
