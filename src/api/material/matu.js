// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 *  queryMatu: 退库查询
 *  queryMatuTemp: 移库查询
 *  updateMatuTemp: 移库领用
 *  queryMatuByDate: 根据月份获取出库金额
 *  queryPrlineWithMatu: 出库查询
 *  listStorehouseByMatrial: 查询仓库
 *  listMaterialBalances: 查询批次
 *  exitStore: 出库
 *  backStore: 退库
 *  exports: 下载出库单
 *  exportsByPrlineNum: 下载入库单（prlineNum）
 *  printByOnline 在线打印出库单
 *  totalAmount 已领用金额
 *  cancelPrlineMatu 核销领用单（将领用单状态改为approval011）
 *  queryErrTransMatu: 查询传送错误出库数据(嘉兴财务)
 *  transToFinancial: 将修正数据重新传送到财务系统
 */
const api = {
  queryMatu: '/matu/queryMatu',
  queryMatuTemp: '/matu/queryMatuTemp',
  updateMatuTemp: '/matu/updateMatuTemp',
  queryMatuByDate: '/matu/queryMatuByDate',
  queryPrlineWithMatu: '/matu/queryPrlineWithMatu',
  queryPrlineWithSecondStoreHouseMatu: '/matu/queryPrlineWithSecondStoreHouseMatu',
  listStorehouseByMatrial: '/matr/listStorehouseByMatrial',
  listMaterialBalances: '/matr/listMaterialBalances',
  exitStore: '/matu/exitStore',
  backStore: '/matu/backStore',
  exports: '/matu/doExport',
  doExportReq: '/matu/doExportReq',
  exportsByPrlineNum: '/matu/doExportByPrlineNum',
  printByOnline: '/matu/getPrintData',
  splitMatu: '/matu/splitMatu',
  totalAmount: '/matu/getTotalAmount',
  cancelPrlineMatu: '/matu/cancelPrlineMatu',
  modifyMatu: '/matu/modifyMatu',
  queryErrTransMatu: '/matu/queryErrTransMatu',
  transToFinancial: '/matu/transToFinancial',
  directMatu: '/matu/directMatu',
  uploadMatuForEas: '/matu/uploadMatuForEas',
  approMatu: '/matu/approMatu',
  approveBackStore: '/matu/approveBackStore',
  doExportTransfer: '/matu/doExportTransfer',
  updatePrlineWithMatuByDept: '/matu/updatePrlineWithMatuByDept',
  getThisYearXh: '/matu/getThisYearXh'
}

export function getThisYearXh (data) {
  return axios({
    url: api.getThisYearXh,
    method: 'post',
    data: data
  })
}

export function updatePrlineWithMatuByDept (data) {
  return axios({
    url: api.updatePrlineWithMatuByDept,
    method: 'post',
    data: data
  })
}

export function approveBackStore (data) {
  return axios({
    url: api.approveBackStore,
    method: 'post',
    data: data
  })
}

export function doExportTransfer (data) {
  return axios({
    url: api.doExportTransfer,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}
export function approMatu (data) {
  return axios({
    url: api.approMatu,
    method: 'post',
    data: data
  })
}

export function uploadMatuForEas (data) {
  return axios({
    url: api.uploadMatuForEas,
    method: 'post',
    data: data
  })
}

export function directMatu (data) {
  return axios({
    url: api.directMatu,
    method: 'post',
    data: data
  })
}

export function queryMatu (data) {
  return axios({
    url: api.queryMatu,
    method: 'post',
    data: data
  })
}

export function queryMatuTemp (data) {
  return axios({
    url: api.queryMatuTemp,
    method: 'post',
    data: data
  })
}

export function updateMatuTemp (data) {
  return axios({
    url: api.updateMatuTemp,
    method: 'post',
    data: data
  })
}

export function queryMatuByDate (data) {
  return axios({
    url: api.queryMatuByDate,
    method: 'post',
    data: data
  })
}

export function queryPrlineWithMatu (data) {
  return axios({
    url: api.queryPrlineWithMatu,
    method: 'post',
    data: data
  })
}

export function queryPrlineWithSecondStoreHouseMatu (data) {
  return axios({
    url: api.queryPrlineWithSecondStoreHouseMatu,
    method: 'post',
    data: data
  })
}

export function listStorehouseByMatrial (data) {
  return axios({
    url: api.listStorehouseByMatrial,
    method: 'post',
    data: data
  })
}

export function listMaterialBalances (data) {
  return axios({
    url: api.listMaterialBalances,
    method: 'post',
    data: data
  })
}

export function exitStore (data) {
  return axios({
    url: api.exitStore,
    method: 'post',
    data: data
  })
}

export function backStore (data) {
  return axios({
    url: api.backStore,
    method: 'post',
    data: data
  })
}

export function exports (data) {
  return axios({
    url: api.exports,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportReq (data) {
  return axios({
    url: api.doExportReq,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function exportsByPrlineNum (data) {
  return axios({
    url: api.exportsByPrlineNum,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function printByOnline (data) {
  return axios({
    url: api.printByOnline,
    method: 'post',
    data: data
  })
}

export function splitMatu (data) {
  return axios({
    url: api.splitMatu,
    method: 'post',
    data: data
  })
}

export function totalAmount (data) {
  return axios({
    url: api.totalAmount,
    method: 'post',
    data: data
  })
}

export function cancelPrlineMatu (data) {
  return axios({
    url: api.cancelPrlineMatu,
    method: 'post',
    data: data
  })
}

export function modifyMatu (data) {
  return axios({
    url: api.modifyMatu,
    method: 'post',
    data: data
  })
}

export function queryErrTransMatu (data) {
  return axios({
    url: api.queryErrTransMatu,
    method: 'post',
    data: data
  })
}

export function transToFinancial (data) {
  return axios({
    url: api.transToFinancial,
    method: 'post',
    data: data
  })
}
export default api
