// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { axios } from '@/utils/request'

/**
 *  queryStorehouseTree:查询库
 *  queryZoneTree:查询区
 *  queryFrameTree：查询架
 *  queryFloorTree：查询层
 *  queryBitTree：查询位
 */
const api = {
  queryStorehouse: '/storehouse/queryStorehouse',
  queryStorehouseTree: '/storehouse/queryStorehouseTree',
  queryZoneTree: '/zone/queryZoneTree',
  queryFrameTree: '/frame/queryFrameTree',
  queryFloorTree: '/floor/queryFloorTree',
  queryBitTree: '/bit/queryBitTree',
  modifyStorehouse: '/storehouse/modifyStorehouse',
  modifyZone: '/zone/modifyZone',
  modifyFrame: '/frame/modifyFrame',
  modifyFloor: '/floor/modifyFloor/',
  modifyBit: '/bit/modifyBit'
}
export function modifyBit (parameter) {
  return axios({
    url: api.modifyBit,
    method: 'post',
    data: parameter
  })
}
export function modifyFloor (parameter) {
  return axios({
    url: api.modifyFloor,
    method: 'post',
    data: parameter
  })
}
export function modifyFrame (parameter) {
  return axios({
    url: api.modifyFrame,
    method: 'post',
    data: parameter
  })
}
export function modifyZone (parameter) {
  return axios({
    url: api.modifyZone,
    method: 'post',
    data: parameter
  })
}
export function modifyStorehouse (parameter) {
  return axios({
    url: api.modifyStorehouse,
    method: 'post',
    data: parameter
  })
}
export function queryStorehouse (parameter) {
  return axios({
    url: api.queryStorehouse,
    method: 'post',
    data: parameter
  })
}
export function queryStorehouseTree (parameter) {
  return axios({
    url: api.queryStorehouseTree,
    method: 'post',
    data: parameter
  })
}
export function queryZoneTree (parameter) {
  return axios({
    url: api.queryZoneTree,
    method: 'post',
    data: parameter
  })
}
export function queryFrameTree (parameter) {
  return axios({
    url: api.queryFrameTree,
    method: 'post',
    data: parameter
  })
}
export function queryFloorTree (parameter) {
  return axios({
    url: api.queryFloorTree,
    method: 'post',
    data: parameter
  })
}
export function queryBitTree (parameter) {
  return axios({
    url: api.queryBitTree,
    method: 'post',
    data: parameter
  })
}
export default api
