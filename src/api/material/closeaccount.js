// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 *  queryMatu: 退库查询
 *  createViews: 创建视图
 */
const api = {
  queryInvStatistic: '/invStatistic/queryInvStatistic',
  modifyInvStatistic: '/invStatistic/modifyInvStatistic',
  importVoucher: '/invStatistic/importVoucher',
  doAddQc: '/invStatistic/doAddQc',
  setCloseDate: '/invStatistic/setCloseDate',
  getCloseDate: '/invStatistic/getCloseDate'
}

export function setCloseDate (data) {
  return axios({
    url: api.setCloseDate,
    method: 'post',
    data: data
  })
}

export function getCloseDate (data) {
  return axios({
    url: api.getCloseDate,
    method: 'post',
    data: data
  })
}

export function queryInvStatistic (data) {
  return axios({
    url: api.queryInvStatistic,
    method: 'post',
    data: data
  })
}

export function modifyInvStatistic (data) {
  return axios({
    url: api.modifyInvStatistic,
    method: 'post',
    data: data
  })
}

export function doAddQc (data) {
  return axios({
    url: api.doAddQc,
    method: 'post',
    data: data
  })
}

export function importVoucher (data) {
  return axios({
    url: api.importVoucher,
    method: 'post',
    data: data
  })
}

export default api
