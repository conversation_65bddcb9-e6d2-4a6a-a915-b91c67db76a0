// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
import { axios } from '@/utils/request'

/**
 *  getMaterialInfo: 获取各单据审批状态详细信息
 * getApproveHis: 按照单号查询审批历史
 *  exportApprove: 下载审批状态表格
 * exportHisJx：下载嘉兴报价单审批历史
 * exportHis:下载审批历史表格
 */
const api = {
  getApproveInfo: '/approve/queryApproveInfo',
  getApproveHis: '/rfqline/queryApproHisPrint',
  exportHis: '/rfqline/doExcelApproHis',
  exportHisJx: '/rfqline/doExportJx',
  exportApprove: '/approve/doExport',
  doPurchaseRequisitionsExport: '/rfqline/doPurchaseRequisitionsExport'
}

export function getApproveInfo (parameter) {
  return axios({
    url: api.getApproveInfo,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function getApproveHis (parameter) {
  return axios({
    url: api.getApproveHis,
    method: 'post',
    data: parameter
  })
}

export function exportApprove (data) {
  return axios({
    url: api.exportApprove,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function exportHis (data) {
  return axios({
    url: api.exportHis,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function exportHisJx (data) {
  return axios({
    url: api.exportHisJx,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doPurchaseRequisitionsExport (data) {
  return axios({
    url: api.doPurchaseRequisitionsExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export default api
