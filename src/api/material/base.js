// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { qs, axios } from '@/utils/request'
import { ORG_ID } from '@/store/mutation-types'
import Vue from 'vue'

/**
 *  getTreeById: 通过id查询公共模块对应tree型数据
 *  getCommboxById: 通过id查询公共模块对应下拉框数据
 *  getMaterialClassInfo: 获取基础类别信息
 *  getMaterialCodeInfo: 获取类别中详细信息
 *  getDeptInfo: 获取部门信息
 *  getOrgTreeInfo: 获取组织节点信息
 *  getPersonInfo: 获取员工信息
 *  getPersonInfoAll: 获取员工信息（不分页）
 *  modifyPersonInfo: 修改员工信息
 *  getOrgInfo: 获取组织信息
 *  modifyOrgDeptInfo: 修改组织部门信息
 *  deleteOrgDeptInfo: 删除组织部门信息
 *  getPersonRoleName: 获取当前用户角色名
 *  getEmergencyContact:获得紧急联系人
 */
const api = {
  getTreeById: '/base/queryTreeNodes',
  getCommboxById: '/base/queryCommboxNodes',
  getMaterialClassInfo: '/base/materialClassInfo',
  getMaterialCodeInfo: '/base/materialCodeInfo',
  getDeptInfo: 'dept/queryDeptsInfo',
  getOrgTreeInfo: 'org/queryOrgsTreeInfo',
  getPersonInfo: 'person/queryPersonInfo',
  getPersonInfoAll: 'person/getPersonInfoAll',
  modifyPersonInfo: 'person/modifyPersonInfo',
  getOrgInfo: 'org/queryOrgsInfo',
  modifyOrgDeptInfo: 'org/modifyOrgDeptInfo',
  deleteOrgDeptInfo: 'org/deleteOrgDeptInfo',
  selectMateNum: '/material/selectMateNum',
  selectJxMateNum: '/material/selectJxMateNum',
  selectVendorNum: '/vendor/selectVendorNum',
  getPersonRoleName: 'person/queryPersonRoleName',
  getEmergencyContact: 'person/getEmergencyContact',
  existingProcess: 'existingProcess',
  queryStorehouse: '/storehouse/queryStorehouse',
  queryNotice: '/notice/queryNotice',
  getCcAdminToken: '/getCcAdminToken',
  doCcImportExport: '/file/modelDownload?fileName=驰骋人员信息导入模板',
  doCcImportExportTest: '/file/modelDownload?fileName=驰骋人员信息导入模板-zs',
  modifyNotice: '/notice/modifyNotice'
}

export function doCcImportExport (data) {
  return axios({
    url: api.doCcImportExport,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export function doCcImportExportTest (data) {
  return axios({
    url: api.doCcImportExportTest,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export function getCcAdminToken (data) {
  return axios({
    url: api.getCcAdminToken,
    method: 'post',
    params: data,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function modifyNotice (parameter) {
  return axios({
    url: api.modifyNotice,
    method: 'post',
    data: parameter
  })
}

export function queryNotice (parameter) {
  return axios({
    url: api.queryNotice,
    method: 'post',
    data: parameter
  })
}

export function queryStorehouse (parameter) {
  return axios({
    url: api.queryStorehouse,
    method: 'post',
    data: parameter
  })
}

export function getCommboxById (data) {
  return axios({
    url: api.getCommboxById,
    method: 'post',
    data: {
      ...data,
      sqlParams: {
        orgId: Vue.ls.get(ORG_ID),
        ...data.sqlParams
      }
    }
  })
}

export function getTreeById (data) {
  return axios({
    url: api.getTreeById,
    method: 'post',
    data: {
      ...data,
      sqlParams: {
        orgId: Vue.ls.get(ORG_ID),
        ...data.sqlParams
      }
    }
  })
}

export function getMaterialClassInfo (parameter) {
  return axios({
    url: api.getMaterialClassInfo,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function getMaterialCodeInfo (parameter) {
  return axios({
    url: api.getMaterialCodeInfo,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function getDeptInfo (parameter) {
  return axios({
    url: api.getDeptInfo,
    method: 'post',
    data: parameter
  })
}

export function getOrgTreeInfo (parameter) {
  return axios({
    url: api.getOrgTreeInfo,
    method: 'post',
    data: parameter
  })
}

export function getPersonInfo (parameter) {
  return axios({
    url: api.getPersonInfo,
    method: 'post',
    data: parameter
  })
}

export function getPersonInfoAll (parameter) {
  return axios({
    url: api.getPersonInfoAll,
    method: 'post',
    data: parameter
  })
}

export function modifyPersonInfo (parameter) {
  return axios({
    url: api.modifyPersonInfo,
    method: 'post',
    data: parameter
  })
}

export function getOrgInfo (parameter) {
  return axios({
    url: api.getOrgInfo,
    method: 'post',
    data: parameter
  })
}

export function modifyOrgDeptInfo (parameter) {
  return axios({
    url: api.modifyOrgDeptInfo,
    method: 'post',
    data: parameter
  })
}

export function deleteOrgDeptInfo (parameter) {
  return axios({
    url: api.deleteOrgDeptInfo,
    method: 'post',
    data: parameter
  })
}

export function selectVendorNum (parameter) {
  return axios({
    url: api.selectVendorNum,
    method: 'get',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function selectMateNum (data) {
  return axios({
    url: api.selectMateNum,
    method: 'post',
    data: data
  })
}

export function selectJxMateNum (data) {
  return axios({
    url: api.selectJxMateNum,
    method: 'post',
    data: data
  })
}

export function getPersonRoleName (parameter) {
  return axios({
    url: api.getPersonRoleName,
    method: 'post',
    data: parameter
  })
}
export function getEmergencyContact (parameter) {
  return axios({
    url: api.getEmergencyContact,
    method: 'get',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function existingProcess (parameter) {
  return axios({
    url: api.existingProcess,
    method: 'get',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}
export default api
