// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { axios } from '@/utils/request'

/**
 *  getUserInfo: 获取用户信息
 *  modifyUserInfo: 修改用户信息
 *  getRoleInfoByUserNo: 获取角色列表
 *  getRoleInfo: 获取角色列表
 *  isPersonExist: 查找用户是否存在
 */
const api = {
  getUserInfo: '/um/getUserInfo',
  modifyUserInfo: '/um/modifyUserInfo',
  getRoleInfo: '/um/getRoleInfo',
  getRoleInfoByUserNo: '/um/getRoleInfoByUserNo',
  isPersonExist: '/um/isPersonExist',
  isUserExist: '/um/isUserExist',
  getRoleInfoByPages: '/um/getRoleInfoByPages',
  modifyRoleInfo: 'um/modifyRoleInfo',
  getButtonInfoList: 'um/getButtonInfoList',
  getButtonPermissonList: 'um/getButtonPermissonList',
  getMenuInfoList: 'um/getMenuInfoList',
  saveResoureByRoleId: 'um/saveResoureByRoleId',
  getResourceInfo: 'um/getResourceInfo',
  getMenuAllInfoList: 'um/getMenuAllInfoList',
  modifyResourceInfo: 'um/modifyResourceInfo',
  ccSync: 'ccSync'
}

export function ccSync (parameter) {
  return axios({
    url: api.ccSync,
    method: 'post',
    data: parameter
  })
}

export function getUserInfo (parameter) {
  return axios({
    url: api.getUserInfo,
    method: 'post',
    data: parameter
  })
}

export function modifyUserInfo (parameter) {
  return axios({
    url: api.modifyUserInfo,
    method: 'post',
    data: parameter
  })
}

export function getRoleInfoByUserNo (parameter) {
  return axios({
    url: api.getRoleInfoByUserNo,
    method: 'post',
    data: parameter
  })
}

export function getRoleInfo (parameter) {
  return axios({
    url: api.getRoleInfo,
    method: 'post',
    data: parameter
  })
}

export function getRoleInfoByPages (parameter) {
  return axios({
    url: api.getRoleInfoByPages,
    method: 'post',
    data: parameter
  })
}

export function modifyRoleInfo (parameter) {
  return axios({
    url: api.modifyRoleInfo,
    method: 'post',
    data: parameter
  })
}

/**
 * 查找人员是否存在
 * @param {*} parameter
 */
export function isPersonExist (parameter) {
  return axios({
    url: api.isPersonExist,
    method: 'post',
    data: parameter
  })
}

/**
 * 查找用户是否存在
 * @param {*} parameter
 */
export function isUserExist (parameter) {
  return axios({
    url: api.isUserExist,
    method: 'post',
    data: parameter
  })
}

/**
 * 根据功能ID查询对应按钮权限列表
 * @param {*} parameter
 */
export function getButtonPermissonList (parameter) {
  return axios({
    url: api.getButtonPermissonList,
    method: 'post',
    data: parameter
  })
}

/**
 * 根据功能ID查询对应按钮信息
 * @param {*} parameter
 */
export function getButtonInfoList (parameter) {
  return axios({
    url: api.getButtonInfoList,
    method: 'post',
    data: parameter
  })
}

/**
 * 根据角色ID查询树形菜单列表
 * @param {*} parameter
 */
export function getMenuInfoList (parameter) {
  return axios({
    url: api.getMenuInfoList,
    method: 'post',
    data: parameter
  })
}

export function saveResoureByRoleId (parameter) {
  return axios({
    url: api.saveResoureByRoleId,
    method: 'post',
    data: parameter
  })
}

export function getResourceInfo (parameter) {
  return axios({
    url: api.getResourceInfo,
    method: 'post',
    data: parameter
  })
}

export function getMenuAllInfoList (parameter) {
  return axios({
    url: api.getMenuAllInfoList,
    method: 'post',
    data: parameter
  })
}

export function modifyResourceInfo (parameter) {
  return axios({
    url: api.modifyResourceInfo,
    method: 'post',
    data: parameter
  })
}

export default api
