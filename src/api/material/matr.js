// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios, qs } from '@/utils/request'

/**
 *  queryPolineWithMatr: 入库查询
 *  connectInvoice: 预入库转正式
 *  enterStore: 入库处理
 *  backVendor: 入库冲红
 *  queryMatr: 冲红查询
 *  exports: 下载入库单
 *  exportsByPolineNum: 下载入库单（polineNum）
 *  queryMatrByDate：根据年月获取入库金额
 *  printByOnline 在线打印入库单
 * getMatuPrintSysId: 获得入库单对应的领用单信息
 *  queryErrTransMatr: 查询传送错误入库数据(嘉兴财务)
 *  transToFinancial: 将修正数据重新传送到财务系统
 *  backVendorEntity: 海建实物回空
 */
const api = {
  queryMatrDetail: '/matr/queryMatrDetail',
  queryPolineWithMatr: '/matr/queryPolineWithMatr',
  connectInvoice: '/matr/connectInvoice',
  // 修旧入库
  repairAndStorage: '/matr/repairAndStorage',
  enterStore: '/matr/enterStore',
  backVendor: '/matr/backVendor',
  queryMatr: '/matr/queryMatr',
  modifyMatr: '/matr/modifyMatr',
  exports: '/matr/doExport',
  exportsByPolineNum: '/matr/doExportByPolineNum',
  queryMatrByDate: '/matr/queryMatrByDate',
  printByOnline: '/matr/getPrintData',
  getMatuPrintSysId: '/matr/getMatuPrintSysId',
  queryErrTransMatr: '/matr/queryErrTransMatr',
  transToFinancial: '/matr/transToFinancial',
  scrapReceipt: '/scrap/scrapReceipt',
  checkBeforeUploadMatrForEas: '/matr/checkBeforeUploadMatrForEas',
  uploadMatrForEas: '/matr/uploadMatrForEas',
  chooseStorehouse: '/storehouse/chooseStorehouse',
  enterStoreEntity: '/matr/enterStoreEntity',
  backVendorEntity: '/matr/backVendorEntity',
  findMatrCockpit: '/matr/findMatrCockpit',
  pushMonthMatrToEas: '/matr/pushMonthMatrToEas',
  exportMD: '/matr/exportMD',
  longTimeNoReceive: '/matr/longTimeNoReceive',
  sqrReceive: '/matr/sqrReceive'
}

export function queryMatrDetail (data) {
  return axios({
    url: api.queryMatrDetail,
    method: 'post',
    data: data
  })
}

export function sqrReceive (data) {
  return axios({
    url: api.sqrReceive,
    method: 'post',
    data: data
  })
}

export function longTimeNoReceive (data) {
  return axios({
    url: api.longTimeNoReceive,
    method: 'post',
    data: data
  })
}

export function exportMD (data) {
  return axios({
    url: api.exportMD,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function pushMonthMatrToEas (data) {
  return axios({
    url: api.pushMonthMatrToEas,
    method: 'post',
    data: data
  })
}

export function findMatrCockpit (data) {
  return axios({
    url: api.findMatrCockpit,
    method: 'post',
    data: data
  })
}

export function enterStoreEntity (data) {
  return axios({
    url: api.enterStoreEntity,
    method: 'post',
    data: data
  })
}

export function chooseStorehouse (data) {
  return axios({
    url: api.chooseStorehouse,
    method: 'post',
    data: data
  })
}

export function queryPolineWithMatr (data) {
  return axios({
    url: api.queryPolineWithMatr,
    method: 'post',
    data: data
  })
}

export function scrapReceipt (parameter) {
  return axios({
    url: api.scrapReceipt,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function queryMatrByDate (data) {
  return axios({
    url: api.queryMatrByDate,
    method: 'post',
    data: data
  })
}

export function modifyMatr (data) {
  return axios({
    url: api.modifyMatr,
    method: 'post',
    data: data
  })
}

export function connectInvoice (data) {
  return axios({
    url: api.connectInvoice,
    method: 'post',
    data: data
  })
}

export function repairAndStorage (data) {
  return axios({
    url: api.repairAndStorage,
    method: 'post',
    data: data
  })
}

export function enterStore (data) {
  return axios({
    url: api.enterStore,
    method: 'post',
    data: data
  })
}

export function backVendor (data) {
  return axios({
    url: api.backVendor,
    method: 'post',
    data: data
  })
}

export function queryMatr (data) {
  return axios({
    url: api.queryMatr,
    method: 'post',
    data: data
  })
}

export function exports (data) {
  return axios({
    url: api.exports,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function exportsByPolineNum (data) {
  return axios({
    url: api.exportsByPolineNum,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function printByOnline (data) {
  return axios({
    url: api.printByOnline,
    method: 'post',
    data: data
  })
}

export function getMatuPrintSysId (data) {
  return axios({
    url: api.getMatuPrintSysId,
    method: 'post',
    data: data
  })
}

export function queryErrTransMatr (data) {
  return axios({
    url: api.queryErrTransMatr,
    method: 'post',
    data: data
  })
}
export function transToFinancial (data) {
  return axios({
    url: api.transToFinancial,
    method: 'post',
    data: data
  })
}
// 发票号返回金额
export function checkBeforeUploadMatrForEas (data) {
  return axios({
    url: api.checkBeforeUploadMatrForEas,
    method: 'post',
    data: data
  })
}
// 发票号提交
export function uploadMatrForEas (data) {
  return axios({
    url: api.uploadMatrForEas,
    method: 'post',
    data: data
  })
}

export function backVendorEntity (data) {
  return axios({
    url: api.backVendorEntity,
    method: 'post',
    data: data
  })
}

export default api
