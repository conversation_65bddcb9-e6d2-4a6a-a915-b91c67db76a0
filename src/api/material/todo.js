// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 *  findToDoCounts: 获取各类待办统计
 *  findPrlineSource: 获取采购申请数据
 *  findRfqlineSource: 获取询价申请数据
 *  findPolineSource: 获取订单申请数据
 *  findQuolineSource:获取报价申请数据
 *  findWorkorderSource:获取工单申请数据
 */
const api = {
  findTodoCount: '/todo/findToDoTasksSummary',
  findPrlineSource: '/todo/queryPrline',
  findRfqlineSource: '/todo/queryRfqline',
  findPolineSource: '/todo/queryPoline',
  findQuolineSource: '/todo/queryQuo',
  findWorkorderSource: '/todo/queryWorkorder',
  findOutmainanceSource: '/todo/queryOutmainance'
}

export function findTodoCount (data) {
  return axios({
    url: api.findTodoCount,
    method: 'post',
    data: data
  })
}

export function findPrlineSource (data) {
  return axios({
    url: api.findPrlineSource,
    method: 'post',
    data: data
  })
}

export function findRfqlineSource (data) {
  return axios({
    url: api.findRfqlineSource,
    method: 'post',
    data: data
  })
}

export function findPolineSource (data) {
  return axios({
    url: api.findPolineSource,
    method: 'post',
    data: data
  })
}

export function findQuolineSource (data) {
  return axios({
    url: api.findQuolineSource,
    method: 'post',
    data: data
  })
}

export function findWorkorderSource (data) {
  return axios({
    url: api.findWorkorderSource,
    method: 'post',
    data: data
  })
}

export function findOutmainanceSource (data) {
  return axios({
    url: api.findOutmainanceSource,
    method: 'post',
    data: data
  })
}

export default api
