// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 * getUniOrderInfo: 查询统购计划
 * modifyUniOrderInfo: 修改统购计划
 */
const api = {
  getUniOrderInfo: '/uniorder/getUniOrderInfo'
  // modifyUniOrderInfo: '/uniorder/modifyUniOrderInfo'
}

export function getUniOrderInfo (data) {
  return axios({
    url: api.getUniOrderInfo,
    method: 'post',
    data: data
  })
}
// 还没弄
// export function modifyUniOrderInfo (data) {
//  return axios({
//    url: api.modifyUniOrderInfo,
//    method: 'post',
//    data: data
//  })
// }

export default api
