
// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 *  queryLabourRecord: 查询
 *  doPurchase: 采购
 *  queryLabour:通用报表查询劳保工具
 *  doExportLabourRecord：导出
 *  queryReceiveRecord: 查询劳保领用记录
 */
const api = {
  queryLabourRecord: '/labourRecord/queryLabourRecord',
  doPurchase: '/labourRecord/doPurchase',
  queryLabour: '/labourRecord/queryLabour',
  doExportLabourRecord: '/labourRecord/doExport',
  generateReceiveRecord: '/labourRecord/generateReceiveRecord',
  modifyLabourRecord: '/labourRecord/modifyLabourRecord',
  modifyReceiveRecord: '/labourRecord/modifyReceiveRecord',
  queryReceiveRecord: '/labourRecord/queryReceiveRecord',
  endReceiveRecord: '/labourRecord/endReceiveRecord',
  exportReceiveRecord: '/labourRecord/exportReceiveRecord',
  doExportReceiveRecord: '/labourRecord/doExportReceiveRecord',
  deleteReceiveRecord: '/labourRecord/deleteReceiveRecord',
  exportDpx: '/labourRecord/exportDpx',
  queryPersonReceiveRecord: '/labourRecord/queryPersonReceiveRecord'
}

export function queryPersonReceiveRecord (data) {
  return axios({
    url: api.queryPersonReceiveRecord,
    method: 'post',
    data: data
  })
}

export function exportDpx (data) {
  return axios({
    url: api.exportDpx,
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

export function deleteReceiveRecord (data) {
  return axios({
    url: api.deleteReceiveRecord,
    method: 'post',
    data: data
  })
}

export function doExportReceiveRecord (data) {
  return axios({
    url: api.doExportReceiveRecord,
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

export function endReceiveRecord (data) {
  return axios({
    url: api.endReceiveRecord,
    method: 'post',
    data: data
  })
}

export function modifyReceiveRecord (data) {
  return axios({
    url: api.modifyReceiveRecord,
    method: 'post',
    data: data
  })
}

export function modifyLabourRecord (data) {
  return axios({
    url: api.modifyLabourRecord,
    method: 'post',
    data: data
  })
}

export function queryLabourRecord (data) {
  return axios({
    url: api.queryLabourRecord,
    method: 'post',
    data: data
  })
}

export function generateReceiveRecord (data) {
  return axios({
    url: api.generateReceiveRecord,
    method: 'post',
    data: data
  })
}

export function doPurchase (data) {
  return axios({
    url: api.doPurchase,
    method: 'post',
    data: data
  })
}

export function queryReceiveRecord (data) {
  return axios({
    url: api.queryReceiveRecord,
    method: 'post',
    data: data
  })
}

export function queryLabour (data) {
  return axios({
    url: api.queryLabour,
    method: 'post',
    data: data
  })
}

export function exportReceiveRecord (data) {
  return axios({
    url: api.exportReceiveRecord,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportLabourRecord (data) {
  return axios({
    url: api.doExportLabourRecord,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export default api
