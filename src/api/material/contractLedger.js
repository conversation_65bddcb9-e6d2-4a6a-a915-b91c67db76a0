import { axios } from '@/utils/request'

/**
 *  queryContractLedger: 查询
 *  modifyContractLedger: 增删改
 *  doExport:导出报表
 */

const api = {
  queryContractLedger: '/contractLedger/queryContractLedger',
  modifyContractLedger: '/contractLedger/modifyContractLedger',
  doExport: '/contractLedger/doExport'
}

export function queryContractLedger (data) {
  return axios({
    url: api.queryContractLedger,
    method: 'post',
    data: data
  })
}

export function modifyContractLedger (data) {
  return axios({
    url: api.modifyContractLedger,
    method: 'post',
    data: data
  })
}

export function doExport (data) {
  return axios({
    url: api.doExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export default api
