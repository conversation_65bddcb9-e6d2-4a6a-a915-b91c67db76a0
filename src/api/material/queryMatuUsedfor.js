
// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { axios } from '@/utils/request'

/**
 *  queryMatuUsedfor: 查询使用方向消耗
 *  exportMatuUsedfor: 导出使用方向消耗
 *  queryUsedForOf26:查询26大类使用方向消耗
 *  exportUsedForOf26:导出26大类使用方向消耗
 */
const api = {
  queryMatuUsedfor: '/matu/queryMatuUsedfor',
  exportMatuUsedfor: '/matu/exportMatuUsedfor',
  queryUsedForOf26: '/matu/queryUsedForOf26',
  exportUsedForOf26: '/matu/exportUsedForOf26'
}

export function queryMatuUsedfor (data) {
  return axios({
    url: api.queryMatuUsedfor,
    method: 'post',
    data: data
  })
}

export function exportMatuUsedfor (data) {
  return axios({
    url: api.exportMatuUsedfor,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryUsedForOf26 (data) {
  return axios({
    url: api.queryUsedForOf26,
    method: 'post',
    data: data
  })
}

export function exportUsedForOf26 (data) {
  return axios({
    url: api.exportUsedForOf26,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export default api
