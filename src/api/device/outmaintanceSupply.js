import { axios } from '@/utils/request'

/**
 *  queryOutMain: 查询
 *  addOutMain: 计量设备新增
 *  ModifyOutMain: 计量设备修改
 *  deleteOutMain: 计量设备删除
 */

const api = {
  queryOutMain: '/outmainance/queryOutmainanceInfo',
  addOutMain: '/outmainance/modifyOutmainance',
  modifyOutMain: '/outmainance/modifyOutmainance',
  deleteOutMain: '/outmainance/modifyOutmainance',
  approOutmainance: '/outmainance/approOutmainance'
}

export function queryOutMain (data) {
  return axios({
    url: api.queryOutMain,
    method: 'post',
    data: data
  })
}

export function addOutMain (data) {
  return axios({
    url: api.addOutMain,
    method: 'post',
    data: data
  })
}

export function modifyOutMain (data) {
  return axios({
    url: api.modifyOutMain,
    method: 'post',
    data: data
  })
}

export function deleteOutMain (data) {
  return axios({
    url: api.deleteOutMain,
    method: 'post',
    data: data
  })
}

export function approOutmainance (data) {
  return axios({
    url: api.approOutmainance,
    method: 'post',
    data: data
  })
}

export default api
