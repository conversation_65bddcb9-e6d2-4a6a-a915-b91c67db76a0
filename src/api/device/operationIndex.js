import { axios } from '@/utils/request'

const api = {
  selectoperationIndex: '/operationIndex/queryFloor',
  doExportProcurePlan: '/operationIndex/exportPrline',
  doMetricsReport: '/operationIndex/doMetricsReport',
  doMonthlyReports: '/operationIndex/doMonthlyReports',
  doMetricsWeeklyReport: '/operationIndex/doMetricsWeeklyReport',
  doStationMonthlyReports: '/operationIndex/doStationMonthlyReports'
}

export function selectoperationIndex (data) {
  return axios({
    url: api.selectoperationIndex,
    method: 'post',
    data: data
  })
}

export function doExportProcurePlan (data) {
  return axios({
    url: api.doExportProcurePlan,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doMetricsReport (data) {
  return axios({
    url: api.doMetricsReport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doMonthlyReports (data) {
  return axios({
    url: api.doMonthlyReports,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doMetricsWeeklyReport (data) {
  return axios({
    url: api.doMetricsWeeklyReport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doStationMonthlyReports (data) {
  return axios({
    url: api.doStationMonthlyReports,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
