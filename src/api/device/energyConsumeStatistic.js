import { axios } from '@/utils/request'

/**
 *  queryAnnualStatistics: 交接记录查询
 *  modifyAnnualStatistics: 交接记录增删改
 *  doExport:导出报表
 */

const api = {
  queryAnnualStatistics: '/energyConsumeStatistic/queryAnnualStatistics',
  modifyAnnualStatistics: '/energyConsumeStatistic/modifyAnnualStatistics',
  queryMonthlyStatistics: '/energyConsumeStatistic/queryMonthlyStatistics',
  modifyMonthlyStatistics: '/energyConsumeStatistic/modifyMonthlyStatistics',
  modifyMonthlyData: '/energyConsumeStatistic/modifyMonthlyData',
  doExport: '/energyConsumeStatistic/doExport',
  doExport2: '/energyConsumeStatistic/doExport2',
  queryLightningProtection: '/energyConsumeStatistic/queryLightningProtection',
  modifyLightningProtection: '/energyConsumeStatistic/modifyLightningProtection',
  modifyLightningProtectionData: '/energyConsumeStatistic/modifyLightningProtectionData',
  doLightningProtectionExport: '/energyConsumeStatistic/doLightningProtectionExport',
  findAllDeviceForYZ: '/energyConsumeStatistic/findAllDeviceForYZ',
  uploadEnergyConsumption: '/energyConsumeStatistic/uploadEnergyConsumption'
}

export function queryAnnualStatistics (data) {
  return axios({
    url: api.queryAnnualStatistics,
    method: 'post',
    data: data
  })
}

export function modifyAnnualStatistics (data) {
  return axios({
    url: api.modifyAnnualStatistics,
    method: 'post',
    data: data
  })
}

export function queryMonthlyStatistics (data) {
  return axios({
    url: api.queryMonthlyStatistics,
    method: 'post',
    data: data
  })
}

export function modifyMonthlyStatistics (data) {
  return axios({
    url: api.modifyMonthlyStatistics,
    method: 'post',
    data: data
  })
}

export function modifyMonthlyData (data) {
  return axios({
    url: api.modifyMonthlyData,
    method: 'post',
    data: data
  })
}

export function doExport (data) {
  return axios({
    url: api.doExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExport2 (data) {
  return axios({
    url: api.doExport2,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryLightningProtection (data) {
  return axios({
    url: api.queryLightningProtection,
    method: 'post',
    data: data
  })
}

export function modifyLightningProtection (data) {
  return axios({
    url: api.modifyLightningProtection,
    method: 'post',
    data: data
  })
}

export function modifyLightningProtectionData (data) {
  return axios({
    url: api.modifyLightningProtectionData,
    method: 'post',
    data: data
  })
}

export function doLightningProtectionExport (data) {
  return axios({
    url: api.doLightningProtectionExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function findAllDeviceForYZ (data) {
  return axios({
    url: api.findAllDeviceForYZ,
    method: 'post',
    data: data
  })
}
export function uploadEnergyConsumption (data) {
  return axios({
    url: api.uploadEnergyConsumption,
    method: 'post',
    data: data
  })
}
export default api
