import { axios } from '@/utils/request'

const api = {
  queryLedgerList: '/inspectionTrain/queryLedgerList',
  modifyLedgerList: '/inspectionTrain/modifyLedgerList',
  exportImportTempleteWeek: '/file/modelDownload?fileName=周报上传模板',
  exportImportTempleteMonth: '/file/modelDownload?fileName=月报上传模板',
  exportImportTempleteDepart: '/file/modelDownload?fileName=月度自查上传模板',
  queryCorrectRecord: '/inspectionTrain/queryCorrectRecord',
  modifyCorrectRecord: '/inspectionTrain/modifyCorrectRecord',
  queryNotReportedDepartInfo: '/inspectionTrain/queryNotReportedDepartInfo'
}

export function queryLedgerList (data) {
  return axios({
    url: api.queryLedgerList,
    method: 'post',
    data: data
  })
}
export function modifyLedgerList (data) {
  return axios({
    url: api.modifyLedgerList,
    method: 'post',
    data: data
  })
}

export function queryCorrectRecord (data) {
  return axios({
    url: api.queryCorrectRecord,
    method: 'post',
    data: data
  })
}

export function queryNotReportedDepartInfo (data) {
  return axios({
    url: api.queryNotReportedDepartInfo,
    method: 'post',
    data: data
  })
}

export function modifyCorrectRecord (data) {
  return axios({
    url: api.modifyCorrectRecord,
    method: 'post',
    data: data
  })
}

export function exportImportTempleteWeek (data) {
  return axios({
    url: api.exportImportTempleteWeek,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export function exportImportTempleteMonth (data) {
  return axios({
    url: api.exportImportTempleteMonth,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export function exportImportTempleteDepart (data) {
  return axios({
    url: api.exportImportTempleteDepart,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}
