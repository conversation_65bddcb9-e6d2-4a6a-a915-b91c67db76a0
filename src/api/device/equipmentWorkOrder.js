
import { qs, axios } from '@/utils/request'

/**
 * equipmentWorkOrder.js
 *
 *  getEquipmentWorkOrderDetail: 获取工单详情
 *  saveEquipmentWorkOrder: 保存工单
 *  getHistoryWorkOrderDetail: 获取历史工单
 *  saveRepairMemo：保存维修描述
 *  saveFaultApply 进入维修池
 *  getWorkOrderPrline 获取工单物资列表
 *  savePrline 保存物资
 *  getApproveRecordByNum 查看流程记录
 *  approPrline 审批
 */
const api = {
  getEquipmentWorkOrderDetail: '/equipmentWorkOrder/getEquipmentWorkOrderDetail',
  saveEquipmentWorkOrder: '/equipmentWorkOrder/saveEquipmentWorkOrder',
  saveRepairMemo: '/equipmentWorkOrder/saveRepairMemo',
  saveFaultApply: '/equipmentWorkOrder/saveFaultApply',
  getHistoryWorkOrderDetail: '/equipmentWorkOrder/getHistoryWorkOrderDetail',
  savePrline: '/equipmentWorkOrder/savePrline',
  getWorkOrderPrline: '/equipmentWorkOrder/getWorkOrderPrline',
  getApproveRecordByNum: '/equipmentWorkOrder/queryApproHis',
  approPrline: '/equipmentWorkOrder/approPrline',
  queryWorkorderMaterial: '/equipmentWorkOrder/queryWorkorderMaterial',
  doProcureRemove: '/equipmentWorkOrder/doProcureRemove',
  saveWorkOrderPlan: '/equipmentWorkOrder/saveWorkOrderPlan'
}

export function approPrline (data) {
  return axios({
    url: api.approPrline,
    method: 'post',
    data: data
  })
}
export function getApproveRecordByNum (parameter) {
  return axios({
    url: api.getApproveRecordByNum,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}
export function savePrline (data) {
  return axios({
    url: api.savePrline,
    method: 'post',
    data: data
  })
}
export function getWorkOrderPrline (data) {
  return axios({
    url: api.getWorkOrderPrline,
    method: 'post',
    data: data
  })
}
export function saveFaultApply (data) {
  return axios({
    url: api.saveFaultApply,
    method: 'post',
    data: data
  })
}
export function saveRepairMemo (data) {
  return axios({
    url: api.saveRepairMemo,
    method: 'post',
    data: data
  })
}
export function getHistoryWorkOrderDetail (data) {
  return axios({
    url: api.getHistoryWorkOrderDetail,
    method: 'post',
    data: data
  })
}
export function getEquipmentWorkOrderDetail (data) {
  return axios({
    url: api.getEquipmentWorkOrderDetail,
    method: 'post',
    data: data
  })
}
export function saveEquipmentWorkOrder (data) {
  return axios({
    url: api.saveEquipmentWorkOrder,
    method: 'post',
    data: data
  })
}
export function queryWorkorderMaterial (data) {
  return axios({
    url: api.queryWorkorderMaterial,
    method: 'post',
    data: data
  })
}
export function doProcureRemove (data) {
  return axios({
    url: api.doProcureRemove,
    method: 'post',
    data: data
  })
}
export function saveWorkOrderPlan (data) {
  return axios({
    url: api.saveWorkOrderPlan,
    method: 'post',
    data: data
  })
}
