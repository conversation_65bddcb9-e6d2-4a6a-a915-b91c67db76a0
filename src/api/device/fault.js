import { axios } from '@/utils/request'

/**
 *  queryFault: 故障查询
 *  modifyFault: 故障修改
 *  deleteFault: 故障删除
 */

const api = {
  queryFault: '/fault/queryFault',
  modifyFault: '/fault/modifyFault',
  deleteFault: '/fault/modifyFault'
}

export function queryFault (data) {
  return axios({
    url: api.queryFault,
    method: 'post',
    data: data
  })
}

export function modifyFault (data) {
  return axios({
    url: api.modifyFault,
    method: 'post',
    data: data
  })
}

export function deleteFault (data) {
  return axios({
    url: api.deleteFault,
    method: 'post',
    data: data
  })
}

export default api
