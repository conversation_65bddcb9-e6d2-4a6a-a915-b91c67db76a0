import { axios } from '@/utils/request'

/**
 *  queryRegularRepairInfo: 点检查询
 *  insertRegularRepairInfo: 点检新增
 *  updateRegularRepairInfo: 点检修改
 *  deleteRegularRepairInfo: 点检删除
 *  queryRegularRepairDailyInfo: 点检日表查询
 *  insertRegularRepairDailyInfo: 点检日表新增
 *  updateRegularRepairDailyInfo: 点检日表修改
 *  deleteRegularRepairDailyInfo: 点检日表删除
 *  queryRegularRepairWeekInfo: 点检周表查询
 *  insertRegularRepairWeekInfo: 点检周表新增
 *  updateRegularRepairWeekInfo: 点检周表修改
 *  deleteRegularRepairWeekInfo: 点检周表删除
 *  queryRegularRepairMonthInfo: 点检月表查询
 *  insertRegularRepairMonthInfo: 点检月表新增
 *  updateRegularRepairMonthInfo: 点检月表修改
 *  updateRegularRepairMonthCompletionInfo: 点检月表完成状态修改
 *  deleteRegularRepairMonthInfo: 点检月表删除
 */

const api = {
  queryRegularRepairInfo: '/regularRepair/queryRegularRepairInfo',
  insertRegularRepairInfo: '/regularRepair/modifyRegularRepair',
  updateRegularRepairInfo: '/regularRepair/modifyRegularRepair',
  deleteRegularRepairInfo: '/regularRepair/modifyRegularRepair',
  updateCompletion: '/regularRepair/updateCompletion',
  getPartInfo: '/regularRepair/getPartInfo',
  queryRegularRepairDailyInfo: '/regularRepairDaily/queryRegularRepairDailyInfo',
  insertRegularRepairDailyInfo: '/regularRepairDaily/modifyRegularRepairDaily',
  updateRegularRepairDailyInfo: '/regularRepairDaily/modifyRegularRepairDaily',
  deleteRegularRepairDailyInfo: '/regularRepairDaily/modifyRegularRepairDaily',
  getTomorrowPlanByDate: '/regularRepairDaily/getTomorrowPlanByDate',
  queryRegularRepairWeekInfo: '/regularRepairWeek/queryRegularRepairWeekInfo',
  insertRegularRepairWeekInfo: '/regularRepairWeek/modifyRegularRepairWeek',
  updateRegularRepairWeekInfo: '/regularRepairWeek/modifyRegularRepairWeek',
  deleteRegularRepairWeekInfo: '/regularRepairWeek/modifyRegularRepairWeek',
  queryRegularRepairMonthInfo: '/regularRepairMonth/queryRegularRepairMonthInfo',
  insertRegularRepairMonthInfo: '/regularRepairMonth/modifyRegularRepairMonth',
  updateRegularRepairMonthInfo: '/regularRepairMonth/modifyRegularRepairMonth',
  deleteRegularRepairMonthInfo: '/regularRepairMonth/modifyRegularRepairMonth',
  doWeekExport: '/regularRepairWeek/doWeekExport',
  doRepairExport: '/regularRepair/doRepairExport',
  doNightShiftExport: '/regularRepair/doNightShiftExport',
  exportDailyPlan: '/regularRepairDaily/exportDailyPlan',
  modifyRegularRepairMonth: '/regularRepairMonth/modifyRegularRepairMonth',
  modifyRegularRepairWeek: '/regularRepairWeek/modifyRegularRepairWeek',
  updateRegularRepairMonthCompletionInfo: '/regularRepairMonth/modifyRegularRepairMonth',
  pushMasMessage: '/regularRepairDaily/pushMasMessage',
  modifyTomorrowPlan: '/regularRepairDaily/modifyTomorrowPlan',
  approMas: '/regularRepairDaily/approMas'
}

export function approMas (data) {
  return axios({
    url: api.approMas,
    method: 'post',
    data: data
  })
}

export function modifyTomorrowPlan (data) {
  return axios({
    url: api.modifyTomorrowPlan,
    method: 'post',
    data: data
  })
}

export function pushMasMessage (data) {
  return axios({
    url: api.pushMasMessage,
    method: 'post',
    data: data
  })
}

export function modifyRegularRepairWeek (data) {
  return axios({
    url: api.modifyRegularRepairWeek,
    method: 'post',
    data: data
  })
}

export function modifyRegularRepairMonth (data) {
  return axios({
    url: api.modifyRegularRepairMonth,
    method: 'post',
    data: data
  })
}

export function doWeekExport (data) {
  return axios({
    url: api.doWeekExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doNightShiftExport (data) {
  return axios({
    url: api.doNightShiftExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function exportDailyPlan (data) {
  return axios({
    url: api.exportDailyPlan,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doRepairExport (data) {
  return axios({
    url: api.doRepairExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryRegularRepairInfo (data) {
  return axios({
    url: api.queryRegularRepairInfo,
    method: 'post',
    data: data
  })
}

export function getPartInfo (data) {
  return axios({
    url: api.getPartInfo,
    method: 'post',
    data: data
  })
}

export function insertRegularRepairInfo (data) {
  return axios({
    url: api.insertRegularRepairInfo,
    method: 'post',
    data: data
  })
}

export function updateRegularRepairInfo (data) {
  return axios({
    url: api.updateRegularRepairInfo,
    method: 'post',
    data: data
  })
}

export function deleteRegularRepairInfo (data) {
  return axios({
    url: api.deleteRegularRepairInfo,
    method: 'post',
    data: data
  })
}

export function updateCompletion (data) {
  return axios({
    url: api.updateCompletion,
    method: 'post',
    data: data
  })
}

export function queryRegularRepairDailyInfo (data) {
  return axios({
    url: api.queryRegularRepairDailyInfo,
    method: 'post',
    data: data
  })
}

export function insertRegularRepairDailyInfo (data) {
  return axios({
    url: api.insertRegularRepairDailyInfo,
    method: 'post',
    data: data
  })
}

export function updateRegularRepairDailyInfo (data) {
  return axios({
    url: api.updateRegularRepairDailyInfo,
    method: 'post',
    data: data
  })
}

export function deleteRegularRepairDailyInfo (data) {
  return axios({
    url: api.deleteRegularRepairDailyInfo,
    method: 'post',
    data: data
  })
}

export function queryRegularRepairWeekInfo (data) {
  return axios({
    url: api.queryRegularRepairWeekInfo,
    method: 'post',
    data: data
  })
}

export function insertRegularRepairWeekInfo (data) {
  return axios({
    url: api.insertRegularRepairWeekInfo,
    method: 'post',
    data: data
  })
}

export function updateRegularRepairWeekInfo (data) {
  return axios({
    url: api.updateRegularRepairWeekInfo,
    method: 'post',
    data: data
  })
}

export function deleteRegularRepairWeekInfo (data) {
  return axios({
    url: api.deleteRegularRepairWeekInfo,
    method: 'post',
    data: data
  })
}

export function queryRegularRepairMonthInfo (data) {
  return axios({
    url: api.queryRegularRepairMonthInfo,
    method: 'post',
    data: data
  })
}

export function insertRegularRepairMonthInfo (data) {
  return axios({
    url: api.insertRegularRepairMonthInfo,
    method: 'post',
    data: data
  })
}

export function updateRegularRepairMonthInfo (data) {
  return axios({
    url: api.updateRegularRepairMonthInfo,
    method: 'post',
    data: data
  })
}

export function updateRegularRepairMonthCompletionInfo (data) {
  return axios({
    url: api.updateRegularRepairMonthCompletionInfo,
    method: 'post',
    data: data
  })
}

export function deleteRegularRepairMonthInfo (data) {
  return axios({
    url: api.deleteRegularRepairMonthInfo,
    method: 'post',
    data: data
  })
}

export function getTomorrowPlanByDate (data) {
  return axios({
    url: api.getTomorrowPlanByDate,
    method: 'post',
    data: data
  })
}

export default api
