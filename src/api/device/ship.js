import { axios } from '@/utils/request'

/**
 *  queryAssetShip: 船舶查询
 *  modifyAssetShip: 船舶修改
 *  deleteAssetShip: 船舶删除
 */
const api = {
  queryAssetShip: '/assetShip/queryAssetShip',
  modifyAssetShip: '/assetShip/modifyAssetShip',
  deleteAssetShip: '/assetShip/modifyAssetShip',
  uploadAssetShip: '/assetShip/uploadAssetShip'
}

export function queryAssetShip (data) {
  return axios({
    url: api.queryAssetShip,
    method: 'post',
    data: data
  })
}

export function modifyAssetShip (data) {
  return axios({
    url: api.modifyAssetShip,
    method: 'post',
    data: data
  })
}

export function deleteAssetShip (data) {
  return axios({
    url: api.deleteAssetShip,
    method: 'post',
    data: data
  })
}

export function uploadAssetShip (data, fn) {
  return axios({
    url: api.uploadAssetShip,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data;charset=UTF-8'
    },
    transformRequest: [
      function (data) {
        return data
      }
    ],
    onUploadProgress: fn,
    data: data
  })
}

export default api
