import { axios } from '@/utils/request'

/**
 *  queryAssetJL: 计量设备查询
 *  modifyAssetJL: 计量设备修改
 *  deleteAssetJL: 计量设备删除
 */
const api = {
  queryAssetJL: '/assetJl/queryAssetJlInfo',
  modifyAssetJL: '/assetJl/modifyAssetJl',
  deleteAssetJL: '/assetJl/modifyAssetJl'
}

export function queryAssetJL (data) {
  return axios({
    url: api.queryAssetJL,
    method: 'post',
    data: data
  })
}

export function modifyAssetJL (data) {
  return axios({
    url: api.modifyAssetJL,
    method: 'post',
    data: data
  })
}

export function deleteAssetJL (data) {
  return axios({
    url: api.deleteAssetJL,
    method: 'post',
    data: data
  })
}

export default api
