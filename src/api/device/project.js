import { axios, qs } from '@/utils/request'

/**
 *  queryProject: 项目查询
 *  modifyProject: 项目增删改
 *  urgeProjectFollow: 项目进度催办
 *  projectHandOver: 项目移交
 */

const api = {
  queryProject: '/project/queryProject',
  modifyProject: '/project/modifyProject',
  queryRelateProject: '/project/queryRelateProject',
  // 项目对应的每月计划的增删改查
  queryProjectFollow: '/project/queryProjectFollow',
  modifyProjectFollow: '/project/modifyProjectFollow',
  queryProjectFollowYear: '/project/queryProjectFollowYear',
  urgeProjectFollow: '/project/urgeProjectFollow',
  approveProject: '/project/approveProject',
  queryProjectSummary: '/project/queryProjectSummary',
  exportProjectSummary: '/project/exportProjectSummary',
  queryProjectStatisticsTotal: '/project/queryProjectStatisticsTotal',
  queryProjectStatistics: '/project/queryProjectStatistics',
  projectHandOver: '/project/projectHandOver',
  queryProjectHandOverRecord: '/project/queryProjectHandOverRecord',
  getProjectYearFinsihAmount: '/project/getProjectYearFinsihAmount',
  queryProjectFollowWeek: '/project/queryProjectFollowWeek',
  addWeekInfo: '/project/addWeekInfo',

  getItemDetail: '/item/getItemDetail',
  getItemYearPlan: '/item/getItemYearPlan',
  modifyItemYearPlan: '/item/modifyItemYearPlan',
  getItemMonthPlan: '/item/getItemMonthPlan',
  modifyItemMonthPlan: '/item/modifyItemMonthPlan',
  getItemYearPlanModifyHistory: '/item/getItemYearPlanModifyHistory',
  getItemYearDetail: '/item/getItemYearDetail',
  getItemProgress: '/item/getItemProgress',
  getItemPayment: '/item/getItemPayment',
  getCompletionsForSelect: '/item/getCompletionsForSelect',
  getNextMonthPlansForSelect: '/item/getNextMonthPlansForSelect',
  getLastMonthProgressPlan: '/item/getLastMonthProgressPlan',
  setConfig: '/config/setConfig',
  getCompanyConfig: '/config/getCompanyConfig',
  importItem: '/item/importItem',
  importItemProgress: '/item/importItemProgress',
  slhItemExport: '/item/slhItemExport',
  modifyItemProgress: '/item/modifyItemProgress',
  modelDownloadXMDR: '/file/modelDownload?fileName=鼠浪湖项目导入模板',
  modelDownloadYDJD: '/file/modelDownload?fileName=鼠浪湖月度进度导入模板'
}

export function modelDownloadYDJD (data) {
  return axios({
    url: api.modelDownloadYDJD,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export function modelDownloadXMDR (data) {
  return axios({
    url: api.modelDownloadXMDR,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export function slhItemExport (data) {
  return axios({
    url: api.slhItemExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function importItem (data) {
  return axios({
    url: api.importItem,
    paramsSerializer: function (parameter) {
      return qs.stringify(parameter, { indices: false })
    },
    method: 'post',
    data: data
  })
}

export function importItemProgress (data) {
  return axios({
    url: api.importItemProgress,
    method: 'post',
    paramsSerializer: function (parameter) {
      return qs.stringify(parameter, { indices: false })
    },
    data: data
  })
}

export function modifyItemProgress (data) {
  return axios({
    url: api.modifyItemProgress,
    method: 'post',
    data: data
  })
}

export function getCompanyConfig (data) {
  return axios({
    url: api.getCompanyConfig,
    method: 'post',
    data: data
  })
}

export function setConfig (data) {
  return axios({
    url: api.setConfig,
    method: 'post',
    data: data
  })
}

export function getLastMonthProgressPlan (data) {
  return axios({
    url: api.getLastMonthProgressPlan,
    method: 'post',
    data: data
  })
}

export function getNextMonthPlansForSelect (data) {
  return axios({
    url: api.getNextMonthPlansForSelect,
    method: 'post',
    data: data
  })
}

export function getCompletionsForSelect (data) {
  return axios({
    url: api.getCompletionsForSelect,
    method: 'post',
    data: data
  })
}

export function getItemPayment (data) {
  return axios({
    url: api.getItemPayment,
    method: 'post',
    data: data
  })
}

export function getItemProgress (data) {
  return axios({
    url: api.getItemProgress,
    method: 'post',
    data: data
  })
}

export function getItemYearDetail (data) {
  return axios({
    url: api.getItemYearDetail,
    method: 'post',
    data: data
  })
}

export function getItemYearPlanModifyHistory (data) {
  return axios({
    url: api.getItemYearPlanModifyHistory,
    method: 'post',
    data: data
  })
}

export function getItemMonthPlan (data) {
  return axios({
    url: api.getItemMonthPlan,
    method: 'post',
    data: data
  })
}

export function modifyItemMonthPlan (data) {
  return axios({
    url: api.modifyItemMonthPlan,
    method: 'post',
    data: data
  })
}

export function modifyItemYearPlan (data) {
  return axios({
    url: api.modifyItemYearPlan,
    method: 'post',
    data: data
  })
}

export function getItemYearPlan (data) {
  return axios({
    url: api.getItemYearPlan,
    method: 'post',
    data: data
  })
}

export function getItemDetail (data) {
  return axios({
    url: api.getItemDetail,
    method: 'post',
    data: data
  })
}

export function addWeekInfo (data) {
  return axios({
    url: api.addWeekInfo,
    method: 'post',
    data: data
  })
}

export function queryProjectFollowWeek (data) {
  return axios({
    url: api.queryProjectFollowWeek,
    method: 'post',
    data: data
  })
}

export function getProjectYearFinsihAmount (data) {
  return axios({
    url: api.getProjectYearFinsihAmount,
    method: 'post',
    data: data
  })
}
export function terminateProject (data) {
  return axios({
    url: api.terminateProject,
    method: 'post',
    data: data
  })
}

export function queryProjectHandOverRecord (data) {
  return axios({
    url: api.queryProjectHandOverRecord,
    method: 'post',
    data: data
  })
}

export function projectHandOver (data) {
  return axios({
    url: api.projectHandOver,
    method: 'post',
    data: data
  })
}

export function queryProjectStatisticsTotal (data) {
  return axios({
    url: api.queryProjectStatisticsTotal,
    method: 'post',
    data: data
  })
}

export function queryProjectStatistics (data) {
  return axios({
    url: api.queryProjectStatistics,
    method: 'post',
    data: data
  })
}

export function exportProjectSummary (data) {
  return axios({
    url: api.exportProjectSummary,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function approveProject (data) {
  return axios({
    url: api.approveProject,
    method: 'post',
    data: data
  })
}

export function urgeProjectFollow (data) {
  return axios({
    url: api.urgeProjectFollow,
    method: 'post',
    data: data
  })
}

export function queryProjectFollowYear (data) {
  return axios({
    url: api.queryProjectFollowYear,
    method: 'post',
    data: data
  })
}

export function queryProjectSummary (data) {
  return axios({
    url: api.queryProjectSummary,
    method: 'post',
    data: data
  })
}

export function queryProject (data) {
  return axios({
    url: api.queryProject,
    method: 'post',
    data: data
  })
}

export function queryRelateProject (data) {
  return axios({
    url: api.queryRelateProject,
    method: 'post',
    data: data
  })
}

export function modifyProject (data) {
  return axios({
    url: api.modifyProject,
    method: 'post',
    data: data
  })
}

export function queryProjectFollow (data) {
  return axios({
    url: api.queryProjectFollow,
    method: 'post',
    data: data
  })
}

export function modifyProjectFollow (data) {
  return axios({
    url: api.modifyProjectFollow,
    method: 'post',
    data: data
  })
}
export default api
