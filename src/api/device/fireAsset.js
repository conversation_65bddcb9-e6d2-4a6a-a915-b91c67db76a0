import { axios } from '@/utils/request'

/**
 *  queryFaultApply: 故障查询
 *  modifyFaultApply: 故障修改
 *  deleteFaultApply: 故障删除
 */

const api = {
  queryAssetFireAuditRecord: '/assetSbFire/queryAssetFireAuditRecord',
  queryAssetSbFire: '/assetSbFire/queryAssetSbFire',
  modifyAssetSbFire: '/assetSbFire/modifyAssetSbFire',
  queryAssetFireArea: '/assetSbFire/queryAssetFireArea',
  modifyAssetFireArea: '/assetSbFire/modifyAssetFireArea',
  queryAssetFireLocation: '/assetSbFire/queryAssetFireLocation',
  modifyAssetFireLocation: '/assetSbFire/modifyAssetFireLocation',
  queryAssetSbFireFeedback: '/assetSbFire/queryAssetSbFireFeedback',
  modifyAssetSbFireFeedback: '/assetSbFire/modifyAssetSbFireFeedback',
  getLocationQrCode: '/assetSbFire/getLocationQrCode',
  getQrCodeList: '/assetSbFire/getQrCodeList'
}

export function getQrCodeList (data) {
  return axios({
    url: api.getQrCodeList,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}

export function getLocationQrCode (data) {
  return axios({
    url: api.getLocationQrCode,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}

export function queryAssetFireAuditRecord (data) {
  return axios({
    url: api.queryAssetFireAuditRecord,
    method: 'post',
    data: data
  })
}

export function queryAssetSbFireFeedback (data) {
  return axios({
    url: api.queryAssetSbFireFeedback,
    method: 'post',
    data: data
  })
}

export function modifyAssetSbFireFeedback (data) {
  return axios({
    url: api.modifyAssetSbFireFeedback,
    method: 'post',
    data: data
  })
}

export function queryAssetSbFire (data) {
  return axios({
    url: api.queryAssetSbFire,
    method: 'post',
    data: data
  })
}

export function modifyAssetSbFire (data) {
  return axios({
    url: api.modifyAssetSbFire,
    method: 'post',
    data: data
  })
}

export function queryAssetFireArea (data) {
  return axios({
    url: api.queryAssetFireArea,
    method: 'post',
    data: data
  })
}

export function modifyAssetFireArea (data) {
  return axios({
    url: api.modifyAssetFireArea,
    method: 'post',
    data: data
  })
}

export function queryAssetFireLocation (data) {
  return axios({
    url: api.queryAssetFireLocation,
    method: 'post',
    data: data
  })
}

export function modifyAssetFireLocation (data) {
  return axios({
    url: api.modifyAssetFireLocation,
    method: 'post',
    data: data
  })
}

export default api
