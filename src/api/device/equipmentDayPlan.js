
import { axios } from '@/utils/request'

/**
 * equipmentDayPlan.js
 *
 *  queryDayPlanList: 获取日计划列表
 *  queryDayPlanEquipmentList: 获取计划下设备列表
 *  listPerson: 获取主修人下拉
 *  modifyHeadMan: 主修人编辑
 *  modifySlavePerson: 主修人编辑
 *  modifyMainPerson: 主修人编辑
 *  modifyPlanUnit: 单位编辑
 *  modifyFormType: 工单类型编辑
 *  modifyFeedbackForm: 反馈表单编辑
 *  generateWorkDoc: 生成日计划工单
 *  checkGenerate: 校验日计划工单
 *  queryDayPlanAsset 查询设备
 *  modifyPerson 批量修改人员
 *  modifyDate 批量设置日期
 */
const api = {
  queryDayPlanList: '/equipmentDayPlan/list',
  queryDayPlanEquipmentList: '/equipmentDayPlan/listEquipment',
  listPerson: '/equipmentDayPlan/listPerson',
  modifyHeadMan: '/equipmentDayPlan/modifyDayPlan/headMan',
  modifyPlanUnit: '/equipmentDayPlan/modifyDayPlan/planUnit',
  modifySlavePerson: '/equipmentDayPlan/modifyDayPlanAsset/slavePerson',
  modifyMainPerson: '/equipmentDayPlan/modifyDayPlanAsset/mainPerson',
  modifyFormType: '/equipmentDayPlan/modifyDayPlan/formType',
  modifyFeedbackForm: '/equipmentDayPlan/modifyDayPlan/feedbackForm',
  generateWorkDoc: '/equipmentDayPlan/generateWorkDoc',
  checkGenerate: '/equipmentDayPlan/checkGenerate',
  modifyFeedbackType: '/equipmentDayPlan/modifyDayPlan/feedbackType',
  queryDayPlanAsset: '/equipmentDayPlan/queryDayPlanAsset',
  modifyPerson: '/equipmentDayPlan/modifyPerson',
  modifyDate: '/equipmentDayPlan/modifyDate'

}
export function modifyDate (data) {
  return axios({
    url: api.modifyDate,
    method: 'post',
    data: data
  })
}
export function modifyPerson (data) {
  return axios({
    url: api.modifyPerson,
    method: 'post',
    data: data
  })
}
export function queryDayPlanAsset (data) {
  return axios({
    url: api.queryDayPlanAsset,
    method: 'post',
    data: data
  })
}
export function checkGenerate (data) {
  return axios({
    url: api.checkGenerate,
    method: 'post',
    data: data
  })
}
export function modifySlavePerson (data) {
  return axios({
    url: api.modifySlavePerson,
    method: 'post',
    data: data
  })
}
export function modifyMainPerson (data) {
  return axios({
    url: api.modifyMainPerson,
    method: 'post',
    data: data
  })
}
export function modifyFeedbackType (data) {
  return axios({
    url: api.modifyFeedbackType,
    method: 'post',
    data: data
  })
}
export function generateWorkDoc (data) {
  return axios({
    url: api.generateWorkDoc,
    method: 'post',
    data: data
  })
}
export function modifyFeedbackForm (data) {
  return axios({
    url: api.modifyFeedbackForm,
    method: 'post',
    data: data
  })
}
export function modifyHeadMan (data) {
  return axios({
    url: api.modifyHeadMan,
    method: 'post',
    data: data
  })
}
export function modifyPlanUnit (data) {
  return axios({
    url: api.modifyPlanUnit,
    method: 'post',
    data: data
  })
}
export function modifyFormType (data) {
  return axios({
    url: api.modifyFormType,
    method: 'post',
    data: data
  })
}
export function listPerson (data) {
  return axios({
    url: api.listPerson,
    method: 'post',
    data: data
  })
}
export function queryDayPlanList (data) {
  return axios({
    url: api.queryDayPlanList,
    method: 'post',
    data: data
  })
}
export function queryDayPlanEquipmentList (data) {
  return axios({
    url: api.queryDayPlanEquipmentList,
    method: 'post',
    data: data
  })
}

export default api
