import { axios } from '@/utils/request'

/**
 *  queryFaultApply: 故障查询
 *  modifyFaultApply: 故障修改
 *  deleteFaultApply: 故障删除
 */

const api = {
  queryFaultApply: '/faultApply/queryFaultApply',
  modifyFaultApply: '/faultApply/modifyFaultApply',
  deleteFaultApply: '/faultApply/modifyFaultApply',
  doExport: '/faultApply/doExport',
  // 审批相关
  approveFaultApply: '/faultApply/approFaultApply'
}

export function queryFaultApply (data) {
  return axios({
    url: api.queryFaultApply,
    method: 'post',
    data: data
  })
}

export function modifyFaultApply (data) {
  return axios({
    url: api.modifyFaultApply,
    method: 'post',
    data: data
  })
}

export function deleteFaultApply (data) {
  return axios({
    url: api.deleteFaultApply,
    method: 'post',
    data: data
  })
}
export function approveFaultApply (data) {
  return axios({
    url: api.approveFaultApply,
    method: 'post',
    data: data
  })
}

export function doExport (data) {
  return axios({
    url: api.doExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export default api
