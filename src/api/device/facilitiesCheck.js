import { axios } from '@/utils/request'

/**
 *  query: 查询
 *  modify: 修改
 */

const api = {
  query: '/facilitiesCheck/query',
  modify: '/facilitiesCheck/modify'
}

export function queryList (param) {
  return axios({
    url: api.query,
    method: 'post',
    data: param
  })
}

export function insert (param) {
  return axios({
    url: api.modify,
    method: 'post',
    data: param
  })
}
export function remove (param) {
  return axios({
    url: api.modify,
    method: 'post',
    data: param
  })
}

export function update (param) {
  return axios({
    url: api.modify,
    method: 'post',
    data: param
  })
}

export default api
