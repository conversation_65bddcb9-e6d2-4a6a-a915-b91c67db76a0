import { axios } from '@/utils/request'

/**
 *
 */

const api = {
  queryBgjj: '/assetBgjj/queryBgjj',
  queryInventoryPlan: '/assetBgjj/queryInventoryPlan',
  generateInventoryPlan: '/assetBgjj/generateInventoryPlan',
  modifyBgjj: '/assetBgjj/modifyBgjj',
  doExport: '/assetBgjj/doExport',
  exportInventoryPlan: '/assetBgjj/exportInventoryPlan',
  getQrCodeList: '/assetBgjj/getQrCodeList',
  getBgjjNum: '/assetBgjj/getBgjjNum',
  endInventoryPlan: '/assetBgjj/endInventoryPlan'
}

export function queryBgjj (data) {
  return axios({
    url: api.queryBgjj,
    method: 'post',
    data: data
  })
}

export function endInventoryPlan (data) {
  return axios({
    url: api.endInventoryPlan,
    method: 'post',
    data: data
  })
}

export function exportInventoryPlan (data) {
  return axios({
    url: api.exportInventoryPlan,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryInventoryPlan (data) {
  return axios({
    url: api.queryInventoryPlan,
    method: 'post',
    data: data
  })
}

export function generateInventoryPlan (data) {
  return axios({
    url: api.generateInventoryPlan,
    method: 'post',
    data: data
  })
}

export function modifyBgjj (data) {
  return axios({
    url: api.modifyBgjj,
    method: 'post',
    data: data
  })
}

export function getBgjjNum (data) {
  return axios({
    url: api.getBgjjNum,
    method: 'post',
    data: data
  })
}

export function doExport (data) {
  return axios({
    url: api.doExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function getQrCodeList (parameter) {
  return axios({
    url: api.getQrCodeList,
    method: 'post',
    data: parameter,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export default api
