import { axios } from '@/utils/request'

const api = {
  selectRepairReport: '/repairReport/queryFloor',
  doExportRepairReport: '/repairReport/exportPrline',
  getAssetSbItemNameList: '/repairReport/getAssetSbItemNameList',
  queryAssetSbItemNameList: '/repairReport/queryAssetSbItemNameList'

}

export function selectRepairReport (data) {
  return axios({
    url: api.selectRepairReport,
    method: 'post',
    data: data
  })
}

export function doExportRepairReport (data) {
  return axios({
    url: api.doExportRepairReport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function getAssetSbItemNameList (data) {
  return axios({
    url: api.getAssetSbItemNameList,
    method: 'post',
    data: data
  })
}

export function queryAssetSbItemNameList (data) {
  return axios({
    url: api.queryAssetSbItemNameList,
    method: 'post',
    data: data
  })
}
