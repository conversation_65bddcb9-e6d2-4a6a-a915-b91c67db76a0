import { axios } from '@/utils/request'

const api = {
  selectfailureAnalysis: '/failureAnalysis/queryFloor',
  doExportProcurePlan: '/failureAnalysis/exportPrline',
  queryFailureAnalysisDetail: '/failureAnalysis/queryFailureAnalysisDetail',
  exportFailureAnalysisDetail: '/failureAnalysis/exportFailureAnalysisDetail'
}

export function selectfailureAnalysis (data) {
  return axios({
    url: api.selectfailureAnalysis,
    method: 'post',
    data: data
  })
}

export function doExportProcurePlan (data) {
  return axios({
    url: api.doExportProcurePlan,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryFailureAnalysisDetail (data) {
  return axios({
    url: api.queryFailureAnalysisDetail,
    method: 'post',
    data: data
  })
}

export function exportFailureAnalysisDetail (data) {
  return axios({
    url: api.exportFailureAnalysisDetail,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
