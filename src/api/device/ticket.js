// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { axios } from '@/utils/request'
const api = {
  saveTicketHotWork: '/ticket/modifyTicketHotWork',
  queryTicketHotWork: '/ticket/queryTicketHotWork',
  exportTicketHotWork: '/ticket/exportTicketHotWork',
  saveTicketWork: '/ticket/modifyTicketWork',
  queryTicketWork: '/ticket/queryTicketWork',
  exportTicketWork: '/ticket/exportTicketWork',
  saveTicketOperation: '/ticket/modifyTicketOperation',
  queryTicketOperation: '/ticket/queryTicketOperation',
  exportTicketOperation: '/ticket/exportTicketOperation',
  // 审批相关
  approveTicketHotWork: '/ticket/approveTicketHotWork',
  approveTicketOperation: '/ticket/approveTicketOperation'
}

export function modifyTicketHotWork (parameter) {
  return axios({
    url: api.saveTicketHotWork,
    method: 'post',
    data: parameter
  })
}

export function queryTicketHotWork (parameter) {
  return axios({
    url: api.queryTicketHotWork,
    method: 'post',
    data: parameter
  })
}

export function exportTicketHotWork (data) {
  return axios({
    url: api.exportTicketHotWork,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function modifyTicketWork (parameter) {
  return axios({
    url: api.saveTicketWork,
    method: 'post',
    data: parameter
  })
}

export function queryTicketWork (parameter) {
  return axios({
    url: api.queryTicketWork,
    method: 'post',
    data: parameter
  })
}

export function exportTicketWork (data) {
  return axios({
    url: api.exportTicketWork,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function modifyTicketOperation (parameter) {
  return axios({
    url: api.saveTicketOperation,
    method: 'post',
    data: parameter
  })
}

export function queryTicketOperation (parameter) {
  return axios({
    url: api.queryTicketOperation,
    method: 'post',
    data: parameter
  })
}

export function exportTicketOperation (data) {
  return axios({
    url: api.exportTicketOperation,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function approveTicketHotWork (data) {
  return axios({
    url: api.approveTicketHotWork,
    method: 'post',
    data: data
  })
}
export function approveTicketOperation (data) {
  return axios({
    url: api.approveTicketOperation,
    method: 'post',
    data: data
  })
}
export default api
