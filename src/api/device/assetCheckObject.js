// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios, qs } from '@/utils/request'

/**
 *  queryAssetCheckArea: 获取点检区域
 *  modifyAssetCheckArea: 修改点检区域
 *  deleteAssetCheckArea: 删除点检区域
 *
 *  queryAssetCheckEquipment: 获取点检设备
 *  modifyAssetCheckEquipment: 修改点检设备
 *  deleteAssetCheckEquipment: 删除点检设备
 *
 *  queryAssetCheckPart: 获取点检部件
 *  modifyAssetCheckPart: 修改点检部件
 *  deleteAssetCheckPart: 删除点检部件
 *
 *  queryAssetCheckItem: 获取点检项目
 *  modifyAssetCheckItem: 修改点检项目
 *  deleteAssetCheckItem: 删除点检项目
 *
 *  uploadTemplate: 模板上传
 */
const api = {
  queryAssetCheckArea: '/assetCheckObject/queryAssetCheckArea',
  modifyAssetCheckArea: '/assetCheckObject/modifyAssetCheckArea',
  deleteAssetCheckArea: '/assetCheckObject/modifyAssetCheckArea',

  queryAssetCheckEquipment: '/assetCheckObject/queryAssetCheckEquipment',
  modifyAssetCheckEquipment: '/assetCheckObject/modifyAssetCheckEquipment',
  deleteAssetCheckEquipment: '/assetCheckObject/modifyAssetCheckEquipment',

  queryAssetCheckPart: '/assetCheckObject/queryAssetCheckPart',
  modifyAssetCheckPart: '/assetCheckObject/modifyAssetCheckPart',
  deleteAssetCheckPart: '/assetCheckObject/modifyAssetCheckPart',

  queryAssetCheckItem: '/assetCheckObject/queryAssetCheckItem',
  modifyAssetCheckItem: '/assetCheckObject/modifyAssetCheckItem',
  deleteAssetCheckItem: '/assetCheckObject/modifyAssetCheckItem',

  uploadTemplate: '/assetCheckObject/uploadTemplate',
  downItemTemplate: '/file/modelDownload?fileName=项目导入模板'
}

export function uploadTemplate (parameter) {
  return axios({
    url: api.uploadTemplate,
    method: 'post',
    data: parameter,
    paramsSerializer: function (parameter) {
      return qs.stringify(parameter, { indices: false })
    }
  })
}

export function queryAssetCheckArea (data) {
  return axios({
    url: api.queryAssetCheckArea,
    method: 'post',
    data: data
  })
}

export function modifyAssetCheckArea (data) {
  return axios({
    url: api.modifyAssetCheckArea,
    method: 'post',
    data: data
  })
}

export function deleteAssetCheckArea (data) {
  return axios({
    url: api.deleteAssetCheckArea,
    method: 'post',
    data: data
  })
}

export function queryAssetCheckEquipment (data) {
  return axios({
    url: api.queryAssetCheckEquipment,
    method: 'post',
    data: data
  })
}

export function modifyAssetCheckEquipment (data) {
  return axios({
    url: api.modifyAssetCheckEquipment,
    method: 'post',
    data: data
  })
}

export function deleteAssetCheckEquipment (data) {
  return axios({
    url: api.deleteAssetCheckEquipment,
    method: 'post',
    data: data
  })
}

export function queryAssetCheckPart (data) {
  return axios({
    url: api.queryAssetCheckPart,
    method: 'post',
    data: data
  })
}

export function modifyAssetCheckPart (data) {
  return axios({
    url: api.modifyAssetCheckPart,
    method: 'post',
    data: data
  })
}

export function deleteAssetCheckPart (data) {
  return axios({
    url: api.deleteAssetCheckPart,
    method: 'post',
    data: data
  })
}

export function queryAssetCheckItem (data) {
  return axios({
    url: api.queryAssetCheckItem,
    method: 'post',
    data: data
  })
}

export function modifyAssetCheckItem (data) {
  return axios({
    url: api.modifyAssetCheckItem,
    method: 'post',
    data: data
  })
}

export function deleteAssetCheckItem (data) {
  return axios({
    url: api.deleteAssetCheckItem,
    method: 'post',
    data: data
  })
}

export function downItemTemplate (data) {
  return axios({
    url: api.downItemTemplate,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export default api
