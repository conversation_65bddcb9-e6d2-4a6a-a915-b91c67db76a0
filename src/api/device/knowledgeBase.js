
import { axios } from '@/utils/request'

/**
 * technicalRepairPlan.js
 *
 *  queryList: 台账列表
 *  saveTechnicalRepairPlan: 保存
 *  queryPersonObject: 人员下拉
 */
const api = {
  queryKnowledgeBase: '/knowledgeBase/list',
  saveKnowledge: '/knowledgeBase/saveKnowledge'
}

export function queryKnowledgeBase (data) {
  return axios({
    url: api.queryKnowledgeBase,
    method: 'post',
    data: data
  })
}

export function saveKnowledge (data) {
  return axios({
    url: api.saveKnowledge,
    method: 'post',
    data: data
  })
}
