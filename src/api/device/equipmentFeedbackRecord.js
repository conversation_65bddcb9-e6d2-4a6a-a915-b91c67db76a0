
import { axios } from '@/utils/request'

/**
 * equipmentFeedbackRecord.js
 *  listForm: 获取反馈表单列表
 *  getRecord: 获取反馈表单
 *  saveRecord: 保存表单
 */
const api = {
  listForm: '/equipmentFeedbackRecord/listForm',
  getRecord: '/equipmentFeedbackRecord/getRecord',
  saveRecord: '/equipmentFeedbackRecord/save'
}
export function listForm (data) {
  return axios({
    url: api.listForm,
    method: 'post',
    data: data
  })
}
export function getRecord (data) {
  return axios({
    url: api.getRecord,
    method: 'post',
    data: data
  })
}
export function saveRecord (data) {
  return axios({
    url: api.saveRecord,
    method: 'post',
    data: data
  })
}
