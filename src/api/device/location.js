// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 *  queryLocation: 获取设备树信息
 *  modifyLocation: 修改设备树信息
 *  deleteLocation: 删除设备树信息
 */
const api = {
  queryLocation: '/location/queryLocation',
  modifyLocation: '/location/modifyLocation',
  deleteLocation: '/location/modifyLocation'
}

export function queryLocation (data) {
  return axios({
    url: api.queryLocation,
    method: 'post',
    data: data
  })
}

export function modifyLocation (data) {
  return axios({
    url: api.modifyLocation,
    method: 'post',
    data: data
  })
}

export function deleteLocation (data) {
  return axios({
    url: api.deleteLocation,
    method: 'post',
    data: data
  })
}

export default api
