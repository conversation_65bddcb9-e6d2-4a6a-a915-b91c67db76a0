
import { axios } from '@/utils/request'

/**
 * equipmentInspectRule.js
 *
 *  queryEquipment: 获取分类下设备
 *  queryPartList: 获取设备绑定的项目
 *  listRuleEquipment: 获取项目规则绑定的设备
 *  queryPersonObject:人员下拉
 *  getRuleDetail: 获取规则详情
 *  saveInspectRule: 新增检查计划项目
 *  listRulePerson:点击项目名称，获取日期时间段和人员
 *  saveDateRange:日期区间修改
 *  savePerson:人员修改
 *  savePersonBatch:批量人员修改
 *  saveItem:设备 新增/删除
 *  queryFeedbackForm 获取反馈表单
 *  generatePlan: 生成年月日计划
 *
 *
 */
const api = {
  queryEquipment: '/equipmentInspectRule/queryEquipment',
  queryPartList: '/equipmentInspectRule/list',
  listRuleEquipment: '/equipmentInspectRule/listRuleEquipment',
  queryPersonObject: '/equipmentInspectRule/queryPersonObject',
  listRulePerson: '/equipmentInspectRule/listRulePerson',
  saveDateRange: '/equipmentInspectRule/saveDateRange',
  savePerson: '/equipmentInspectRule/savePerson',
  savePersonBatch: '/equipmentInspectRule/savePersonBatch',
  saveItem: '/equipmentInspectRule/saveItem',
  getRuleDetail: '/equipmentInspectRule/getRule',
  saveInspectRule: '/equipmentInspectRule/save',
  generatePlan: '/equipmentInspectRule/generatePlan',
  queryFeedbackForm: '/equipmentInspectRule/queryFeedbackForm',
  queryAssetSbRule: '/equipmentInspectRule/queryAssetSbRule'
}
export function queryAssetSbRule (data) {
  return axios({
    url: api.queryAssetSbRule,
    method: 'post',
    data: data
  })
}
export function savePersonBatch (data) {
  return axios({
    url: api.savePersonBatch,
    method: 'post',
    data: data
  })
}
export function generatePlan (data) {
  return axios({
    url: api.generatePlan,
    method: 'post',
    data: data
  })
}
export function queryFeedbackForm (data) {
  return axios({
    url: api.queryFeedbackForm,
    method: 'post',
    data: data
  })
}
export function saveDateRange (data) {
  return axios({
    url: api.saveDateRange,
    method: 'post',
    data: data
  })
}
export function savePerson (data) {
  return axios({
    url: api.savePerson,
    method: 'post',
    data: data
  })
}
export function saveItem (data) {
  return axios({
    url: api.saveItem,
    method: 'post',
    data: data
  })
}
export function saveInspectRule (data) {
  return axios({
    url: api.saveInspectRule,
    method: 'post',
    data: data
  })
}
export function getRuleDetail (data) {
  return axios({
    url: api.getRuleDetail + `/${data}`,
    method: 'post'
  })
}
export function listRulePerson (data) {
  return axios({
    url: api.listRulePerson + `/${data}`,
    method: 'post'
  })
}
export function queryPersonObject (data) {
  return axios({
    url: api.queryPersonObject,
    method: 'post',
    data: data
  })
}
export function queryEquipment (data) {
  return axios({
    url: api.queryEquipment,
    method: 'post',
    data: data
  })
}
export function queryPartList (data) {
  return axios({
    url: api.queryPartList,
    method: 'post',
    data: data
  })
}
export function listRuleEquipment (data) {
  return axios({
    url: api.listRuleEquipment,
    method: 'post',
    data: data
  })
}
export default api
