// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
import { axios } from '@/utils/request'

/**
 *  getMaterialForm: 获取物资代码详细信息和余量信息
 */
const api = {
  getMaterialForm: '/material/queryMaterialForm',
  queryMaterialForWorkOrder: '/material/queryMaterialForWorkOrder'
}

export function getMaterialForm (parameter) {
  return axios({
    url: api.getMaterialForm,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function queryMaterialForWorkOrder (parameter) {
  return axios({
    url: api.queryMaterialForWorkOrder,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export default api
