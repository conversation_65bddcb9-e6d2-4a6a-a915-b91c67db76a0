import { axios } from '@/utils/request'

/**
 *  queryMaterial: 查询物资列表
 *  queryUnifiedMainPlan: 查询统购计划主表
 *  queryUnifiedChildPlan: 查询统购计划子表
 *  modifyUnifiedPlan: 修改统购计划
 *  deleteUnifiedPlan: 删除统购计划
 *  approveUnifiedPlan: 审批
 *  sendUnifiedPlan: 统购下发
 */
const api = {
  queryMaterial: 'unitorderMaterial/queryUnitorderMaterial',
  queryUnifiedMainPlan: '/unifiedPlan/queryUnifiedPr',
  queryUnifiedChildPlan: '/unifiedPlan/queryUnifiedPlan',
  modifyUnifiedPlan: '/unifiedPlan/modifyUnifiedPlan',
  deleteUnifiedPlan: '/unifiedPlan/modifyUnifiedPlan',
  approUnifiedPlan: '/unifiedPlan/approUnifiedPlan',
  sendUnifiedPlan: '/unifiedPlan/sendUnifiedPlan'
}

export function queryMaterial (data) {
  return axios({
    url: api.queryMaterial,
    method: 'post',
    data: data
  })
}

export function queryUnifiedMainPlan (data) {
  return axios({
    url: api.queryUnifiedMainPlan,
    method: 'post',
    data: data
  })
}

export function queryUnifiedChildPlan (data) {
  return axios({
    url: api.queryUnifiedChildPlan,
    method: 'post',
    data: data
  })
}

export function modifyUnifiedPlan (data) {
  return axios({
    url: api.modifyUnifiedPlan,
    method: 'post',
    data: data
  })
}

export function deleteUnifiedPlan (data) {
  return axios({
    url: api.deleteUnifiedPlan,
    method: 'post',
    data: data
  })
}

export function approUnifiedPlan (data) {
  return axios({
    url: api.approUnifiedPlan,
    method: 'post',
    data: data
  })
}

export function sendUnifiedPlan (data) {
  return axios({
    url: api.sendUnifiedPlan,
    method: 'post',
    data: data
  })
}

export default api
