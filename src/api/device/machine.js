import { axios } from '@/utils/request'

/**
 *  queryMachineMain: 查询装卸机械运行报表
 *  deleteMachineMain: 删除装卸机械运行报表
 *  modifyMachineMain: 新增修改装卸机械运行报表
 *  queryMachineLine:查询装卸机械运行情况
 *  modifyMachineLine:修改装卸机械运行情况
 *  approMachuseMain：审批装卸机械运行情况
 */
const api = {
  queryMachineMain: '/machuse/queryMachineMain',
  deleteMachineMain: '/machuse/modifyMachineMain',
  modifyMachineMain: '/machuse/modifyMachineMain',
  queryMachineLine: '/machuse/queryMachineLine',
  modifyMachineLine: '/machuse/modifyMachineLine',
  deleteMachineLine: '/machuse/modifyMachineLine',
  approMachuseMain: '/machuse/approMachuseMain'
}

export function queryMachineMain (data) {
  return axios({
    url: api.queryMachineMain,
    method: 'post',
    data: data
  })
}

export function deleteMachineMain (data) {
  return axios({
    url: api.deleteMachineMain,
    method: 'post',
    data: data
  })
}

export function modifyMachineMain (data) {
  return axios({
    url: api.modifyMachineMain,
    method: 'post',
    data: data
  })
}

export function queryMachineLine (data) {
  return axios({
    url: api.queryMachineLine,
    method: 'post',
    data: data
  })
}

export function modifyMachineLine (data) {
  return axios({
    url: api.modifyMachineLine,
    method: 'post',
    data: data
  })
}

export function deleteMachineLine (data) {
  return axios({
    url: api.deleteMachineLine,
    method: 'post',
    data: data
  })
}

export function approMachuseMain (data) {
  return axios({
    url: api.approMachuseMain,
    method: 'post',
    data: data
  })
}

export default api
