
import { axios } from '@/utils/request'

/**
 * technicalRepairPlan.js
 *
 *  queryList: 台账列表
 *  saveTechnicalRepairPlan: 保存
 *  queryPersonObject: 人员下拉
 */
const api = {
  queryTechnicalRepairPlan: '/equipmentRepairPlan/list',
  saveTechnicalRepairPlan: '/equipmentRepairPlan/savePlan',
  queryPersonObject: '/equipmentRepairPlan/queryPersonObject',
  doExportPlan: '/equipmentRepairPlan/exportPlan',
  listByYear: '/equipmentRepairPlan/listByYear',
  listPrlineSum: '/equipmentRepairPlan/listPrlineSum',
  listProjectSum: '/equipmentRepairPlan/listProjectSum',
  doExportPlanSum: 'equipmentRepairPlan/exportSum'
}

export function listPrlineSum (data) {
  return axios({
    url: api.listPrlineSum,
    method: 'post',
    data: data
  })
}
export function listProjectSum (data) {
  return axios({
    url: api.listProjectSum,
    method: 'post',
    data: data
  })
}
export function queryTechnicalRepairPlan (data) {
  return axios({
    url: api.queryTechnicalRepairPlan,
    method: 'post',
    data: data
  })
}

export function saveTechnicalRepairPlan (data) {
  return axios({
    url: api.saveTechnicalRepairPlan,
    method: 'post',
    data: data
  })
}

export function queryPersonObject (data) {
  return axios({
    url: api.queryPersonObject,
    method: 'post',
    data: data
  })
}
export function doExportPlan (data) {
  return axios({
    url: api.doExportPlan,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function listByYear (data) {
  return axios({
    url: api.listByYear,
    method: 'post',
    data: data
  })
}
export function doExportPlanSum (data) {
  return axios({
    url: api.doExportPlanSum,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
