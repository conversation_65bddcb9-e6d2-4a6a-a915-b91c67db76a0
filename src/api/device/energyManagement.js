
import { axios } from '@/utils/request'

/**
 * energyManagement.js
 *
 *  listOil: 油列表
 * exportOil：油导出
 * delOil:删除
 */
const api = {
  listOil: '/engmanagement/listOil',
  exportOil: '/engmanagement/exportOil',
  delOil: '/engmanagement/delOil',
  listWater: '/engmanagement/listWater',
  exportWater: '/engmanagement/exportWater',
  delWater: '/engmanagement/delWater',
  listEle: '/engmanagement/listEle',
  exportEle: '/engmanagement/exportEle',
  delEle: '/engmanagement/delEle',
  getLastRecord: '/engmanagement/getLastRecord',
  saveOil: '/engmanagement/saveOil',
  saveWater: '/engmanagement/saveWater',
  saveEle: '/engmanagement/saveEle',
  exportWaterTemp: '/engmanagement/exportWaterTemp',
  exportElkeTemp: '/engmanagement/exportElkeTemp',
  queryMeter: '/engmanagement/queryMeter'
}
export function saveWater (data) {
  return axios({
    url: api.saveWater,
    method: 'post',
    data: data
  })
}
export function saveEle (data) {
  return axios({
    url: api.saveEle,
    method: 'post',
    data: data
  })
}
export function saveOil (data) {
  return axios({
    url: api.saveOil,
    method: 'post',
    data: data
  })
}
export function getLastRecord (data) {
  return axios({
    url: api.getLastRecord,
    method: 'post',
    data: data
  })
}
export function listOil (data) {
  return axios({
    url: api.listOil,
    method: 'post',
    data: data
  })
}
export function delOil (data) {
  return axios({
    url: api.delOil,
    method: 'post',
    data: data
  })
}
export function exportOil (data) {
  return axios({
    url: api.exportOil,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function listWater (data) {
  return axios({
    url: api.listWater,
    method: 'post',
    data: data
  })
}
export function delWater (data) {
  return axios({
    url: api.delWater,
    method: 'post',
    data: data
  })
}
export function exportWater (data) {
  return axios({
    url: api.exportWater,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function listEle (data) {
  return axios({
    url: api.listEle,
    method: 'post',
    data: data
  })
}
export function delEle (data) {
  return axios({
    url: api.delEle,
    method: 'post',
    data: data
  })
}
export function exportEle (data) {
  return axios({
    url: api.exportEle,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function exportWaterTemp (data) {
  return axios({
    url: api.exportWaterTemp,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function exportElkeTemp (data) {
  return axios({
    url: api.exportElkeTemp,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function queryMeter (data) {
  return axios({
    url: api.queryMeter,
    method: 'post',
    data: data
  })
}
