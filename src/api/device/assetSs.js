// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 *  queryFacility: 获取设施信息
 *  modifyFacility: 修改设施信息
 *  deleteFacility: 删除设施信息
 *  queryAssetetSsSet: 获取子表信息
 */
const api = {
  queryFacility: '/assetSs/queryAssetSs',
  modifyFacility: '/assetSs/modifyAssetSs',
  deleteFacility: '/assetSs/modifyAssetSs',
  queryAssetetSsSet: '/assetSs/queryAssetetSsSet'
}

export function queryFacility (data) {
  return axios({
    url: api.queryFacility,
    method: 'post',
    data: data
  })
}

export function modifyFacility (data) {
  return axios({
    url: api.modifyFacility,
    method: 'post',
    data: data
  })
}

export function deleteFacility (data) {
  return axios({
    url: api.deleteFacility,
    method: 'post',
    data: data
  })
}

export function queryAssetetSsSet (data) {
  return axios({
    url: api.queryAssetetSsSet,
    method: 'post',
    data: data
  })
}

export default api
