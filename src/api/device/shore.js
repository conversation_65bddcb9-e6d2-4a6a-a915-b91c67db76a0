// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { axios } from '@/utils/request'

/**
 *  queryShoreInfo: 查询岸电信息
 *  doPushInfo2ETMS: 推送给etms(demo)
 */
const api = {
  queryShoreInfo: '/shore/queryShoreInfo',
  queryShoreInfoDetail: '/shore/queryShoreInfoDetail',
  modifyShoreInfoDetail: '/shore/modifyShoreInfoDetail',
  doPushInfo2ETMS: '/shore/doPushInfo2ETMS'
}

export function modifyShoreInfoDetail (parameter) {
  return axios({
    url: api.modifyShoreInfoDetail,
    method: 'post',
    data: parameter
  })
}
export function queryShoreInfo (parameter) {
  return axios({
    url: api.queryShoreInfo,
    method: 'post',
    data: parameter
  })
}

export function doPushInfo2ETMS (parameter) {
  return axios({
    url: api.doPushInfo2ETMS,
    method: 'post',
    data: parameter
  })
}
export function queryShoreInfoDetail (parameter) {
  return axios({
    url: api.queryShoreInfoDetail,
    method: 'post',
    data: parameter
  })
}
export default api
