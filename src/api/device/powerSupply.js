import { axios } from '@/utils/request'

/**
 *  queryAssetGpd: 查询
 *  addAssetGpdL: 计量设备新增
 *  modifyAssetGpd: 计量设备修改
 *  deleteAssetGpd: 计量设备删除
 */

const api = {
  queryAssetGpd: '/assetGpd/queryAssetGpd',
  addAssetGpd: '/assetGpd/modifyAssetGpd',
  modifyAssetGpd: '/assetGpd/modifyAssetGpd',
  deleteAssetGpd: '/assetGpd/modifyAssetGpd'
}

export function queryAssetGpd (data) {
  return axios({
    url: api.queryAssetGpd,
    method: 'post',
    data: data
  })
}

export function addAssetGpd (data) {
  return axios({
    url: api.addAssetGpd,
    method: 'post',
    data: data
  })
}

export function modifyAssetGpd (data) {
  return axios({
    url: api.modifyAssetGpd,
    method: 'post',
    data: data
  })
}

export function deleteAssetGpd (data) {
  return axios({
    url: api.deleteAssetGpd,
    method: 'post',
    data: data
  })
}

export default api
