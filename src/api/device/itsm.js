import { axios } from '@/utils/request'

const api = {
  creatNewITSM: '/ITSM/creatITSM',
  deleteITSM: '/ITSM/deleteITSM',
  queryList: '/ITSM/query',
  updateITSM: '/ITSM/updateITSM',
  queryDatabase: '/ITSM/queryDatabase',
  ITSMapproval: '/ITSM/approITSMWorkorder',
  validateITSMInfo: '/ITSM/validateBastionInfo',
  creatEvent: '/event/creatEvent',
  queryEvent: '/event/queryEvent',
  updateEvent: '/event/updateEvent',
  ITSMEventapproval: '/event/approEventWorkorder',
  queryTodo: '/event/queryTodo',
  queryById: '/ITSMTodo/queryById',
  selectDispatchInfoByEventId: '/event/selectDispatchInfoByEventId',
  updateDispatchInfo: '/event/updateDispatchInfo',
  creatIssue: '/issue/creatIssue',
  updateIssue: '/issue/updateIssue',
  queryIssue: '/issue/queryIssue',
  deleteIssue: '/issue/deleteIssue',
  createMeasure: '/issue/measure/newMeasure',
  updateMeasure: '/issue/measure/updateMeasure',
  queryMeasureList: '/issue/measure/queryMeasureList',
  deleteMeasure: '/issue/measure/deleteMeasure',
  queryTodoCount: '/ITSMTodo/queryCount'
}

export function validateITSMInfo (data) {
  return axios({
    url: api.validateITSMInfo,
    method: 'post',
    data
  })
}

export function deleteIssue (data) {
  return axios({
    url: api.deleteIssue,
    method: 'post',
    data
  })
}

export function updateMeasure (data) {
  return axios({
    url: api.updateMeasure,
    method: 'post',
    data
  })
}

export function deleteMeasure (data) {
  return axios({
    url: api.deleteMeasure,
    method: 'post',
    data
  })
}

export function queryMeasureList (data) {
  return axios({
    url: api.queryMeasureList,
    method: 'post',
    data
  })
}

export function createMeasure (data) {
  return axios({
    url: api.createMeasure,
    method: 'post',
    data
  })
}

export function queryIssue (data) {
  return axios({
    url: api.queryIssue,
    method: 'post',
    data
  })
}

export function updateIssue (data) {
  return axios({
    url: api.updateIssue,
    method: 'post',
    data
  })
}

export function creatIssue (data) {
  return axios({
    url: api.creatIssue,
    method: 'post',
    data
  })
}

export function creatNewITSM (data) {
  return axios({
    url: api.creatNewITSM,
    method: 'post',
    data
  })
}

export function deleteITSM (data) {
  return axios({
    url: api.deleteITSM,
    method: 'post',
    data
  })
}

export function queryITSMList (data) {
  return axios({
    url: api.queryList,
    method: 'post',
    data
  })
}

export function updateITSM (data) {
  return axios({
    url: api.updateITSM,
    method: 'post',
    data
  })
}

export function ITSMapproval (data) {
  return axios({
    url: api.ITSMapproval,
    method: 'post',
    data
  })
}

export function queryDatabase (data) {
  return axios({
    url: api.queryDatabase,
    method: 'post',
    data
  })
}

export function creatEvent (data) {
  return axios({
    url: api.creatEvent,
    method: 'post',
    data
  })
}

export function queryEventList (data) {
  return axios({
    url: api.queryEvent,
    method: 'post',
    data
  })
}

export function updateEvent (data) {
  return axios({
    url: api.updateEvent,
    method: 'post',
    data
  })
}

export function ITSMEventapproval (data) {
  return axios({
    url: api.ITSMEventapproval,
    method: 'post',
    data
  })
}

export function queryTodo (data) {
  return axios({
    url: api.queryTodo,
    method: 'post',
    data
  })
}

export function queryById (data) {
  return axios({
    url: api.queryById,
    method: 'post',
    data
  })
}

export function selectDispatchInfoByEventId (data) {
  return axios({
    url: api.selectDispatchInfoByEventId,
    method: 'post',
    data
  })
}

export function updateDispatchInfo (data) {
  return axios({
    url: api.updateDispatchInfo,
    method: 'post',
    data
  })
}

export function queryTodoCount (data) {
  return axios({
    url: api.queryTodoCount,
    method: 'post',
    data
  })
}
