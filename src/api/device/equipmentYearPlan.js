
import { axios } from '@/utils/request'

/**
 * equipmentDayPlan.js
 *
 *  queryYearPlanList: 获取年计划列表
 *  queryYearPlanEquipmentList: 获取计划下设备列表
 *  getPlan: 获取计划明细
 *  saveYearPlan: 新增年计划

 */
const api = {
  queryYearPlanList: '/equipmentYearPlan/list',
  queryYearPlanEquipmentList: '/equipmentYearPlan/listEquipment',
  getPlan: '/equipmentYearPlan/getPlan',
  saveYearPlan: '/equipmentYearPlan/save',
  approveYearPlan: '/equipmentYearPlan/approveYearPlan'
}

export function approveYearPlan (data) {
  return axios({
    url: api.approveYearPlan,
    method: 'post',
    data: data
  })
}

export function queryYearPlanList (data) {
  return axios({
    url: api.queryYearPlanList,
    method: 'post',
    data: data
  })
}
export function queryYearPlanEquipmentList (data) {
  return axios({
    url: api.queryYearPlanEquipmentList,
    method: 'post',
    data: data
  })
}
export function getPlan (data) {
  return axios({
    url: api.getPlan + `/${data}`,
    method: 'post'
  })
}
export function saveYearPlan (data) {
  return axios({
    url: api.saveYearPlan,
    method: 'post',
    data: data
  })
}

export default api
