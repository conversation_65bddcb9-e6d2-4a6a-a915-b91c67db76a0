import { axios } from '@/utils/request'

const api = {
  selectrepairCosts: '/repairCosts/queryFloor',
  querySiteMaintenanceCosts: '/repairCosts/querySiteMaintenanceCosts',
  doExportRepairCosts: '/repairCosts/doExportRepairCosts'
}

export function selectrepairCosts (data) {
  return axios({
    url: api.selectrepairCosts,
    method: 'post',
    data: data
  })
}

export function querySiteMaintenanceCosts (data) {
  return axios({
    url: api.querySiteMaintenanceCosts,
    method: 'post',
    data: data
  })
}

export function doExportRepairCosts (data) {
  return axios({
    url: api.doExportRepairCosts,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
