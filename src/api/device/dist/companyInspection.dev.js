"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.selectcompanyInspection = selectcompanyInspection;
exports.modifyCompanyInspection = modifyCompanyInspection;

var _request = require("@/utils/request");

var api = {
  querycompanyInspection: '/companyInspection/queryFloor',
  modifycompanyInspection: '/companyInspection/modifyCompanyInspection'
};

function selectcompanyInspection(data) {
  return (0, _request.axios)({
    url: api.querycompanyInspection,
    method: 'post',
    data: data
  });
}

function modifyCompanyInspection(data) {
  return (0, _request.axios)({
    url: api.modifycompanyInspection,
    method: 'post',
    data: data
  });
}