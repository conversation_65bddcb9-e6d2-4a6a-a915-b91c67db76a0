import { axios } from '@/utils/request'
const api = {
  queryFeeplanMain: '/feeplanMain/queryFeeplanMain',
  modifyFeeplanMain: '/feeplanMain/modifyFeeplanMain',
  delFeeplanMain: '/feeplanMain/delFeeplanMain',
  editIsApprovalEnd: '/feeplanMain/editIsApprovalEnd',
  approFeeplanMain: '/feeplanMain/approFeeplanMain',
  queryFeeplan: '/feeplan/queryFeeplan',
  feeplanMainXj: '/feeplanMain/feeplanMainXj',
  queryTenderInfo: '/feeplanMain/queryTenderInfo',
  sendToTender: '/feeplanMain/sendToTender',
  feeplanToContract: '/feeplanMain/feeplanToContract',
  modifyTenderDescription: '/feeplanMain/modifyTenderDescription',
  doExportMonthFeeplan: '/feeplanMain/doExportMonthFeeplan',
  modifyFeeplanBj: '/feeplanXj/modifyFeeplanBj',
  approFeeplanMainRfq: '/feeplanMain/approFeeplanMainRfq',
  queryFeeplanMainRemind: '/feeplanMain/queryFeeplanMainRemind',
  feeplanMainAllocationPerson: '/feeplanMain/feeplanMainAllocationPerson',
  deleteFeeplanVendor: '/feeplanMain/deleteFeeplanVendor',
  approFeeplanMainXj: '/feeplanMain/approFeeplanMainXj',
  addFeeplanVendor: '/feeplanMain/addFeeplanVendor'
}

export function addFeeplanVendor (data) {
  return axios({
    url: api.addFeeplanVendor,
    method: 'post',
    data: data
  })
}

export function approFeeplanMainXj (data) {
  return axios({
    url: api.approFeeplanMainXj,
    method: 'post',
    data: data
  })
}

export function deleteFeeplanVendor (data) {
  return axios({
    url: api.deleteFeeplanVendor,
    method: 'post',
    data: data
  })
}
export function feeplanMainAllocationPerson (data) {
  return axios({
    url: api.feeplanMainAllocationPerson,
    method: 'post',
    data: data
  })
}
export function queryFeeplanMainRemind (data) {
  return axios({
    url: api.queryFeeplanMainRemind,
    method: 'post',
    data: data
  })
}
export function approFeeplanMainRfq (data) {
  return axios({
    url: api.approFeeplanMainRfq,
    method: 'post',
    data: data
  })
}
export function queryFeeplanMain (data) {
  return axios({
    url: api.queryFeeplanMain,
    method: 'post',
    data: data
  })
}
export function queryFeeplan (data) {
  return axios({
    url: api.queryFeeplan,
    method: 'post',
    data: data
  })
}

export function delFeeplanMain (data) {
  return axios({
    url: api.delFeeplanMain,
    method: 'post',
    data: data
  })
}
export function editIsApprovalEnd (data) {
  return axios({
    url: api.editIsApprovalEnd,
    method: 'post',
    data: data
  })
}

export function modifyFeeplanMain (data) {
  return axios({
    url: api.modifyFeeplanMain,
    method: 'post',
    data: data
  })
}
export function approFeeplanMain (data) {
  return axios({
    url: api.approFeeplanMain,
    method: 'post',
    data: data
  })
}
export function feeplanMainXj (data) {
  return axios({
    url: api.feeplanMainXj,
    method: 'post',
    data: data
  })
}
export function modifyFeeplanBj (data) {
  return axios({
    url: api.modifyFeeplanBj,
    method: 'post',
    data: data
  })
}
export function feeplanToContract (data) {
  return axios({
    url: api.feeplanToContract,
    method: 'post',
    data: data
  })
}
export function sendToTender (data) {
  return axios({
    url: api.sendToTender,
    method: 'post',
    data: data
  })
}
export function queryTenderInfo (data) {
  return axios({
    url: api.queryTenderInfo,
    method: 'post',
    data: data
  })
}
export function modifyTenderDescription (data) {
  return axios({
    url: api.modifyTenderDescription,
    method: 'post',
    data: data
  })
}
export function doExportMonthFeeplan (data) {
  return axios({
    url: api.doExportMonthFeeplan,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
