import { axios } from '@/utils/request'

const api = {
  selectDevelopmentDatabase: '/developmentDatabase/queryFloor',
  modifyDevelopmentDatabase: '/developmentDatabase/modifyDevelopmentDatabase'
}

export function selectDevelopmentDatabase (data) {
  return axios({
    url: api.selectDevelopmentDatabase,
    method: 'post',
    data: data
  })
}
export function modifyDevelopmentDatabase (data) {
  return axios({
    url: api.modifyDevelopmentDatabase,
    method: 'post',
    data: data
  })
}
export default api
