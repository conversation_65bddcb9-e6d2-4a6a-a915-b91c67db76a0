import { axios } from '@/utils/request'

/**
 *  queryAssetMainArea: 获取保养区域
 *  modifyAssetMainArea: 修改保养区域
 *  deleteAssetMainArea: 删除保养区域
 *
 *  queryAssetMainEquipment: 获取保养设备
 *  modifyAssetMainEquipment: 修改保养设备
 *  deleteAssetMainEquipment: 删除保养设备
 *
 *  queryAssetMainPart: 获取保养部件
 *  modifyAssetMainPart: 修改保养部件
 *  deleteAssetMainPart: 删除保养部件
 *
 *  queryAssetMainItem: 获取保养项目
 *  modifyAssetMainItem: 修改保养项目
 *  deleteAssetMainItem: 删除保养项目
 */
const api = {
  queryAssetMainArea: '/maintain/queryMaintainArea',
  modifyAssetMainArea: '/maintain/modifyMaintainArea',
  deleteAssetMainArea: '/maintain/modifyMaintainArea',

  queryAssetMainEquipment: '/maintain/queryMaintainEquipment',
  modifyAssetMainEquipment: '/maintain/modifyMaintainEquipment',
  deleteAssetMainEquipment: '/maintain/modifyMaintainEquipment',

  queryAssetMainPart: '/maintain/queryMaintainPart',
  modifyAssetMainPart: '/maintain/modifyMaintainPart',
  deleteAssetMainPart: '/maintain/modifyMaintainPart',

  queryAssetMainItem: '/maintain/queryMaintainItem',
  modifyAssetMainItem: '/maintain/modifyMaintainItem',
  deleteAssetMainItem: '/maintain/modifyMaintainItem'
}

export function queryAssetMainArea (data) {
  return axios({
    url: api.queryAssetMainArea,
    method: 'post',
    data: data
  })
}

export function modifyAssetMainArea (data) {
  return axios({
    url: api.modifyAssetMainArea,
    method: 'post',
    data: data
  })
}

export function deleteAssetMainArea (data) {
  return axios({
    url: api.deleteAssetMainArea,
    method: 'post',
    data: data
  })
}

export function queryAssetMainEquipment (data) {
  return axios({
    url: api.queryAssetMainEquipment,
    method: 'post',
    data: data
  })
}

export function modifyAssetMainEquipment (data) {
  return axios({
    url: api.modifyAssetMainEquipment,
    method: 'post',
    data: data
  })
}

export function deleteAssetMainEquipment (data) {
  return axios({
    url: api.deleteAssetMainEquipment,
    method: 'post',
    data: data
  })
}

export function queryAssetMainPart (data) {
  return axios({
    url: api.queryAssetMainPart,
    method: 'post',
    data: data
  })
}

export function modifyAssetMainPart (data) {
  return axios({
    url: api.modifyAssetMainPart,
    method: 'post',
    data: data
  })
}

export function deleteAssetMainPart (data) {
  return axios({
    url: api.deleteAssetMainPart,
    method: 'post',
    data: data
  })
}

export function queryAssetMainItem (data) {
  return axios({
    url: api.queryAssetMainItem,
    method: 'post',
    data: data
  })
}

export function modifyAssetMainItem (data) {
  return axios({
    url: api.modifyAssetMainItem,
    method: 'post',
    data: data
  })
}

export function deleteAssetMainItem (data) {
  return axios({
    url: api.deleteAssetMainItem,
    method: 'post',
    data: data
  })
}

export default api
