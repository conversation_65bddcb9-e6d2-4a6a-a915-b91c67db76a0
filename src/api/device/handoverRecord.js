import { axios } from '@/utils/request'

/**
 *  queryHandoverRecord: 交接记录查询
 *  modifyHandoverRecord: 交接记录增删改
 *  doExport:导出报表
 */

const api = {
  queryHandoverRecord: '/handoverRecord/queryHandoverRecord',
  modifyHandoverRecord: '/handoverRecord/modifyHandoverRecord',
  doExport: '/handoverRecord/doExport'
}

export function queryHandoverRecord (data) {
  return axios({
    url: api.queryHandoverRecord,
    method: 'post',
    data: data
  })
}

export function modifyHandoverRecord (data) {
  return axios({
    url: api.modifyHandoverRecord,
    method: 'post',
    data: data
  })
}

export function doExport (data) {
  return axios({
    url: api.doExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export default api
