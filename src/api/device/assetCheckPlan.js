// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 *  queryAssetCheckPlan: 获取点检计划
 *  modifyAssetCheckPlan: 修改点检计划
 *  deleteAssetCheckPlan: 删除点检计划
 *
 *  modifyAssetCheckArea: 修改点检计划区域
 *  deleteAssetCheckArea: 删除点检计划区域
 *
 *  queryAssetCheckItem: 查询点检计划项目
 *  modifyAssetCheckItem: 修改点检计划项目
 *  deleteAssetCheckItem: 删除点检计划项目
 *
 *  queryAssetCheckTask: 获取点检计划/任务
 *  queryAssetCheckFeedback: 查询点检计划反馈
 *  modifyAssetCheckFeedback: 修改点检计划反馈
 *  createCheckFeedbackWorkorder: 点检反馈选择设备生成工单
 *  createDg:创建定稿
 *
 */
const api = {
  queryAssetCheckPlan: '/assetCheckPlan/queryAssetCheckPlan',
  modifyAssetCheckPlan: '/assetCheckPlan/modifyAssetCheckPlan',
  deleteAssetCheckPlan: '/assetCheckPlan/modifyAssetCheckPlan',

  modifyAssetCheckArea: '/assetCheckPlan/updateAssetCheckPlanAreaOnly',
  deleteAssetCheckArea: '/assetCheckPlan/removeAssetCheckPlanAreaOnly',

  queryAssetCheckItem: '/assetCheckPlan/queryItem',
  modifyAssetCheckItem: '/assetCheckPlan/modifyItem',
  deleteAssetCheckItem: '/assetCheckPlan/modifyItem',

  queryAssetCheckTask: '/assetCheckPlan/queryFeedback',
  toCompleted: '/assetCheckPlan/toCompleted',
  queryAssetCheckFeedback: '/assetCheckPlan/queryFeedbackItem',
  modifyAssetCheckFeedback: '/assetCheckPlan/modifyFeedback',
  createDg: '/assetCheckPlan/createDg',

  createCheckFeedbackWorkorder: '/assetCheckPlan/saveWorkorderByAssetCheck'
}

export function queryAssetCheckPlan (data) {
  return axios({
    url: api.queryAssetCheckPlan,
    method: 'post',
    data: data
  })
}

export function modifyAssetCheckPlan (data) {
  return axios({
    url: api.modifyAssetCheckPlan,
    method: 'post',
    data: data
  })
}

export function deleteAssetCheckPlan (data) {
  return axios({
    url: api.deleteAssetCheckPlan,
    method: 'post',
    data: data
  })
}

export function modifyAssetCheckArea (data) {
  return axios({
    url: api.modifyAssetCheckArea,
    method: 'post',
    data: data
  })
}

export function deleteAssetCheckArea (data) {
  return axios({
    url: api.deleteAssetCheckArea,
    method: 'post',
    data: data
  })
}

export function queryAssetCheckItem (data) {
  return axios({
    url: api.queryAssetCheckItem,
    method: 'post',
    data: data
  })
}

export function modifyAssetCheckItem (data) {
  return axios({
    url: api.modifyAssetCheckItem,
    method: 'post',
    data: data
  })
}

export function deleteAssetCheckItem (data) {
  return axios({
    url: api.deleteAssetCheckItem,
    method: 'post',
    data: data
  })
}

export function queryAssetCheckTask (data) {
  return axios({
    url: api.queryAssetCheckTask,
    method: 'post',
    data: data
  })
}

export function toCompleted (data) {
  return axios({
    url: api.toCompleted,
    method: 'post',
    data: data
  })
}

export function createDg (data) {
  return axios({
    url: api.createDg,
    method: 'post',
    data: data
  })
}

export function queryAssetCheckFeedback (data) {
  return axios({
    url: api.queryAssetCheckFeedback,
    method: 'post',
    data: data
  })
}

export function modifyAssetCheckFeedback (data) {
  return axios({
    url: api.modifyAssetCheckFeedback,
    method: 'post',
    data: data
  })
}

export function createCheckFeedbackWorkorder (data) {
  return axios({
    url: api.createCheckFeedbackWorkorder,
    method: 'post',
    data: data
  })
}

export default api
