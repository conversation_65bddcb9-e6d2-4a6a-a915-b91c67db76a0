// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 */
const api = {
  queryBudget: '/budgetNew/queryBudget',
  modifyBudget: '/budgetNew/modifyBudget',
  modelDownload: '/file/modelDownload?fileName=budgetMD',
  getSumBudgetByDept: '/budgetNew/getSumBudgetByDept',
  queryBudgetCockpit: '/budgetNew/queryBudgetCockpit',
  modifyBudgetCockpit: '/budgetNew/modifyBudgetCockpit'
}

export function modifyBudgetCockpit (data) {
  return axios({
    url: api.modifyBudgetCockpit,
    method: 'post',
    data: data
  })
}

export function queryBudgetCockpit (data) {
  return axios({
    url: api.queryBudgetCockpit,
    method: 'post',
    data: data
  })
}

export function getSumBudgetByDept (data) {
  return axios({
    url: api.getSumBudgetByDept,
    method: 'post',
    data: data
  })
}

export function queryBudget (data) {
  return axios({
    url: api.queryBudget,
    method: 'post',
    data: data
  })
}

export function modifyBudget (data) {
  return axios({
    url: api.modifyBudget,
    method: 'post',
    data: data
  })
}

export function modelDownload (data) {
  return axios({
    url: api.modelDownload,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export default api
