// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { axios } from '@/utils/request'

/**
 */
const api = {
  // 轮胎相关
  queryTireInfo: '/tire/queryTireInfo',
  deleteTireInfo: '/tire/deleteTireInfo',
  exportTireInfo: '/tire/exportTireInfo',
  queryTireInfoDetail: '/tire/queryTireInfoDetail',
  exportTireInfoDetail: '/tire/exportTireInfoDetail',
  downTireTemplate: '/file/modelDownload?fileName=轮胎导入模板'
}

export function queryTireInfo (data) {
  return axios({
    url: api.queryTireInfo,
    method: 'post',
    data: data
  })
}

export function deleteTireInfo (data) {
  return axios({
    url: api.deleteTireInfo,
    method: 'post',
    data: data
  })
}

export function exportTireInfo (data) {
  return axios({
    url: api.exportTireInfo,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryTireInfoDetail (data) {
  return axios({
    url: api.queryTireInfoDetail,
    method: 'post',
    data: data
  })
}

export function exportTireInfoDetail (data) {
  return axios({
    url: api.exportTireInfoDetail,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function downTireTemplate (data) {
  return axios({
    url: api.downTireTemplate,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export default api
