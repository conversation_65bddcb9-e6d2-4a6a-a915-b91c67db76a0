import { axios } from '@/utils/request'

/**
 *  queryAssetxxh: 信息化管理查询
 *  addAssetxxh: 信息化管理新增
 *  modifyAssetxxh: 信息化管理修改
 *  deleteAssetxxh: 信息化管理删除
 */

const api = {
  queryAssetxxh: '/assetXxh/queryAssetXxh',
  addAssetxxh: '/assetXxh/modifyAssetXxh',
  modifyAssetxxh: '/assetXxh/modifyAssetXxh',
  deleteyAssetxxh: '/assetXxh/modifyAssetXxh'
}

export function queryAssetxxh (data) {
  return axios({
    url: api.queryAssetxxh,
    method: 'post',
    data: data
  })
}

export function addAssetxxh (data) {
  return axios({
    url: api.addAssetxxh,
    method: 'post',
    data: data
  })
}

export function modifyAssetxxh (data) {
  return axios({
    url: api.modifyAssetxxh,
    method: 'post',
    data: data
  })
}

export function deleteAssetxxh (data) {
  return axios({
    url: api.deleteyAssetxxh,
    method: 'post',
    data: data
  })
}

export default api
