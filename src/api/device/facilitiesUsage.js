import { axios } from '@/utils/request'

/**
 *  query: 查询FacilitiesUsage
 *  modify: 修改FacilitiesUsage
 */

const api = {
  query: '/facilitiesUsage/queryFacilitiesUsage',
  modify: '/facilitiesUsage/modifyFacilitiesUsage',
  approve: '/facilitiesUsage/approFacilitiesUsage',
  approFacilitiesUsage: '/facilitiesUsage/approFacilitiesUsage'
}

export function query (param) {
  return axios({
    url: api.query,
    method: 'post',
    data: param
  })
}

export function modify (param) {
  return axios({
    url: api.modify,
    method: 'post',
    data: param
  })
}

export function remove (param) {
  return axios({
    url: api.modify,
    method: 'post',
    data: param
  })
}

export function approFacilitiesUsage (param) {
  return axios({
    url: api.approFacilitiesUsage,
    method: 'post',
    data: param
  })
}

export default api
