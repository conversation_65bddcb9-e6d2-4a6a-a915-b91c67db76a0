// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 *  queryBudgetType: 查询预算分类
 *  modifyBudgetType: 修改预算分类
 *  deleteBudgetType: 删除预算分类
 *
 *  queryBudget: 查询预算
 *  modifyBudget: 修改预算
 *  deleteBudget: 删除预算
 */
const api = {
  queryBudgetType: '/budget/queryBudgetType',
  modifyBudgetType: '/budget/modifyBudgetType',
  deleteBudgetType: '/budget/modifyBudgetType',

  queryBudget: '/budget/queryBudget',
  modifyBudget: '/budget/modifyBudget',
  deleteBudget: '/budget/modifyBudget',

  queryBudgetStatistics: '/budget/queryBudgetStatistics',
  modifyBudgetStatistics: '/budget/modifyBudgetStatistics',
  doExport: '/budget/doExport',
  getGridAmountInfor: '/budget/getGridAmountInfor',
  queryPlanInfo: '/budget/queryPlanInfo'
}

export function getGridAmountInfor (data) {
  return axios({
    url: api.getGridAmountInfor,
    method: 'post',
    data: data
  })
}

export function queryPlanInfo (data) {
  return axios({
    url: api.queryPlanInfo,
    method: 'post',
    data: data
  })
}

export function queryBudgetType (data) {
  return axios({
    url: api.queryBudgetType,
    method: 'post',
    data: data
  })
}

export function modifyBudgetType (data) {
  return axios({
    url: api.modifyBudgetType,
    method: 'post',
    data: data
  })
}

export function deleteBudgetType (data) {
  return axios({
    url: api.deleteBudgetType,
    method: 'post',
    data: data
  })
}

export function queryBudget (data) {
  return axios({
    url: api.queryBudget,
    method: 'post',
    data: data
  })
}

export function modifyBudget (data) {
  return axios({
    url: api.modifyBudget,
    method: 'post',
    data: data
  })
}

export function deleteBudget (data) {
  return axios({
    url: api.deleteBudget,
    method: 'post',
    data: data
  })
}

export function queryBudgetStatistics (data) {
  return axios({
    url: api.queryBudgetStatistics,
    method: 'post',
    data: data
  })
}

export function modifyBudgetStatistics (data) {
  return axios({
    url: api.modifyBudgetStatistics,
    method: 'post',
    data: data
  })
}

export function doExport (data) {
  return axios({
    url: api.doExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export default api
