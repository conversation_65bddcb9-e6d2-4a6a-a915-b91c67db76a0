import { axios } from '@/utils/request'

const api = {
  queryItems: '/item/queryItems',
  directBuy: '/item/directBuy',
  cancelDirectBuy: '/item/cancelDirectBuy',
  modifyItems: '/item/modifyItems',
  doExportRequest: '/item/doExportRequest',
  sendToTender: '/item/sendToTender',
  distributeItem: '/item/distributeItem',
  modifyDistributeItem: '/item/modifyDistributeItem',
  getDistributeMen: '/item/getDistributeMen',
  queryItemsProgress: '/item/queryItemsProgress',
  sendItem: '/item/sendItem',
  modifyItemsProgress: '/item/modifyItemsProgress',
  approItem: '/item/approItem',
  queryFinalAccountAndGuarantee: '/item/queryFinalAccountAndGuarantee',
  modifyFinalAccountAndGuarantee: '/item/modifyFinalAccountAndGuarantee',
  queryItemsPayment: '/item/queryItemsPayment',
  modifyItemsPayment: '/item/modifyItemsPayment',
  sendToContract: '/item/sendToContract',

  getItemNearlyOutGuaranteeDate: '/item/getItemNearlyOutGuaranteeDate',
  getContractNearlyTotalFinalAccount: '/item/getContractNearlyTotalFinalAccount',

  queryItemSubmit: '/item/queryItemSubmit',
  getBudgetsForItem: '/item/getBudgetsForItem',
  pushEasItem: '/item/pushEas',
  bx: '/item/bxItem',
  getItemSubmitDetails: '/item/getItemSubmitDetails',
  modifyItemSubmitDetails: '/item/modifyItemSubmitDetails',
  doExport: '/item/doExport',
  doExportCBFY: '/item/doExportCBFY',
  doExportGuarantee: '/item/doExportGuarantee',
  exportRfqEvaWord: '/item/exportRfqEvaWord',
  queryFinancialPayment: '/item/queryFinancialPayment',
  approItemRfqEvaluate: '/item/approItemRfqEvaluate',
  modifyTenderDescription: '/item/modifyTenderDescription',
  sendToFixedAssetCheck: '/item/sendToFixedAssetCheck',
  queryVendorInfoForSelectOption: '/vendor/queryVendorInfoForSelectOption',
  doExportApproving: '/item/doExportApproving',
  getItemEvaUserNext: '/item/getItemEvaUserNext',
  setItemEvaUserNext: '/item/setItemEvaUserNext',
  getAllYearBudget: '/budgetNew/getAllYearBudget',
  getAllYearFinishedInvestMoney: '/item/getAllYearFinishedInvestMoney',
  sendToContractMulti: '/item/sendToContractMulti',
  remindItem: '/item/remindItem'
}

export function remindItem (data) {
  return axios({
    url: api.remindItem,
    method: 'post',
    data: data
  })
}

export function sendToContractMulti (data) {
  return axios({
    url: api.sendToContractMulti,
    method: 'post',
    data: data
  })
}

export function getAllYearFinishedInvestMoney (data) {
  return axios({
    url: api.getAllYearFinishedInvestMoney,
    method: 'post',
    data: data
  })
}

export function getAllYearBudget (data) {
  return axios({
    url: api.getAllYearBudget,
    method: 'post',
    data: data
  })
}

export function bx (data) {
  return axios({
    url: api.bx,
    method: 'post',
    data: data
  })
}

export function getItemEvaUserNext (data) {
  return axios({
    url: api.getItemEvaUserNext,
    method: 'post',
    data: data
  })
}

export function setItemEvaUserNext (data) {
  return axios({
    url: api.setItemEvaUserNext,
    method: 'post',
    data: data
  })
}

export function cancelDirectBuy (data) {
  return axios({
    url: api.cancelDirectBuy,
    method: 'post',
    data: data
  })
}

export function modifyDistributeItem (data) {
  return axios({
    url: api.modifyDistributeItem,
    method: 'post',
    data: data
  })
}

export function directBuy (data) {
  return axios({
    url: api.directBuy,
    method: 'post',
    data: data
  })
}

export function doExportApproving (data) {
  return axios({
    url: api.doExportApproving,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryVendorInfoForSelectOption (data) {
  return axios({
    url: api.queryVendorInfoForSelectOption,
    method: 'post',
    data: data
  })
}

export function sendToFixedAssetCheck (data) {
  return axios({
    url: api.sendToFixedAssetCheck,
    method: 'post',
    data: data
  })
}

export function modifyTenderDescription (data) {
  return axios({
    url: api.modifyTenderDescription,
    method: 'post',
    data: data
  })
}

export function approItemRfqEvaluate (data) {
  return axios({
    url: api.approItemRfqEvaluate,
    method: 'post',
    data: data
  })
}

export function queryFinancialPayment (data) {
  return axios({
    url: api.queryFinancialPayment,
    method: 'post',
    data: data
  })
}

export function exportRfqEvaWord (data) {
  return axios({
    url: api.exportRfqEvaWord,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportGuarantee (data) {
  return axios({
    url: api.doExportGuarantee,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportCBFY (data) {
  return axios({
    url: api.doExportCBFY,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExport (data) {
  return axios({
    url: api.doExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryItemSubmit (data) {
  return axios({
    url: api.queryItemSubmit,
    method: 'post',
    data: data
  })
}

export function getBudgetsForItem (data) {
  return axios({
    url: api.getBudgetsForItem,
    method: 'post',
    data: data
  })
}

export function pushEasItem (data) {
  return axios({
    url: api.pushEasItem,
    method: 'post',
    data: data
  })
}

export function getItemSubmitDetails (data) {
  return axios({
    url: api.getItemSubmitDetails,
    method: 'post',
    data: data
  })
}

export function modifyItemSubmitDetails (data) {
  return axios({
    url: api.modifyItemSubmitDetails,
    method: 'post',
    data: data
  })
}

export function getItemNearlyOutGuaranteeDate (data) {
  return axios({
    url: api.getItemNearlyOutGuaranteeDate,
    method: 'post',
    data: data
  })
}

export function getContractNearlyTotalFinalAccount (data) {
  return axios({
    url: api.getContractNearlyTotalFinalAccount,
    method: 'post',
    data: data
  })
}

export function modifyItemsPayment (data) {
  return axios({
    url: api.modifyItemsPayment,
    method: 'post',
    data: data
  })
}

export function sendToContract (data) {
  return axios({
    url: api.sendToContract,
    method: 'post',
    data: data
  })
}

export function queryItemsPayment (data) {
  return axios({
    url: api.queryItemsPayment,
    method: 'post',
    data: data
  })
}

export function modifyFinalAccountAndGuarantee (data) {
  return axios({
    url: api.modifyFinalAccountAndGuarantee,
    method: 'post',
    data: data
  })
}

export function queryFinalAccountAndGuarantee (data) {
  return axios({
    url: api.queryFinalAccountAndGuarantee,
    method: 'post',
    data: data
  })
}

export function approItem (data) {
  return axios({
    url: api.approItem,
    method: 'post',
    data: data
  })
}

export function modifyItemsProgress (data) {
  return axios({
    url: api.modifyItemsProgress,
    method: 'post',
    data: data
  })
}

export function sendItem (data) {
  return axios({
    url: api.sendItem,
    method: 'post',
    data: data
  })
}

export function queryItemsProgress (data) {
  return axios({
    url: api.queryItemsProgress,
    method: 'post',
    data: data
  })
}

export function getDistributeMen (data) {
  return axios({
    url: api.getDistributeMen,
    method: 'post',
    data: data
  })
}

export function distributeItem (data) {
  return axios({
    url: api.distributeItem,
    method: 'post',
    data: data
  })
}

export function sendToTender (data) {
  return axios({
    url: api.sendToTender,
    method: 'post',
    data: data
  })
}

export function doExportRequest (data) {
  return axios({
    url: api.doExportRequest,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryItems (data) {
  return axios({
    url: api.queryItems,
    method: 'post',
    data: data
  })
}

export function modifyItems (data) {
  return axios({
    url: api.modifyItems,
    method: 'post',
    data: data
  })
}
