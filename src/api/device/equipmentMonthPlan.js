
import { axios } from '@/utils/request'

/**
 * equipmentMonthPlan.js
 *
 *  queryMonthPlanList: 获取月计划列表
 *  queryMonthPlanEquipmentList: 获取计划下设备列表
 *  getPlan: 获取计划明细
 *  saveMonthPlan: 新增月计划
 *  saveFromFault: 生成月计划

 */
const api = {
  queryMonthPlanList: '/equipmentMonthPlan/list',
  queryMonthPlanEquipmentList: '/equipmentMonthPlan/listEquipment',
  getPlan: '/equipmentMonthPlan/getPlan',
  saveMonthPlan: '/equipmentMonthPlan/save',
  saveFromFault: '/equipmentMonthPlan/saveFromFault',
  saveFromFaultPlan: '/equipmentMonthPlan/saveFromFaultPlan'
}
export function saveFromFaultPlan (data) {
  return axios({
    url: api.saveFromFaultPlan,
    method: 'post',
    data: data
  })
}
export function saveFromFault (data) {
  return axios({
    url: api.saveFromFault,
    method: 'post',
    data: data
  })
}
export function queryMonthPlanList (data) {
  return axios({
    url: api.queryMonthPlanList,
    method: 'post',
    data: data
  })
}
export function queryMonthPlanEquipmentList (data) {
  return axios({
    url: api.queryMonthPlanEquipmentList,
    method: 'post',
    data: data
  })
}
export function getPlan (data) {
  return axios({
    url: api.getPlan + `/${data}`,
    method: 'post'
  })
}
export function saveMonthPlan (data) {
  return axios({
    url: api.saveMonthPlan,
    method: 'post',
    data: data
  })
}

export default api
