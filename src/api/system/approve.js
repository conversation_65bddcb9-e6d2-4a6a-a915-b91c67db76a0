import { qs, axios } from '@/utils/request'

/**
 *  queryAssigee: 下一个审批人
 *  queryApproHis: 查询审批历史
 */
const api = {
  queryAssigee: '/appro/queryPrlineAssigee',
  queryApproHis: '/appro/queryApproHis',
  doUrge: '/approve/doUrge',
  queryAppointMen: '/appro/queryAppointMen',
  approve: '/appro/approve'
}

export function approve (data) {
  return axios({
    url: api.approve,
    method: 'post',
    data: data
  })
}

export function queryAppointMen (data) {
  return axios({
    url: api.queryAppointMen,
    method: 'post',
    data: data
  })
}

export function doUrge (data) {
  return axios({
    url: api.doUrge,
    method: 'post',
    data: data
  })
}

export function queryAssigee (data) {
  return axios({
    url: api.queryAssigee,
    method: 'post',
    data: data
  })
}

export function queryApproHis (data) {
  return axios({
    url: api.queryApproHis,
    method: 'post',
    params: data,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export default api
