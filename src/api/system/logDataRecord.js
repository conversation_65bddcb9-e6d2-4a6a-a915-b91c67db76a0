
import { axios } from '@/utils/request'

/**
 *  queryLog: 获取修改记录
 */
const api = {
  queryLog: '/logDataRecord/queryLog'
}

export function queryBusiness (url, parameter) {
  return axios({
    url: url,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function queryLog (parameter) {
  return axios({
    url: api.queryLog,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export default api
