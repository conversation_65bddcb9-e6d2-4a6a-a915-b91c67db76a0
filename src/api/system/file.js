// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { qs, axios } from '@/utils/request'

/**
 * deleteFile: 删除附件
 * listFiles: 查看附件
 */
const api = {
  deleteFile: '/file/deleteFile',
  listFiles: '/file/listFiles',
  listFileMinio: '/file/minio/listFiles',
  fileUpload: '/file/fileUploadMinio',
  fileUploadMinio: '/file/minio/fileUpload',
  getPicMinio: '/file/getPicMinio',
  deleteFileMinioNew: '/file/minio/deleteFile',
  deleteFileMinio: '/file/deleteFileMinio',
  queryUploadFile: '/file/queryUploadFile',
  fileUp: '/file/fileUp',
  fileDown: '/file/fileDown',
  fileDownloadAll: '/file/fileDownloadAll'
}

export function fileDownloadAll (parameter) {
  return axios({
    url: api.fileDownloadAll,
    method: 'get',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

// export function fileDownloadAll (data) {
//   return axios({
//     url: api.fileDownloadAll,
//     responseType: 'blob',
//     method: 'get',
//     data: data
//   })
// }

export function fileDown (params) {
  return axios({
    url: api.fileDown,
    method: 'post',
    params
  })
}

export function fileUp (params) {
  return axios({
    url: api.fileUp,
    method: 'post',
    params
  })
}

export function queryUploadFile (data) {
  return axios({
    url: api.queryUploadFile,
    method: 'post',
    data: data
  })
}

export function deleteFile (parameter) {
  return axios({
    url: api.deleteFile,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}
export function listFileMinio (parameter) {
  return axios({
    url: api.listFileMinio,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function listFiles (parameter) {
  return axios({
    url: api.listFiles,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function fileUploadMinio (data) {
  return axios({
    url: api.fileUploadMinio,
    method: 'post',
    data
  })
}

export function fileUpload (data) {
  return axios({
    url: api.fileUpload,
    method: 'post',
    data
  })
}

export function getPicMinio (data) {
  return axios({
    url: api.getPicMinio,
    method: 'post',
    data: data
  })
}

export function deleteFileMinio (params) {
  return axios({
    url: api.deleteFileMinio,
    method: 'delete',
    params
  })
}

export function deleteFileMinioNew (params) {
  return axios({
    url: api.deleteFileMinioNew,
    method: 'get',
    params
  })
}
export default api
