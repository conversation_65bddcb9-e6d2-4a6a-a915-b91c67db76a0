// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 *  findKpiCompanySources: 获取 KPI 图表数据
 *
 *  findTodoCount: 获取各类待办统计
 *  findPrlineSource: 获取采购申请数据
 *  findRfqlineSource: 获取询价申请数据
 *  findPolineSource: 获取订单申请数据
 *  findQuolineSource:获取报价申请数据
 *  findStatusSource: 获取设备申请数据
 *  findEnergyforcomSource: 获取综合能耗申请
 *  findEnergyformacSource: 获取装卸机械能耗申请
 *  findEnergyFoShipSource: 获取船舶能耗申请
 *  findMachuseMain: 获取装卸机械运行情况数据
 *  findWorkorderSource: 获取工单申请数据
 *  findOutmainanceSource: 获取设备申请数据
 *  findKeyProjectMain: 获取重点任务月度执行申请
 *  findShiprunMain: 获取船舶运行情况
 *  findFacilitiesUsage: 获取设施使用情况
 *  findUnifiedPlan: 获取统购计划
 *  findSupplierCodeSource: 获取供应商申请
 *  findKpiManage: 获取指标管理
 *  findTechinnovationSource: 获取科技创新申请
 *  findShoreconsSource: 获取岸电建设情况申报
 *  findFacilitiesProjectMain: 获取重大项目月度执行申请
 */
const api = {
  findKpiCompanySources: '/todo/queryAssetOrgCount',

  findTodoCount: '/todo/findToDoTasksSummary',
  findPrlineSource: '/todo/queryPrline',
  findRfqlineSource: '/todo/queryRfqline',
  findPolineSource: '/todo/queryPoline',
  findQuolineSource: '/todo/queryQuo',
  findStatusSource: '/todo/queryAssetSbStatus',
  findEnergyforcomSource: '/todo/queryEnergyforcom',
  findEnergyformacSource: '/todo/queryEnergyformac',
  findEnergyFoShipSource: '/todo/queryEnergyforship',
  findMachuseMain: '/todo/queryMachuseMain',
  findWorkorderSource: '/todo/queryWorkorder',
  findOutmainanceSource: '/todo/queryOutmainance',
  findKeyProjectMain: '/todo/keyProjectMain',
  findShiprunMain: '/todo/queryShipRunMain',
  findFacilitiesUsage: '/todo/facilitiesUsage',
  findUnifiedPlan: '/todo/findUnifiedPlan',
  findSupplierCodeSource: '/todo/findSupplierCode',
  findKpiManage: '/todo/findKpiManage',
  findTechinnovationSource: '/todo/findTechinnovation',
  findShoreconsSource: '/todo/findShorecons',
  findFacilitiesProjectMain: '/todo/FacilitiesProjectMain'
}

export function findKpiCompanySources (data) {
  return axios({
    url: api.findKpiCompanySources,
    method: 'post',
    data: data
  })
}

export function findTodoCount (data) {
  return axios({
    url: api.findTodoCount,
    method: 'post',
    data: data
  })
}

export function findPrlineSource (data) {
  return axios({
    url: api.findPrlineSource,
    method: 'post',
    data: data
  })
}

export function findRfqlineSource (data) {
  return axios({
    url: api.findRfqlineSource,
    method: 'post',
    data: data
  })
}

export function findPolineSource (data) {
  return axios({
    url: api.findPolineSource,
    method: 'post',
    data: data
  })
}

export function findQuolineSource (data) {
  return axios({
    url: api.findQuolineSource,
    method: 'post',
    data: data
  })
}

export function findStatusSource (data) {
  return axios({
    url: api.findStatusSource,
    method: 'post',
    data: data
  })
}

export function findEnergyforcomSource (data) {
  return axios({
    url: api.findEnergyforcomSource,
    method: 'post',
    data: data
  })
}

export function findEnergyformacSource (data) {
  return axios({
    url: api.findEnergyformacSource,
    method: 'post',
    data: data
  })
}

export function findEnergyFoShipSource (data) {
  return axios({
    url: api.findEnergyFoShipSource,
    method: 'post',
    data: data
  })
}

export function findMachuseMain (data) {
  return axios({
    url: api.findMachuseMain,
    method: 'post',
    data: data
  })
}

export function findWorkorderSource (data) {
  return axios({
    url: api.findWorkorderSource,
    method: 'post',
    data: data
  })
}

export function findOutmainanceSource (data) {
  return axios({
    url: api.findOutmainanceSource,
    method: 'post',
    data: data
  })
}

export function findKeyProjectMain (data) {
  return axios({
    url: api.findKeyProjectMain,
    method: 'post',
    data: data
  })
}

export function findShiprunMain (data) {
  return axios({
    url: api.findShiprunMain,
    method: 'post',
    data: data
  })
}

export function findFacilitiesUsage (data) {
  return axios({
    url: api.findFacilitiesUsage,
    method: 'post',
    data: data
  })
}

export function findUnifiedPlan (data) {
  return axios({
    url: api.findUnifiedPlan,
    method: 'post',
    data: data
  })
}

export function findSupplierCodeSource (data) {
  return axios({
    url: api.findSupplierCodeSource,
    method: 'post',
    data: data
  })
}

export function findKpiManage (data) {
  return axios({
    url: api.findKpiManage,
    method: 'post',
    data: data
  })
}

export function findTechinnovationSource (data) {
  return axios({
    url: api.findTechinnovationSource,
    method: 'post',
    data: data
  })
}

export function findShoreconsSource (data) {
  return axios({
    url: api.findShoreconsSource,
    method: 'post',
    data: data
  })
}

export function findFacilitiesProjectMain (data) {
  return axios({
    url: api.findFacilitiesProjectMain,
    method: 'post',
    data: data
  })
}
export default api
