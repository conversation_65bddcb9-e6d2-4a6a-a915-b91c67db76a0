// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { axios } from '@/utils/request'

/**
 *  getUserInfo: 获取用户信息
 *  modifyUserInfo: 修改用户信息
 *
 *  getRoleInfo: 获取角色列表
 *  modifyRoleInfo:
 *  getRoleInfoByUserNo: 获取角色列表
 *  getRoleInfoByPages:
 *
 *  getResourceInfo:
 *  modifyResourceInfo:
 *  saveResoureByRoleId:
 *
 *  getProcessModelInfo:
 *  modifyProcessModelInfo:
 *  getDeploymentProcessInfo:
 *
 *  getButtonPermissonList:
 *  getButtonInfoList:
 *  getMenuAllInfoList:
 *  getMenuInfoList:
 *
 *  isPersonExist: 查找用户是否存在
 *  isUserExist: 查找用户是否存在
 */
const api = {
  getUserInfo: '/um/getUserInfo',
  modifyUserInfo: '/um/modifyUserInfo',

  getRoleInfo: '/um/getRoleInfo',
  modifyRoleInfo: 'um/modifyRoleInfo',
  getRoleInfoByUserNo: '/um/getRoleInfoByUserNo',
  getRoleInfoByPages: '/um/getRoleInfoByPages',

  getResourceInfo: 'um/getResourceInfo',
  modifyResourceInfo: 'um/modifyResourceInfo',
  saveResoureByRoleId: 'um/saveResoureByRoleId',

  getProcessModelInfo: '/bpmn/getProcessModelInfo',
  modifyProcessModelInfo: '/bpmn/modifyProcessModelInfo',
  getDeploymentProcessInfo: '/bpmn/getDeploymentProcessInfo',

  getButtonPermissonList: 'um/getButtonPermissonList',
  getButtonInfoList: 'um/getButtonInfoList',
  getMenuAllInfoList: 'um/getMenuAllInfoList',
  getMenuInfoList: 'um/getMenuInfoList',

  isPersonExist: '/um/isPersonExist',
  isUserExist: '/um/isUserExist'
}

export function getUserInfo (parameter) {
  return axios({
    url: api.getUserInfo,
    method: 'post',
    data: parameter
  })
}

export function modifyUserInfo (parameter) {
  return axios({
    url: api.modifyUserInfo,
    method: 'post',
    data: parameter
  })
}

export function getRoleInfo (parameter) {
  return axios({
    url: api.getRoleInfo,
    method: 'post',
    data: parameter
  })
}

export function modifyRoleInfo (parameter) {
  return axios({
    url: api.modifyRoleInfo,
    method: 'post',
    data: parameter
  })
}

export function getRoleInfoByUserNo (parameter) {
  return axios({
    url: api.getRoleInfoByUserNo,
    method: 'post',
    data: parameter
  })
}

export function getRoleInfoByPages (parameter) {
  return axios({
    url: api.getRoleInfoByPages,
    method: 'post',
    data: parameter
  })
}

export function getResourceInfo (parameter) {
  return axios({
    url: api.getResourceInfo,
    method: 'post',
    data: parameter
  })
}

export function modifyResourceInfo (parameter) {
  return axios({
    url: api.modifyResourceInfo,
    method: 'post',
    data: parameter
  })
}

export function saveResoureByRoleId (parameter) {
  return axios({
    url: api.saveResoureByRoleId,
    method: 'post',
    data: parameter
  })
}

export function getProcessModelInfo (parameter) {
  return axios({
    url: api.getProcessModelInfo,
    method: 'post',
    data: parameter
  })
}

export function modifyProcessModelInfo (parameter) {
  return axios({
    url: api.modifyProcessModelInfo,
    method: 'post',
    data: parameter
  })
}

export function getDeploymentProcessInfo (parameter) {
  return axios({
    url: api.getDeploymentProcessInfo,
    method: 'post',
    data: parameter
  })
}

export function getButtonPermissonList (parameter) {
  return axios({
    url: api.getButtonPermissonList,
    method: 'post',
    data: parameter
  })
}

export function getButtonInfoList (parameter) {
  return axios({
    url: api.getButtonInfoList,
    method: 'post',
    data: parameter
  })
}

export function getMenuAllInfoList (parameter) {
  return axios({
    url: api.getMenuAllInfoList,
    method: 'post',
    data: parameter
  })
}

export function getMenuInfoList (parameter) {
  return axios({
    url: api.getMenuInfoList,
    method: 'post',
    data: parameter
  })
}

export function isPersonExist (parameter) {
  return axios({
    url: api.isPersonExist,
    method: 'post',
    data: parameter
  })
}

export function isUserExist (parameter) {
  return axios({
    url: api.isUserExist,
    method: 'post',
    data: parameter
  })
}

export default api
