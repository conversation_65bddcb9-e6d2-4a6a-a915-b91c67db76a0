// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
import { axios } from '@/utils/request'

/**
 *  queryCodeBasCode: 获取基础代码值信息
 *  queryCodeBasClass: 获取基础代码类别信息
 *  modifyCodeBasClass: 基础代码类别信息
 *  modifyCodeBasCode: 基础代码值信息
 */
const api = {
  queryCodeBasCode: '/codeBasCode/queryCodeBasCode',
  queryCodeBasClass: '/codebasclass/queryCodeBasClass',
  modifyCodeBasClass: '/codebasclass/modifyCodeBasClass',
  modifyCodeBasCode: '/codeBasCode/modifyCodeBasCode'
}

export function queryCodeBasCode (parameter) {
  return axios({
    url: api.queryCodeBasCode,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function queryCodeBasClass (parameter) {
  return axios({
    url: api.queryCodeBasClass,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function modifyCodeBasClass (parameter) {
  return axios({
    url: api.modifyCodeBasClass,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function modifyCodeBasCode (parameter) {
  return axios({
    url: api.modifyCodeBasCode,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export default api
