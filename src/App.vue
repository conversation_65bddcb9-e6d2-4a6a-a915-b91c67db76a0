<template>
  <a-config-provider :locale="locale">
    <div id="app" @click="isTimeOut">
      <router-view />
    </div>
  </a-config-provider>
</template>

<script>
import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN'
import { AppDeviceEnquire } from '@/utils/mixin'
import { mapActions } from 'vuex'

export default {
  mixins: [AppDeviceEnquire],
  data () {
    return {
      locale: zhCN,
      lastTime: null,
      timeOut: 240 * 60 * 1000
    }
  },
  created () {
    this.lastTime = new Date().getTime()
  },
  methods: {
    ...mapActions(['Logout']),
    isTimeOut () {
      var currentTime = new Date().getTime()
      if (currentTime - this.lastTime > this.timeOut) {
        if (localStorage.getItem('nbggoods__Access-Token')) {
          // 如果是登录状态
          sessionStorage.clear()
          this.$info({
            title: '提示',
            content: '长时间未操作，请重新登录',
            onOk: async () => {
              this.Logout({}).then(() => {
                setTimeout(() => {
                  window.location.reload()
                }, 16)
              })
              this.lastTime = new Date().getTime()
            }
          })
        } else {
          this.lastTime = new Date().getTime()
        }
      } else {
        this.lastTime = new Date().getTime()
      }
    }
  }
}
</script>

<style lang="less">
#app {
  height: 100%;
}
</style>
