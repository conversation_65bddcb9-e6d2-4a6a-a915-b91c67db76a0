const path = require('path')
const webpack = require('webpack')
const createThemeColorReplacerPlugin = require('./config/plugin.config')

function resolve (dir) {
  return path.join(__dirname, dir)
}

// CDN资源
const assetsCDN = {
  externals: {
    // vue: 'Vue',
    // 'vue-router': 'VueRouter',
    // vuex: 'Vuex',
    // axios: 'axios'
  },

  css: [],

  js: [
    // '//cdn.jsdelivr.net/npm/vue@2.6.10/dist/vue.min.js',
    // '//cdn.jsdelivr.net/npm/vue-router@3.1.3/dist/vue-router.min.js',
    // '//cdn.jsdelivr.net/npm/vuex@3.1.1/dist/vuex.min.js',
    // '//cdn.jsdelivr.net/npm/axios@0.19.0/dist/axios.min.js'
  ]
}

// 运行项目
const isAnalyzer = process.env.VUE_APP_ANALYZER === 'true'
const isEnvProd = process.env.NODE_ENV === 'production'

const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')
const CompressionWebpackPlugin = require('compression-webpack-plugin')
const UglifyJsPlugin = require('uglifyjs-webpack-plugin')

// 运行配置
const vueConfig = {
  publicPath: process.env.VUE_APP_BASE_URL,

  pages: {
    default: {
      title: 'ITSM',
      entry: 'src/main.js',
      template: 'public/index.html',
      filename: 'index.html',
      chunks: ['vendors', 'xlsx', 'ant-design', 'ant-design-vue', 'default']
    }
  },

  configureWebpack: {
    // devtool: 'source-map',
    devtool: 'none',

    plugins: [
      ...(isEnvProd ? [
        new CompressionWebpackPlugin({
          filename: '[path].gz[query]',
          algorithm: 'gzip',
          test: new RegExp('\\.(' + ['html', 'js', 'css'].join('|') + ')$'),
          threshold: 10240,
          minRatio: 0.8,
          deleteOriginalAssets: false
        }),

        new UglifyJsPlugin({
          uglifyOptions: {
            warnings: false,
            compress: {
              drop_debugger: true,
              drop_console: true,
              pure_funcs: ['console.log']
            }
          },
          sourceMap: false,
          parallel: true
        })
      ] : []),

      ...(isAnalyzer ? [ new BundleAnalyzerPlugin({
        analyzerPort: '8881',
        analyzerMode: 'server',
        generateStatsFile: true,
        statsOptions: { source: false }
      }) ] : []),

      new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/)
    ],

    optimization: {
      splitChunks: {
        chunks: 'all',
        minSize: 30000,
        minChunks: 1,
        maxAsyncRequests: 6,
        maxInitialRequests: 4,
        automaticNameDelimiter: '~',
        cacheGroups: {
          vendors: {
            chunks: 'all',
            name: 'vendors',
            test: /\/node_modules\//,
            minChunks: 1,
            maxInitialRequests: 5,
            minSize: 0,
            priority: 10
          },
          xlsx: {
            chunks: 'all',
            name: 'xlsx',
            test: /\/node_modules\/(xlsx|yxg-xlsx-style)\//,
            minChunks: 1,
            minSize: 0,
            priority: 20
          },
          antDesign: {
            chunks: 'all',
            name: 'ant-design',
            test: /\/node_modules\/(@antv|@ant-design)\//,
            minChunks: 1,
            minSize: 0,
            priority: 30
          },
          antDesignVue: {
            chunks: 'all',
            name: 'ant-design-vue',
            test: /\/node_modules\/ant-design-vue\//,
            minChunks: 1,
            minSize: 0,
            priority: 40
          }
        }
      }
    },

    externals: isEnvProd ? assetsCDN.externals : {}
  },

  chainWebpack: config => {
    config.plugins.delete('prefetch')
    config.resolve.alias.set('@$', resolve('src'))

    const imagesRule = config.module.rule('images')
    imagesRule.uses.clear()
    imagesRule.use('file-loader')
      .loader('url-loader')
      .options({
        limit: 10240,
        fallback: {
          loader: 'file-loader',
          options: {
            outputPath: 'static/images'
          }
        }
      })

    const svgRule = config.module.rule('svg')
    svgRule.uses.clear()
    svgRule
      .oneOf('inline')
      .resourceQuery(/inline/)
      .use('vue-svg-icon-loader')
      .loader('vue-svg-icon-loader')
      .end()
      .end()
      .oneOf('external')
      .use('file-loader')
      .loader('file-loader')
      .options({
        name: 'assets/[name].[hash:8].[ext]'
      })

    if (isEnvProd) {
      config
        .plugin(`html-default`)
        .tap(args => {
          args[0].cdn = assetsCDN
          return args
        })
    }
  },

  transpileDependencies: [],

  productionSourceMap: false,

  lintOnSave: undefined,

  css: {
    sourceMap: true,
    loaderOptions: {
      less: {
        modifyVars: {
          // less vars，customize ant design theme
          'primary-color': '#4557FF'
          // 'link-color': '#F5222D',
          // 'border-radius-base': '4px'
        },
        // DO NOT REMOVE THIS LINE
        sourceMap: true,
        javascriptEnabled: true
      }
    }
  },

  devServer: {
    port: 8000,
    proxy: {
      '/api/wzport': {
        target: 'http://127.0.0.1:9992/api',
        ws: false,
        changeOrigin: true,
        pathRewrite: {
          '^/api/wzport': ''
        }
      },
      '/api/iam': {
        target: 'http://***************:9010',
        ws: false,
        changeOrigin: true,
        pathRewrite: {
          '^/api/iam': '/iam'
        }
      },
      '/api': {
        target: 'http://127.0.0.1:8888/api',
        // target: 'http://**************:8888/api', // 测试
        // target: 'http://************:81/api', // 泰兴
        // target: 'http://**************:8000/api', // 周
        // target: 'http://**************:8888//api', // 邹
        // target: 'http://**************:8888/api', // 王
        // target: 'http://**************:8888/api', // 溪
        // target: 'http://*************:8888/api', // 溪
        // target: 'http://**************:8000/api',
        ws: false,
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  }
}

if (process.env.VUE_APP_PREVIEW === 'true') {
  vueConfig.configureWebpack.plugins.push(createThemeColorReplacerPlugin())
}

module.exports = vueConfig
