物资系统WEB框架（前台）

环境和依赖（技术栈）
----
- node
- yarn
- webpack
- eslint
- @vue/cli ~3
- ant-design-vue

- 安装依赖
```
yarn install
```

- 开发模式运行
```
yarn run dev
```

- 编译项目
```
yarn run build
```

- Lints and fixes files
```
yarn run lint
```

其他说明
----
写了一个简单的数据库增删改查的功能DEMO，系统进入后选择业务功能->查询Demo查看
Web框架必须配合物资系统后端框架一起运行，部分数据是通过Mock进行模拟，系统登录模块+查询DEMO模块是真实数据库模拟。
系统登录用户名/密码：admin/admin123

具体框架UI部分详见：
https://www.antdv.com/docs/vue/introduce/
