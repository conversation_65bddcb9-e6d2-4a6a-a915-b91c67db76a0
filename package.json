{"name": "iam-web", "version": "1.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:development": "vue-cli-service build --mode development", "build:test": "vue-cli-service build --mode test", "postinstall": "opencollective-postinstall", "lint:nofix": "vue-cli-service lint --no-fix", "lint": "vue-cli-service lint"}, "dependencies": {"@antv/data-set": "^0.10.2", "ant-design-vue": "1.5.0-rc.5", "axios": "^0.19.0", "core-js": "^3.1.2", "echarts": "^5.0.2", "enquire.js": "^2.1.6", "file-saver": "^2.0.2", "js-base64": "^2", "lodash.clonedeep": "^4.5.0", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "md5": "^2.2.1", "mime": "^2.4.7", "mockjs2": "1.0.8", "moment": "^2.24.0", "nprogress": "^0.2.0", "print-js": "^1.6.0", "qs": "^6.9.2", "script-loader": "^0.7.2", "viser-vue": "^2.4.6", "vue": "^2.6.10", "vue-clipboard2": "^0.2.1", "vue-count-to": "^1.0.13", "vue-cropper": "0.4.9", "vue-draggable-resizable": "^2.1.0", "vue-fullscreen": "^2.6.1", "vue-ls": "^3.2.1", "vue-quill-editor": "^3.0.6", "vue-router": "^3.1.2", "vue-slicksort": "^1.2.0", "vue-svg-component-runtime": "^1.0.1", "vuedraggable": "^2.24.3", "vuex": "^3.1.1", "wangeditor": "^3.1.1", "xlsx": "^0.16.3", "yxg-xlsx-style": "^0.0.1"}, "devDependencies": {"@ant-design/colors": "^3.2.1", "@vue/cli-plugin-babel": "^4.0.4", "@vue/cli-plugin-eslint": "^4.0.4", "@vue/cli-plugin-router": "^4.0.4", "@vue/cli-plugin-unit-jest": "^4.0.4", "@vue/cli-plugin-vuex": "^4.0.4", "@vue/cli-service": "^4.0.4", "@vue/eslint-config-standard": "^4.0.0", "@vue/test-utils": "^1.0.0-beta.29", "babel-eslint": "^8.2.2", "babel-plugin-import": "^1.12.2", "babel-plugin-transform-remove-console": "^6.9.4", "compression-webpack-plugin": "5.0.1", "eslint": "^5.16.0", "eslint-plugin-html": "^5.0.0", "eslint-plugin-vue": "^5.2.3", "less": "^3.0.4", "less-loader": "^5.0.0", "opencollective": "^1.0.3", "opencollective-postinstall": "^2.0.2", "uglifyjs-webpack-plugin": "^2.2.0", "vue-svg-icon-loader": "^2.1.1", "vue-template-compiler": "2.6.11", "webpack-bundle-analyzer": "^4.4.1", "webpack-theme-color-replacer": "^1.2.17"}, "collective": {"type": "opencollective", "url": "https://opencollective.com/ant-design-pro-vue"}}