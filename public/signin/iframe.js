var ACCESS_TOKEN = 'Access-Token'
var PERSON_ID = 'PERSON_ID'
var OPERATOR = 'OPERATOR'
var ORG_ID = 'ORG_ID'

Vue.use(VueStorage, {
    namespace: 'nbggoods__',
    name: 'ls',
    storage: 'local'
})

window.addEventListener(
  'message',
  function (e) {
    if (e.source === window.parent) {
      try {
        var DATA = JSON.parse(e.data) || {}
        var DATA_ACCESS_TOKEN = DATA.ACCESS_TOKEN
        var DATA_PERSON_ID = DATA.PERSON_ID
        var DATA_OPERATOR = DATA.OPERATOR
        var DATA_ORG_ID = DATA.ORG_ID
        // Vue.ls.set(ACCESS_TOKEN, DATA_ACCESS_TOKEN, 7 * 24 * 60 * 60 * 1000)
        // Vue.ls.set(PERSON_ID, DATA_PERSON_ID, 7 * 24 * 60 * 60 * 1000)
        // Vue.ls.set(OPERATOR, DATA_OPERATOR, 7 * 24 * 60 * 60 * 1000)
        // Vue.ls.set(ORG_ID, DATA_ORG_ID, 7 * 24 * 60 * 60 * 1000)
        let a = {"value" : DATA_ACCESS_TOKEN,"expire" : null}
        let b = {"value" : DATA_PERSON_ID,"expire" : null}
        let c = {"value" : DATA_OPERATOR,"expire" : null}
        let d = {"value" : DATA_ORG_ID,"expire" : null}
        this.localStorage.setItem("nbggoods__" + ACCESS_TOKEN, JSON.stringify(a))
        localStorage.setItem("nbggoods__" + PERSON_ID, JSON.stringify(b))
        localStorage.setItem("nbggoods__" + OPERATOR, JSON.stringify(c))
        localStorage.setItem("nbggoods__" + ORG_ID, JSON.stringify(d))
        console.log(e)
        window.parent.postMessage('success', '*')
        // console.log(success)
      } catch (e) {
        console.log(e)
        window.parent.postMessage('error', '*')
      }
    } else {
      window.parent.postMessage('error', '*')
    }
  },
  false
)