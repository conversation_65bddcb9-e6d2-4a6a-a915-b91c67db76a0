// IE 兼容 Object.assign
if (typeof Object.assign !== 'function') {
  Object.defineProperty(Object, "assign", {
    value: function assign(target, varArgs) {
      'use strict';
      if (target === null || target === undefined) {
        throw new TypeError('Cannot convert undefined or null to object');
      }

      var to = Object(target);

      for (var index = 1; index < arguments.length; index++) {
        var nextSource = arguments[index];

        if (nextSource !== null && nextSource !== undefined) { 
          for (var nextKey in nextSource) {
            if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
              to[nextKey] = nextSource[nextKey];
            }
          }
        }
      }
      return to;
    },
    writable: true,
    configurable: true
  });
}
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).VueStorage=t()}(this,function(){"use strict";function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function t(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}var n={},r=new(function(){function e(){o(this,e),Object.defineProperty(this,"length",{get:function(){return Object.keys(n).length}})}return t(e,[{key:"getItem",value:function(e){return e in n?n[e]:null}},{key:"setItem",value:function(e,t){return n[e]=t,!0}},{key:"removeItem",value:function(e){return!!(e in n)&&delete n[e]}},{key:"clear",value:function(){return n={},!0}},{key:"key",value:function(e){var t=Object.keys(n);return void 0!==t[e]?t[e]:null}}]),e}()),s={},a=function(){function e(){o(this,e)}return t(e,null,[{key:"on",value:function(e,t){void 0===s[e]&&(s[e]=[]),s[e].push(t)}},{key:"off",value:function(e,t){s[e].length?s[e].splice(s[e].indexOf(t),1):s[e]=[]}},{key:"emit",value:function(e){var t=e||window.event,n=function(t){try{return JSON.parse(t).value}catch(e){return t}};if(void 0!==t&&void 0!==t.key){var o=s[t.key];void 0!==o&&o.forEach(function(e){e(n(t.newValue),n(t.oldValue),t.url||t.uri)})}}}]),e}(),u=function(){function n(e){if(o(this,n),this.storage=e,this.options={namespace:"",events:["storage"]},Object.defineProperty(this,"length",{get:function(){return this.storage.length}}),"undefined"!=typeof window)for(var t in this.options.events)window.addEventListener?window.addEventListener(this.options.events[t],a.emit,!1):window.attachEvent?window.attachEvent("on".concat(this.options.events[t]),a.emit):window["on".concat(this.options.events[t])]=a.emit}return t(n,[{key:"setOptions",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};this.options=Object.assign(this.options,e)}},{key:"set",value:function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null,o=JSON.stringify({value:t,expire:null!==n?(new Date).getTime()+n:null});this.storage.setItem(this.options.namespace+e,o)}},{key:"get",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,n=this.storage.getItem(this.options.namespace+e);if(null!==n)try{var o=JSON.parse(n);if(null===o.expire)return o.value;if(o.expire>=(new Date).getTime())return o.value;this.remove(e)}catch(e){return t}return t}},{key:"key",value:function(e){return this.storage.key(e)}},{key:"remove",value:function(e){return this.storage.removeItem(this.options.namespace+e)}},{key:"clear",value:function(){if(0!==this.length){for(var e=[],t=0;t<this.length;t++){var n=this.storage.key(t);!1!==new RegExp("^".concat(this.options.namespace,".+"),"i").test(n)&&e.push(n)}for(var o in e)this.storage.removeItem(e[o])}}},{key:"on",value:function(e,t){a.on(this.options.namespace+e,t)}},{key:"off",value:function(e,t){a.off(this.options.namespace+e,t)}}]),n}(),l="undefined"!=typeof window?window:global||{},e={install:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=Object.assign({},t,{storage:t.storage||"local",name:t.name||"ls"});if(n.storage&&-1===["memory","local","session"].indexOf(n.storage))throw new Error('Vue-ls: Storage "'.concat(n.storage,'" is not supported'));var o=null;switch(n.storage){case"local":o="localStorage"in l?l.localStorage:null;break;case"session":o="sessionStorage"in l?l.sessionStorage:null;break;case"memory":o=r}o||(o=r,console.error('Vue-ls: Storage "'.concat(n.storage,'" is not supported your system, use memory storage')));var i=new u(o);i.setOptions(Object.assign(i.options,{namespace:""},n||{})),e[n.name]=i,Object.defineProperty(e.prototype,"$".concat(n.name),{get:function(){return i}})}};return l.VueStorage=e});
